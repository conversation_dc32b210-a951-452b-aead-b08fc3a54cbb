"""
资源过滤器
"""
import django_filters
from django.db import models
from .models import Resource


class ResourceFilter(django_filters.FilterSet):
    """资源过滤器"""
    
    # 分类过滤
    category = django_filters.NumberFilter(field_name='category__id')
    category_slug = django_filters.CharFilter(field_name='category__slug')
    
    # 标签过滤
    tags = django_filters.CharFilter(method='filter_tags')
    
    # 文件类型过滤
    file_type = django_filters.CharFilter(field_name='file_type', lookup_expr='icontains')
    file_format = django_filters.CharFilter(field_name='file_format', lookup_expr='iexact')
    
    # 积分范围过滤
    min_points = django_filters.NumberFilter(field_name='required_points', lookup_expr='gte')
    max_points = django_filters.NumberFilter(field_name='required_points', lookup_expr='lte')
    
    # 评分过滤
    min_rating = django_filters.NumberFilter(field_name='rating_score', lookup_expr='gte')
    
    # 时间范围过滤
    date_from = django_filters.DateFilter(field_name='created_at', lookup_expr='gte')
    date_to = django_filters.DateFilter(field_name='created_at', lookup_expr='lte')
    
    # 特殊过滤
    is_free = django_filters.BooleanFilter(method='filter_is_free')
    is_premium = django_filters.BooleanFilter(field_name='is_premium')
    is_featured = django_filters.BooleanFilter(field_name='is_featured')
    
    # 作者过滤
    author = django_filters.NumberFilter(field_name='author__id')
    author_username = django_filters.CharFilter(field_name='author__username', lookup_expr='iexact')
    
    class Meta:
        model = Resource
        fields = [
            'category', 'category_slug', 'tags', 'file_type', 'file_format',
            'min_points', 'max_points', 'min_rating', 'date_from', 'date_to',
            'is_free', 'is_premium', 'is_featured', 'author', 'author_username'
        ]
    
    def filter_tags(self, queryset, name, value):
        """标签过滤"""
        if value:
            tag_names = [tag.strip() for tag in value.split(',')]
            return queryset.filter(tags__name__in=tag_names).distinct()
        return queryset
    
    def filter_is_free(self, queryset, name, value):
        """免费资源过滤"""
        if value is True:
            return queryset.filter(required_points=0, is_premium=False)
        elif value is False:
            return queryset.filter(
                models.Q(required_points__gt=0) | models.Q(is_premium=True)
            )
        return queryset
