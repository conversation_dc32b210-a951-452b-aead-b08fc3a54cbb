"""
用户相关模型
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel
import uuid


class UserGroup(BaseModel):
    """用户组模型"""
    name = models.CharField('组名', max_length=50, unique=True)
    description = models.TextField('描述', blank=True)
    permissions = models.JSONField('权限配置', default=dict)
    download_limit = models.PositiveIntegerField('下载限制', default=10)
    upload_limit = models.PositiveIntegerField('上传限制', default=5)
    max_file_size = models.PositiveIntegerField('最大文件大小(MB)', default=10)
    is_vip = models.BooleanField('是否VIP', default=False)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    class Meta:
        verbose_name = '用户组'
        verbose_name_plural = '用户组'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class User(AbstractUser):
    """自定义用户模型"""
    email = models.EmailField('邮箱', unique=True)
    phone = models.CharField('手机号', max_length=20, blank=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True)
    nickname = models.CharField('昵称', max_length=50, blank=True)
    bio = models.TextField('个人简介', max_length=500, blank=True)
    birthday = models.DateField('生日', null=True, blank=True)
    gender = models.CharField(
        '性别',
        max_length=10,
        choices=[('male', '男'), ('female', '女'), ('other', '其他')],
        blank=True
    )
    
    # 用户状态
    user_group = models.ForeignKey(
        UserGroup,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='用户组'
    )
    is_verified = models.BooleanField('是否验证', default=False)
    is_vip = models.BooleanField('是否VIP', default=False)
    vip_expire_date = models.DateTimeField('VIP到期时间', null=True, blank=True)
    
    # 积分系统
    points = models.PositiveIntegerField('积分', default=0)
    total_points_earned = models.PositiveIntegerField('累计获得积分', default=0)
    total_points_spent = models.PositiveIntegerField('累计消费积分', default=0)
    
    # 统计信息
    download_count = models.PositiveIntegerField('下载次数', default=0)
    upload_count = models.PositiveIntegerField('上传次数', default=0)
    login_count = models.PositiveIntegerField('登录次数', default=0)
    last_login_ip = models.GenericIPAddressField('最后登录IP', null=True, blank=True)
    
    # 时间戳
    created_at = models.DateTimeField('注册时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['is_active', '-created_at']),
        ]
    
    def __str__(self):
        return self.email
    
    @property
    def display_name(self):
        """显示名称"""
        return self.nickname or self.username or self.email
    
    @property
    def is_vip_active(self):
        """VIP是否有效"""
        if not self.is_vip:
            return False
        if self.vip_expire_date and self.vip_expire_date < timezone.now():
            return False
        return True
    
    def add_points(self, amount, reason=''):
        """增加积分"""
        self.points += amount
        self.total_points_earned += amount
        self.save(update_fields=['points', 'total_points_earned'])
        
        # 记录积分变动
        PointsHistory.objects.create(
            user=self,
            amount=amount,
            balance=self.points,
            type='earn',
            reason=reason
        )
    
    def spend_points(self, amount, reason=''):
        """消费积分"""
        if self.points < amount:
            raise ValueError('积分不足')
        
        self.points -= amount
        self.total_points_spent += amount
        self.save(update_fields=['points', 'total_points_spent'])
        
        # 记录积分变动
        PointsHistory.objects.create(
            user=self,
            amount=-amount,
            balance=self.points,
            type='spend',
            reason=reason
        )


class PointsHistory(BaseModel):
    """积分历史记录"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='points_history',
        verbose_name='用户'
    )
    amount = models.IntegerField('变动积分')
    balance = models.PositiveIntegerField('余额')
    type = models.CharField(
        '类型',
        max_length=20,
        choices=[
            ('earn', '获得'),
            ('spend', '消费'),
            ('refund', '退还'),
            ('admin', '管理员调整'),
        ]
    )
    reason = models.CharField('原因', max_length=200)
    related_object_type = models.CharField('关联对象类型', max_length=50, blank=True)
    related_object_id = models.CharField('关联对象ID', max_length=50, blank=True)
    
    class Meta:
        verbose_name = '积分记录'
        verbose_name_plural = '积分记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['type', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} {self.type} {self.amount} 积分"


class UserProfile(BaseModel):
    """用户扩展资料"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name='用户'
    )
    website = models.URLField('个人网站', blank=True)
    location = models.CharField('所在地', max_length=100, blank=True)
    company = models.CharField('公司', max_length=100, blank=True)
    job_title = models.CharField('职位', max_length=100, blank=True)
    
    # 社交媒体
    weibo = models.CharField('微博', max_length=100, blank=True)
    wechat = models.CharField('微信', max_length=100, blank=True)
    qq = models.CharField('QQ', max_length=20, blank=True)
    github = models.CharField('GitHub', max_length=100, blank=True)
    
    # 偏好设置
    language = models.CharField(
        '语言',
        max_length=10,
        choices=[('zh-hans', '简体中文'), ('en', 'English')],
        default='zh-hans'
    )
    timezone = models.CharField('时区', max_length=50, default='Asia/Shanghai')
    email_notifications = models.BooleanField('邮件通知', default=True)
    
    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
    
    def __str__(self):
        return f"{self.user.display_name} 的资料"


class UserFavorite(BaseModel):
    """用户收藏"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='favorites',
        verbose_name='用户'
    )
    content_type = models.CharField('内容类型', max_length=50)
    object_id = models.CharField('对象ID', max_length=50)
    
    class Meta:
        verbose_name = '用户收藏'
        verbose_name_plural = '用户收藏'
        unique_together = ['user', 'content_type', 'object_id']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['content_type', 'object_id']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 收藏 {self.content_type}#{self.object_id}"


class UserHistory(BaseModel):
    """用户浏览历史"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='history',
        verbose_name='用户'
    )
    content_type = models.CharField('内容类型', max_length=50)
    object_id = models.CharField('对象ID', max_length=50)
    view_count = models.PositiveIntegerField('浏览次数', default=1)
    last_viewed_at = models.DateTimeField('最后浏览时间', default=timezone.now)
    
    class Meta:
        verbose_name = '浏览历史'
        verbose_name_plural = '浏览历史'
        unique_together = ['user', 'content_type', 'object_id']
        ordering = ['-last_viewed_at']
        indexes = [
            models.Index(fields=['user', '-last_viewed_at']),
            models.Index(fields=['content_type', 'object_id']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 浏览 {self.content_type}#{self.object_id}"


class SignInRecord(BaseModel):
    """签到记录"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='signin_records',
        verbose_name='用户'
    )
    date = models.DateField('签到日期', default=timezone.now)
    points_earned = models.PositiveIntegerField('获得积分', default=0)
    consecutive_days = models.PositiveIntegerField('连续天数', default=1)
    
    class Meta:
        verbose_name = '签到记录'
        verbose_name_plural = '签到记录'
        unique_together = ['user', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['user', '-date']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} {self.date} 签到"
