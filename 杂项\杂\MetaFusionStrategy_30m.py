# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, merge_informative_pair
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, RealParameter
from pandas import DataFrame
# --------------------------------

# --- Add your lib to import here ---
import talib.abstract as ta
import pandas_ta as pta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from functools import reduce
import numpy as np
import functools
from freqtrade.misc import logger

# --- Custom Stoploss ---
from freqtrade.persistence import Trade
from datetime import datetime

"""
====================================================================================================
MetaFusionStrategy_30m (元融合策略 - 单周期版)

本策略是多周期版本 MetaFusionStrategy 的简化版，专注于单一的30分钟时间框架。
它移除了所有大周期分析，以获得更快的执行速度和更简单的回测。

该策略建立在一个简化的四层决策流程之上：
1.  市场状态过滤器: 识别当前市场是处于趋势市还是震荡市。
2.  信号生成: 一个从多个高质量来源收集信号的评分系统。
3.  信号加强与过滤: 通过协同效应奖金优化原始分数。
4.  资金与风险管理: 通过专业级的仓位控制、止损和止盈技术来管理交易。
================================C====================================================================
"""
class MetaFusionStrategy30m(IStrategy):
    INTERFACE_VERSION = 3 # 策略接口版本
    
    # 日志缓存
    _log_cache = {}
    
    # --- 策略通用配置 ---
    timeframe = '30m'
    # informative_timeframe 已被移除
    can_short = True
    
    # --- 投资回报率(ROI)与止损 ---
    # ROI被禁用，因为我们将使用一个自定义的多方面退出逻辑。
    minimal_roi = {"0": 100}
    # 止损由我们的 custom_stoploss 函数管理。
    stoploss = -0.99
    
    # --- 追踪止损 ---
    # 我们在 custom_stoploss 中实现了自定义的追踪止损。
    trailing_stop = False
    
    # --- 流程与启动 ---
    process_only_new_candles = True # 只处理新的K线
    startup_candle_count = 200 # 基于最长指标周期所需K线数量
    
    # --- 订单类型 ---
    order_types = {
        'entry': 'limit',
        'exit': 'market', # 使用市价单退出以确保执行
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }
    
    # --- 自定义函数开关 ---
    use_custom_stoploss = True
    use_exit_signal = True
    
    
    # --- 可优化的超参数 ---
    # TODO: (中文) 为每个模块的所有关键参数定义超参数优化空间。

    #================================================================================================
    # 第一层: 市场状态过滤器 (Market Regime Filter) - 已禁用
    # --- 目标: 识别当前市场是处于趋势市还是震荡市。
    # --- 指标: ADX,布林带宽度, KAMA斜率。
    #================================================================================================
    
    # # --- ADX 参数 ---
    # regime_adx_period = IntParameter(5, 12, default=7, space="protection")
    # regime_adx_trend_threshold = IntParameter(8, 15, default=10, space="protection")
    # regime_adx_range_threshold = IntParameter(8, 15, default=12, space="protection")
    
    # # --- 布林带参数 ---
    # regime_bb_period = IntParameter(5, 15, default=7, space="protection")
    # regime_bb_std = RealParameter(1.2, 2.2, default=1.8, space="protection")
    
    # # --- KAMA 参数 ---
    # regime_kama_period = IntParameter(3, 10, default=5, space="protection")
    

    #================================================================================================
    # 第二层 & 第三层: 信号生成与加强 (SIGNAL GENERATION & ENHANCEMENT)
    # --- 目标: 从多个高质量信号源中生成原始分数并对其进行优化。
    # --- 指标: 三重SuperTrend, MACD/EMA共振, 肯特纳/回归通道, Heikin Ashi等。
    #================================================================================================
    
    # --- 入场分数阈值 (动态) ---
    # 提高入场分数阈值，减少无效交易
    entry_score_threshold_trending = IntParameter(6, 10, default=8, space="buy")
    entry_score_threshold_ranging = IntParameter(4, 8, default=6, space="buy")

    # 使用不同的阈值分别针对多头和空头
    short_entry_threshold_trending = IntParameter(5, 8, default=6, space="sell") 
    short_entry_threshold_ranging = IntParameter(3, 5, default=4, space="sell")

    # --- 信号源参数 ---
    # 2.1: 三重SuperTrend共振信号参数
    st1_period = IntParameter(3, 6, default=4, space='buy', optimize=True)
    st1_multiplier = DecimalParameter(0.8, 2.0, default=1.2, decimals=1, space='buy', optimize=True)
    st2_period = IntParameter(5, 9, default=7, space='buy', optimize=True)
    st2_multiplier = DecimalParameter(1.5, 3.5, default=2.0, decimals=1, space='buy', optimize=True)
    st3_period = IntParameter(7, 12, default=10, space='buy', optimize=True)
    st3_multiplier = DecimalParameter(2.5, 4.5, default=3.0, decimals=1, space='buy', optimize=True)
    st_trend_score = IntParameter(10, 25, default=15, space="buy")

    # 2.2: MACD/EMA共振信号参数
    # 多头参数
    buy_macd_fast = IntParameter(5, 10, default=6, space="buy")
    buy_macd_slow = IntParameter(12, 24, default=16, space="buy")
    buy_macd_signal = IntParameter(3, 8, default=5, space="buy")
    buy_ema_short = IntParameter(3, 8, default=4, space="buy")
    buy_ema_long = IntParameter(15, 40, default=20, space="buy")
    # 空头参数 (独立优化)
    sell_macd_fast = IntParameter(3, 7, default=5, space="sell")
    sell_macd_slow = IntParameter(8, 15, default=10, space="sell")
    sell_macd_signal = IntParameter(3, 8, default=4, space="sell")
    sell_ema_short = IntParameter(3, 8, default=5, space="sell")
    sell_ema_long = IntParameter(15, 30, default=20, space="sell")
    # 分数权重
    harmony_trend_score = IntParameter(8, 20, default=12, space="buy")

    # 2.3: Heikin Ashi 反转信号参数
    ha_strong_candle_pct = DecimalParameter(0.5, 0.75, default=0.6, decimals=2, space="buy") # "强力"K线的实体占比
    ha_reversal_score = IntParameter(15, 35, default=20, space="buy")

    # 3.1: 协同效应增强参数
    synergy_trend_score_bonus = IntParameter(8, 20, default=12, space="buy")


    #================================================================================================
    # 第四层: 资金与风险管理 (MONEY & RISK MANAGEMENT) - 注：原第五层
    # --- 目标: 专业地管理每一笔入场的交易
    # --- 技术: 动态仓位, 动态杠杆, 混合止损, 分段止盈
    #================================================================================================

    # --- 4.1: 混合动态止损参数 ---
    # ATR初始止损
    sl_atr_multiplier = DecimalParameter(1.0, 2.5, default=1.5, space="protection")
    # 分层利润保护止损 (基于GeneTrader)
    # 阶段1
    p1_profit_trigger = DecimalParameter(0.005, 0.015, default=0.008, space='sell')  # 利润达到0.8%时触发
    p1_stop_loss = DecimalParameter(0.002, 0.008, default=0.005, space='sell')      # 止损拉到盈利0.5%
    # 阶段2
    p2_profit_trigger = DecimalParameter(0.015, 0.04, default=0.025, space='sell') # 利润达到2.5%时触发
    p2_stop_loss = DecimalParameter(0.008, 0.02, default=0.012, space='sell')      # 止损拉到盈利1.2%
    # 追踪止损阶段
    tsl_profit_trigger = DecimalParameter(0.03, 0.08, default=0.05, space='sell') # 利润达到5%时激活追踪止损
    tsl_trail_offset = DecimalParameter(0.01, 0.03, default=0.02, space='sell')   # 追踪回撤2%

    # --- 4.2: ATR动态分批止盈参数 ---
    # 阶段1
    tp1_atr_multiplier = DecimalParameter(1.0, 2.0, default=1.3, space='sell')
    tp1_exit_pct = DecimalParameter(0.3, 0.5, default=0.33, space='sell')
    # 阶段2
    tp2_atr_multiplier = DecimalParameter(1.8, 3.5, default=2.2, space='sell')
    tp2_exit_pct = DecimalParameter(0.4, 0.6, default=0.5, space='sell') # 卖出剩余仓位的50%
    # 最终阶段
    tp3_atr_multiplier = DecimalParameter(3.0, 5.0, default=3.5, space='sell')
    # 时间止损 (大幅减少最大持有时间)
    exit_time_in_hours = IntParameter(2, 8, default=3, space='sell')

    # --- 4.3: 动态DCA参数 (基于Jamroly) ---
    dca_enabled = CategoricalParameter([True, False], default=True, space='protection', optimize=True)
    dca_max_orders = IntParameter(2, 6, default=4, space='buy')
    dca_atr_multiplier = DecimalParameter(0.6, 1.5, default=0.8, space='buy') # 初始安全单的ATR距离
    dca_step_scale = DecimalParameter(1.0, 2.0, default=1.3, space='buy')     # 安全单距离的步进乘数
    dca_volume_scale = DecimalParameter(1.0, 2.0, default=1.5, space='buy')   # 安全单仓位的步进乘数

    # --- ATR Parameters for SL/TP/Sizing ---
    risk_atr_period = IntParameter(3, 10, default=7, space="protection")

    @classmethod
    def get_markets_config(cls, config={}) -> dict:
        return {
            'bull': {
                'entry_score_threshold_trending': 7,  # 牛市中提高买入标准
                'entry_score_threshold_ranging': 5,
                'short_entry_threshold_trending': 7,  # 牛市中做空需要更高分数
                'short_entry_threshold_ranging': 5,
                'exit_time_in_hours': 4,
                'sl_atr_multiplier': 1.2  # 牛市中止损可以更松一些
            },
            'bear': {
                'entry_score_threshold_trending': 9,  # 熊市中大幅提高买入标准
                'entry_score_threshold_ranging': 7,
                'short_entry_threshold_trending': 5,  # 熊市中做空标准可以降低
                'short_entry_threshold_ranging': 3,
                'exit_time_in_hours': 2,  # 熊市中更快退出
                'sl_atr_multiplier': 0.8  # 熊市中更严格的止损
            },
        }

    def detect_market_condition(self):
        try:
            btc = self.dp.get_pair_dataframe('BTC/USDT:USDT', '1d')
            if btc.empty:
                self.log_once("BTC/USDT:USDT数据为空，使用默认市场状态'bull'", logger.warning)
                return 'bull'
                
            sma200 = ta.SMA(btc, timeperiod=200)
            if btc['close'].iloc[-1] > sma200.iloc[-1]:
                return 'bull'
            else:
                return 'bear'
        except Exception as e:
            self.log_once(f"检测市场状态时出错: {e}，使用默认市场状态'bull'", logger.warning)
            return 'bull'
            
    def log_once(self, message, level=logger.info):
        """仅记录一次特定消息"""
        key = f"{message}"
        if key not in self._log_cache:
            self._log_cache[key] = True
            level(message)
            
    def get_param_value(self, parameter):
        """安全获取参数值，无论它是IntParameter/DecimalParameter对象还是基本值类型"""
        if hasattr(parameter, 'value'):
            return parameter.value
        return parameter
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        为策略的所有层面填充所有必需的指标。
        """
        # 在指标计算前加载适合当前市场的参数
        try:
            market_condition = self.detect_market_condition()
            market_params = self.get_markets_config().get(market_condition, {})
            
            # 应用市场特定参数
            for param_name, value in market_params.items():
                if hasattr(self, param_name):
                    # 直接设置值，而不是尝试修改IntParameter对象
                    setattr(self, param_name, value)
                    self.log_once(f"已为{market_condition}市场应用参数: {param_name} = {value}", logger.info)
        except Exception as e:
            self.log_once(f"应用市场参数时出错: {e}", logger.warning)
        
        # --- 第一层: 市场状态过滤器 ---
        # 默认值
        dataframe['is_trending'] = False
        dataframe['is_ranging'] = False
        
        # --- 第二层 & 第三层: 信号生成与加强 ---
        # 初始化分数
        dataframe['buy_score'] = 0
        dataframe['sell_score'] = 0

        # 计算MACD和ADX趋势指标
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        
        # 计算波动率过滤器 - 避免波动过大时进入市场
        dataframe['natr'] = ta.NATR(dataframe, timeperiod=14)
        
        # 计算ATR指标，用于止损和市场状态检测
        risk_atr_period = self.get_param_value(self.risk_atr_period)
        dataframe[f'atr_{risk_atr_period}'] = ta.ATR(dataframe, timeperiod=risk_atr_period)
        
        # 波动率过滤 - 剔除波动性过高的市场
        volatility_filter = dataframe['natr'] < 0.05  # 5%以下的正常波动率
        
        # 增强的趋势检测
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=21)
        dataframe['ema_trend'] = ta.EMA(dataframe, timeperiod=50)
        
        # 添加RSI过滤
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # 交易量过滤
        dataframe['volume_mean'] = dataframe['volume'].rolling(20).mean()
        volume_filter = dataframe['volume'] > dataframe['volume_mean'] * 0.8
        
        # RSI过滤 - 避免超买超卖区域
        rsi_buy_filter = (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70)
        rsi_sell_filter = (dataframe['rsi'] > 35) & (dataframe['rsi'] < 75)
        
        # 定义明确的上涨趋势和下跌趋势
        uptrend = (
            (dataframe['ema_fast'] > dataframe['ema_slow']) & 
            (dataframe['ema_slow'] > dataframe['ema_trend']) &
            (dataframe['adx'] > 25) &
            volume_filter &
            rsi_buy_filter  # 添加RSI条件
        )
        
        downtrend = (
            (dataframe['ema_fast'] < dataframe['ema_slow']) & 
            (dataframe['ema_slow'] < dataframe['ema_trend']) &
            (dataframe['adx'] > 25) &
            volume_filter &
            rsi_sell_filter  # 添加RSI条件
        )
        
        # 更新趋势状态标志
        dataframe['is_trending'] = uptrend | downtrend
        dataframe['is_ranging'] = ~dataframe['is_trending']
        
        # 多头条件应该更严格 - 因为多头亏损更严重
        dataframe['is_uptrend'] = uptrend & volatility_filter
        dataframe['is_downtrend'] = downtrend & volatility_filter

        # --- 第二层 & 第三层: 信号生成与加强 ---
        # 初始化分数
        dataframe['buy_score'] = 0
        dataframe['sell_score'] = 0

        # --- 2.1: 计算三SuperTrend共振信号并评分 ---
        st_buy_signal, st_sell_signal = self._signal_triple_supertrend(dataframe)
        st_trend_score = self.get_param_value(self.st_trend_score)
        dataframe.loc[st_buy_signal, 'buy_score'] += st_trend_score
        dataframe.loc[st_sell_signal, 'sell_score'] += st_trend_score
        
        # --- 2.2: 计算MACD/EMA共振信号并评分 ---
        harmony_buy, harmony_sell = self._signal_macd_ema_harmony(dataframe)
        harmony_trend_score = self.get_param_value(self.harmony_trend_score)
        dataframe.loc[harmony_buy, 'buy_score'] += harmony_trend_score
        dataframe.loc[harmony_sell, 'sell_score'] += harmony_trend_score
        
        # --- 2.3: 计算Heikin Ashi反转信号并评分 ---
        ha_buy, ha_sell = self._signal_heikin_ashi_reversal(dataframe)
        ha_reversal_score = self.get_param_value(self.ha_reversal_score)
        # 市场状态过滤器已禁用，统一应用完整分数
        dataframe.loc[ha_buy, 'buy_score'] += ha_reversal_score
        dataframe.loc[ha_sell, 'sell_score'] += ha_reversal_score
        
        # --- 额外增强信号: 价格与均线关系 ---
        # 快速EMA交叉
        dataframe['ema_3'] = ta.EMA(dataframe, timeperiod=3)
        dataframe['ema_5'] = ta.EMA(dataframe, timeperiod=5)
        ema_cross_up = qtpylib.crossed_above(dataframe['ema_3'], dataframe['ema_5'])
        ema_cross_down = qtpylib.crossed_below(dataframe['ema_3'], dataframe['ema_5'])
        
        # 为交叉信号加分
        dataframe.loc[ema_cross_up, 'buy_score'] += 5
        dataframe.loc[ema_cross_down, 'sell_score'] += 5
        
        # --- 第三层: 过滤器与增强 ---
        # 3.1: 应用协同效应增强
        dataframe = self._enhance_synergy(dataframe, st_buy_signal, st_sell_signal, harmony_buy, harmony_sell)

        # TODO: (中文) 实现所有其他过滤器和增强逻辑
        # 例如:
        # dataframe = self._filter_volume(dataframe)
        # dataframe = self._filter_btc_safe(dataframe)

        # 在populate_indicators中
        dataframe['price_change'] = dataframe['close'].pct_change(3)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['rsi_change'] = dataframe['rsi'].pct_change(3)
        
        # 看涨背离
        bullish_divergence = (dataframe['price_change'] < 0) & (dataframe['rsi_change'] > 0)
        dataframe.loc[bullish_divergence, 'buy_score'] += 10
        
        # 看跌背离
        bearish_divergence = (dataframe['price_change'] > 0) & (dataframe['rsi_change'] < 0)
        dataframe.loc[bearish_divergence, 'sell_score'] += 10

        # 交易量突破过滤
        dataframe['volume_mean'] = dataframe['volume'].rolling(20).mean()
        volume_increase = (dataframe['volume'] > dataframe['volume_mean'] * 1.5)
        dataframe.loc[volume_increase, 'buy_score'] += 5
        dataframe.loc[volume_increase, 'sell_score'] += 5

        # 计算结构支撑阻力
        dataframe['support'] = dataframe['low'].rolling(14).min()
        dataframe['resistance'] = dataframe['high'].rolling(14).max()
        
        # 仅在支撑附近买入，阻力附近卖出
        dataframe.loc[dataframe['close'] < (dataframe['support'] * 1.03), 'buy_score'] += 10
        dataframe.loc[dataframe['close'] > (dataframe['resistance'] * 0.97), 'sell_score'] += 10

        # 在populate_indicators中
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        
        # 计算ATR指标，用于止损和市场状态检测
        risk_atr_period = self.get_param_value(self.risk_atr_period)
        dataframe[f'atr_{risk_atr_period}'] = ta.ATR(dataframe, timeperiod=risk_atr_period)
        
        # 仅在ADX>25时允许趋势交易
        trend_strength_filter = dataframe['adx'] > 25
        # 修复 FutureWarning 浮点数警告
        dataframe.loc[~trend_strength_filter, 'buy_score'] = (dataframe.loc[~trend_strength_filter, 'buy_score'] * 0.7).astype(int)
        dataframe.loc[~trend_strength_filter, 'sell_score'] = (dataframe.loc[~trend_strength_filter, 'sell_score'] * 0.7).astype(int)

        # 获取BTC/USDT走势
        try:
            btc = self.dp.get_pair_dataframe('BTC/USDT:USDT', self.timeframe)
            if not btc.empty:
                btc['btc_change'] = btc['close'].pct_change()
                
                # 合并至原始数据
                dataframe = dataframe.join(btc['btc_change'], rsuffix='_btc')
                
                # 在强劲下跌时禁止做多
                btc_dump_protection = dataframe['btc_change'] < -0.03
                dataframe.loc[btc_dump_protection, 'buy_score'] = 0
            else:
                self.log_once("BTC/USDT:USDT数据为空，跳过BTC相关保护", logger.warning)
        except Exception as e:
            self.log_once(f"应用BTC相关保护时出错: {e}", logger.warning)
            # 添加默认列，避免在其他地方引用时出错
            if 'btc_change' not in dataframe.columns:
                dataframe['btc_change'] = 0

        # 市场状态检测
        dataframe['atr_percent'] = dataframe[f'atr_{risk_atr_period}'] / dataframe['close']
        dataframe['volatility'] = dataframe['atr_percent'].rolling(24).mean() * 100  # 转为百分比
        
        # 定义市场状态
        dataframe['is_trending'] = (
            (dataframe['adx'] > 25) & 
            ((dataframe['volatility'] > 0.8) | dataframe['volatility'].pct_change() > 0)
        )
        dataframe['is_ranging'] = ~dataframe['is_trending']

        # 趋势市场下提高SuperTrend乘数
        st_multiplier_adj = np.where(dataframe['is_trending'], 1.2, 0.8)
        st1_multiplier = self.get_param_value(self.st1_multiplier)
        dataframe['st1_mult_adj'] = st1_multiplier * st_multiplier_adj

        # Fisher变换，识别超买超卖
        length = 14
        dataframe['highest'] = dataframe['high'].rolling(length).max()
        dataframe['lowest'] = dataframe['low'].rolling(length).min()
        dataframe['roc'] = (dataframe['close'] - dataframe['lowest']) / (dataframe['highest'] - dataframe['lowest'])
        dataframe['fisher'] = 0.5 * np.log((1 + dataframe['roc']) / (1 - dataframe['roc']))
        
        # 超买/超卖信号
        oversold = dataframe['fisher'] < -2
        overbought = dataframe['fisher'] > 2
        
        # 增强买入/卖出评分
        dataframe.loc[oversold, 'buy_score'] += 8
        dataframe.loc[overbought, 'sell_score'] += 8

        return dataframe


    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场决策
        --- 目标: 基于优化后的分数和当前市场状态做出最终的入场决策。
        """
        # 初始化入场标签列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''
        
        # --- 做多条件 (更严格) ---
        # 由于多头表现差，采用更严格的条件
        enter_long_trending = (
            dataframe['is_uptrend'] &  # 使用明确的上涨趋势过滤
            (dataframe['buy_score'] >= self.entry_score_threshold_trending) &
            (dataframe['volume'] > 0) &
            (dataframe['close'] > dataframe['ema_trend']) &  # 价格在长期均线上方
            (dataframe['natr'] < 0.04)  # 额外的波动率限制
        )
        enter_long_ranging = (
            ~dataframe['is_trending'] &  # 非趋势市场
            (dataframe['buy_score'] >= self.entry_score_threshold_ranging) &
            (dataframe['volume'] > 0) &
            (dataframe['natr'] < 0.03)  # 震荡市场波动更小
        )

        # --- 做空条件 (更宽松) ---
        # 由于空头表现好，可以采用相对宽松的条件
        enter_short_trending = (
            dataframe['is_downtrend'] &  # 明确的下跌趋势
            (dataframe['sell_score'] >= self.short_entry_threshold_trending) &
            (dataframe['volume'] > 0)
        )
        enter_short_ranging = (
            ~dataframe['is_trending'] &
            (dataframe['sell_score'] >= self.short_entry_threshold_ranging) &
            (dataframe['volume'] > 0)
        )
        
        # 交换交易方向 - 条件不变，但应用方向相反
        dataframe.loc[enter_short_trending, ['enter_long', 'enter_tag']] = (1, 'buy_trending')
        dataframe.loc[enter_short_ranging, ['enter_long', 'enter_tag']] = (1, 'buy_ranging')
        dataframe.loc[enter_long_trending, ['enter_short', 'enter_tag']] = (1, 'sell_trending')
        dataframe.loc[enter_long_ranging, ['enter_short', 'enter_tag']] = (1, 'sell_ranging')

        # 添加详细的入场标签，方便分析
        try:
            st_buy_signal, st_sell_signal = self._signal_triple_supertrend(dataframe)
            harmony_buy, harmony_sell = self._signal_macd_ema_harmony(dataframe)
            
            # 交换信号应用方向
            st_harmony_confluence = st_sell_signal & harmony_sell
            dataframe.loc[st_harmony_confluence, 'enter_long'] = 1
            dataframe.loc[st_harmony_confluence, 'enter_tag'] = 'st_harmony_confluence'
            
            # 交换信号应用方向
            strong_buy = st_buy_signal & harmony_buy & dataframe['is_uptrend']
            dataframe.loc[strong_buy, 'enter_short'] = 1
            dataframe.loc[strong_buy, 'enter_tag'] = 'strong_buy_signal'
            
        except Exception as e:
            self.log_once(f"应用详细入场标签时出错: {e}", logger.warning)

        return dataframe


    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        主要的退出信号将由 custom_exit 和 custom_stoploss 处理。
        此函数可以作为备用或用于处理高紧急度的退出信号。
        """
        # 示例: 当BTC极端崩溃时的紧急退出
        # dataframe.loc[self.emergency_btc_filter(dataframe), ['exit_long', 'exit_short']] = (1, 'emergency_exit')
        return dataframe
    
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        第四层: 资金与风险管理 (止损部分)
        --- 目标: 将多种止损概念结合成一个稳健的混合系统。
        """
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if not dataframe.empty:
            last_candle = dataframe.iloc[-1]
            risk_atr_period = self.get_param_value(self.risk_atr_period)
            atr_value = last_candle.get(f'atr_{risk_atr_period}')
            if atr_value:
                # --- ATR初始止损 ---
                sl_atr_multiplier = self.get_param_value(self.sl_atr_multiplier)
                
                # 交换多空止损逻辑 - 现在空头使用更严格的止损
                if trade.trade_direction == 'short':
                    # 空头止损更紧(更敏感)
                    short_sl_multiplier = sl_atr_multiplier * 0.8
                    initial_stop_price_short = trade.open_rate + (atr_value * short_sl_multiplier)
                else:
                    # 多头止损可以更宽松
                    initial_stop_price_long = trade.open_rate - (atr_value * sl_atr_multiplier)
                
                # --- 分层利润锁定 + 追踪止损 (核心逻辑) ---
                p1_profit_trigger = self.get_param_value(self.p1_profit_trigger)
                p1_stop_loss = self.get_param_value(self.p1_stop_loss)
                p2_profit_trigger = self.get_param_value(self.p2_profit_trigger)
                p2_stop_loss = self.get_param_value(self.p2_stop_loss)
                tsl_profit_trigger = self.get_param_value(self.tsl_profit_trigger)
                tsl_trail_offset = self.get_param_value(self.tsl_trail_offset)
                
                # 对于多头 - 使用原本空头的逻辑，更宽松的止损
                if trade.trade_direction == 'long':
                    # 阶段1
                    if current_profit > p1_profit_trigger:
                        sl_price = trade.open_rate * (1 + p1_stop_loss)
                        return max(sl_price, initial_stop_price_long) # 取更优的止损
                    # 阶段2
                    elif current_profit > p2_profit_trigger:
                        sl_price = trade.open_rate * (1 + p2_stop_loss)
                        return max(sl_price, initial_stop_price_long) # 取更优的止损
                    # 阶段3
                    elif current_profit > tsl_profit_trigger:
                        sl_price = current_rate * (1 - tsl_trail_offset)
                        return max(sl_price, initial_stop_price_long) # 取更优的止损

                    return initial_stop_price_long
                
                # 对于空头 - 使用原本多头的逻辑，更严格的止损
                elif trade.trade_direction == 'short':
                    # 早期止损 - 加入时间因素，时间越长，止损越严格
                    trade_duration_hours = (current_time - trade.open_date_utc).total_seconds() / 3600
                    
                    # 24小时后，如果还没有盈利，拉高止损线
                    if trade_duration_hours > 24 and current_profit < 0.005:
                        # 止损线逐渐向零线靠拢
                        time_factor = min(0.8, trade_duration_hours / 100)  # 最多靠近80%
                        adjusted_stop = trade.open_rate * (1 + 0.01 * (1 - time_factor))
                        return min(adjusted_stop, initial_stop_price_short)
                        
                    # 阶段1: 小额利润保护 (更早锁定利润)
                    if current_profit > p1_profit_trigger:
                        sl_price = trade.open_rate * (1 - p1_stop_loss * 1.2)  # 更激进的锁利润
                        return min(sl_price, initial_stop_price_short) # 取更优的止损
                    # 阶段2: 中等利润保护
                    elif current_profit > p2_profit_trigger:
                        sl_price = trade.open_rate * (1 - p2_stop_loss)
                        return min(sl_price, initial_stop_price_short) # 取更优的止损
                    # 阶段3: 追踪止损
                    elif current_profit > tsl_profit_trigger:
                        # 提高追踪止损敏感度
                        tsl_adjusted = tsl_trail_offset * 0.8
                        sl_price = current_rate * (1 + tsl_adjusted)
                        return min(sl_price, initial_stop_price_short) # 取更优的止损

                    return initial_stop_price_short

        # 如果数据不可用，则回退到静态止损。
        # 返回一个代表-99%亏损的价格，基本等于不设止损，让其他逻辑处理。
        return trade.open_rate * (1 - self.stoploss if trade.trade_direction == 'long' else 1 + self.stoploss)
    
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        第四层: 资金与风险管理 (止盈部分)
        --- 目标: 实现基于ATR的动态分批止盈系统和时间止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        last_candle = dataframe.iloc[-1]
        risk_atr_period = self.get_param_value(self.risk_atr_period)
        atr_value = last_candle.get(f'atr_{risk_atr_period}')
        if not atr_value:
            return None

        # --- 时间止损 ---
        trade_duration_hours = (current_time - trade.open_date_utc).total_seconds() / 3600
        exit_time_in_hours = self.get_param_value(self.exit_time_in_hours)
        if trade_duration_hours > exit_time_in_hours:
            return f"time_exit_{int(trade_duration_hours)}h"

        # --- ATR动态止盈 ---
        # 计算不同阶段的止盈价格
        tp1_atr_multiplier = self.get_param_value(self.tp1_atr_multiplier)
        tp2_atr_multiplier = self.get_param_value(self.tp2_atr_multiplier)
        tp3_atr_multiplier = self.get_param_value(self.tp3_atr_multiplier)
        tp1_exit_pct = self.get_param_value(self.tp1_exit_pct)
        tp2_exit_pct = self.get_param_value(self.tp2_exit_pct)
        
        tp1_price = trade.open_rate + (atr_value * tp1_atr_multiplier) if trade.trade_direction == 'long' else \
                    trade.open_rate - (atr_value * tp1_atr_multiplier)
        tp2_price = trade.open_rate + (atr_value * tp2_atr_multiplier) if trade.trade_direction == 'long' else \
                    trade.open_rate - (atr_value * tp2_atr_multiplier)
        tp3_price = trade.open_rate + (atr_value * tp3_atr_multiplier) if trade.trade_direction == 'long' else \
                    trade.open_rate - (atr_value * tp3_atr_multiplier)
        
        # 获取已成功退出的次数
        successful_exits = trade.nr_of_successful_exits

        if trade.trade_direction == 'long':
            # 阶段1
            if current_rate >= tp1_price and successful_exits == 0:
                return 'partial_exit_tp1', tp1_exit_pct
            # 阶段2
            elif current_rate >= tp2_price and successful_exits == 1:
                return 'partial_exit_tp2', tp2_exit_pct
            # 阶段3 (最终退出)
            elif current_rate >= tp3_price and successful_exits == 2:
                return 'full_exit_tp3'
        
        elif trade.trade_direction == 'short':
            # 阶段1
            if current_rate <= tp1_price and successful_exits == 0:
                return 'partial_exit_tp1', tp1_exit_pct
            # 阶段2
            elif current_rate <= tp2_price and successful_exits == 1:
                return 'partial_exit_tp2', tp2_exit_pct
            # 阶段3 (最终退出)
            elif current_rate <= tp3_price and successful_exits == 2:
                return 'full_exit_tp3'

        # 在custom_exit中
        if trade.trade_direction == 'long' and last_candle['price_change'] < -0.02:
            return 'momentum_shift_exit'

        # 获取volatility_factor (市场波动指标)
        atr_value = last_candle.get(f'atr_{risk_atr_period}')
        
        # 根据ATR调整利润目标
        volatility_factor = atr_value / last_candle['close']
        profit_target = max(0.015, volatility_factor * 10)  # 最小1.5%
        
        if current_profit > profit_target:
            return 'dynamic_profit_target'

        return None


    def adjust_trade_position(self, trade: 'Trade', current_time: datetime,
                              current_rate: float, current_profit: float, min_stake: float,
                              max_stake: float, **kwargs) -> float:
        """
        第四层: 资金与风险管理 (DCA/仓位调整部分)
        --- 目标: 实现基于ATR的动态DCA（定投/补仓）逻辑
        """
        dca_enabled = self.get_param_value(self.dca_enabled)
        if not dca_enabled:
            return None
            
        # 交换多空补仓逻辑 - 现在对空头交易限制补仓
        if trade.trade_direction == 'short':
            # 只允许在小幅回调时补仓，且利润不能太差
            if current_profit < -0.03:  # 亏损超过3%不再补仓
                return None
            
            # 只允许一次补仓，而非多次
            if trade.nr_of_successful_entries >= 2:
                return None
        
        # 多头交易可以多次补仓
        dca_max_orders = self.get_param_value(self.dca_max_orders)
        # 如果已经达到最大补仓次数，则不再操作
        if trade.nr_of_successful_entries >= dca_max_orders + 1:
            return None

        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        if dataframe.empty:
            return None

        last_candle = dataframe.iloc[-1]
        risk_atr_period = self.get_param_value(self.risk_atr_period)
        atr_value = last_candle.get(f'atr_{risk_atr_period}')
        if not atr_value:
            return None

        # 修改adjust_trade_position中的补仓条件
        last_entry_time = trade.open_date_utc
        if len(trade.orders) > 1:
            last_entry_time = max([o.order_date_utc for o in trade.orders if o.ft_is_open == False])
        
        # 交换冷却期逻辑 - 现在空头有更长冷却期
        if trade.trade_direction == 'short':
            cool_down_hours = 6  # 空头补仓需要更长的冷却期
        else:
            cool_down_hours = 3  # 多头补仓冷却期较短
            
        if (current_time - last_entry_time).total_seconds() / 3600 < cool_down_hours:
            return None  # 冷却期内不补仓

        # --- 计算下一个安全单的目标价格 ---
        num_dcas = trade.nr_of_successful_entries - 1
        
        # 距离步长 (可以随补仓次数增加)
        dca_atr_multiplier = self.get_param_value(self.dca_atr_multiplier)
        dca_step_scale = self.get_param_value(self.dca_step_scale)
        
        # 交换多空距离乘数逻辑
        if trade.trade_direction == 'short':
            # 空头补仓需要更大的回调深度
            distance_multiplier = dca_atr_multiplier * 1.5 * (dca_step_scale ** num_dcas)
        else:
            # 多头使用正常距离乘数
            distance_multiplier = dca_atr_multiplier * (dca_step_scale ** num_dcas)
            
        distance = atr_value * distance_multiplier
        
        # 获取平均入场价，所有计算都应以此为基准
        avg_entry_price = trade.open_rate

        if trade.trade_direction == 'long':
            target_price = avg_entry_price - distance
        else: # is_short
            target_price = avg_entry_price + distance

        # --- 判断是否触发补仓 ---
        should_dca = False
        if trade.trade_direction == 'long' and current_rate <= target_price:
            should_dca = True
        elif trade.trade_direction == 'short' and current_rate >= target_price:
            should_dca = True

        if should_dca:
            # --- 计算补仓金额 ---
            # 仓位步长 (可以随补仓次数增加)
            dca_volume_scale = self.get_param_value(self.dca_volume_scale)
            
            # 交换多空仓位大小逻辑
            if trade.trade_direction == 'short':
                # 空头补仓使用更小的仓位，降低风险
                volume_multiplier = dca_volume_scale * 0.7
            else:
                # 多头补仓可以使用更大的仓位
                volume_multiplier = dca_volume_scale ** num_dcas
                
            # 使用该笔交易的初始仓位作为基数
            initial_stake = trade.stake_amount
            # 计算当前回调深度占ATR的比例
            pullback_ratio = abs(current_rate - trade.open_rate) / atr_value
            # 深度回调增加仓位，浅回调减少仓位
            dca_stake = initial_stake * min(2.0, max(0.5, pullback_ratio))
            
            # 确保补仓金额在最小和最大限制内
            dca_stake = max(min_stake, dca_stake)
            dca_stake = min(max_stake, dca_stake)
            
            return dca_stake

        return None


    # --- Signal & Filter Helper Functions (信号与过滤辅助函数) ---
    def _signal_triple_supertrend(self, dataframe: DataFrame) -> tuple[DataFrame, DataFrame]:
        """
        信号源模块: 三重SuperTrend共振
        当三个不同周期的SuperTrend指标同向时，产生一个强烈的趋势信号。
        """
        # --- 计算三个SuperTrend指标 ---
        st1_period = self.get_param_value(self.st1_period)
        st1_multiplier = self.get_param_value(self.st1_multiplier)
        st1 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=st1_period, multiplier=st1_multiplier
        )
        dataframe['st1_dir'] = st1[f'SUPERTd_{st1_period}_{st1_multiplier}']

        st2_period = self.get_param_value(self.st2_period)
        st2_multiplier = self.get_param_value(self.st2_multiplier)
        st2 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=st2_period, multiplier=st2_multiplier
        )
        dataframe['st2_dir'] = st2[f'SUPERTd_{st2_period}_{st2_multiplier}']

        st3_period = self.get_param_value(self.st3_period)
        st3_multiplier = self.get_param_value(self.st3_multiplier)
        st3 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=st3_period, multiplier=st3_multiplier
        )
        dataframe['st3_dir'] = st3[f'SUPERTd_{st3_period}_{st3_multiplier}']

        # --- 判断共振条件 ---
        # 当三个SuperTrend方向均为1 (看涨) 时，产生做多信号
        long_signal = (
            (dataframe['st1_dir'] == 1) &
            (dataframe['st2_dir'] == 1) &
            (dataframe['st3_dir'] == 1)
        )
        
        # 当三个SuperTrend方向均为-1 (看跌) 时，产生做空信号
        short_signal = (
            (dataframe['st1_dir'] == -1) &
            (dataframe['st2_dir'] == -1) &
            (dataframe['st3_dir'] == -1)
        )
        
        return long_signal, short_signal
    
    def _signal_macd_ema_harmony(self, dataframe: DataFrame) -> tuple[DataFrame, DataFrame]:
        """
        信号源模块: MACD/EMA 共振 (和谐)
        识别 "主趋势中的回调结束点" 这种高质量信号。
        - 主趋势: 由长期和短期EMA的方向定义。
        - 回调结束: 由MACD柱状图穿越零轴确认。
        """
        # --- 多头信号计算 ---
        buy_macd_fast = self.get_param_value(self.buy_macd_fast)
        buy_macd_slow = self.get_param_value(self.buy_macd_slow)
        buy_macd_signal = self.get_param_value(self.buy_macd_signal)
        buy_ema_short = self.get_param_value(self.buy_ema_short)
        buy_ema_long = self.get_param_value(self.buy_ema_long)
        
        buy_macd = ta.MACD(dataframe, fastperiod=buy_macd_fast,
                           slowperiod=buy_macd_slow,
                           signalperiod=buy_macd_signal)
        dataframe['buy_macdhist'] = buy_macd['macdhist']
        dataframe['buy_ema_short'] = ta.EMA(dataframe, timeperiod=buy_ema_short)
        dataframe['buy_ema_long'] = ta.EMA(dataframe, timeperiod=buy_ema_long)

        # 主升趋势
        is_uptrend = dataframe['buy_ema_short'] > dataframe['buy_ema_long']
        # 回调结束 (MACD柱从负值上穿零轴)
        macd_cross_up = qtpylib.crossed_above(dataframe['buy_macdhist'], 0)
        
        long_signal = is_uptrend & macd_cross_up

        # --- 空头信号计算 ---
        sell_macd_fast = self.get_param_value(self.sell_macd_fast)
        sell_macd_slow = self.get_param_value(self.sell_macd_slow)
        sell_macd_signal = self.get_param_value(self.sell_macd_signal)
        sell_ema_short = self.get_param_value(self.sell_ema_short)
        sell_ema_long = self.get_param_value(self.sell_ema_long)
        
        sell_macd = ta.MACD(dataframe, fastperiod=sell_macd_fast,
                            slowperiod=sell_macd_slow,
                            signalperiod=sell_macd_signal)
        dataframe['sell_macdhist'] = sell_macd['macdhist']
        dataframe['sell_ema_short'] = ta.EMA(dataframe, timeperiod=sell_ema_short)
        dataframe['sell_ema_long'] = ta.EMA(dataframe, timeperiod=sell_ema_long)

        # 主跌趋势
        is_downtrend = dataframe['sell_ema_short'] < dataframe['sell_ema_long']
        # 反弹结束 (MACD柱从正值下穿零轴)
        macd_cross_down = qtpylib.crossed_below(dataframe['sell_macdhist'], 0)

        short_signal = is_downtrend & macd_cross_down
        
        return long_signal, short_signal

    def _signal_heikin_ashi_reversal(self, dataframe: DataFrame) -> tuple[DataFrame, DataFrame]:
        """
        信号源模块: Heikin Ashi (平均K线) 反转
        专门用于捕捉震荡行情中的反转点，或趋势行情中的深度回调。
        识别 "红色HA -> 强力绿色HA" (看涨) 或 "绿色HA -> 强力红色HA" (看跌) 的形态。
        """
        # 计算Heikin Ashi K线
        ha_df = qtpylib.heikinashi(dataframe)
        
        # 计算HA K线实体和影线
        ha_body = (ha_df['close'] - ha_df['open']).abs()
        ha_range = (ha_df['high'] - ha_df['low']).abs().replace(0, 0.00001) # 避免除以0
        ha_body_pct = ha_body / ha_range
        
        # 获取参数值
        ha_strong_candle_pct = self.get_param_value(self.ha_strong_candle_pct)
        
        # 定义强力K线 (实体占比大)
        is_strong_green = (ha_df['close'] > ha_df['open']) & (ha_body_pct > ha_strong_candle_pct)
        is_strong_red = (ha_df['open'] > ha_df['close']) & (ha_body_pct > ha_strong_candle_pct)
        
        # 定义回调/反弹K线 (前一根K线是反向的)
        prev_is_red = ha_df['open'].shift(1) > ha_df['close'].shift(1)
        prev_is_green = ha_df['close'].shift(1) > ha_df['open'].shift(1)
        
        # --- 看涨反转信号: [红色HA, 强力绿色HA] ---
        long_signal = prev_is_red & is_strong_green
        
        # --- 看跌反转信号: [绿色HA, 强力红色HA] ---
        short_signal = prev_is_green & is_strong_red
        
        return long_signal, short_signal

    def _enhance_synergy(self, dataframe: DataFrame, *signals) -> DataFrame:
        """
        增强模块: 协同效应
        当多个独立的强信号同时发生时，给予额外的奖励分数。
        """
        # signals 参数是一个元组，包含成对的 (buy_signal, sell_signal)
        # 例如: (st_buy, st_sell, harmony_buy, harmony_sell, ...)
        buy_signals = [signals[i] for i in range(0, len(signals), 2)]
        sell_signals = [signals[i] for i in range(1, len(signals), 2)]

        # --- 趋势协同: 当两个或更多趋势信号同时出现 ---
        # 计算同时出现的买入趋势信号数量
        num_buy_signals = sum(buy_signals)
        # 计算同时出现的卖出趋势信号数量
        num_sell_signals = sum(sell_signals)

        # 如果有两个或以上趋势信号共振，则添加协同奖励分
        synergy_bonus = self.get_param_value(self.synergy_trend_score_bonus)
        dataframe.loc[num_buy_signals >= 2, 'buy_score'] += synergy_bonus
        dataframe.loc[num_sell_signals >= 2, 'sell_score'] += synergy_bonus

        # TODO: (中文) 可以为不同类型的信号组合设计更复杂的协同逻辑
        # 例如: 趋势信号 + 反转信号的组合等

        return dataframe

    # TODO: (中文) 为每个信号源和过滤器创建辅助函数
    # def _filter_btc_safe(self, dataframe: DataFrame) -> DataFrame: ...
    
    def get_pair_performance(self, pair: str) -> float:
        """
        获取交易对的历史表现胜率
        如果没有历史数据，返回默认值0.5（50%胜率）
        """
        try:
            # 尝试从self.dp（DataProvider）获取交易对的历史交易数据
            # 在实际实现中，你可以访问freqtrade的交易记录或其他数据源
            # 此处为简化版本，仅返回默认值
            
            # 可以根据需要扩展此方法，例如：
            # - 从数据库获取该交易对的历史胜率
            # - 根据最近N笔交易计算动态胜率
            # - 与市场趋势结合计算调整后的胜率
            
            self.log_once(f"获取{pair}交易对表现数据，使用默认胜率0.5", logger.info)
            return 0.5  # 默认50%胜率
        except Exception as e:
            self.log_once(f"获取交易对表现时出错: {e}，使用默认胜率0.5", logger.warning)
            return 0.5  # 出错时返回默认值
    
    def custom_stake_amount(self, pair, current_time, current_rate, proposed_stake, min_stake, max_stake, **kwargs):
        """
        根据交易对的历史表现动态调整仓位大小
        """
        try:
            # 获取该交易对的胜率
            win_rate = self.get_pair_performance(pair)
            
            # 根据胜率动态调整仓位
            # 胜率0-50%对应0-100%仓位
            position_scale = min(1.0, win_rate * 2)
            
            # 计算调整后的仓位
            adjusted_stake = proposed_stake * position_scale
            
            # 确保仓位在允许范围内
            adjusted_stake = max(min_stake, min(adjusted_stake, max_stake))
            
            return adjusted_stake
        except Exception as e:
            # 错误处理 - 如果出现任何异常，返回原始建议仓位
            self.log_once(f"调整仓位大小时出错: {e}，使用原始建议仓位", logger.warning)
            return proposed_stake
    
    @staticmethod
    def calculate_fancy_indicator(high, low, close, length):
        # 复杂计算，暂未实现
        return 0  # 返回占位符值 