import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from pandas import DataFrame
from datetime import datetime, timedelta
from freqtrade.persistence import Trade
from typing import Dict, List, Optional
from functools import reduce
import logging
import math

logger = logging.getLogger(__name__)

class VolumaticVIDYAStrategy(IStrategy):
    """
    VolumaticVIDYAStrategy - 基于Volumatic Variable Index Dynamic Average的多空策略
    
    策略思路:
    1. 使用VIDYA (Variable Index Dynamic Average)作为主要趋势指标
    2. 结合ATR建立动态上下通道判断趋势方向
    3. 识别价格支撑阻力区域作为额外的入场信号
    4. 使用成交量分析确认趋势强度
    5. 采用ATR动态止盈止损
    6. 实现仓位管理和杠杆控制
    """

    # 策略参数
    # 买入参数
    buy_vidya_length = IntParameter(5, 30, default=10, space="buy")
    buy_vidya_momentum = IntParameter(10, 40, default=20, space="buy")
    buy_band_distance = DecimalParameter(1.0, 3.0, default=2.0, decimals=1, space="buy")
    buy_pivot_lookback = IntParameter(2, 5, default=3, space="buy")
    buy_volume_threshold = DecimalParameter(1.0, 3.0, default=1.5, decimals=1, space="buy")
    
    # 卖出参数
    sell_vidya_length = IntParameter(5, 30, default=10, space="sell")
    sell_vidya_momentum = IntParameter(10, 40, default=20, space="sell")
    sell_band_distance = DecimalParameter(1.0, 3.0, default=2.0, decimals=1, space="sell")
    sell_pivot_lookback = IntParameter(2, 5, default=3, space="sell")
    sell_volume_threshold = DecimalParameter(1.0, 3.0, default=1.5, decimals=1, space="sell")
    
    # 通用参数
    atr_period = IntParameter(10, 200, default=200, space="protection")
    atr_stop_loss = DecimalParameter(1.0, 4.0, default=2.5, decimals=1, space="protection")
    atr_trailing_stop = DecimalParameter(1.0, 4.0, default=2.0, decimals=1, space="protection")
    atr_take_profit = DecimalParameter(2.0, 6.0, default=4.0, decimals=1, space="protection")
    
    # 仓位管理参数
    max_leverage = DecimalParameter(1.0, 10.0, default=3.0, decimals=1, space="protection")
    risk_per_trade = DecimalParameter(0.01, 0.05, default=0.02, decimals=2, space="protection")
    
    # 时间过滤参数
    startup_candle_count: int = 200  # 需要的历史K线数量
    process_only_new_candles = True  # 只处理新的K线
    use_exit_signal = True  # 使用卖出信号
    can_short = True  # 允许做空
    
    # 订单类型和超时设置
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }
    
    # 杠杆设置
    leverage_optimization = True  # 启用杠杆优化
    
    # 止损设置 - 将在代码中动态设置
    stoploss = -0.99  # 初始止损设置为较大值，实际会通过custom_stoploss动态调整
    
    @property
    def plot_config(self):
        """
        为策略设置图表配置
        """
        return {
            'main_plot': {
                'vidya': {'color': 'green'},
                'upper_band': {'color': 'red'},
                'lower_band': {'color': 'blue'},
            },
            'subplots': {
                "Volume Delta": {
                    'volume_delta': {'color': 'purple'},
                },
                "ATR": {
                    'atr': {'color': 'orange'},
                }
            }
        }
    
    def vidya_calc(self, dataframe: DataFrame, src_column: str, length: int, momentum_length: int) -> np.array:
        """
        计算VIDYA (Variable Index Dynamic Average)
        
        参数:
            dataframe: 包含价格数据的DataFrame
            src_column: 源数据列名
            length: VIDYA长度
            momentum_length: 动量长度
        
        返回:
            np.array: VIDYA值的数组
        """
        src = dataframe[src_column].values
        momentum = np.diff(src, prepend=src[0])
        
        # 计算正向和负向动量
        pos_momentum = np.where(momentum >= 0, momentum, 0)
        neg_momentum = np.where(momentum < 0, -momentum, 0)
        
        # 计算动量的滚动和
        sum_pos_momentum = pd.Series(pos_momentum).rolling(momentum_length).sum().values
        sum_neg_momentum = pd.Series(neg_momentum).rolling(momentum_length).sum().values
        
        # 计算CMO (Chande Momentum Oscillator)
        cmo = np.zeros_like(src)
        divisor = sum_pos_momentum + sum_neg_momentum
        valid_idx = divisor != 0
        cmo[valid_idx] = 100 * (sum_pos_momentum[valid_idx] - sum_neg_momentum[valid_idx]) / divisor[valid_idx]
        abs_cmo = np.abs(cmo)
        
        # 计算VIDYA
        alpha = 2 / (length + 1)
        vidya = np.zeros_like(src)
        vidya[0] = src[0]
        
        for i in range(1, len(src)):
            k = alpha * abs_cmo[i] / 100
            vidya[i] = k * src[i] + (1 - k) * vidya[i-1]
        
        # 应用简单移动平均进行平滑
        vidya_smooth = pd.Series(vidya).rolling(15).mean().values
        
        return vidya_smooth
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        为策略计算技术指标
        """
        # 计算VIDYA指标
        dataframe['vidya'] = self.vidya_calc(
            dataframe, 'close', self.buy_vidya_length.value, self.buy_vidya_momentum.value
        )
        
        # 计算ATR
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        
        # 计算上下通道
        dataframe['upper_band'] = dataframe['vidya'] + dataframe['atr'] * self.buy_band_distance.value
        dataframe['lower_band'] = dataframe['vidya'] - dataframe['atr'] * self.buy_band_distance.value
        
        # 计算趋势状态
        dataframe['trend_up'] = False
        dataframe['trend_down'] = False
        
        # 判断趋势
        for i in range(1, len(dataframe)):
            if dataframe['close'].iloc[i] > dataframe['upper_band'].iloc[i-1]:
                dataframe['trend_up'].iloc[i] = True
            elif dataframe['close'].iloc[i] < dataframe['lower_band'].iloc[i-1]:
                dataframe['trend_down'].iloc[i] = True
            else:
                dataframe['trend_up'].iloc[i] = dataframe['trend_up'].iloc[i-1]
                dataframe['trend_down'].iloc[i] = dataframe['trend_down'].iloc[i-1]
        
        # 计算枢轴点
        pivot_lookback = self.buy_pivot_lookback.value
        
        # 计算局部高点和低点
        dataframe['pivot_high'] = False
        dataframe['pivot_low'] = False
        
        for i in range(pivot_lookback, len(dataframe) - pivot_lookback):
            # 判断是否为局部高点
            is_high = True
            for j in range(1, pivot_lookback + 1):
                if dataframe['high'].iloc[i] <= dataframe['high'].iloc[i-j] or \
                   dataframe['high'].iloc[i] <= dataframe['high'].iloc[i+j]:
                    is_high = False
                    break
            dataframe['pivot_high'].iloc[i] = is_high
            
            # 判断是否为局部低点
            is_low = True
            for j in range(1, pivot_lookback + 1):
                if dataframe['low'].iloc[i] >= dataframe['low'].iloc[i-j] or \
                   dataframe['low'].iloc[i] >= dataframe['low'].iloc[i+j]:
                    is_low = False
                    break
            dataframe['pivot_low'].iloc[i] = is_low
        
        # 计算支撑阻力区
        dataframe['support_zone'] = False
        dataframe['resistance_zone'] = False
        
        for i in range(pivot_lookback, len(dataframe)):
            if dataframe['pivot_low'].iloc[i] and dataframe['low'].iloc[i] > dataframe['vidya'].iloc[i]:
                dataframe['support_zone'].iloc[i] = True
            
            if dataframe['pivot_high'].iloc[i] and dataframe['high'].iloc[i] < dataframe['vidya'].iloc[i]:
                dataframe['resistance_zone'].iloc[i] = True
        
        # 计算成交量分析
        dataframe['up_volume'] = np.where(dataframe['close'] > dataframe['open'], dataframe['volume'], 0)
        dataframe['down_volume'] = np.where(dataframe['close'] < dataframe['open'], dataframe['volume'], 0)
        
        # 计算成交量差值百分比
        dataframe['up_vol_sum'] = dataframe['up_volume'].rolling(20).sum()
        dataframe['down_vol_sum'] = dataframe['down_volume'].rolling(20).sum()
        dataframe['avg_volume'] = (dataframe['up_vol_sum'] + dataframe['down_vol_sum']) / 2
        dataframe['volume_delta'] = (dataframe['up_vol_sum'] - dataframe['down_vol_sum']) / dataframe['avg_volume']
        
        # 计算趋势变化点
        dataframe['trend_change_up'] = False
        dataframe['trend_change_down'] = False
        
        for i in range(1, len(dataframe)):
            dataframe['trend_change_up'].iloc[i] = not dataframe['trend_up'].iloc[i-1] and dataframe['trend_up'].iloc[i]
            dataframe['trend_change_down'].iloc[i] = not dataframe['trend_down'].iloc[i-1] and dataframe['trend_down'].iloc[i]
        
        # 计算EMA作为额外的趋势确认
        dataframe['ema50'] = ta.EMA(dataframe, timeperiod=50)
        dataframe['ema200'] = ta.EMA(dataframe, timeperiod=200)
        
        # RSI作为超买超卖指标
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入信号
        """
        dataframe.loc[
            (
                # 多头入场条件
                (dataframe['trend_change_up']) &  # 趋势向上转变
                (dataframe['close'] > dataframe['vidya']) &  # 价格在VIDYA上方
                (dataframe['volume'] > 0) &  # 确保有成交量
                (dataframe['volume_delta'] > self.buy_volume_threshold.value / 10) &  # 买方力量强于卖方
                (dataframe['rsi'] > 40) &  # RSI不在超卖区
                (
                    (dataframe['support_zone']) |  # 在支撑区附近
                    (dataframe['close'] > dataframe['ema50'])  # 或价格在EMA50上方
                )
            ),
            'enter_long'] = 1
        
        dataframe.loc[
            (
                # 空头入场条件
                (dataframe['trend_change_down']) &  # 趋势向下转变
                (dataframe['close'] < dataframe['vidya']) &  # 价格在VIDYA下方
                (dataframe['volume'] > 0) &  # 确保有成交量
                (dataframe['volume_delta'] < -self.sell_volume_threshold.value / 10) &  # 卖方力量强于买方
                (dataframe['rsi'] < 60) &  # RSI不在超买区
                (
                    (dataframe['resistance_zone']) |  # 在阻力区附近
                    (dataframe['close'] < dataframe['ema50'])  # 或价格在EMA50下方
                )
            ),
            'enter_short'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成卖出信号
        """
        dataframe.loc[
            (
                # 多头退出条件
                (
                    (dataframe['trend_change_down']) |  # 趋势向下转变
                    (dataframe['close'] < dataframe['vidya']) |  # 价格跌破VIDYA
                    (dataframe['rsi'] > 75)  # RSI进入超买区
                )
            ),
            'exit_long'] = 1
        
        dataframe.loc[
            (
                # 空头退出条件
                (
                    (dataframe['trend_change_up']) |  # 趋势向上转变
                    (dataframe['close'] > dataframe['vidya']) |  # 价格突破VIDYA
                    (dataframe['rsi'] < 25)  # RSI进入超卖区
                )
            ),
            'exit_short'] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        基于ATR的动态止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr_value = current_candle['atr']
        
        # 计算动态止损距离
        if trade.is_short:
            # 空头止损 - 当前价格 + ATR * 倍数
            stop_distance = current_rate + (atr_value * self.atr_stop_loss.value)
            stop_loss_pct = (stop_distance / current_rate - 1)
        else:
            # 多头止损 - 当前价格 - ATR * 倍数
            stop_distance = current_rate - (atr_value * self.atr_stop_loss.value)
            stop_loss_pct = (stop_distance / current_rate - 1)
        
        # 转换为负数百分比
        return stop_loss_pct
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[str]:
        """
        自定义退出逻辑，包括ATR止盈和追踪止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr_value = current_candle['atr']
        
        # ATR止盈
        take_profit_threshold = self.atr_take_profit.value * atr_value / current_rate
        
        if trade.is_short and current_profit <= -take_profit_threshold:
            return "short_atr_take_profit"
        
        if not trade.is_short and current_profit >= take_profit_threshold:
            return "long_atr_take_profit"
        
        # ATR追踪止损
        # 如果利润已经达到一定水平，启用追踪止损
        min_profit_threshold = self.atr_trailing_stop.value * atr_value / current_rate / 2
        
        if trade.is_short and current_profit < -min_profit_threshold:
            # 获取交易期间的最大利润
            max_profit = trade.calc_profit_ratio(trade.max_rate)
            
            # 如果从最大利润回撤超过ATR的一定比例，触发止损
            trailing_threshold = self.atr_trailing_stop.value * atr_value / current_rate
            
            if current_profit > max_profit + trailing_threshold:
                return "short_trailing_stop_loss"
        
        if not trade.is_short and current_profit > min_profit_threshold:
            # 获取交易期间的最大利润
            max_profit = trade.calc_profit_ratio(trade.max_rate)
            
            # 如果从最大利润回撤超过ATR的一定比例，触发止损
            trailing_threshold = self.atr_trailing_stop.value * atr_value / current_rate
            
            if current_profit < max_profit - trailing_threshold:
                return "long_trailing_stop_loss"
        
        return None
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        根据市场状况动态调整杠杆
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取当前趋势强度
        trend_strength = abs(current_candle['volume_delta'])
        
        # 根据趋势强度调整杠杆
        if trend_strength > 0.5:  # 强趋势
            adjusted_leverage = self.max_leverage.value
        elif trend_strength > 0.3:  # 中等趋势
            adjusted_leverage = self.max_leverage.value * 0.75
        else:  # 弱趋势
            adjusted_leverage = self.max_leverage.value * 0.5
        
        # 确保杠杆在合理范围内
        adjusted_leverage = max(1.0, min(adjusted_leverage, max_leverage))
        
        return adjusted_leverage
    
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                           proposed_stake: float, min_stake: Optional[float], max_stake: float,
                           entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        基于风险管理的仓位大小计算
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr_value = current_candle['atr']
        
        # 计算止损点位
        if side == "long":
            stop_price = current_rate - (atr_value * self.atr_stop_loss.value)
            risk_per_unit = current_rate - stop_price
        else:  # short
            stop_price = current_rate + (atr_value * self.atr_stop_loss.value)
            risk_per_unit = stop_price - current_rate
        
        # 计算账户总价值
        total_portfolio_value = self.wallets.get_total_stake_amount()
        
        # 计算风险金额
        risk_amount = total_portfolio_value * self.risk_per_trade.value
        
        # 计算仓位大小
        position_size = risk_amount / risk_per_unit
        
        # 转换为交易金额
        stake_amount = position_size * current_rate
        
        # 确保在最小和最大仓位之间
        if min_stake is not None:
            stake_amount = max(min_stake, min(stake_amount, max_stake))
        else:
            stake_amount = min(stake_amount, max_stake)
        
        return stake_amount
    
    def informative_pairs(self):
        """
        定义需要的信息对
        """
        return [] 