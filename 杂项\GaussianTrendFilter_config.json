{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "timeframe": "5m", "dry_run_wallet": 1000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "SOL/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "AVAX/USDT:USDT", "DOGE/USDT:USDT", "DOT/USDT:USDT", "MATIC/USDT:USDT"], "pair_blacklist": [".*UP/USDT", ".*DOWN/USDT", ".*BEAR/USDT", ".*BULL/USDT"]}, "pairlists": [{"method": "StaticPairList"}, {"method": "VolumePairList", "number_assets": 20, "sort_key": "quoteVolume", "min_value": 0, "refresh_period": 1800}, {"method": "<PERSON><PERSON><PERSON>er", "min_days_listed": 30}, {"method": "Sp<PERSON><PERSON><PERSON>er", "max_spread_ratio": 0.005}, {"method": "RangeStabilityFilter", "lookback_days": 3, "min_rate_of_change": 0.05, "refresh_period": 1440}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": true, "jwt_secret_key": "", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "GaussianTrendFilterBot", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}