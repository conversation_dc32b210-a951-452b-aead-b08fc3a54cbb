#!/bin/bash
#
# 该脚本提供了一个通用的 Freqtrade 回测与分析工作流。
# 它会逐个回测白名单中的所有交易对，并将结果汇总到一个日志文件中。
#
# 此脚本由 main.sh 调用，并从其接收环境变量:
# - PROJECT_ROOT, CONFIG_FILE_NAME, STRATEGY_PATH
# - BT_STRATEGY_NAME, TIMEFRAME, BACKTEST_TIMERANGE

# --- Trap for graceful exit ---
trap "echo; echo '捕获到中断信号，正在强制退出...'; exit 1" INT TERM

# --- 脚本开始 ---
echo "启动通用回测与分析工作流 (逐对回测模式)..."

# --- 步骤 0: 校验环境变量并设置路径 ---
if [ -z "$PROJECT_ROOT" ] || [ -z "$BT_STRATEGY_NAME" ] || [ -z "$CONFIG_FILE_NAME" ] || [ -z "$STRATEGY_PATH" ]; then
    echo "错误: 必要的环境变量 (e.g., PROJECT_ROOT, BT_STRATEGY_NAME) 未设置。" >&2
    echo "请通过主脚本 main.sh 运行此工作流。" >&2
    exit 1
fi

# Freqtrade 主配置文件路径
CONFIG_FILE="${PROJECT_ROOT}/user_data/${CONFIG_FILE_NAME}"
# 策略文件所在目录
STRATEGY_DIR="${PROJECT_ROOT}/user_data/${STRATEGY_PATH}"
# 回测策略使用的参数设置文件 (遵循Freqtrade约定，与策略同名)
SETTINGS_FILE="${STRATEGY_DIR}/${BT_STRATEGY_NAME}_Settings.json"
# 原始回测日志文件 (由本脚本生成，作为分析脚本的输入)
RAW_LOG_FILE="${STRATEGY_DIR}/${BT_STRATEGY_NAME}_backtest_raw_log.txt"

# --- 从环境变量读取回测参数, 若为空则使用默认值 ---
TIMEFRAME=${TIMEFRAME:-"15m"}
BACKTEST_TIMERANGE=${BACKTEST_TIMERANGE:-"20240501-"}

echo "将使用以下配置进行回测："
echo "  - 回测策略: ${BT_STRATEGY_NAME}"
echo "  - 配置文件: ${CONFIG_FILE}"
echo "  - 时间周期: ${TIMEFRAME}"
echo "  - 时间范围: ${BACKTEST_TIMERANGE}"
echo "  - 参数来源文件: ${SETTINGS_FILE}"
echo "  - 原始日志输出: ${RAW_LOG_FILE}"

# --- 函数定义 ---
set_permissions() {
    echo "正在更新项目文件权限以兼容Docker容器..."
    if [ -d "${PROJECT_ROOT}/user_data" ]; then
        sudo chmod -R a+rX "${PROJECT_ROOT}/user_data"
        echo "权限更新完成。"
    else
        echo "警告: '${PROJECT_ROOT}/user_data' 目录不存在，跳过权限设置。"
    fi
}

# --- 步骤 1: 环境准备 ---
set_permissions

if ! [ -f "$SETTINGS_FILE" ]; then
    echo "错误: 找不到回测所需的参数文件: ${SETTINGS_FILE}" >&2
    echo "请先运行步骤1和2进行参数优化和处理，或确保该文件存在。" >&2
    exit 1
fi

# 从主配置文件中提取 pair_whitelist 列表
PAIRS=$(jq -r '.exchange.pair_whitelist[]' ${CONFIG_FILE})
if [ -z "$PAIRS" ]; then
    echo "错误：无法从 ${CONFIG_FILE} 中读取交易对列表。" >&2
    exit 1
fi

echo "正在准备回测环境..."
# 初始化原始日志文件
echo "通用回测原始日志 - 策略: ${BT_STRATEGY_NAME} - $(date)" > "$RAW_LOG_FILE"

# --- 步骤 2: 循环运行回测 ---
TOTAL_PAIRS=$(echo "$PAIRS" | wc -w)
CURRENT_PAIR_NUM=0

for PAIR in $PAIRS
do
    CURRENT_PAIR_NUM=$((CURRENT_PAIR_NUM + 1))
    echo "-------------------------------------------------------------------"
    echo "回测 [${CURRENT_PAIR_NUM}/${TOTAL_PAIRS}]: $PAIR"
    echo "-------------------------------------------------------------------"
    
    # 将绝对路径转换为Docker所需的相对路径
    CONFIG_FILE_DOCKER=$(echo "$CONFIG_FILE" | sed "s|^${PROJECT_ROOT}/||")
    STRATEGY_PATH_DOCKER="user_data/${STRATEGY_PATH}"

    # 将每个交易对的回测结果追加到原始日志文件中
    echo "==================== START BACKTEST FOR $PAIR ====================" >> "$RAW_LOG_FILE"
    cd "$PROJECT_ROOT" && docker compose run --rm freqtrade backtesting \
        --config "$CONFIG_FILE_DOCKER" \
        --strategy-path "$STRATEGY_PATH_DOCKER" \
        --strategy "$BT_STRATEGY_NAME" \
        --timeframe "$TIMEFRAME" \
        --timerange "$BACKTEST_TIMERANGE" \
        --cache day \
        -p "$PAIR" >> "$RAW_LOG_FILE" 2>&1
    echo "==================== END BACKTEST FOR $PAIR ====================" >> "$RAW_LOG_FILE"
    echo "" >> "$RAW_LOG_FILE"

    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        echo "警告: 为 $PAIR 的回测任务返回了非零退出代码。"
    fi
done

echo "==================================================================="
echo "所有交易对的回测已完成。"
echo "原始日志已保存在: ${RAW_LOG_FILE}"
echo "请继续执行步骤4来分析这些结果。"
echo "==================================================================="

# 在退出前，再次设置权限
set_permissions

echo ""
echo "回测脚本执行完毕。"
echo "" 