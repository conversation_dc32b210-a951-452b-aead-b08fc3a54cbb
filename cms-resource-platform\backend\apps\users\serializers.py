"""
用户系统序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, UserGroup, UserProfile, PointsHistory, SignInRecord


class UserGroupSerializer(serializers.ModelSerializer):
    """用户组序列化器"""
    
    class Meta:
        model = UserGroup
        fields = [
            'id', 'name', 'description', 'permissions',
            'download_limit', 'upload_limit', 'max_file_size',
            'is_vip', 'sort_order'
        ]


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    
    class Meta:
        model = UserProfile
        fields = [
            'website', 'location', 'company', 'job_title',
            'weibo', 'wechat', 'qq', 'github',
            'language', 'timezone', 'email_notifications'
        ]


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    profile = UserProfileSerializer(read_only=True)
    user_group = UserGroupSerializer(read_only=True)
    display_name = serializers.CharField(read_only=True)
    is_vip_active = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'nickname', 'avatar', 'bio', 'phone', 'birthday', 'gender',
            'user_group', 'is_verified', 'is_vip', 'vip_expire_date',
            'points', 'total_points_earned', 'total_points_spent',
            'download_count', 'upload_count', 'login_count',
            'date_joined', 'last_login', 'display_name', 'is_vip_active',
            'profile'
        ]
        read_only_fields = [
            'id', 'date_joined', 'last_login', 'is_verified',
            'points', 'total_points_earned', 'total_points_spent',
            'download_count', 'upload_count', 'login_count'
        ]


class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'nickname'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("密码不匹配")
        return attrs
    
    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("邮箱已被注册")
        return value
    
    def validate_username(self, value):
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已被使用")
        return value
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # 创建用户资料
        UserProfile.objects.create(user=user)
        
        # 赠送注册积分
        from django.conf import settings
        signup_points = getattr(settings, 'DEFAULT_SIGNUP_POINTS', 100)
        if signup_points > 0:
            user.add_points(signup_points, '注册奖励')
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    email = serializers.EmailField()
    password = serializers.CharField()
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError("邮箱或密码错误")
            if not user.is_active:
                raise serializers.ValidationError("账户已被禁用")
            attrs['user'] = user
        else:
            raise serializers.ValidationError("请提供邮箱和密码")
        
        return attrs


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("新密码不匹配")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("原密码错误")
        return value


class PasswordResetSerializer(serializers.Serializer):
    """密码重置序列化器"""
    email = serializers.EmailField()
    
    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise serializers.ValidationError("邮箱不存在")
        return value


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器"""
    profile = UserProfileSerializer(required=False)
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'nickname', 'bio',
            'phone', 'birthday', 'gender', 'profile'
        ]
    
    def update(self, instance, validated_data):
        profile_data = validated_data.pop('profile', None)
        
        # 更新用户基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新用户资料
        if profile_data:
            profile, created = UserProfile.objects.get_or_create(user=instance)
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()
        
        return instance


class PointsHistorySerializer(serializers.ModelSerializer):
    """积分历史序列化器"""
    
    class Meta:
        model = PointsHistory
        fields = [
            'id', 'amount', 'balance', 'type', 'reason',
            'related_object_type', 'related_object_id', 'created_at'
        ]


class SignInSerializer(serializers.Serializer):
    """签到序列化器"""
    pass


class SignInRecordSerializer(serializers.ModelSerializer):
    """签到记录序列化器"""
    
    class Meta:
        model = SignInRecord
        fields = [
            'id', 'date', 'points_earned', 'consecutive_days', 'created_at'
        ]
