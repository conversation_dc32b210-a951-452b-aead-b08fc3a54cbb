# -*- coding: utf-8 -*-
"""
该脚本从 Binance Futures API 获取交易数据，根据24小时交易量筛选出排名前100的USDT本位合约，
并用这个列表更新 Freqtrade 的主配置文件。

- 使用 ccxt 库，只连接币安U本位合约市场。
- 自动筛选活跃的永续合约。
- 自动过滤上市不足90天的新合约。
- 自动过滤24小时内涨跌幅超过25%的合约。
- 自动更新配置文件并触发数据下载。
- 新增：自动从策略文件解析并下载所有需要的时间周期数据。
- 需要 `ccxt` 库。请通过 `pip install -r requirements.txt` 安装。
"""
import os
import json
from pathlib import Path
import logging
from datetime import datetime, timedelta
import subprocess
import ccxt
import re

# --- 日志设置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# --- 配置加载 ---
def get_config_from_env():
    """从环境变量中获取配置。"""
    project_root = os.getenv("PROJECT_ROOT")
    config_file_name = os.getenv("CONFIG_FILE_NAME")

    if not all([project_root, config_file_name]):
        logging.error("错误：必要的环境变量 (PROJECT_ROOT, CONFIG_FILE_NAME) 未设置。")
        exit(1)

    config_path = Path(project_root) / "user_data" / config_file_name
    return config_path

# --- 新增：策略文件解析 ---
def get_strategy_timeframes() -> list[str]:
    """从策略文件中解析出所有需要的时间周期。"""
    project_root = os.getenv("PROJECT_ROOT")
    # 优化策略通常是定义所有参数和周期的基础
    strategy_name = os.getenv("OPT_STRATEGY_NAME")
    strategy_path_rel = os.getenv("STRATEGY_PATH")

    if not all([project_root, strategy_name, strategy_path_rel]):
        logging.warning("未提供策略文件信息 (OPT_STRATEGY_NAME, STRATEGY_PATH)，将仅使用默认TIMEFRAME。")
        return []

    # Freqtrade的 --strategy-path 是相对于项目根目录的。
    # 根据脚本的设计，策略的完整路径应为：
    strategy_file_path = Path(project_root) / "user_data" / "strategies" / strategy_path_rel / f"{strategy_name}.py"
    
    # 兼容 reverted 的路径，以防万一
    if not strategy_file_path.exists():
        strategy_file_path = Path(project_root) / "user_data" / strategy_path_rel / f"{strategy_name}.py"

    if not strategy_file_path.exists():
        logging.warning(f"策略文件 {strategy_file_path} 不存在。请检查 main.sh 中的 PROJECT_ROOT 和 STRATEGY_PATH 设置。")
        return []

    logging.info(f"正在从 {strategy_file_path} 中解析时间周期...")
    try:
        with open(strategy_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        timeframes = set()

        # Regex to find: timeframe = '...'
        main_tf_match = re.search(r"^\s*timeframe\s*=\s*['\"]([^'\"]+)['\"]", content, re.MULTILINE)
        if main_tf_match:
            tf = main_tf_match.group(1)
            timeframes.add(tf)
            logging.info(f"  找到主时间周期: {tf}")

        # 新增: Regex to find other timeframe definitions, like higher_tf = '1h'
        other_tf_matches = re.findall(r"^\s*(?:higher_tf|informative_timeframe)\s*=\s*['\"]([^'\"]+)['\"]", content, re.MULTILINE)
        if other_tf_matches:
            for tf in other_tf_matches:
                timeframes.add(tf)
                logging.info(f"  找到其它时间周期定义: {tf}")

        # Regex to find: @informative('...')
        informative_tf_matches = re.findall(r"^\s*@informative\(['\"]([^'\"]+)['\"]\)", content, re.MULTILINE)
        if informative_tf_matches:
            for tf in informative_tf_matches:
                timeframes.add(tf)
                logging.info(f"  找到辅助时间周期: {tf}")

        if not timeframes:
            logging.warning("未在策略文件中找到任何时间周期定义。")
            return []

        # 返回排序后的列表，以便命令具有一致性
        return sorted(list(timeframes))

    except Exception as e:
        logging.error(f"解析策略文件时出错: {e}")
        return []

# --- 核心逻辑 ---
def fetch_and_filter_pairs(config_path: Path):
    """从币安期货获取并筛选Top 100 USDT永续合约。"""
    logging.info("正在连接币安期货...")
    try:
        # U本位合约市场
        exchange = ccxt.binanceusdm({'options': {'defaultType': 'swap'}})
        exchange.load_markets()
    except Exception as e:
        logging.error(f"连接币安失败: {e}")
        return

    # 1. 初步筛选活跃的USDT永续合约
    logging.info("正在筛选活跃的USDT永续合约...")
    candidate_symbols = []
    for symbol, market in exchange.markets.items():
        if market.get('active') and market.get('quote') == 'USDT' and market.get('type') == 'swap' and market.get('linear'):
             candidate_symbols.append(market['symbol'])
    
    logging.info(f"找到 {len(candidate_symbols)} 个活跃的USDT永续合约。正在获取行情数据...")

    # 2. 获取行情数据并按交易量排序
    try:
        all_tickers = exchange.fetch_tickers(candidate_symbols)
        # 过滤掉没有交易量信息的tickers
        valid_tickers = [t for t in all_tickers.values() if t.get('quoteVolume') is not None and t.get('quoteVolume') > 0]
        sorted_tickers = sorted(valid_tickers, key=lambda x: x['quoteVolume'], reverse=True)
    except Exception as e:
        logging.error(f"获取或处理行情数据失败: {e}")
        return

    # 3. 应用高级过滤器 (上市日期 > 90天, 波动率 < 25%)
    logging.info("正在应用高级过滤器 (上市日期 > 90天, 波动率 < 25%)...")
    final_pairlist = []
    checked_count = 0
    # 检查交易量最高的200个，以确保筛选后能剩下100个
    for ticker in sorted_tickers[:200]: 
        if len(final_pairlist) >= 100:
            break
            
        symbol = ticker['symbol']
        checked_count += 1
        logging.info(f"  ({checked_count}/200) 正在检查 {symbol}...")

        try:
            # 检查上市日期 (必须有至少90天的K线数据)
            ninety_days_ago = exchange.parse8601((datetime.utcnow() - timedelta(days=91)).isoformat())
            # fetch_ohlcv 使用的是 ms 时间戳
            ohlcv = exchange.fetch_ohlcv(symbol, '1d', since=ninety_days_ago, limit=91)
            
            if len(ohlcv) < 90:
                logging.warning(f"    -> 跳过 {symbol}: 上市时间不足90天。")
                continue

            # 检查波动率 (昨天的振幅必须小于25%)
            yesterday_candle = ohlcv[-2] # [-1] 是今天的未完成K线
            high, low = yesterday_candle[2], yesterday_candle[3]
            
            if low == 0: continue # 避免除以零

            volatility = (high - low) / low
            if volatility > 0.25:
                logging.warning(f"    -> 跳过 {symbol}: 昨日波动率为 {volatility:.2%}, 超过25%。")
                continue
            
            # 所有检查通过，添加到最终列表 (ccxt为U本位合约返回的symbol格式已是 'ETH/USDT:USDT')
            final_pairlist.append(symbol)
            logging.info(f"    -> 通过! {symbol} 已添加。({len(final_pairlist)}/100)")

        except Exception as e:
            logging.error(f"    -> 处理 {symbol} 时出错: {e}。已跳过。")
            continue
            
    if len(final_pairlist) < 100:
        logging.warning(f"只找到了 {len(final_pairlist)} 个满足所有条件的交易对。")
    
    # 4. 更新配置文件
    if not config_path.exists():
        logging.error(f"配置文件 '{config_path}' 未找到。无法更新交易对列表。")
        return
        
    logging.info(f"正在使用 {len(final_pairlist)} 个交易对更新 '{config_path}' 中的 pair_whitelist...")
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    # Freqtrade期货的格式是 'BTC/USDT:USDT'
    config_data['exchange']['pair_whitelist'] = final_pairlist
    config_data['stake_currency'] = 'USDT'
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=4)
        
    logging.info("配置文件更新成功。")
    logging.info("推荐进行一次数据下载。脚本将立即为您下载新交易对列表的数据。")
    
    # 5. 触发数据下载
    project_root = os.getenv("PROJECT_ROOT")
    
    # 自动从策略文件解析时间周期
    timeframes_to_download = get_strategy_timeframes()
    
    # 如果解析失败或未找到，则回退到环境变量或默认值
    if not timeframes_to_download:
        logging.info("无法从策略文件解析时间周期，将使用 TIMEFRAME 环境变量。")
        default_timeframe = os.getenv("TIMEFRAME", "1h")
        timeframes_to_download.append(default_timeframe)
        
    timeframes_str = " ".join(timeframes_to_download)
    
    command = (
        f"docker compose run --rm freqtrade download-data "
        f"--config user_data/{config_path.name} "
        f"-t {timeframes_str}"
    )
    
    logging.info(f"正在执行数据下载命令:\n{command}")
    
    try:
        process = subprocess.Popen(command, shell=True, cwd=project_root, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding='utf-8')
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                
        rc = process.poll()
        if rc == 0:
            logging.info("数据下载成功。")
        else:
            logging.error(f"数据下载失败，退出码: {rc}。")
    except FileNotFoundError:
        logging.error("错误： 'docker-compose' 命令未找到。请确保Docker已安装并正在运行。")
    except Exception as e:
        logging.error(f"执行下载命令时发生未知错误: {e}")

def run():
    """主执行函数"""
    config_path = get_config_from_env()
    fetch_and_filter_pairs(config_path)

if __name__ == "__main__":
    run() 