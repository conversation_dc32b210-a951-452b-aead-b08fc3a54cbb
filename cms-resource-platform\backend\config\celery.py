"""
Celery配置
"""
import os
from celery import Celery
from django.conf import settings

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

app = Celery('cms_platform')

# 使用Django设置配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# 任务配置
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟
    task_soft_time_limit=25 * 60,  # 25分钟
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# 定时任务
app.conf.beat_schedule = {
    'cleanup-expired-tokens': {
        'task': 'apps.core.tasks.cleanup_expired_tokens',
        'schedule': 300.0,  # 每5分钟执行一次
    },
    'update-resource-stats': {
        'task': 'apps.resources.tasks.update_resource_stats',
        'schedule': 3600.0,  # 每小时执行一次
    },
}

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
