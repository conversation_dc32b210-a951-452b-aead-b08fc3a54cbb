import os
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

"""
HeikinAshiTrendFollowerV7 - A Trend Following Strategy

* Changelog V7:
    - 明确策略定位为趋势跟踪，而非均值回归。
    - 使用OBV替代原始成交量过滤，以更好地确认趋势中的资金流向。
    - 调整策略名称和描述，使其与核心逻辑保持一致。

* 描述: 这是一个无未来函数的趋势跟踪策略。
        - 使用唐奇安通道 (Donchian Channels) 动态识别交易区间的边界，用于止损和止盈。
        - 核心入场逻辑：在宏观上涨趋势中（由多时间框架EMA确认），当ADX指示强趋势、OBV确认资金流入时，捕捉由Heikin Ashi强阳线启动的波段。
        - 核心出场逻辑：
            1. 止盈: 交易盈利，且价格回归到通道中线。
            2. 时间止损: 交易在指定时间内仍亏损，则平仓。
            3. 硬止损: 价格触及基于入场ATR计算的初始止损位。
"""

import logging
import numpy as np
import talib.abstract as ta
from datetime import datetime, timezone
from freqtrade.strategy import IntParameter, IStrategy, DecimalParameter
from freqtrade.exchange import timeframe_to_seconds
from pandas import DataFrame, Series
import pandas as pd
from typing import Optional, Any

# 自定义merge_informative_pair函数实现
def merge_informative_pair(dataframe: pd.DataFrame, informative: pd.DataFrame, 
                          timeframe: str, timeframe_inf: str, 
                          ffill: bool = True) -> pd.DataFrame:
    """
    合并informative dataframe到主要timeframe
    """
    # 确保两个DataFrame都有日期索引
    if not isinstance(dataframe.index, pd.DatetimeIndex):
        dataframe = dataframe.set_index('date', drop=False)
    if not isinstance(informative.index, pd.DatetimeIndex):
        informative = informative.set_index('date', drop=False)
    
    # 重命名列
    informative_cols = informative.columns
    informative_cols_names = [f"{col}_{timeframe_inf}" for col in informative_cols]
    informative.columns = informative_cols_names
    
    # 重采样informative数据以匹配主timeframe
    resampled = informative.copy()
    
    # 使用简单的reindex操作
    resampled = resampled.reindex(dataframe.index, method='ffill')
    
    # 合并数据
    dataframe = pd.concat([dataframe, resampled], axis=1)
    
    # 前向填充缺失值
    if ffill:
        dataframe = dataframe.ffill()
    
    return dataframe

class HeikinAshiTrendFollowerV7(IStrategy):
    # --- Strategy Configuration ---
    INTERFACE_VERSION: int = 4
    
    timeframe = "15m"
    startup_candle_count = 200
    higher_tf = '1h'
    can_short: bool = False

    stoploss = -0.04  # Fallback stoploss
    use_custom_stoploss = True
    cooldown_lookback = 3
    
    # --- Hyperoptable Parameters ---
    donchian_period = IntParameter(20, 50, default=49, space='buy', optimize=True)
    rsi_period = IntParameter(10, 25, default=18, space='buy', optimize=True)
    rsi_oversold_threshold = IntParameter(20, 40, default=33, space='buy', optimize=True)
    rsi_overbought_threshold = IntParameter(65, 85, default=75, space='buy', optimize=True)
    atr_period = IntParameter(10, 30, default=16, space='buy', optimize=True)
    atr_multiplier = DecimalParameter(1.5, 2.8, default=2.4, decimals=1, space='buy', optimize=True)
    max_trade_duration_candles = IntParameter(24, 48, default=36, space='buy', optimize=True)
    adx_threshold = IntParameter(22, 35, default=33, space='buy', optimize=True)

    # --- Plotting Configuration ---
    @property
    def plot_config(self):
        return {
            "main_plot": {
                "ha_high": {"color": "green"},
                "ha_low": {"color": "red"},
                "pivot_zone_low": {"color": "green", "type": "line", "linestyle": "--"},
                "pivot_zone_high": {"color": "red", "type": "line", "linestyle": "--"},
                "ema_short": {"color": "blue"},
                "ema200": {"color": "orange"},
            },
            "subplots": {
                "ADX": {
                    "adx": {"color": "brown"},
                },
                "RSI": {
                    "rsi": {"color": "purple"},
                },
                "OBV": {
                    "obv": {"color": "blue"},
                    "obv_ema": {"color": "orange"},
                }
            }
        }

    # --- Informative Pairs ---
    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, self.higher_tf) for pair in pairs]
        return informative_pairs 

    # --- Indicator Population ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        if not self.dp:
            return dataframe
        
        # --- Higher Timeframe (MTA) ---
        inf_tf = self.higher_tf
        try:
            informative = self.dp.get_pair_dataframe(metadata['pair'], inf_tf)
            informative['ema50'] = ta.EMA(informative, timeperiod=50)
            informative['ema200'] = ta.EMA(informative, timeperiod=200)
            informative['higher_trend'] = 0
            informative.loc[(informative['close'] > informative['ema50']) & 
                            (informative['ema50'] > informative['ema200']), 'higher_trend'] = 1
            informative.loc[(informative['close'] < informative['ema50']) & 
                            (informative['ema50'] < informative['ema200']), 'higher_trend'] = -1
            dataframe = merge_informative_pair(dataframe, informative, self.timeframe, inf_tf, ffill=True)
            higher_tf_trend = f"higher_trend_{inf_tf}"
        except Exception as e:
            higher_tf_trend = "higher_trend_fallback"
            dataframe[higher_tf_trend] = 0
            print(f"Could not get higher timeframe data: {e}")
        
        # --- Heikin Ashi Candles ---
        heikin_ashi = dataframe.copy()
        heikin_ashi['ha_close'] = (dataframe['open'] + dataframe['high'] + dataframe['low'] + dataframe['close']) / 4
        first_idx = dataframe.index[0] if len(dataframe) > 0 else None
        if first_idx is not None:
            heikin_ashi.at[first_idx, 'ha_open'] = (dataframe.at[first_idx, 'open'] + dataframe.at[first_idx, 'close']) / 2
        for i in range(1, len(dataframe)):
            current_idx = dataframe.index[i]
            prev_idx = dataframe.index[i-1]
            heikin_ashi.at[current_idx, 'ha_open'] = (heikin_ashi.at[prev_idx, 'ha_open'] + heikin_ashi.at[prev_idx, 'ha_close']) / 2
        heikin_ashi['ha_high'] = heikin_ashi[['ha_open', 'ha_close']].join(dataframe['high']).max(axis=1)
        heikin_ashi['ha_low'] = heikin_ashi[['ha_open', 'ha_close']].join(dataframe['low']).min(axis=1)
        dataframe['ha_open'] = heikin_ashi['ha_open']
        dataframe['ha_high'] = heikin_ashi['ha_high']
        dataframe['ha_low'] = heikin_ashi['ha_low']
        dataframe['ha_close'] = heikin_ashi['ha_close']
        dataframe['ha_strong_bull'] = (dataframe['ha_close'] > dataframe['ha_open']) & (dataframe['ha_low'] == dataframe['ha_open'])
        dataframe['ha_strong_bear'] = (dataframe['ha_close'] < dataframe['ha_open']) & (dataframe['ha_high'] == dataframe['ha_open'])
        
        # --- Core Indicators ---
        dataframe['pivot_zone_high'] = dataframe['high'].shift(1).rolling(self.donchian_period.value).max()
        dataframe['pivot_zone_low'] = dataframe['low'].shift(1).rolling(self.donchian_period.value).min()
        dataframe['pivot_zone_mid'] = (dataframe['pivot_zone_high'] + dataframe['pivot_zone_low']) / 2
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['ema50'] = ta.EMA(dataframe, timeperiod=50)
        dataframe['ema200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_ema'] = ta.EMA(dataframe['obv'], timeperiod=20)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['atr_pct'] = dataframe['atr'] / dataframe['close']
        dataframe['volatility_factor'] = np.clip(dataframe['atr_pct'] * 100, 0.5, 2.0)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        
        # --- Trend Identification ---
        dataframe['trend'] = 0
        try:
            uptrend_condition = ((dataframe['close'] > dataframe['pivot_zone_mid']) & 
                                (dataframe['close'] > dataframe['ema50']) & 
                                (dataframe['ema50'] > dataframe['ema200']) &
                                (dataframe[higher_tf_trend] >= 0))
            downtrend_condition = ((dataframe['close'] < dataframe['pivot_zone_mid']) & 
                                (dataframe['close'] < dataframe['ema50']) & 
                                (dataframe['ema50'] < dataframe['ema200']) &
                                (dataframe[higher_tf_trend] <= 0))
        except Exception:
            uptrend_condition = ((dataframe['close'] > dataframe['pivot_zone_mid']) & 
                                (dataframe['close'] > dataframe['ema50']) & 
                                (dataframe['ema50'] > dataframe['ema200']))
            downtrend_condition = ((dataframe['close'] < dataframe['pivot_zone_mid']) & 
                                (dataframe['close'] < dataframe['ema50']) & 
                                (dataframe['ema50'] < dataframe['ema200']))
        dataframe.loc[uptrend_condition, 'trend'] = 1
        dataframe.loc[downtrend_condition, 'trend'] = -1
        
        return dataframe

    # --- Entry Logic ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        trend_long_conditions = (
            (dataframe['close'] > dataframe['ema200']) &
            (dataframe['ema200'] > dataframe['ema200'].shift(20)) &
            (dataframe['close'] > dataframe['ema50']) &
            (dataframe['adx'] > self.adx_threshold.value) &
            dataframe['ha_strong_bull'] &
            (dataframe['obv'] > dataframe['obv_ema'])
        )
        dataframe.loc[trend_long_conditions, ['enter_long', 'enter_tag']] = (1, 'trend_long')
        return dataframe

    def custom_position_size(self, pair, current_time, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) < 20:
            return 1.0
        
        last_candle = dataframe.iloc[-1]
        market_volatility = last_candle['atr_pct']
        base_position = 1.0
        position_size = base_position * (1.0 - min(0.5, market_volatility * 10))
        return max(0.3, position_size)

    def check_cooldown(self, pair: str, current_time: datetime) -> bool:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty or len(dataframe) < self.cooldown_lookback:
            return False
            
        last_candles = dataframe.iloc[-self.cooldown_lookback:]
        exit_tags = ['exit_profit_rsi', 'exit_profit_medium', 'exit_profit_large']
        if last_candles['exit_tag'].isin(exit_tags).any():
            return True
        return False

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float, 
                           time_in_force: str, current_time: datetime, **kwargs) -> bool:
        if self.check_cooldown(pair, current_time):
            return False
        return True
    
    # --- Exit Logic ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Failsafe exit for very strong reversals
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        dataframe.loc[
            (
                dataframe['ha_strong_bear'] & 
                dataframe['ha_strong_bear'].shift(1) &
                (dataframe['close'] < dataframe['ema_short'] * 0.98) &
                (dataframe['adx'] > self.adx_threshold.value * 1.2) &
                (dataframe['volume'] > dataframe['volume_mean'])
            ),
            ['exit_long', 'exit_tag']] = (1, 'strong_reversal')
        return dataframe

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime,
                    current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None
        last_candle = dataframe.iloc[-1]
        
        if current_profit > 0:
            if current_profit >= 0.05: return 'exit_profit_large'
            if current_profit >= 0.02 and last_candle['close'] > last_candle['pivot_zone_mid']: return 'exit_profit_medium'
            if current_profit > 0.01 and last_candle['rsi'] > self.rsi_overbought_threshold.value: return 'exit_profit_rsi'

        if current_profit > 0.01 and last_candle['trend'] == -1:
            return 'exit_trend_reversal'
            
        trade_duration_candles = (current_time.replace(tzinfo=timezone.utc) - trade.open_date_utc).total_seconds() / timeframe_to_seconds(self.timeframe)
        if current_profit < 0 and trade_duration_candles > self.max_trade_duration_candles.value:
            return 'exit_stop_loss_timed'
        
        return None

    def confirm_trade_exit(self, pair: str, trade: 'Trade', order_type: str, amount: float,
                         rate: float, time_in_force: str, exit_reason: str,
                         current_time: datetime, **kwargs) -> bool:
        if exit_reason == 'trailing_stop_loss':
            return False
        return True

    # --- Custom Stoploss ---
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return -0.99

        open_candle_df = dataframe.loc[dataframe['date'] < trade.open_date_utc]
        if open_candle_df.empty: return -0.99
        
        open_candle = open_candle_df.iloc[-1]
        atr_value = open_candle.get('atr', 0)
        volatility_factor = open_candle.get('volatility_factor', 1.0)
        pivot_zone_low = open_candle.get('pivot_zone_low', 0)
        
        if current_profit > 0.03:
            stop_price = trade.open_rate * (1 + (current_profit * 0.5))
            return (stop_price / current_rate) - 1
        elif current_profit > 0.01:
            stop_price = trade.open_rate * 1.001
            return (stop_price / current_rate) - 1
        
        adjusted_multiplier = 1.8 * volatility_factor
        stoploss_price = pivot_zone_low - (atr_value * adjusted_multiplier)
        return (stoploss_price / current_rate) - 1 