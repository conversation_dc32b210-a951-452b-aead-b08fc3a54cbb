"""
支付系统URL配置
"""
from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # 套餐
    path('points/packages/', views.PointsPackageListView.as_view(), name='points_packages'),
    path('vip/packages/', views.VIPPackageListView.as_view(), name='vip_packages'),
    
    # 订单
    path('orders/create/', views.OrderCreateView.as_view(), name='order_create'),
    path('orders/<str:order_no>/', views.OrderDetailView.as_view(), name='order_detail'),
    path('orders/', views.MyOrdersView.as_view(), name='my_orders'),
    
    # 卡密
    path('cards/redeem/', views.CardRedeemView.as_view(), name='card_redeem'),
    
    # 提现
    path('withdrawals/create/', views.WithdrawalCreateView.as_view(), name='withdrawal_create'),
    path('withdrawals/', views.WithdrawalListView.as_view(), name='withdrawal_list'),
    
    # 回调
    path('callback/', views.payment_callback, name='payment_callback'),
]
