{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "timeframe": "5m", "strategy": "DSL_Oscillator_Strategy", "hyperopt": true, "hyperopt_loss": "SharpeHyperOptLoss", "hyperopt_path": "user_data/hyperopt_results", "hyperopt_epoch": 500, "hyperopt_spaces": ["buy", "sell", "protection"], "order_types": {"entry": "market", "exit": "market", "emergency_exit": "market", "force_exit": "market", "force_entry": "market", "stoploss": "market", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "BNB/USDT", "SOL/USDT", "XRP/USDT", "ADA/USDT", "DOGE/USDT", "MATIC/USDT", "DOT/USDT", "LTC/USDT"], "pair_blacklist": [".*UP/USDT", ".*DOWN/USDT", ".*BEAR/USDT", ".*BULL/USDT"]}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false}, "telegram": {"enabled": false}, "api_server": {"enabled": false}, "bot_name": "DSL_Oscillator_Hyperopt", "initial_state": "running", "forcebuy_enable": false, "internals": {"process_throttle_secs": 5}}