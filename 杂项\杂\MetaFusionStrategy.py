# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, merge_informative_pair
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, RealParameter
from pandas import DataFrame
# --------------------------------

# --- Add your lib to import here ---
import talib.abstract as ta
import pandas_ta as pta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from functools import reduce

# --- Custom Stoploss ---
from freqtrade.persistence import Trade
from datetime import datetime

"""
====================================================================================================
MetaFusionStrategy (元融合策略) - 一个多层次、多范式的量化交易策略

本策略是分析了大量成功交易策略后的集大成之作。
它被设计成一个模块化、适应性强且稳健的系统，融合了趋势跟踪、均值回归
以及先进风险管理范式的最佳思想。

该策略建立在一个五层决策流程之上：
1.  市场状态过滤器: 识别当前市场是处于趋势市还是震荡市。
2.  信号生成: 一个从多个高质量来源收集信号的评分系统。
3.  信号加强与过滤: 通过协同效应奖金和宏观过滤器优化原始分数。
4.  入场决策: 基于动态的、根据市场状态调整的阈值做出最终入场决策。
5.  资金与风险管理: 通过专业级的仓位控制、止损和止盈技术来管理交易。
====================================================================================================
"""
class MetaFusionStrategy(IStrategy):
    INTERFACE_VERSION = 3 # 策略接口版本
    
    # --- 策略通用配置 ---
    timeframe = '15m'
    informative_timeframe = '1h' # 定义信息时间框架
    can_short = True
    
    # --- 投资回报率(ROI)与止损 ---
    # ROI被禁用，因为我们将使用一个自定义的多方面退出逻辑。
    minimal_roi = {"0": 100}
    # 止损由我们的 custom_stoploss 函数管理。
    stoploss = -0.99
    
    # --- 追踪止损 ---
    # 我们在 custom_stoploss 中实现了自定义的追踪止损。
    trailing_stop = False
    
    # --- 流程与启动 ---
    process_only_new_candles = True # 只处理新的K线
    startup_candle_count = 200 # 基于最长指标周期所需K线数量
    
    # --- 订单类型 ---
    order_types = {
        'entry': 'limit',
        'exit': 'market', # 使用市价单退出以确保执行
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }
    
    # --- 自定义函数开关 ---
    use_custom_stoploss = True
    use_exit_signal = True
    
    
    # --- 可优化的超参数 ---
    # TODO: (中文) 为每个模块的所有关键参数定义超参数优化空间。

    #================================================================================================
    # 第一层: 市场状态过滤器 (Market Regime Filter)
    # --- 目标: 识别当前市场是处于趋势市还是震荡市。
    # --- 指标: ADX,布林带宽度, KAMA斜率。
    #================================================================================================
    
    # --- ADX 参数 ---
    regime_adx_period = IntParameter(14, 28, default=21, space="protection")
    regime_adx_trend_threshold = IntParameter(20, 30, default=25, space="protection")
    regime_adx_range_threshold = IntParameter(15, 25, default=20, space="protection")
    
    # --- 布林带参数 ---
    regime_bb_period = IntParameter(20, 40, default=20, space="protection")
    regime_bb_std = RealParameter(1.5, 2.5, default=2.0, space="protection")
    
    # --- KAMA 参数 ---
    regime_kama_period = IntParameter(10, 30, default=14, space="protection")
    

    #================================================================================================
    # 第二层 & 第三层: 信号生成与加强 (SIGNAL GENERATION & ENHANCEMENT)
    # --- 目标: 从多个高质量信号源中生成原始分数并对其进行优化。
    # --- 指标: 三重SuperTrend, MACD/EMA共振, 海龟通道, 肯特纳/回归通道, Heikin Ashi等。
    #================================================================================================
    
    # --- 入场分数阈值 (动态) ---
    entry_score_threshold_trending = IntParameter(55, 85, default=65, space="buy")
    entry_score_threshold_ranging = IntParameter(45, 75, default=55, space="buy")
    
    # --- 信号源参数 ---
    # 2.1: 三重SuperTrend共振信号参数
    st1_period = IntParameter(7, 20, default=10, space='buy', optimize=True)
    st1_multiplier = DecimalParameter(1.0, 3.0, default=1.0, decimals=1, space='buy', optimize=True)
    st2_period = IntParameter(15, 30, default=11, space='buy', optimize=True)
    st2_multiplier = DecimalParameter(2.0, 4.0, default=2.0, decimals=1, space='buy', optimize=True)
    st3_period = IntParameter(25, 50, default=12, space='buy', optimize=True)
    st3_multiplier = DecimalParameter(3.0, 5.0, default=3.0, decimals=1, space='buy', optimize=True)
    st_trend_score = IntParameter(10, 40, default=20, space="buy")

    # 2.2: MACD/EMA共振信号参数
    # 多头参数
    buy_macd_fast = IntParameter(7, 21, default=12, space="buy")
    buy_macd_slow = IntParameter(14, 42, default=31, space="buy")
    buy_macd_signal = IntParameter(6, 18, default=14, space="buy")
    buy_ema_short = IntParameter(5, 20, default=12, space="buy")
    buy_ema_long = IntParameter(35, 90, default=65, space="buy")
    # 空头参数 (独立优化)
    sell_macd_fast = IntParameter(7, 21, default=10, space="sell")
    sell_macd_slow = IntParameter(14, 42, default=26, space="sell")
    sell_macd_signal = IntParameter(6, 18, default=9, space="sell")
    sell_ema_short = IntParameter(5, 20, default=20, space="sell")
    sell_ema_long = IntParameter(35, 90, default=67, space="sell")
    # 分数权重
    harmony_trend_score = IntParameter(10, 30, default=15, space="buy")

    # 2.3: Heikin Ashi 反转信号参数
    ha_strong_candle_pct = DecimalParameter(0.6, 0.9, default=0.75, decimals=2, space="buy") # "强力"K线的实体占比
    ha_reversal_score = IntParameter(20, 50, default=30, space="buy")

    # 3.1: 大周期趋势过滤器参数
    mtf_ema_period = IntParameter(50, 200, default=100, space="protection")
    mtf_trend_filter_enabled = CategoricalParameter([True, False], default=True, space='protection', optimize=True)

    # 3.2: 协同效应增强参数
    synergy_trend_score_bonus = IntParameter(10, 30, default=20, space="buy")


    #================================================================================================
    # 第五层: 资金与风险管理 (MONEY & RISK MANAGEMENT)
    # --- 目标: 专业地管理每一笔入场的交易
    # --- 技术: 动态仓位, 动态杠杆, 混合止损, 分段止盈
    #================================================================================================

    # --- 5.1: 混合动态止损参数 ---
    # ATR初始止损
    sl_atr_multiplier = DecimalParameter(1.0, 5.0, default=3.0, space="protection")
    # 分层利润保护止损 (基于GeneTrader)
    # 阶段1
    p1_profit_trigger = DecimalParameter(0.01, 0.03, default=0.02, space='sell')  # 利润达到2%时触发
    p1_stop_loss = DecimalParameter(0.001, 0.01, default=0.005, space='sell')      # 止损拉到盈利0.5%
    # 阶段2
    p2_profit_trigger = DecimalParameter(0.03, 0.07, default=0.05, space='sell') # 利润达到5%时触发
    p2_stop_loss = DecimalParameter(0.01, 0.03, default=0.02, space='sell')      # 止损拉到盈利2%
    # 追踪止损阶段
    tsl_profit_trigger = DecimalParameter(0.07, 0.15, default=0.1, space='sell') # 利润达到10%时激活追踪止损
    tsl_trail_offset = DecimalParameter(0.02, 0.05, default=0.03, space='sell')   # 追踪回撤3%

    # --- 5.2: ATR动态分批止盈参数 ---
    # 阶段1
    tp1_atr_multiplier = DecimalParameter(1.0, 3.0, default=1.5, space='sell')
    tp1_exit_pct = DecimalParameter(0.3, 0.5, default=0.33, space='sell')
    # 阶段2
    tp2_atr_multiplier = DecimalParameter(2.0, 5.0, default=3.0, space='sell')
    tp2_exit_pct = DecimalParameter(0.4, 0.6, default=0.5, space='sell') # 卖出剩余仓位的50%
    # 最终阶段
    tp3_atr_multiplier = DecimalParameter(4.0, 8.0, default=5.0, space='sell')
    # 时间止损
    exit_time_in_hours = IntParameter(12, 48, default=24, space='sell')

    # --- 5.3: 动态DCA参数 (基于Jamroly) ---
    dca_enabled = CategoricalParameter([True, False], default=False, space='protection', optimize=True)
    dca_max_orders = IntParameter(1, 5, default=3, space='buy')
    dca_atr_multiplier = DecimalParameter(1.0, 3.0, default=2.0, space='buy') # 初始安全单的ATR距离
    dca_step_scale = DecimalParameter(1.0, 2.0, default=1.5, space='buy')     # 安全单距离的步进乘数
    dca_volume_scale = DecimalParameter(1.0, 2.0, default=1.8, space='buy')   # 安全单仓位的步进乘数

    # --- ATR Parameters for SL/TP/Sizing ---
    risk_atr_period = IntParameter(14, 28, default=14, space="protection")


    def informative_pairs(self):
        """
        定义策略需要用到的信息对(informative pairs)
        我们将为白名单中的所有交易对获取1小时的数据
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        为策略的所有层面填充所有必需的指标。
        """
        # --- 首先，处理并合并大周期数据 ---
        informative_df = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=self.informative_timeframe)
        informative_df[f'mtf_ema_{self.mtf_ema_period.value}'] = ta.EMA(informative_df, timeperiod=self.mtf_ema_period.value)
        dataframe = merge_informative_pair(dataframe, informative_df, self.timeframe, self.informative_timeframe, ffill=True)

        # --- 第一层: 市场状态过滤器 ---
        # 1.1: ADX
        dataframe['regime_adx'] = ta.ADX(dataframe, timeperiod=self.regime_adx_period.value)
        dataframe['is_trending'] = dataframe['regime_adx'] > self.regime_adx_trend_threshold.value
        dataframe['is_ranging'] = dataframe['regime_adx'] < self.regime_adx_range_threshold.value
        
        # 1.2: 布林带
        bollinger = qtpylib.bollinger_bands(dataframe['close'], window=self.regime_bb_period.value, stds=self.regime_bb_std.value)
        dataframe['regime_bb_width'] = (bollinger['upper'] - bollinger['lower']) / bollinger['mid']
        
        # 1.3: KAMA
        dataframe['regime_kama'] = ta.KAMA(dataframe, timeperiod=self.regime_kama_period.value)
        dataframe['regime_kama_slope'] = dataframe['regime_kama'].diff()

        # --- 第二层 & 第三层: 信号生成与加强 ---
        # 初始化分数
        dataframe['buy_score'] = 0
        dataframe['sell_score'] = 0

        # --- 2.1: 计算三SuperTrend共振信号并评分 ---
        st_buy_signal, st_sell_signal = self._signal_triple_supertrend(dataframe)
        dataframe.loc[st_buy_signal, 'buy_score'] += self.st_trend_score.value
        dataframe.loc[st_sell_signal, 'sell_score'] += self.st_trend_score.value
        
        # --- 2.2: 计算MACD/EMA共振信号并评分 ---
        harmony_buy, harmony_sell = self._signal_macd_ema_harmony(dataframe)
        dataframe.loc[harmony_buy, 'buy_score'] += self.harmony_trend_score.value
        dataframe.loc[harmony_sell, 'sell_score'] += self.harmony_trend_score.value
        
        # --- 2.3: 计算Heikin Ashi反转信号并评分 ---
        ha_buy, ha_sell = self._signal_heikin_ashi_reversal(dataframe)
        # 在震荡市中，此信号权重更高
        dataframe.loc[ha_buy & dataframe['is_ranging'], 'buy_score'] += self.ha_reversal_score.value
        dataframe.loc[ha_sell & dataframe['is_ranging'], 'sell_score'] += self.ha_reversal_score.value
        # 在趋势市中，也可作为回调信号，但权重较低
        dataframe.loc[ha_buy & dataframe['is_trending'], 'buy_score'] += int(self.ha_reversal_score.value / 2)
        dataframe.loc[ha_sell & dataframe['is_trending'], 'sell_score'] += int(self.ha_reversal_score.value / 2)
        
        # TODO: (中文) 实现所有其他信号源并加入评分系统
        
        # --- 第三层: 过滤器与增强 ---
        # 3.1: 应用大周期过滤器
        dataframe = self._filter_mtf_trend(dataframe)

        # 3.2: 应用协同效应增强
        dataframe = self._enhance_synergy(dataframe, st_buy_signal, st_sell_signal, harmony_buy, harmony_sell)

        # TODO: (中文) 实现所有其他过滤器和增强逻辑
        # 例如:
        # dataframe = self._filter_volume(dataframe)
        # dataframe = self._filter_btc_safe(dataframe)

        return dataframe


    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        第四层: 入场决策
        --- 目标: 基于优化后的分数和当前市场状态做出最终的入场决策。
        """
        # --- 动态入场阈值 ---
        # 根据市场状态 (趋势/震荡) 选择不同的入场分数阈值

        # 做多条件
        enter_long_trending = (
            dataframe['is_trending'] &
            (dataframe['buy_score'] >= self.entry_score_threshold_trending.value)
        )
        enter_long_ranging = (
            dataframe['is_ranging'] &
            (dataframe['buy_score'] >= self.entry_score_threshold_ranging.value)
        )

        dataframe.loc[
            (enter_long_trending | enter_long_ranging) &
            (dataframe['volume'] > 0),
            ['enter_long', 'enter_tag']] = (1, 'buy_signal_scored')

        # 做空条件
        enter_short_trending = (
            dataframe['is_trending'] &
            (dataframe['sell_score'] >= self.entry_score_threshold_trending.value)
        )
        enter_short_ranging = (
            dataframe['is_ranging'] &
            (dataframe['sell_score'] >= self.entry_score_threshold_ranging.value)
        )
        
        dataframe.loc[
            (enter_short_trending | enter_short_ranging) &
            (dataframe['volume'] > 0),
            ['enter_short', 'enter_tag']] = (1, 'sell_signal_scored')

        return dataframe


    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        主要的退出信号将由 custom_exit 和 custom_stoploss 处理。
        此函数可以作为备用或用于处理高紧急度的退出信号。
        """
        # 示例: 当BTC极端崩溃时的紧急退出
        # dataframe.loc[self.emergency_btc_filter(dataframe), ['exit_long', 'exit_short']] = (1, 'emergency_exit')
        return dataframe
    
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        第五层: 资金与风险管理 (止损部分)
        --- 目标: 将多种止损概念结合成一个稳健的混合系统。
        """
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if not dataframe.empty:
            last_candle = dataframe.iloc[-1]
            atr_value = last_candle.get(f'atr_{self.risk_atr_period.value}')
            if atr_value:
                # --- ATR初始止损 ---
                initial_stop_price_long = trade.open_rate - (atr_value * self.sl_atr_multiplier.value)
                initial_stop_price_short = trade.open_rate + (atr_value * self.sl_atr_multiplier.value)
                
                # --- 分层利润锁定 + 追踪止损 (核心逻辑) ---
                # 对于多头
                if trade.is_long:
                    # 阶段1: 小额利润保护
                    if current_profit > self.p1_profit_trigger.value:
                        sl_price = trade.open_rate * (1 + self.p1_stop_loss.value)
                        return max(sl_price, initial_stop_price_long) # 取更优的止损
                    # 阶段2: 中等利润保护
                    elif current_profit > self.p2_profit_trigger.value:
                        sl_price = trade.open_rate * (1 + self.p2_stop_loss.value)
                        return max(sl_price, initial_stop_price_long) # 取更优的止损
                    # 阶段3: 追踪止损
                    elif current_profit > self.tsl_profit_trigger.value:
                        sl_price = current_rate * (1 - self.tsl_trail_offset.value)
                        return max(sl_price, initial_stop_price_long) # 取更优的止损

                    return initial_stop_price_long
                
                # 对于空头 (逻辑相反)
                elif trade.is_short:
                    # 阶段1
                    if current_profit > self.p1_profit_trigger.value:
                        sl_price = trade.open_rate * (1 - self.p1_stop_loss.value)
                        return min(sl_price, initial_stop_price_short) # 取更优的止损
                    # 阶段2
                    elif current_profit > self.p2_profit_trigger.value:
                        sl_price = trade.open_rate * (1 - self.p2_stop_loss.value)
                        return min(sl_price, initial_stop_price_short) # 取更优的止损
                    # 阶段3
                    elif current_profit > self.tsl_profit_trigger.value:
                        sl_price = current_rate * (1 + self.tsl_trail_offset.value)
                        return min(sl_price, initial_stop_price_short) # 取更优的止损

                    return initial_stop_price_short

        # 如果数据不可用，则回退到静态止损。
        # 返回一个代表-99%亏损的价格，基本等于不设止损，让其他逻辑处理。
        return trade.open_rate * (1 - self.stoploss if trade.is_long else 1 + self.stoploss)
    
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        第五层: 资金与风险管理 (止盈部分)
        --- 目标: 实现基于ATR的动态分批止盈系统和时间止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        last_candle = dataframe.iloc[-1]
        atr_value = last_candle.get(f'atr_{self.risk_atr_period.value}')
        if not atr_value:
            return None

        # --- 时间止损 ---
        trade_duration_hours = (current_time - trade.open_date_utc).total_seconds() / 3600
        if trade_duration_hours > self.exit_time_in_hours.value:
            return f"time_exit_{int(trade_duration_hours)}h"

        # --- ATR动态止盈 ---
        # 计算不同阶段的止盈价格
        tp1_price = trade.open_rate + (atr_value * self.tp1_atr_multiplier.value) if trade.is_long else \
                    trade.open_rate - (atr_value * self.tp1_atr_multiplier.value)
        tp2_price = trade.open_rate + (atr_value * self.tp2_atr_multiplier.value) if trade.is_long else \
                    trade.open_rate - (atr_value * self.tp2_atr_multiplier.value)
        tp3_price = trade.open_rate + (atr_value * self.tp3_atr_multiplier.value) if trade.is_long else \
                    trade.open_rate - (atr_value * self.tp3_atr_multiplier.value)
        
        # 获取已成功退出的次数
        successful_exits = trade.nr_of_successful_exits

        if trade.is_long:
            # 阶段1
            if current_rate >= tp1_price and successful_exits == 0:
                return 'partial_exit_tp1', self.tp1_exit_pct.value
            # 阶段2
            elif current_rate >= tp2_price and successful_exits == 1:
                return 'partial_exit_tp2', self.tp2_exit_pct.value
            # 阶段3 (最终退出)
            elif current_rate >= tp3_price and successful_exits == 2:
                return 'full_exit_tp3'
        
        elif trade.is_short:
            # 阶段1
            if current_rate <= tp1_price and successful_exits == 0:
                return 'partial_exit_tp1', self.tp1_exit_pct.value
            # 阶段2
            elif current_rate <= tp2_price and successful_exits == 1:
                return 'partial_exit_tp2', self.tp2_exit_pct.value
            # 阶段3 (最终退出)
            elif current_rate <= tp3_price and successful_exits == 2:
                return 'full_exit_tp3'

        return None


    def adjust_trade_position(self, trade: 'Trade', current_time: datetime,
                              current_rate: float, current_profit: float, min_stake: float,
                              max_stake: float, **kwargs) -> float:
        """
        第五层: 资金与风险管理 (DCA/仓位调整部分)
        --- 目标: 实现基于ATR的动态DCA（定投/补仓）逻辑
        """
        if not self.dca_enabled.value:
            return None

        # 如果已经达到最大补仓次数，则不再操作
        if trade.nr_of_successful_entries >= self.dca_max_orders.value + 1:
            return None

        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        if dataframe.empty:
            return None

        last_candle = dataframe.iloc[-1]
        atr_value = last_candle.get(f'atr_{self.risk_atr_period.value}')
        if not atr_value:
            return None

        # --- 计算下一个安全单的目标价格 ---
        num_dcas = trade.nr_of_successful_entries - 1
        
        # 距离步长 (可以随补仓次数增加)
        distance_multiplier = self.dca_atr_multiplier.value * (self.dca_step_scale.value ** num_dcas)
        distance = atr_value * distance_multiplier
        
        # 获取平均入场价，所有计算都应以此为基准
        avg_entry_price = trade.open_rate

        if trade.is_long:
            target_price = avg_entry_price - distance
        else: # is_short
            target_price = avg_entry_price + distance

        # --- 判断是否触发补仓 ---
        should_dca = False
        if trade.is_long and current_rate <= target_price:
            should_dca = True
        elif trade.is_short and current_rate >= target_price:
            should_dca = True

        if should_dca:
            # --- 计算补仓金额 ---
            # 仓位步长 (可以随补仓次数增加)
            volume_multiplier = self.dca_volume_scale.value ** num_dcas
            # 使用该笔交易的初始仓位作为基数
            initial_stake = trade.stake_amount
            # 计算本次补仓的金额
            dca_stake = initial_stake * volume_multiplier
            
            # 确保补仓金额在最小和最大限制内
            dca_stake = max(min_stake, dca_stake)
            dca_stake = min(max_stake, dca_stake)
            
            return dca_stake

        return None


    # --- Signal & Filter Helper Functions (信号与过滤辅助函数) ---
    def _signal_triple_supertrend(self, dataframe: DataFrame) -> tuple[DataFrame, DataFrame]:
        """
        信号源模块: 三重SuperTrend共振
        当三个不同周期的SuperTrend指标同向时，产生一个强烈的趋势信号。
        """
        # --- 计算三个SuperTrend指标 ---
        st1 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=self.st1_period.value, multiplier=self.st1_multiplier.value
        )
        dataframe['st1_dir'] = st1[f'SUPERTd_{self.st1_period.value}_{self.st1_multiplier.value}']

        st2 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=self.st2_period.value, multiplier=self.st2_multiplier.value
        )
        dataframe['st2_dir'] = st2[f'SUPERTd_{self.st2_period.value}_{self.st2_multiplier.value}']

        st3 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=self.st3_period.value, multiplier=self.st3_multiplier.value
        )
        dataframe['st3_dir'] = st3[f'SUPERTd_{self.st3_period.value}_{self.st3_multiplier.value}']

        # --- 判断共振条件 ---
        # 当三个SuperTrend方向均为1 (看涨) 时，产生做多信号
        long_signal = (
            (dataframe['st1_dir'] == 1) &
            (dataframe['st2_dir'] == 1) &
            (dataframe['st3_dir'] == 1)
        )
        
        # 当三个SuperTrend方向均为-1 (看跌) 时，产生做空信号
        short_signal = (
            (dataframe['st1_dir'] == -1) &
            (dataframe['st2_dir'] == -1) &
            (dataframe['st3_dir'] == -1)
        )
        
        return long_signal, short_signal
    
    def _signal_macd_ema_harmony(self, dataframe: DataFrame) -> tuple[DataFrame, DataFrame]:
        """
        信号源模块: MACD/EMA 共振 (和谐)
        识别 "主趋势中的回调结束点" 这种高质量信号。
        - 主趋势: 由长期和短期EMA的方向定义。
        - 回调结束: 由MACD柱状图穿越零轴确认。
        """
        # --- 多头信号计算 ---
        buy_macd = ta.MACD(dataframe, fastperiod=self.buy_macd_fast.value,
                           slowperiod=self.buy_macd_slow.value,
                           signalperiod=self.buy_macd_signal.value)
        dataframe['buy_macdhist'] = buy_macd['macdhist']
        dataframe['buy_ema_short'] = ta.EMA(dataframe, timeperiod=self.buy_ema_short.value)
        dataframe['buy_ema_long'] = ta.EMA(dataframe, timeperiod=self.buy_ema_long.value)

        # 主升趋势
        is_uptrend = dataframe['buy_ema_short'] > dataframe['buy_ema_long']
        # 回调结束 (MACD柱从负值上穿零轴)
        macd_cross_up = qtpylib.crossed_above(dataframe['buy_macdhist'], 0)
        
        long_signal = is_uptrend & macd_cross_up

        # --- 空头信号计算 ---
        sell_macd = ta.MACD(dataframe, fastperiod=self.sell_macd_fast.value,
                            slowperiod=self.sell_macd_slow.value,
                            signalperiod=self.sell_macd_signal.value)
        dataframe['sell_macdhist'] = sell_macd['macdhist']
        dataframe['sell_ema_short'] = ta.EMA(dataframe, timeperiod=self.sell_ema_short.value)
        dataframe['sell_ema_long'] = ta.EMA(dataframe, timeperiod=self.sell_ema_long.value)

        # 主跌趋势
        is_downtrend = dataframe['sell_ema_short'] < dataframe['sell_ema_long']
        # 反弹结束 (MACD柱从正值下穿零轴)
        macd_cross_down = qtpylib.crossed_below(dataframe['sell_macdhist'], 0)

        short_signal = is_downtrend & macd_cross_down
        
        return long_signal, short_signal

    def _signal_heikin_ashi_reversal(self, dataframe: DataFrame) -> tuple[DataFrame, DataFrame]:
        """
        信号源模块: Heikin Ashi (平均K线) 反转
        专门用于捕捉震荡行情中的反转点，或趋势行情中的深度回调。
        识别 "红色HA -> 强力绿色HA" (看涨) 或 "绿色HA -> 强力红色HA" (看跌) 的形态。
        """
        # 计算Heikin Ashi K线
        ha_df = qtpylib.heikinashi(dataframe)
        
        # 计算HA K线实体和影线
        ha_body = (ha_df['close'] - ha_df['open']).abs()
        ha_range = (ha_df['high'] - ha_df['low']).abs().replace(0, 0.00001) # 避免除以0
        ha_body_pct = ha_body / ha_range
        
        # 定义强力K线 (实体占比大)
        is_strong_green = (ha_df['close'] > ha_df['open']) & (ha_body_pct > self.ha_strong_candle_pct.value)
        is_strong_red = (ha_df['open'] > ha_df['close']) & (ha_body_pct > self.ha_strong_candle_pct.value)
        
        # 定义回调/反弹K线 (前一根K线是反向的)
        prev_is_red = ha_df['open'].shift(1) > ha_df['close'].shift(1)
        prev_is_green = ha_df['close'].shift(1) > ha_df['open'].shift(1)
        
        # --- 看涨反转信号: [红色HA, 强力绿色HA] ---
        long_signal = prev_is_red & is_strong_green
        
        # --- 看跌反转信号: [绿色HA, 强力红色HA] ---
        short_signal = prev_is_green & is_strong_red
        
        return long_signal, short_signal

    def _filter_mtf_trend(self, dataframe: DataFrame) -> DataFrame:
        """
        过滤器模块: 大周期趋势过滤器 (MTF)
        确保所有交易都顺应更高时间框架的主趋势。
        """
        if not self.mtf_trend_filter_enabled.value:
            return dataframe

        # 从合并后的DataFrame中获取1小时EMA列的名称
        mtf_ema_col = f'mtf_ema_{self.mtf_ema_period.value}_{self.informative_timeframe}'
        
        # 定义大周期趋势: 简单地判断价格在长周期EMA的哪一侧
        is_mtf_up = (dataframe['close'] > dataframe[mtf_ema_col])
        is_mtf_down = (dataframe['close'] < dataframe[mtf_ema_col])

        # 如果交易方向与大趋势相反，则将分数清零
        # 这是一个硬过滤器，直接否决逆势交易
        dataframe.loc[is_mtf_down, 'buy_score'] = 0
        dataframe.loc[is_mtf_up, 'sell_score'] = 0
        
        return dataframe

    def _enhance_synergy(self, dataframe: DataFrame, *signals) -> DataFrame:
        """
        增强模块: 协同效应
        当多个独立的强信号同时发生时，给予额外的奖励分数。
        """
        # signals 参数是一个元组，包含成对的 (buy_signal, sell_signal)
        # 例如: (st_buy, st_sell, harmony_buy, harmony_sell, ...)
        buy_signals = [signals[i] for i in range(0, len(signals), 2)]
        sell_signals = [signals[i] for i in range(1, len(signals), 2)]

        # --- 趋势协同: 当两个或更多趋势信号同时出现 ---
        # 计算同时出现的买入趋势信号数量
        num_buy_signals = sum(buy_signals)
        # 计算同时出现的卖出趋势信号数量
        num_sell_signals = sum(sell_signals)

        # 如果有两个或以上趋势信号共振，则添加协同奖励分
        dataframe.loc[num_buy_signals >= 2, 'buy_score'] += self.synergy_trend_score_bonus.value
        dataframe.loc[num_sell_signals >= 2, 'sell_score'] += self.synergy_trend_score_bonus.value

        # TODO: (中文) 可以为不同类型的信号组合设计更复杂的协同逻辑
        # 例如: 趋势信号 + 反转信号的组合等

        return dataframe

    # TODO: (中文) 为每个信号源和过滤器创建辅助函数
    # def _filter_btc_safe(self, dataframe: DataFrame) -> DataFrame: ...
    
    pass 