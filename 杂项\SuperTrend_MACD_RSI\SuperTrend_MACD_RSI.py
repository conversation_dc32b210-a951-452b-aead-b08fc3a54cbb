# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these imports ---
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Dict, Optional, Union, Tuple, List
from freqtrade.optimize.space import Categorical, Dimension, Integer, SKDecimal
import logging

logger = logging.getLogger(__name__)

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)


# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


# ==========================================
# Supertrend + MACD + RSI Strategy | Backtest 299% Profit Using Python & Freqtrade
# https://youtu.be/71EA1u4K_Zk
# ==========================================


# ================================
# Download Historical Data
# ================================

"""
freqtrade download-data \
    -c user_data/binance_futures_SuperTrend_MACD_RSI.json \
    --timerange 20230101- \
    -t 1m 5m 15m 30m 1h 2h 4h 1d
"""


# ================================
# Lookahead Analysis 
# ================================

"""
freqtrade lookahead-analysis \
    --strategy SuperTrend_MACD_RSI \
    --timeframe 1h \
    --timerange 20240401-20241201 \
    -c user_data/binance_futures_SuperTrend_MACD_RSI.json \
    --max-open-trades 1 \
    -p DOT/USDT:USDT
"""

# ================================
# Hyperopt Optimization
# ================================

"""
freqtrade hyperopt \
    --strategy SuperTrend_MACD_RSI \
    --config user_data/binance_futures_SuperTrend_MACD_RSI.json \
    --timeframe 1h \
    --timerange 20240401-20241201 \
    --hyperopt-loss MultiMetricHyperOptLoss \
    --spaces buy\
    -e 50 \
    --j -2 \
    --random-state 9319 \
    --min-trades 20 \
    --max-open-trades 1 \
    -p DOT/USDT:USDT
"""

# ================================
# Backtesting
# ================================

"""
freqtrade backtesting \
    --strategy SuperTrend_MACD_RSI \
    --timeframe 1h \
    --timerange 20240401-20250401 \
    --breakdown month \
    -c user_data/binance_futures_SuperTrend_MACD_RSI.json \
    --max-open-trades 1 \
    --cache none \
    --timeframe-detail 5m \
    -p DOT/USDT:USDT
"""

# ================================
# Start FreqUI Web Interface
# ================================

"""
freqtrade webserver \
    --config user_data/binance_futures_SuperTrend_MACD_RSI.json
"""


class SuperTrend_MACD_RSI(IStrategy):
            
    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = "30m"

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi".
    minimal_roi = {}

    # Dictionary defining the exit points for take profit and stop loss levels.
    exit_loss_profit = {}
    
    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.99

    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Tell freqtrade to a custom stoploss function
    use_custom_stoploss = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    # Stoploss
    stoploss_lookback = IntParameter(8, 24, default=12, space='buy', optimize=True)

    # Trailing stoploss
    trailing_stop = True
    trailing_only_offset_is_reached = True
    trailing_stop_positive = 0.008
    trailing_stop_positive_offset = 0.015

    supertrend_length = IntParameter(14, 30, default=20, space='buy') 
    supertrend_multiplier = IntParameter(3, 7, default=4, space='buy') 
    
    rsi_threshold = CategoricalParameter([50, 55, 60, 65], default=55, space="buy")
    max_candle_size = CategoricalParameter([0.03, 0.05, 0.07], default=0.05, space="buy")
    
    leverage_level = IntParameter(1, 10, default=1, space='buy', optimize=True, load=False)

    @staticmethod
    def sell_hyperopt_parameters() -> List[Dimension]:
        """
        Define the hyperopt parameters for the sell side.
        """
        return [
            RealParameter(0.003, 0.015, default=0.008, name='trailing_stop_positive'),
            RealParameter(0.008, 0.03, default=0.015, name='trailing_stop_positive_offset'),
        ]

    @property
    def plot_config(self):

        plot_config = {
            "main_plot": {
                f"supertrend_{self.supertrend_length.value}_{self.supertrend_multiplier.value}": 
                    {
                        "color": "#4caf50",
                        "type": "line",
                        "fill_to": "close"
                },
            },
            "subplots": {

                "RSI": {
                    f"rsi": {
                        "color": "#9e57c2",
                        "type": "line"
                    }
                },
                "MACD": {
                    'macd': {'color': '#2962ff', 'fill_to': 'macdhist'},
                    'macdsignal': {'color': '#ff6d00'},
                    'macdhist': {'type': 'bar', 'plotly': {'opacity': 0.9}}
                    }
            }
        }
        
        return plot_config
    
    
    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pair/interval combinations are non-tradeable, unless they are part
        of the whitelist as well.
        For more information, please consult the documentation
        :return: List of tuples in the format (pair, interval)
            Sample: return [("ETH/USDT", "5m"),
                            ("BTC/USDT", "15m"),
                            ]
        """
        
        # get access to all pairs available in whitelist.
        # pairs = self.dp.current_whitelist()

        # # Assign tf to each pair so they can be downloaded and cached for strategy.
        # informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        
        return []
    
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        macd = ta.MACD(dataframe)
        dataframe["macd"] = macd["macd"]
        dataframe["macdsignal"] = macd["macdsignal"]
        dataframe["macdhist"] = macd["macdhist"]
        
        dataframe["rsi"] = ta.RSI(dataframe)

        dataframe["atr"] = ta.ATR(dataframe)
        
        new_columns = {}
        for multiplier in self.supertrend_multiplier.range:
            for period in self.supertrend_length.range:
                superTrend = pta.supertrend(dataframe['high'], dataframe['low'], dataframe['close'], length=period, multiplier=multiplier)
                if superTrend is not None and not superTrend.empty:
                    new_columns[f'supertrend_{period}_{multiplier}'] = superTrend[f'SUPERT_{period}_{multiplier}.0']
                    new_columns[f'supertrend_direction_{period}_{multiplier}'] = superTrend[f'SUPERTd_{period}_{multiplier}.0']
        
        if new_columns:
            dataframe = pd.concat([dataframe, pd.DataFrame(new_columns, index=dataframe.index)], axis=1)
                
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:        
        
        supertrend_col = f'supertrend_direction_{self.supertrend_length.value}_{self.supertrend_multiplier.value}'
        
        dataframe.loc[
            (   
                (dataframe[supertrend_col] == 1) &
                (dataframe[supertrend_col].shift(1) == -1) &
                (dataframe['rsi'] > self.rsi_threshold.value) &
                (dataframe['macd'] > dataframe['macdsignal']) &
                (abs(dataframe['close'] - dataframe['open']) / dataframe['open'] < self.max_candle_size.value) &
                (dataframe["volume"] > 0) 
            ),
            'enter_long'] = 1

        dataframe.loc[
            (
                (dataframe[supertrend_col] == -1) &
                (dataframe[supertrend_col].shift(1) == 1) &
                (dataframe['rsi'] < (100 - self.rsi_threshold.value)) &
                (dataframe['macd'] < dataframe['macdsignal']) &
                (abs(dataframe['close'] - dataframe['open']) / dataframe['open'] < self.max_candle_size.value) &
                (dataframe["volume"] > 0) 
            ),
            'enter_short'] = 1

        return dataframe
    

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        supertrend_col = f'supertrend_direction_{self.supertrend_length.value}_{self.supertrend_multiplier.value}'

        # Exit long when supertrend flips to sell
        dataframe.loc[
            (
                (dataframe[supertrend_col] == -1) &
                (dataframe[supertrend_col].shift(1) == 1)
            ),
            'exit_long'] = 1

        # Exit short when supertrend flips to buy
        dataframe.loc[
            (
                (dataframe[supertrend_col] == 1) &
                (dataframe[supertrend_col].shift(1) == -1)
            ),
            'exit_short'] = 1

        return dataframe
    
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:

        return self.leverage_level.value