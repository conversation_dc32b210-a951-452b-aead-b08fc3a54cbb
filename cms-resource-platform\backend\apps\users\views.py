"""
用户系统视图
"""
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login, logout
from django.utils import timezone
from django.db.models import Q
from django.conf import settings
from .models import User, PointsHistory, SignInRecord
from .serializers import (
    UserSerializer, UserRegistrationSerializer, UserLoginSerializer,
    PasswordChangeSerializer, PasswordResetSerializer, UserUpdateSerializer,
    PointsHistorySerializer, SignInSerializer, SignInRecordSerializer
)


class UserRegistrationView(generics.CreateAPIView):
    """用户注册"""
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        
        return Response({
            'message': '注册成功',
            'user': UserSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        }, status=status.HTTP_201_CREATED)


class UserLoginView(APIView):
    """用户登录"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = serializer.validated_data['user']
        
        # 更新登录信息
        user.login_count += 1
        user.last_login_ip = self.get_client_ip(request)
        user.save(update_fields=['login_count', 'last_login_ip'])
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        
        return Response({
            'message': '登录成功',
            'user': UserSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        })
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserProfileView(generics.RetrieveUpdateAPIView):
    """用户资料"""
    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    def get(self, request, *args, **kwargs):
        """获取用户资料"""
        user = self.get_object()
        serializer = UserSerializer(user)
        return Response(serializer.data)


class PasswordChangeView(APIView):
    """修改密码"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return Response({'message': '密码修改成功'})


class PasswordResetView(APIView):
    """密码重置"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = PasswordResetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # TODO: 发送密码重置邮件
        # email = serializer.validated_data['email']
        # send_password_reset_email(email)
        
        return Response({'message': '密码重置邮件已发送'})


class PointsHistoryView(generics.ListAPIView):
    """积分历史"""
    serializer_class = PointsHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return PointsHistory.objects.filter(
            user=self.request.user
        ).order_by('-created_at')


class SignInView(APIView):
    """用户签到"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        user = request.user
        today = timezone.now().date()
        
        # 检查今天是否已签到
        if SignInRecord.objects.filter(user=user, date=today).exists():
            return Response(
                {'message': '今天已经签到过了'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算连续签到天数
        yesterday = today - timezone.timedelta(days=1)
        yesterday_record = SignInRecord.objects.filter(
            user=user, date=yesterday
        ).first()
        
        if yesterday_record:
            consecutive_days = yesterday_record.consecutive_days + 1
        else:
            consecutive_days = 1
        
        # 计算签到奖励积分
        base_points = getattr(settings, 'DAILY_SIGNIN_POINTS', 10)
        bonus_points = min(consecutive_days - 1, 6)  # 最多额外6积分
        total_points = base_points + bonus_points
        
        # 创建签到记录
        signin_record = SignInRecord.objects.create(
            user=user,
            date=today,
            points_earned=total_points,
            consecutive_days=consecutive_days
        )
        
        # 增加积分
        user.add_points(total_points, f'每日签到奖励 (连续{consecutive_days}天)')
        
        return Response({
            'message': '签到成功',
            'points_earned': total_points,
            'consecutive_days': consecutive_days,
            'total_points': user.points
        })


class SignInRecordView(generics.ListAPIView):
    """签到记录"""
    serializer_class = SignInRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return SignInRecord.objects.filter(
            user=self.request.user
        ).order_by('-date')


class UserStatsView(APIView):
    """用户统计"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        user = request.user
        today = timezone.now().date()
        
        # 今日是否已签到
        has_signed_today = SignInRecord.objects.filter(
            user=user, date=today
        ).exists()
        
        # 连续签到天数
        latest_record = SignInRecord.objects.filter(user=user).first()
        consecutive_days = latest_record.consecutive_days if latest_record else 0
        
        # 本月积分变动
        this_month = timezone.now().replace(day=1)
        month_points_earned = PointsHistory.objects.filter(
            user=user,
            type='earn',
            created_at__gte=this_month
        ).aggregate(total=models.Sum('amount'))['total'] or 0
        
        month_points_spent = PointsHistory.objects.filter(
            user=user,
            type='spend',
            created_at__gte=this_month
        ).aggregate(total=models.Sum('amount'))['total'] or 0
        
        return Response({
            'points': user.points,
            'total_points_earned': user.total_points_earned,
            'total_points_spent': user.total_points_spent,
            'download_count': user.download_count,
            'upload_count': user.upload_count,
            'has_signed_today': has_signed_today,
            'consecutive_days': consecutive_days,
            'month_points_earned': month_points_earned,
            'month_points_spent': abs(month_points_spent),
            'is_vip': user.is_vip,
            'is_vip_active': user.is_vip_active,
            'vip_expire_date': user.vip_expire_date,
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """用户登出"""
    logout(request)
    return Response({'message': '登出成功'})
