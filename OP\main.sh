#!/bin/bash
#
# Freqtrade 通用工作流主控脚本
#
# 该脚本提供一个菜单驱动的界面，用于引导用户完成 Freqtrade 的各项任务，
# 包括：参数优化、回测、结果分析和配置更新。
#
# 使用方法:
# 1. 直接运行此脚本: ./main.sh
# 2. 根据提示输入您的 Freqtrade 项目根目录和策略名称。
# 3. 从菜单中选择要执行的操作。
#

# --- 脚本初始化 ---
# 获取脚本所在目录，以便调用其他子脚本
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
CONFIG_CACHE_FILE="${SCRIPT_DIR}/op_config.env" # 新增：配置文件路径
PYTHON_EXEC="${SCRIPT_DIR}/venv/bin/python"     # 新增: 指定使用虚拟环境的Python
MAIN_LOG_FILE="${SCRIPT_DIR}/op_workflow_main.log" # 新增: 主日志文件

# --- 初始化主日志 ---
# 每次启动时，如果选择了新配置，可以考虑清空旧日志
# 这里我们选择追加模式，以便保留历史记录
echo "===================================================================" >> "$MAIN_LOG_FILE"
echo "工作流启动于: $(date)" >> "$MAIN_LOG_FILE"
echo "===================================================================" >> "$MAIN_LOG_FILE"

# --- 函数定义 ---

# 打印标题
print_header() {
    echo "==================================================================="
    echo "$1"
    echo "==================================================================="
}

# 封装一个函数用于带默认值的读取
read_with_default() {
    local prompt_message=$1
    local default_value=$2
    local -n result_var=$3 # 使用-n将变量名作为引用传递
    
    read -p "$prompt_message [$default_value]: " user_input
    result_var="${user_input:-$default_value}"
}

# 新增：检查并设置Python虚拟环境
check_and_setup_python_env() {
    print_header "Python 环境检查"
    # 检查 python3 命令是否存在
    if ! command -v python3 &> /dev/null; then
        echo "错误: python3 未安装。请先安装 Python 3.8 或更高版本。"
        exit 1
    fi

    # 检查虚拟环境目录是否存在
    if [ ! -d "${SCRIPT_DIR}/venv" ]; then
        echo "未检测到 Python 虚拟环境，正在为您创建..."
        python3 -m venv "${SCRIPT_DIR}/venv"
        if [ $? -ne 0 ]; then
            echo "错误: 创建 Python 虚拟环境失败。"
            exit 1
        fi
        echo "虚拟环境创建成功。"
    fi

    # 检查依赖文件是否存在
    if [ ! -f "${SCRIPT_DIR}/requirements.txt" ]; then
        echo "警告: 未找到依赖文件 'requirements.txt'，跳过依赖安装。"
        return
    fi
    
    echo "正在检查并安装必要的 Python 依赖包..."
    # 使用虚拟环境的 pip 安装依赖
    # 添加 --quiet 标志以减少不必要的输出
    "${PYTHON_EXEC}" -m pip install --quiet -r "${SCRIPT_DIR}/requirements.txt"
    if [ $? -ne 0 ]; then
        echo "错误: 安装 Python 依赖失败。请检查 'requirements.txt' 文件和您的 pip 配置。"
        exit 1
    fi
    echo "Python 环境已准备就绪。"
}

# 新增：保存配置的函数
save_user_config() {
    echo
    echo "正在保存当前配置以备下次使用..."
    {
        echo "PROJECT_ROOT=\"${PROJECT_ROOT}\""
        echo "OPT_STRATEGY_NAME=\"${OPT_STRATEGY_NAME}\""
        echo "BT_STRATEGY_NAME=\"${BT_STRATEGY_NAME}\""
        echo "CONFIG_FILE_NAME=\"${CONFIG_FILE_NAME}\""
        echo "STRATEGY_PATH=\"${STRATEGY_PATH}\""
        echo "TIMEFRAME=\"${TIMEFRAME}\""
        echo "HYPEROPT_SPACES=\"${HYPEROPT_SPACES}\""
        echo "EPOCHS=\"${EPOCHS}\""
        echo "OPT_START_DATE=\"${OPT_START_DATE}\""
        echo "OPT_END_DATE=\"${OPT_END_DATE}\""
        echo "BT_START_DATE=\"${BT_START_DATE}\""
        echo "BT_END_DATE=\"${BT_END_DATE}\""
    } > "$CONFIG_CACHE_FILE"
    echo "配置已保存至 ${CONFIG_CACHE_FILE}"
}

# 新增: 仅用于显示和导出配置的函数
display_and_export_config() {
    # 构造 timerange 字符串
    HYPEROPT_TIMERANGE="${OPT_START_DATE}-${OPT_END_DATE}"
    BACKTEST_TIMERANGE="${BT_START_DATE}-${BT_END_DATE}"
    
    # 将配置导出为环境变量，供子脚本使用
    export PROJECT_ROOT
    export OPT_STRATEGY_NAME
    export BT_STRATEGY_NAME
    export CONFIG_FILE_NAME
    export STRATEGY_PATH
    export TIMEFRAME
    export HYPEROPT_TIMERANGE
    export BACKTEST_TIMERANGE
    export HYPEROPT_SPACES
    export EPOCHS

    print_header "配置确认"
    echo "项目根目录:       $PROJECT_ROOT"
    echo "策略目录:         $PROJECT_ROOT/user_data/$STRATEGY_PATH"
    echo "配置文件:         $PROJECT_ROOT/user_data/$CONFIG_FILE_NAME"
    echo "---"
    echo "优化策略:         $OPT_STRATEGY_NAME"
    echo "回测策略:         $BT_STRATEGY_NAME"
    echo "---"
    echo "通用时间周期:     $TIMEFRAME"
    echo "优化参数空间:     $HYPEROPT_SPACES"
    echo "优化轮数:         $EPOCHS"
    echo "优化时间范围:     $HYPEROPT_TIMERANGE"
    echo "回测时间范围:     $BACKTEST_TIMERANGE"
    echo "==================================================================="
}

# 新增: 仅用于逐项提问的函数
prompt_all_settings() {
    print_header "欢迎使用 Freqtrade 通用工作流脚本"
    echo "在开始之前，需要您提供一些基本配置信息。"
    echo "如果括号内有默认值，直接按 Enter 即可使用该值。"
    echo

    # --- 核心配置 ---
    read_with_default "请输入 Freqtrade 项目的绝对路径" "${PROJECT_ROOT:-/root/ft}" PROJECT_ROOT
    while ! [ -f "$PROJECT_ROOT/docker-compose.yml" ]; do
        echo "错误: 指定的路径 '$PROJECT_ROOT' 不是一个有效的 Freqtrade 项目目录 (未找到 docker-compose.yml)。"
        read -p "请重新输入 Freqtrade 项目的绝对路径: " PROJECT_ROOT
    done

    read_with_default "请输入用于[优化]的策略名称 (e.g., MyStrategy_Final)" "${OPT_STRATEGY_NAME:-HAPBS_Final}" OPT_STRATEGY_NAME
    read_with_default "请输入用于[回测]的参数化策略名称 (e.g., MyStrategy_PairOptimized)" "${BT_STRATEGY_NAME:-HAPBS_PairOptimized}" BT_STRATEGY_NAME

    read_with_default "请输入主配置文件的名称 (位于 user_data/ 下)" "${CONFIG_FILE_NAME:-HAPBS_config.json}" CONFIG_FILE_NAME
    while ! [ -f "$PROJECT_ROOT/user_data/$CONFIG_FILE_NAME" ]; do
        echo "错误: 在 '$PROJECT_ROOT/user_data/' 下找不到名为 '$CONFIG_FILE_NAME' 的文件。"
        read -p "请重新输入主配置文件的名称: " CONFIG_FILE_NAME
    done
    
    read_with_default "请输入策略文件所在的目录 (相对于 user_data/)" "${STRATEGY_PATH:-strategies/HAPBS}" STRATEGY_PATH
    while ! [ -d "$PROJECT_ROOT/user_data/$STRATEGY_PATH" ]; do
        echo "错误: 在 '$PROJECT_ROOT/user_data/' 下找不到名为 '$STRATEGY_PATH' 的目录。"
        read -p "请重新输入策略文件所在的目录: " STRATEGY_PATH
    done

    # --- 自动检测参数空间 ---
    OPT_STRATEGY_FILE="$PROJECT_ROOT/user_data/$STRATEGY_PATH/${OPT_STRATEGY_NAME}.py"
    DETECTED_SPACES=""
    if [ -f "$OPT_STRATEGY_FILE" ]; then
        # 从策略文件中提取所有 'space' 的值，去重并合并为一行
        DETECTED_SPACES=$(grep -o 'space[= ]*["'\'']\w+["'\'']' "$OPT_STRATEGY_FILE" | sed -e 's/space[= ]*["'\'']//' -e 's/["'\'']//' | sort -u | tr '\n' ' ' | sed 's/ $//')
        if [ -n "$DETECTED_SPACES" ]; then
            echo "---"
            echo "已从策略文件 ${OPT_STRATEGY_NAME}.py 中自动检测到以下参数空间:"
            echo "  ${DETECTED_SPACES}"
        fi
    fi

    # --- 时间周期和范围配置 ---
    print_header "时间和周期配置"
    read_with_default "请输入策略主周期 (5m, 15m, 1h...)" "${TIMEFRAME:-15m}" TIMEFRAME
    read_with_default "请输入用于优化的参数空间 (用空格分隔)" "${DETECTED_SPACES:-buy}" HYPEROPT_SPACES
    read_with_default "请输入参数优化的轮数 (epochs)" "${EPOCHS:-200}" EPOCHS
    
    echo "--- 优化时间范围 ---"
    read_with_default "请输入优化开始日期 (YYYYMMDD)" "${OPT_START_DATE:-20240101}" OPT_START_DATE
    read_with_default "请输入优化结束日期 (YYYYMMDD, 留空表示至今)" "${OPT_END_DATE:-}" OPT_END_DATE
    
    echo "--- 回测时间范围 ---"
    read_with_default "请输入回测开始日期 (YYYYMMDD)" "${BT_START_DATE:-20240501}" BT_START_DATE
    read_with_default "请输入回测结束日期 (YYYYMMDD, 留空表示至今)" "${BT_END_DATE:-}" BT_END_DATE
}

# 提示用户输入配置
get_user_config() {
    # 如果配置文件存在，则加载它并直接进入确认步骤
    if [ -f "$CONFIG_CACHE_FILE" ]; then
        echo "检测到上次的配置文件，将作为默认值加载。"
        source "$CONFIG_CACHE_FILE"
        
        display_and_export_config
        
        read -p "以上配置是否正确? ([Y]/n) " confirm
        confirm=${confirm:-y} # 默认为 'y'
        
        if [[ "$confirm" == "y" ]]; then
            save_user_config
            return 0 # 配置确认无误，直接退出函数
        fi
        echo "缓存配置已被拒绝。请手动输入新配置。"
        echo
    fi

    # 如果没有缓存文件，或用户拒绝了缓存，则进入循环提问模式
    while true; do
        # 加载上次的配置作为默认值（如果有的话），方便修改
        if [ -f "$CONFIG_CACHE_FILE" ]; then
            source "$CONFIG_CACHE_FILE"
        fi
        
        prompt_all_settings
        display_and_export_config

        read -p "以上配置是否正确? (y/n): " final_confirm
        if [[ "$final_confirm" == "y" ]]; then
            save_user_config
            break # 用户确认，跳出循环
        else
            echo "配置已取消，请重新输入。"
            echo
        fi
    done
}

# 显示主菜单
show_main_menu() {
    print_header "主菜单"
    echo "请选择要执行的操作:"
    echo "  1. [优化] 运行参数优化 (耗时较长)"
    echo "  2. [优化] 处理优化结果并生成参数文件"
    echo "  3. [回测] 运行回测与结果分析"
    echo "  4. [分析] 分析回测结果并生成报告"
    echo "  5. [更新] 读取报告并自动更新配置"
    echo "  6. [工具] 从Binance获取Top100交易对"
    echo "  ----------------------------------------------------"
    echo "  A. [工作流] 按顺序执行步骤 1 -> 2 -> 3 -> 4 -> 5"
    echo "  ----------------------------------------------------"
    echo "  q. 退出"
    echo
    read -p "请输入您的选择 [1-6, A, q]: " main_menu_choice
}

# --- 主逻辑 ---

# 1. 获取用户配置
get_user_config

# 2. 检查Python环境
check_and_setup_python_env

# 3. 循环显示菜单
while true; do
    show_main_menu
    case $main_menu_choice in
        1)
            print_header "即将执行: 步骤 1a - 参数优化"
            echo "此步骤将为您的策略优化 'buy', 'sell' 等参数空间。"
            echo "请注意：这是一个计算密集型过程，可能需要很长时间。"
            read -p "按 Enter 键继续，或按 Ctrl+C 取消..."
            # 修正：现在统一调用一个脚本，该脚本内部处理所有优化步骤
            bash "${SCRIPT_DIR}/1_run_optimization.sh" | tee -a "$MAIN_LOG_FILE"
            ;;
        2)
            print_header "即将执行: 步骤 2 - 处理优化结果"
            "$PYTHON_EXEC" "${SCRIPT_DIR}/2_process_hyperopt_results.py" | tee -a "$MAIN_LOG_FILE"
            read -p "按任意键返回主菜单..."
            ;;
        3)
            print_header "即将执行: 3. 运行回测与结果分析"
            bash "${SCRIPT_DIR}/3_run_backtest_and_analysis.sh" | tee -a "$MAIN_LOG_FILE"
            read -p "按任意键返回主菜单..."
            ;;
        4)
            print_header "即将执行: 4. 分析回测结果并生成报告"
            "$PYTHON_EXEC" "${SCRIPT_DIR}/4_analyze_backtest_results.py" | tee -a "$MAIN_LOG_FILE"
            read -p "按任意键返回主菜单..."
            ;;
        5)
            print_header "即将执行: 5. 读取报告并自动更新配置"
            "$PYTHON_EXEC" "${SCRIPT_DIR}/5_update_config_from_report.py" | tee -a "$MAIN_LOG_FILE"
            read -p "按任意键返回主菜单..."
            ;;
        6)
            print_header "即将执行: 6. [工具] 从Binance获取Top100交易对"
            "$PYTHON_EXEC" "${SCRIPT_DIR}/6_update_pairlist_by_volume.py" | tee -a "$MAIN_LOG_FILE"
            read -p "按任意键返回主菜单..."
            ;;
        A|a)
            print_header "即将执行: 完整工作流 (1 -> 2 -> 3 -> 4 -> 5)"
            echo "此操作将按顺序自动执行从参数优化到配置更新的所有步骤。"
            read -p "按 Enter 键继续，或按 Ctrl+C 取消..."
            
            (
                # 设置此执行块遇到任何错误立即退出
                set -e
                
                echo "正在准备优化环境，清理旧的优化结果..."
                # 删除旧的JSON格式的结果文件，为本次运行做准备
                find "${PROJECT_ROOT}/user_data/hyperopt_results/" -type f -name '*.json' -delete
                echo "旧结果清理完毕。"

                # 步骤1: 运行完整的两步优化
                print_header "步骤 1: 运行参数优化 (Buy/Sell & Stoploss 空间)"
                bash "${SCRIPT_DIR}/1_run_optimization.sh"

                # 步骤2: 处理结果
                print_header "步骤 2: 处理Hyperopt结果并生成参数文件"
                "$PYTHON_EXEC" "${SCRIPT_DIR}/2_process_hyperopt_results.py"

                # 步骤3: 运行回测
                print_header "步骤 3: 运行回测与结果分析"
                bash "${SCRIPT_DIR}/3_run_backtest_and_analysis.sh"

                # 步骤4: 分析回测结果
                print_header "步骤 4: 分析回测结果并生成报告"
                "$PYTHON_EXEC" "${SCRIPT_DIR}/4_analyze_backtest_results.py"
                
                # 步骤5: 更新配置
                print_header "步骤 5: 读取报告并自动更新配置"
                "$PYTHON_EXEC" "${SCRIPT_DIR}/5_update_config_from_report.py"

            ) | tee -a "$MAIN_LOG_FILE"

            # 检查子shell的退出状态
            if [ ${PIPESTATUS[0]} -ne 0 ]; then
                echo "工作流因错误而中止。请检查日志 ${MAIN_LOG_FILE} 获取详细信息。"
            else
                echo "完整工作流执行完毕。"
            fi

            read -p "按任意键返回主菜单..."
            ;;
        q)
            echo "感谢使用，再见！"
            exit 0
            ;;
        *)
            echo "无效输入，请重试。"
            sleep 1
            ;;
    esac
done 