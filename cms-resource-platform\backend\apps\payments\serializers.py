"""
支付系统序列化器
"""
from rest_framework import serializers
from django.db import transaction
from .models import PointsPackage, VIPPackage, PaymentOrder, CardCode, Withdrawal


class PointsPackageSerializer(serializers.ModelSerializer):
    """积分套餐序列化器"""
    total_points = serializers.IntegerField(read_only=True)
    final_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = PointsPackage
        fields = [
            'id', 'name', 'description', 'points', 'price', 'bonus_points',
            'discount_rate', 'total_points', 'final_price', 'is_active', 'sort_order'
        ]


class VIPPackageSerializer(serializers.ModelSerializer):
    """VIP套餐序列化器"""
    discount_rate = serializers.DecimalField(max_digits=5, decimal_places=4, read_only=True)
    
    class Meta:
        model = VIPPackage
        fields = [
            'id', 'name', 'description', 'duration_days', 'price', 'original_price',
            'features', 'bonus_points', 'discount_rate', 'is_active', 'sort_order'
        ]


class PaymentOrderSerializer(serializers.ModelSerializer):
    """支付订单序列化器"""
    
    class Meta:
        model = PaymentOrder
        fields = [
            'id', 'order_no', 'order_type', 'product_name', 'product_description',
            'amount', 'discount_amount', 'final_amount', 'payment_method',
            'status', 'created_at', 'paid_at', 'completed_at'
        ]
        read_only_fields = ['order_no', 'created_at']


class OrderCreateSerializer(serializers.Serializer):
    """订单创建序列化器"""
    package_id = serializers.IntegerField()
    package_type = serializers.ChoiceField(choices=['points', 'vip'])
    payment_method = serializers.ChoiceField(choices=['alipay', 'wechat', 'balance'])
    
    def validate(self, attrs):
        package_id = attrs['package_id']
        package_type = attrs['package_type']
        
        # 验证套餐是否存在
        if package_type == 'points':
            if not PointsPackage.objects.filter(id=package_id, is_active=True).exists():
                raise serializers.ValidationError("积分套餐不存在或已下架")
        elif package_type == 'vip':
            if not VIPPackage.objects.filter(id=package_id, is_active=True).exists():
                raise serializers.ValidationError("VIP套餐不存在或已下架")
        
        return attrs


class CardCodeSerializer(serializers.ModelSerializer):
    """卡密序列化器"""
    
    class Meta:
        model = CardCode
        fields = [
            'id', 'code', 'card_type', 'points_value', 'vip_days',
            'discount_rate', 'is_used', 'used_at', 'expires_at'
        ]
        read_only_fields = ['code', 'is_used', 'used_at']


class CardRedeemSerializer(serializers.Serializer):
    """卡密兑换序列化器"""
    code = serializers.CharField(max_length=32)
    
    def validate_code(self, value):
        try:
            card = CardCode.objects.get(code=value.upper())
            if not card.is_valid():
                raise serializers.ValidationError("卡密无效或已过期")
            return value.upper()
        except CardCode.DoesNotExist:
            raise serializers.ValidationError("卡密不存在")


class WithdrawalSerializer(serializers.ModelSerializer):
    """提现申请序列化器"""
    
    class Meta:
        model = Withdrawal
        fields = [
            'id', 'amount', 'fee', 'actual_amount', 'withdraw_type',
            'account_info', 'status', 'created_at', 'processed_at', 'reject_reason'
        ]
        read_only_fields = ['fee', 'actual_amount', 'status', 'processed_at', 'reject_reason']
    
    def validate_amount(self, value):
        if value < 10:
            raise serializers.ValidationError("最小提现金额为10元")
        if value > 10000:
            raise serializers.ValidationError("单次提现金额不能超过10000元")
        return value
