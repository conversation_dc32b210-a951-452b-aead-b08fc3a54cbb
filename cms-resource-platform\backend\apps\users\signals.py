"""
用户系统信号处理器
"""
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import User, UserProfile, PointsHistory


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """创建用户时自动创建用户资料"""
    if created:
        UserProfile.objects.get_or_create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """保存用户时同时保存用户资料"""
    if hasattr(instance, 'profile'):
        instance.profile.save()


@receiver(pre_save, sender=User)
def update_vip_status(sender, instance, **kwargs):
    """更新VIP状态"""
    if instance.pk:
        # 检查VIP是否过期
        if instance.vip_expire_date and instance.vip_expire_date < timezone.now():
            instance.is_vip = False
