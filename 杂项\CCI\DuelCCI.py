# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these imports ---
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Dict, Optional, Union, Tuple, List
from freqtrade.optimize.space import Categorical, Dimension, Integer, SKDecimal
from functools import reduce
import json
from pathlib import Path

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


# ==========================================
# I Backtested The Best CCI Indicator Strategy (Full Tutorial)
# YouTube Link: https://youtu.be/MaDNoHplj2U
# ==========================================


# ================================
# Download Historical Data
# ================================

"""
freqtrade download-data \
    -c user_data/config_binance_futures.json \
    --timerange 20230101- \
    -t 1m 5m 15m 30m 1h 2h 4h 1d
"""

# ================================
# Hyperopt Optimization
# ================================
"""
freqtrade hyperopt \
    --strategy DuelCCI \
    --config user_data/config_binance_futures.json \
    --timeframe 1h \
    --timerange 20231201-20240801 \
    --hyperopt-loss MultiMetricHyperOptLoss \
    --spaces buy\
    -e 50 \
    --j -2 \
    --random-state 9319 \
    --min-trades 30 \
    -p AVAX/USDT:USDT \
    --max-open-trades 1
"""

# ================================
# Backtesting
# ================================

"""
freqtrade backtesting \
    --strategy DuelCCI \
    --timeframe 1h \
    --timerange 20231201-20241201 \
    --breakdown month \
    -c user_data/config_binance_futures.json \
    --max-open-trades 1 \
    -p AVAX/USDT:USDT \
    --cache none \
    --timeframe-detail 5m
    
"""

# ================================
# Start FreqUI Web Interface
# ================================

"""
freqtrade webserver \
    --config user_data/config_binance_futures.json
"""


class DuelCCI(IStrategy):
            
    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = "1h"

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi".
    minimal_roi = {}

    # Dictionary defining the exit points for take profit and stop loss levels.
    exit_loss_profit = {}
    
    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.25

    # Trailing stoploss
    trailing_stop = False
    # trailing_only_offset_is_reached = False
    # trailing_stop_positive = 0.01
    # trailing_stop_positive_offset = 0.0  # Disabled / not configured
    
    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    risk_ratio = CategoricalParameter([1, 1.5, 2, 2.5, 3], default=2, space="buy")    
    atr_period = CategoricalParameter([5, 10, 15], default=10, space="buy")
    atr_mult = CategoricalParameter([2, 2.5, 3, 3.5, 4], default=2.5, space="buy")

    long_cci_len = CategoricalParameter([80, 90, 100, 110], default=80, space="buy")
    short_cci_len = CategoricalParameter([5, 10, 15, 20], default=5, space="buy")
    
    long_cci_thresh = CategoricalParameter([80, 100, 120], default=100, space="buy")  
    short_cci_thresh = CategoricalParameter([-80, -100, -120], default=-100, space="buy")  

    leverage_level = IntParameter(1, 5, default=1, space='buy', optimize=False, load=False)

    @property
    def plot_config(self):

        plot_config = {
            "main_plot": {
            },
            "subplots": {
                "long_cci": {
                    f"long_cci_{self.long_cci_len.value}": {
                        "color": "#2962ff",
                        "type": "line"
                    },
                    f"long_cci_thresh": {
                        "color": "#FFFFFF",
                        "type": "scatter"
                    }
                },
                "short_cci": {
                    f"short_cci_{self.short_cci_len.value}": {
                        "color": "#ff9800",
                        "type": "line"
                    },
                    f"short_cci_thresh": {
                        "color": "#FFFFFF",
                        "type": "scatter"
                    }
                }
            }
        }
        
        return plot_config
    
    
    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pair/interval combinations are non-tradeable, unless they are part
        of the whitelist as well.
        For more information, please consult the documentation
        :return: List of tuples in the format (pair, interval)
            Sample: return [("ETH/USDT", "5m"),
                            ("BTC/USDT", "15m"),
                            ]
        """
        
        # get access to all pairs available in whitelist.
        # pairs = self.dp.current_whitelist()

        # # Assign tf to each pair so they can be downloaded and cached for strategy.
        # informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        
        return []
    
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        for val in self.short_cci_len.range:
            dataframe[f'short_cci_{val}'] = ta.CCI(dataframe, timeperiod=val)
            
        for val in self.long_cci_len.range:
            dataframe[f'long_cci_{val}'] = ta.CCI(dataframe, timeperiod=val)

        for val in self.atr_period.range:
            dataframe[f"atr_{val}"] = ta.ATR(dataframe, timeperiod=val)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:        

        dataframe.loc[
            (
                (dataframe[f'long_cci_{self.long_cci_len.value}'].rolling(3).max() > self.long_cci_thresh.value) &
                (qtpylib.crossed_above(dataframe[f'short_cci_{self.short_cci_len.value}'], self.short_cci_thresh.value)) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1
    
        return dataframe
    

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        dataframe.loc[:, 'exit_long'] = 0
        dataframe.loc[:, 'exit_short'] = 0

        return dataframe
    
    
    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        
        # Check if exit conditions for the pair have been defined
        if pair not in self.exit_loss_profit:
            
            # Initialize exit conditions (take profit and stop loss) for the pair if not already set
            # If signal candle data is not available or incorrect, use default settings with fixed 5% take profit and stop loss
            self.exit_loss_profit[pair] = {
                'take_profit': trade.open_rate * 1.05,
                'stop_loss': trade.open_rate * 0.95
            }
            
            # Retrieve the analyzed dataframe for the pair and timeframe, then get the historical data prior to the trade's open date.
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            trade_date = timeframe_to_prev_date(self.timeframe, trade.open_date_utc)
            trade_signal = dataframe.loc[dataframe['date'] < trade_date]
            
            if not trade_signal.empty:
                
                # Extract the last candle's data
                signal_candle = trade_signal.iloc[-1].squeeze()
                
                # Set take profit and stop loss levels based on ATR, risk ratio, and the signal candle's close price.
                self.exit_loss_profit[pair]['take_profit'] = signal_candle['close'] + (self.atr_mult.value * signal_candle[f"atr_{self.atr_period.value}"] * self.risk_ratio.value)
                self.exit_loss_profit[pair]['stop_loss'] = signal_candle['close'] - (self.atr_mult.value * signal_candle[f"atr_{self.atr_period.value}"])
            
        # Get the most recent candle data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # Check if the current price has reached the take profit level
        if current_candle['close'] >= self.exit_loss_profit[pair]['take_profit']:
            return "take_profit_achieved"

        # Check if the current price has fallen to the stop loss level
        if current_candle['close'] <= self.exit_loss_profit[pair]['stop_loss']:
            return "stop_loss_achieved"
    
    def confirm_trade_exit(
        self,
        pair: str,
        trade: Trade,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        exit_reason: str,
        current_time: datetime,
        **kwargs,
    ) -> bool:
        
        # Confirms the trade exit by removing the exit loss/profit levels for the given pair
        if pair in self.exit_loss_profit:
            del self.exit_loss_profit[pair]
        return True
        
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:

        return self.leverage_level.value