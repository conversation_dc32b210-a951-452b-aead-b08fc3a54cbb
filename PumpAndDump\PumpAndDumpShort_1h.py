# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, stoploss_from_open
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime
from freqtrade.persistence import Trade
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy

# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging
import pytz

log = logging.getLogger(__name__)


class PumpAndDumpShort1h(IStrategy):
    """
    ## Pump and Dump Shorting Strategy (1h-Only Version) V3.0

    **作者:** Gemini 2.5 Pro & User
    **版本:** 3.0
    **核心理念:**
    - V3.0 (Profit-locking TSL): 重大重构止损逻辑。追踪止损(TSL)现在只在能够锁定确定利润（即止损价低于开仓价）时才会激活。这修复了TSL在盈利刚超过阈值后，因微小回调导致亏损离场（被“洗出场”）的问题，完美实现了“硬止损为底线，追踪止损为利润保护”的设计意图。
    - V2.9 (Cooldown Fix): 统一并优化了冷却期逻辑，确保其在所有环境中都能稳健运行。
    - V2.8 (Compatibility Fix): 兼容旧版 Freqtrade，通过手动计算最大利润来修复 `trade.max_profit` 不存在的属性错误。
    - V2.7 (Pytz Fix): 改用 `pytz` 库处理时区，以修复在旧版 `pandas` 环境中出现的 `tz` 参数错误，增强兼容性。
    - V2.6 (Timezone Fix): 修复了 `custom_stoploss` 中因时区问题 (tz-aware vs tz-naive) 导致的 `TypeError`，确保在所有环境下 `trade.open_date` 和 `dataframe['date']` 的兼容性。
    - V2.5 (Final Stoploss Fix): 增加了 `or current_rate` 后备，彻底修复了在边界条件下可能出现的 `void()` 错误，使止损逻辑完全稳健。
    - V2.4 (Stoploss Fix): 修复了追踪止损在利润回落后会失效的根本性漏洞。现在使用 `trade.max_profit` 来确保追踪止损一旦激活，便永久生效，绝不放宽。
    - V2.3 (Logic Overhaul): 对入场逻辑进行重大重构。
        1. **"高潮K线"定义更新:** 趋势条件从"连续上涨"升级为"连续量价齐升"。同时放宽了RSI、形态、成交量和振幅的阈值，以捕捉更多潜在信号。
        2. **"确认入场"逻辑升级:** 引入"停顿K线"概念。现在入场有两个路径：a) 高潮后直接出现下跌确认K线；b) 高潮后出现一根量价萎缩的"停顿K线"，再之后出现下跌确认K线。这使得策略能更好地处理复杂的顶部形态。
    - V2.2 (Parameter Rollback): 根据回测结果，恢复了V1.7版本左右的高盈利性参数设置，收紧了入场条件，旨在重现高胜率、高回报率的表现。同时，保留了V2.0版本引入的杠杆感知型止损等所有关键风控修复。
    - V2.0 (Leverage-Aware Stoploss): 终极风控升级。引入杠杆感知型止损，后备止损位会根据当前杠杆动态计算，确保永远比强制平仓线更严格，从根本上杜绝强平的发生。策略已完全准备好进行优化与实盘。
    - V1.9 (Robustness): 修复了 `custom_stoploss` 中一个导致强制平仓的最终漏洞。现在所有备用止损分支都返回绝对价格，确保在任何边界条件下，止损都能被正确设置。策略已为超参数优化做好准备。
    - V1.8: 为了解决样本量过少可能导致的"幸存者偏差"问题，进一步放宽了入场条件，将默认的"高潮信号"满足数从4个下调至3个，并将冷却时间从12小时缩短至6小时，以期在回测和优化中产生更具统计代表性的交易数量。
    - V1.7 (Final): 修复了 `custom_stoploss` 在边界条件下会失效从而导致强制平仓的致命漏洞。现在策略在任何情况下都有一个明确的止损价，更加健壮。
    - V1.6: 1. 增加了一个后备止损，防止极端情况下的巨大亏损。 2. 放宽了入场条件，不再要求所有5个高潮信号都满足，而是满足指定数量即可，以提高交易频率。
    - V1.5: 解决了 `custom_stoploss` 和 `trailing_stop` 的兼容性问题。将追踪止损逻辑手动实现在 `custom_stoploss` 中，以防止巨大亏损的发生。
    - V1.4: 修复了在边界条件下 `custom_stoploss` 可能出现的 `IndexError`，并优化了止损的回看逻辑。
    - V1.3: 根本性重构。引入统一的"高潮K线"信号，并增加图表绘图功能。
    - 本策略完全运行在 1h 时间框架上。
    - **核心改动: 动态止损位设置为入场前10根K线的最高点。**

    **时间框架:** 1小时 (1h)

    **入场条件 (做空):**
    1.  **力竭信号 (前一根K线):**
        - 必须是一根"高潮K线"，即同时满足：趋势连续上涨、RSI过热、形态为射击之星、成交量和振幅均远超近期平均值。
    2.  **入场确认 (当前K线):**
        - 必须是一根下跌的"确认K线"。

    **出场条件 (平仓):**
    1.  **统一动态止损 (Unified Custom Stop Loss):**
        - 初始止损位被设置为入场那一刻，往前数N根K线的最高点。
        - 当利润超过预设阈值后，自动切换为追踪止损来保护利润。
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '1h'
    can_short = True
    process_only_new_candles = True

    # --- 启动和回测所需的数据量 ---
    startup_candle_count: int = 40

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }

    # Optional plot configuration
    plot_config = {
        'main_plot': {
            # V2.3: 将信号移至子图，以修复主图的缩放问题
            # V2.9: 增加初始止损线
            'initial_stop_price': {
                'color': '#FF5733',  # 使用醒目的颜色
                'linestyle': '--',
                'name': 'Initial Stop'
            }
        },
        'subplots': {
            "Signals": {
                'climax_candle': {'color': '#FFDD32', 'type': 'bar', 'name': 'Climax Candle'},
                'pause_candle': {'color': '#800080', 'type': 'bar', 'name': 'Pause Candle'}
            },
            "Climax Conditions Met": {
                'climax_conditions_met': {'color': 'purple', 'type': 'bar'},
            },
            "Climax Individual Conditions": {
                'trend_condition': {'color': 'green', 'type': 'bar'},
                'rsi_condition': {'color': 'orange', 'type': 'bar'},
                'wick_condition': {'color': 'cyan', 'type': 'bar'},
                'volume_condition': {'color': 'red', 'type': 'bar'},
                'range_condition': {'color': 'blue', 'type': 'bar'},
            },
            "RSI": {
                'pump_rsi': {'color': 'orange'},
            }
        }
    }

    # --- 止损和止盈设置 ---
    # V2.0: stoploss现在只作为1倍杠杆时的后备，或在自定义止损完全失效时的最终防线。
    # 主要逻辑已移至leverage-aware的custom_stoploss中。
    stoploss = -0.10 

    # 启用自定义止损
    use_custom_stoploss = True

    # 追踪止盈设置 (V1.5: 已禁用，逻辑移至 custom_stoploss)
    trailing_stop = False
    # trailing_stop_positive = 0.008
    # trailing_stop_positive_offset = 0.015
    # trailing_only_offset_is_reached = True

    # --- Hyperopt 参数 (可优化参数) ---
    # V2.3: 更新小时线级别暴涨条件参数
    consecutive_green_candles = IntParameter(2, 4, default=2, space="buy", optimize=True)
    pump_rsi_threshold = IntParameter(60, 80, default=65, space="buy", optimize=True)
    
    # V2.3: 更新小时线级别力竭/触发参数
    upper_wick_body_ratio = DecimalParameter(1.2, 1.8, default=1.3, decimals=1, space="buy", optimize=True)
    climax_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    
    # V2.2: 恢复更严格的入场条件
    min_climax_conditions = IntParameter(3, 5, default=4, space="buy", optimize=True)
    
    # 风险管理参数
    stoploss_lookback = IntParameter(5, 20, default=10, space="sell", optimize=True)
    
    # 手动追踪止损参数 (V1.5)
    tsl_positive = DecimalParameter(0.005, 0.02, default=0.008, decimals=3, space="sell", optimize=True)
    tsl_offset = DecimalParameter(0.01, 0.03, default=0.015, decimals=3, space="sell", optimize=True)

    # V2.0: 杠杆感知型止损的安全边际
    leverage_stop_margin = DecimalParameter(0.80, 0.95, default=0.9, decimals=2, space="sell", optimize=True)

    # 防止重复开仓的冷却时间（小时）
    cooldown_period = IntParameter(6, 48, default=12, space="buy", optimize=True)
    
    # 杠杆设置
    leverage_optimize = CategoricalParameter([1.0, 2.0, 3.0, 5.0, 10.0], default=1.0, space="buy", optimize=True, load=True)

    def __init__(self, config: dict) -> None:
        super().__init__(config)
        # V2.9: last_trade_time 现在存储 datetime 对象以实现更可靠的冷却逻辑
        self.last_trade_time: dict[str, datetime] = {}

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算所有指标 (V2.3)
        """
        # --- 趋势背景 (V2.3: 量价齐升) ---
        dataframe['is_green_candle'] = (dataframe['close'] > dataframe['open']).astype(int)
        dataframe['volume_is_rising'] = dataframe['volume'] > dataframe['volume'].shift(1)
        dataframe['green_with_rising_vol'] = (dataframe['is_green_candle'] == 1) & (dataframe['volume_is_rising'] == 1)
        dataframe['consecutive_green_rising_vol'] = dataframe['green_with_rising_vol'].rolling(
            window=self.consecutive_green_candles.value).sum()
        
        # --- 过热指标 ---
        dataframe['pump_rsi'] = ta.RSI(dataframe, timeperiod=14)

        # --- 力竭指标 ---
        # 形态力竭
        body = abs(dataframe['close'] - dataframe['open'])
        upper_wick = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        dataframe['shooting_star'] = (
            (upper_wick > body * self.upper_wick_body_ratio.value) &
            (body > 0.000001)
        ).astype('int')
        
        # 振幅力竭 (Range Spike)
        dataframe['range_1h'] = dataframe['high'] - dataframe['low']
        range_sma = ta.SMA(dataframe['range_1h'], timeperiod=self.climax_sma_period.value)
        dataframe['range_spike'] = (dataframe['range_1h'] > range_sma * self.range_spike_multiplier.value).astype('int')

        # 成交量力竭 (Volume Spike)
        volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.climax_sma_period.value)
        dataframe['volume_spike'] = (dataframe['volume'] > volume_sma * self.volume_spike_multiplier.value).astype('int')
        
        # --- (V2.3) 分解并统计所有高潮条件 ---
        dataframe['trend_condition'] = (dataframe['consecutive_green_rising_vol'] >= self.consecutive_green_candles.value).astype('int')
        dataframe['rsi_condition'] = (dataframe['pump_rsi'] >= self.pump_rsi_threshold.value).astype('int')
        dataframe['wick_condition'] = dataframe['shooting_star']
        dataframe['volume_condition'] = dataframe['volume_spike']
        dataframe['range_condition'] = dataframe['range_spike']

        dataframe['climax_conditions_met'] = (
            dataframe['trend_condition'] +
            dataframe['rsi_condition'] +
            dataframe['wick_condition'] +
            dataframe['volume_condition'] +
            dataframe['range_condition']
        )
        
        # --- 统一的 "高潮K线" 信号 ---
        dataframe['climax_candle'] = (dataframe['climax_conditions_met'] >= self.min_climax_conditions.value).astype('int')

        # --- 入场确认 (V2.3: 多路径确认) ---
        dataframe['is_red_candle'] = (dataframe['close'] < dataframe['open']).astype('int')
        dataframe['is_shrinking_volume'] = dataframe['volume'] < dataframe['volume'].shift(1)
        dataframe['is_lower_high'] = dataframe['high'] <= dataframe['high'].shift(1)
        
        dataframe['pause_candle'] = (
            (dataframe['is_green_candle'] == 1) &
            (dataframe['is_shrinking_volume'] == 1) &
            (dataframe['is_lower_high'] == 1)
        ).astype('int')

        # --- (V2.9 New) 增加初始止损位绘图 ---
        # 这个值代表了如果在当前K线之后入场，初始硬止损会被设置在哪个价位
        dataframe['initial_stop_price'] = dataframe['high'].rolling(window=self.stoploss_lookback.value).max()

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        定义做空入场条件 (V2.9)
        """
        pair = metadata['pair']
        cooldown_seconds = self.cooldown_period.value * 3600

        # --- V2.9: 稳健的冷却期逻辑 ---
        last_trade_time = self.last_trade_time.get(pair)
        if last_trade_time is not None and \
           (dataframe['date'].max() - last_trade_time).total_seconds() < cooldown_seconds:
            dataframe['enter_short'] = 0
            return dataframe
        
        # 路径A: 高潮K线 + 下跌K线
        trigger_1 = (
            (dataframe['climax_candle'].shift(1) == 1) &
            (dataframe['is_red_candle'] == 1)
        )
        
        # 路径B: 高潮K线 + 停顿K线 + 下跌K线
        trigger_2 = (
            (dataframe['climax_candle'].shift(2) == 1) &
            (dataframe['pause_candle'].shift(1) == 1) &
            (dataframe['is_red_candle'] == 1)
        )
        
        dataframe['enter_short'] = (trigger_1 | trigger_2).astype('int')
        
        # 如果有新的入场信号，则更新最后交易时间
        if dataframe['enter_short'].any():
            self.last_trade_time[pair] = dataframe.loc[dataframe['enter_short'] == 1, 'date'].max()
            dataframe.loc[dataframe['enter_short'] == 1, 'enter_tag'] = 'pump_dump_1h_v3.0'

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        做空离场信号 (此处为空，因为我们使用动态止损和追踪止盈)
        """
        dataframe.loc[(), 'exit_short'] = 0
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义动态止损 V2.8: 修复时区问题，并保留 V2.5 的完整止损逻辑。
        """
        if not trade.is_short:
            return -1

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)

        # --- Section 0: V2.0 - 杠杆感知型后备止损价计算 ---
        # 默认的后备止损价，将在所有边界条件下使用
        leverage_fallback_stop_price = self.calculate_leverage_fallback_stop(trade)

        # --- Section 1: 计算初始硬止损 (Hard Stop) ---
        if dataframe.empty:
            return leverage_fallback_stop_price

        # V2.7: 使用 pytz 修复时区比较错误，兼容旧版 pandas
        trade_open_date_utc = pytz.utc.localize(trade.open_date)
        candles_before_trade = dataframe[dataframe['date'] < trade_open_date_utc]
        if candles_before_trade.empty:
            return leverage_fallback_stop_price

        signal_candle_index = candles_before_trade.index[-1]
        
        lookback_period = self.stoploss_lookback.value
        start_index = max(0, signal_candle_index - lookback_period + 1)
        end_index = signal_candle_index + 1

        if start_index >= end_index:
            return leverage_fallback_stop_price
            
        lookback_df = dataframe.iloc[start_index:end_index]
        if lookback_df.empty:
            return leverage_fallback_stop_price
            
        hard_stop_price = lookback_df['high'].max()

        # --- Section 2: 手动追踪止损 (Manual Trailing Stop) ---
        # V2.8: 兼容旧版 Freqtrade，手动计算 max_profit
        max_profit = 0.0
        if trade.min_rate is not None:
            max_profit = (trade.open_rate - trade.min_rate) / trade.open_rate

        # V2.4: 修复了追踪止损在利润回落后会失效的根本性漏洞。
        # 使用 trade.max_profit 来确保追踪止损一旦激活，就永久生效。
        if max_profit > self.tsl_positive.value:
            # 计算追踪止损价格
            lowest_rate = trade.min_rate or current_rate
            trailing_stop_price = lowest_rate * (1 + self.tsl_offset.value)

            # V3.0: 只有在追踪止损能够锁定利润时（止损价低于开仓价），才考虑收紧止损
            if trailing_stop_price < trade.open_rate:
                # 返回初始硬止损和追踪止损中，更严格（价格更低）的那个
                return min(hard_stop_price, trailing_stop_price)

        # 如果历史最高利润从未达到追踪止损的触发条件，则返回固定的硬止损
        return hard_stop_price
    
    def calculate_leverage_fallback_stop(self, trade: 'Trade') -> float:
        """
        V2.0: 计算基于杠杆的后备止损绝对价格。
        """
        leverage_val = self.leverage_optimize.value
        if leverage_val > 1:
            # 对于杠杆交易，基于强平线和安全边际计算止损
            # 强平线约等于 1 / leverage
            stop_pct = (1 / leverage_val) * self.leverage_stop_margin.value
        else:
            # 对于 1x 交易, 使用全局的 stoploss 参数
            stop_pct = abs(self.stoploss)
        
        stoploss_price = trade.open_rate * (1 + stop_pct)
        return stoploss_price

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:
        return self.leverage_optimize.value


class PumpAndDumpShort1hHyperopt(PumpAndDumpShort1h):
    pass 