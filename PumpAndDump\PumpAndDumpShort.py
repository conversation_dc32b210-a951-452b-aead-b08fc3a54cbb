# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, merge_informative_pair
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime, timedelta, timezone
from freqtrade.persistence import Trade
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy

# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging

log = logging.getLogger(__name__)


class PumpAndDumpShort(IStrategy):
    """
    ## Pump and Dump Shorting Strategy (做空暴涨后的砸盘) V29 (Pattern-Specific Params)

    **作者:** Gemini 2.5 Pro
    **版本:** 29.0
    **核心理念:**
    - V29 (Pattern-Specific Params): 精细化改造。为不同的K线反转形态（射击之星、黄昏之星）设置各自独立的、可优化的过滤参数（如RSI阈值、成交量乘数）。旨在解决“一刀切”参数无法同时适应多种形态的问题，让每种信号都在最优条件下触发。
    - V28 (Evening Star): 根据V27的调试结果，移除了与“射击之星”信号高度重叠的“看跌吞没”和“乌云盖顶”。引入了结构差异更大的三K线反转形态——“黄昏之星(Evening Star)”，旨在捕捉全新的、非重叠的交易机会。
    - V27 (Pattern Debug): 这是一个调试版本，旨在解决V26中交易数量未增加的谜题。通过为每种K线反转形态（射击之星、看跌吞没、乌云盖顶）设置独立的入场标签，我们可以在回测报告中清晰地看到各自贡献的交易数量，以判断信号重叠的程度。
    
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '1h'
    can_short = True
    process_only_new_candles = True

    # --- 启动和回测所需的数据量 ---
    startup_candle_count: int = 40

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True  # V4 核心改动: 在交易所设置止损单，防止滑点
    }

    # --- 止损和止盈设置 ---
    # V14 核心改动: 移除固定止损，改用基于ATR的动态止损 (Chandelier Exit)
    use_custom_stoploss = True
    stoploss = -0.99  # V14 修复: freqtrade启动验证需要一个占位符

    # 追踪止损设置 (V25: 恢复至被证明最优的紧凑设置)
    trailing_stop = True
    trailing_stop_positive = 0.04
    trailing_stop_positive_offset = 0.08
    trailing_only_offset_is_reached = True

    # 自定义离场信号
    use_exit_signal = False # V14.3 最终优化: 移除导致亏损的RSI离场信号
    use_custom_exit = False # V18: 彻底禁用 custom_exit

    # V14 核心改动: 禁用ROI，让利润奔跑，依赖追踪止损和离场信号
    minimal_roi = {}

    # --- Hyperopt 参数 (可优化参数) ---
    # 日线级别参数
    consecutive_green_days = IntParameter(1, 5, default=2, space="buy", optimize=True)
    range_expansion_days = IntParameter(1, 3, default=1, space="buy", optimize=True)
    volume_expansion_days = IntParameter(1, 3, default=1, space="buy", optimize=True)
    daily_rsi_threshold = IntParameter(55, 75, default=60, space="buy", optimize=True) # V8: 放宽默认值
    daily_signal_stickiness_days = IntParameter(1, 7, default=5, space="buy", optimize=True) # V5: 增加范围和默认值
    
    # --- V29: 为不同形态设置专属参数 ---
    # 射击之星 (Shooting Star) 的参数
    ss_upper_wick_body_ratio = DecimalParameter(1.0, 4.0, default=1.5, decimals=1, space="buy", optimize=True, real_name='upper_wick_body_ratio')
    ss_hourly_rsi_threshold = IntParameter(55, 75, default=65, space="buy", optimize=True)
    ss_volume_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    ss_volume_spike_multiplier = DecimalParameter(1.5, 4.0, default=2.5, decimals=1, space="buy", optimize=True)
    
    # 黄昏之星 (Evening Star) 的参数
    es_hourly_rsi_threshold = IntParameter(50, 70, default=60, space="buy", optimize=True)
    es_volume_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    es_volume_spike_multiplier = DecimalParameter(1.2, 3.5, default=2.0, decimals=1, space="buy", optimize=True)
    
    # V14 新增: 钱德利尔止损 (ATR-based)
    atr_period = IntParameter(5, 25, default=14, space="sell", optimize=True)
    stoploss_atr_multiplier = DecimalParameter(2.0, 5.0, default=3.7, decimals=1, space="sell", optimize=True)
    
    # V14.3 最终优化: 移除 exit_rsi_threshold
    # exit_rsi_threshold = IntParameter(45, 60, default=55, space="sell", optimize=True) # V14.2: 提高默认值

    # V6 改进: 流动性过滤器参数 (不可优化)
    # min_daily_volume_threshold = IntParameter(1000000, 20000000, default=5000000, space="buy", optimize=False)
    # liquidity_sma_days = IntParameter(14, 60, default=30, space="buy", optimize=False)

    # 风险管理参数 (V17: 移除带_param的重复定义，统一由hyperopt_space处理)
    # trailing_stop_positive_param = DecimalParameter(0.02, 0.10, default=0.05, decimals=3, space="sell", optimize=True)
    # trailing_stop_positive_offset_param = DecimalParameter(0.08, 0.20, default=0.15, decimals=2, space="sell", optimize=True)
    
    # 自定义出场参数 (V18: 已移除)
    # lookback_days = IntParameter(10, 45, default=20, space="sell", optimize=True)
    # exit_time_days = IntParameter(2, 10, default=3, space="sell", optimize=True)
    # loss_exit_hours = IntParameter(6, 48, default=48, space="sell", optimize=True) # 新增：亏损持仓时间止损

    # 防止重复开仓的冷却时间（小时）
    cooldown_period = IntParameter(6, 48, default=6, space="buy", optimize=True)

    # 杠杆设置
    leverage_value = 1.0


    def __init__(self, config: dict) -> None:
        super().__init__(config)
        # 从配置中读取杠杆信息 (如果存在)
        self.leverage_value = float(self.config.get('custom_info', {}).get('leverage', 1.0))
        
        # V17 修正: 移除复杂的追踪止损参数加载逻辑。
        # Freqtrade 会自动处理：
        # - 回测时，使用类属性 (e.g., self.trailing_stop_positive)
        # - Hyperopt时，使用 hyperopt_space 中定义的同名参数覆盖类属性
        
        # 用于跟踪上次交易时间的字典
        self.last_trade_time = {}
        # V13: 用于跟踪日线观察列表状态的字典
        self.daily_watchlist_status = {}


    def informative_pairs(self):
        """
        定义需要获取的额外数据源 (日线数据)
        """
        pairs = self.dp.current_whitelist()
        # 为白名单中的所有交易对加载日线数据
        informative_pairs = [(pair, '1d') for pair in pairs]
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算主要指标 (V11: 回归数据合并模式)
        """
        # --- 获取并聚合日线数据 ---
        informative_df = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe='1d')

        if not informative_df.empty:
            # --- 在日线数据上计算指标 ---
            informative_df['is_green_day'] = (informative_df['close'] > informative_df['open']).astype(int)
            informative_df['consecutive_green_days'] = informative_df['is_green_day'].rolling(window=self.consecutive_green_days.value).sum()
            informative_df['range_1d'] = informative_df['high'] - informative_df['low']
            informative_df['range_expansion'] = (informative_df['range_1d'] > informative_df['range_1d'].shift(1)).rolling(window=self.range_expansion_days.value).sum()
            informative_df['volume_expansion'] = (informative_df['volume'] > informative_df['volume'].shift(1)).rolling(window=self.volume_expansion_days.value).sum()
            informative_df['daily_rsi'] = ta.RSI(informative_df, timeperiod=14)

            # 构建日线信号
            informative_df['daily_pump_signal_raw'] = (
                (informative_df['consecutive_green_days'] >= self.consecutive_green_days.value) &
                (informative_df['range_expansion'] >= self.range_expansion_days.value) &
                (informative_df['volume_expansion'] >= self.volume_expansion_days.value) &
                (informative_df['daily_rsi'] >= self.daily_rsi_threshold.value)
            )
            
            # 应用信号粘滞性
            informative_df['daily_pump_signal'] = informative_df['daily_pump_signal_raw'].rolling(
                window=self.daily_signal_stickiness_days.value, min_periods=1
            ).max().fillna(0).astype(bool)
            
            # --- 将日线指标安全地合并到小时图 ---
            dataframe = merge_informative_pair(dataframe, informative_df, self.timeframe, '1d', ffill=True)

        # 如果合并失败或日线数据为空，确保列存在
        if 'daily_pump_signal_1d' not in dataframe.columns:
            dataframe['daily_pump_signal_1d'] = False

        # --- 小时级别 (1h) 的入场触发信号 ---
        dataframe['hourly_rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # --- V29: 为不同形态分别计算指标 ---
        # 1. 射击之星 (Shooting Star)
        body = abs(dataframe['close'] - dataframe['open'])
        upper_wick = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        dataframe['shooting_star'] = (
            (upper_wick > body * self.ss_upper_wick_body_ratio.value) &
            (body > 0.000001)
        )
        # 射击之星的成交量激增判断
        ss_volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.ss_volume_sma_period.value)
        dataframe['ss_volume_spike'] = dataframe['volume'] > ss_volume_sma * self.ss_volume_spike_multiplier.value

        # 2. 黄昏之星 (Evening Star)
        dataframe['evening_star'] = ta.CDLEVENINGSTAR(dataframe) == -100
        # 黄昏之星的成交量激增判断
        es_volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.es_volume_sma_period.value)
        dataframe['es_volume_spike'] = dataframe['volume'] > es_volume_sma * self.es_volume_spike_multiplier.value

        dataframe['confirmation_candle'] = dataframe['close'] < dataframe['open']
        
        # V14 新增: 计算钱德利尔止损所需指标
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        # N周期内的最高价，用于计算钱德利尔止损位
        dataframe['high_max_rolling'] = dataframe['high'].rolling(window=self.atr_period.value).max()


        # 添加时间戳列，用于冷却期检查
        if 'date' in dataframe.columns:
            dataframe['timestamp'] = pd.to_datetime(dataframe['date']).astype(np.int64) // 10**9
        else:
            dataframe['timestamp'] = pd.to_datetime(dataframe.index).astype(np.int64) // 10**9

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        定义做空入场条件 (V13: 增加日志)
        """
        pair = metadata['pair']
        
        # V13: 增加日线信号日志
        if not dataframe.empty and 'daily_pump_signal_1d' in dataframe.columns:
            is_on_watchlist = dataframe['daily_pump_signal_1d'].iloc[-1]
            was_on_watchlist = self.daily_watchlist_status.get(pair, False)

            if is_on_watchlist and not was_on_watchlist:
                log.info(f"✅ '{pair}' is now on the watchlist (daily signal is active).")
            elif not is_on_watchlist and was_on_watchlist:
                log.info(f"❌ '{pair}' removed from watchlist (daily signal is now inactive).")
            
            # 更新状态
            self.daily_watchlist_status[pair] = is_on_watchlist

        cooldown_seconds = self.cooldown_period.value * 3600
        
        # --- V29: 为不同形态分别构建入场逻辑 ---
        
        # 1. 射击之星的入场条件
        ss_entry = (
            dataframe['daily_pump_signal_1d'] &
            (dataframe['hourly_rsi'] >= self.ss_hourly_rsi_threshold.value) &
            dataframe['shooting_star'].shift(1, fill_value=False) &
            dataframe['ss_volume_spike'].shift(1, fill_value=False) &
            dataframe['confirmation_candle']
        )
        
        # 2. 黄昏之星的入场条件
        es_entry = (
            dataframe['daily_pump_signal_1d'] &
            (dataframe['hourly_rsi'] >= self.es_hourly_rsi_threshold.value) &
            dataframe['evening_star'].shift(1, fill_value=False) &
            dataframe['es_volume_spike'].shift(1, fill_value=False) &
            dataframe['confirmation_candle']
        )

        dataframe.loc[ss_entry, 'enter_tag'] = 'shooting_star'
        dataframe.loc[es_entry, 'enter_tag'] = 'evening_star'
        
        dataframe.loc[ss_entry | es_entry, 'enter_short'] = 1


        # 应用冷却期
        # V11 核心修复: 通过显式比较 (== 1) 修复 "The truth value of a Series is ambiguous" 错误
        if (dataframe['enter_short'] == 1).any():
            for i in dataframe[dataframe['enter_short'] == 1].index:
                row_time = dataframe.at[i, 'timestamp']
                last_trade = self.last_trade_time.get(pair, 0)
                if row_time - last_trade > cooldown_seconds:
                    self.last_trade_time[pair] = row_time
                else:
                    dataframe.at[i, 'enter_short'] = 0 
                    dataframe.at[i, 'enter_tag'] = None

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        V14.3 最终优化: 移除此功能，返回空的离场信号
        """
        dataframe.loc[(), 'exit_short'] = 0
        return dataframe

    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        V18.1: 此函数已不再使用。
        为防止在某些模式（如Hyperopt）下意外调用导致错误，函数体已被清空。
        """
        return None

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        V15: 修正并简化钱德利尔止损 (Chandelier Exit)
        - 移除盈利后禁用自定义止损的逻辑，修复了0-5%盈利区间的止损空白。
        - 确保在整个交易周期内，钱德利尔止损始终作为基准保护。
        - Freqtrade的追踪止损将在激活后自动选择更严格的止损位。
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        # 增加对空dataframe的保护，防止崩溃
        if dataframe.empty:
            return 0.99

        last_candle = dataframe.iloc[-1]

        # 计算钱德利尔止损位
        chandelier_exit_price = last_candle['high_max_rolling'] + last_candle['atr'] * self.stoploss_atr_multiplier.value
        
        # 对于做空交易，止损价比当前价格高，所以返回正值
        # Freqtrade期望返回一个百分比
        stoploss_pct = (chandelier_exit_price / current_rate) - 1
        
        return stoploss_pct

    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:
        """
        为交易对设置杠杆。
        """
        return self.leverage_value


# 添加Hyperopt类，用于参数优化
class PumpAndDumpShortHyperopt(PumpAndDumpShort):
    """
    PumpAndDumpShort策略的Hyperopt类 V3
    """
    @staticmethod
    def hyperopt_space() -> dict:
        """
        定义Hyperopt优化空间 V3
        """
        # V14 核心改动: 移除ROI table的优化，因为它已经被禁用
        return {
            'buy': {
                'consecutive_green_days': IntParameter(1, 5),
                'range_expansion_days': IntParameter(1, 3),
                'volume_expansion_days': IntParameter(1, 3),
                'daily_rsi_threshold': IntParameter(55, 75),
                'daily_signal_stickiness_days': IntParameter(1, 7),
                'cooldown_period': IntParameter(6, 48),

                # --- V29: 为不同形态设置专属可优化参数 ---
                # 射击之星
                'ss_upper_wick_body_ratio': DecimalParameter(1.0, 4.0, decimals=1),
                'ss_hourly_rsi_threshold': IntParameter(55, 75),
                'ss_volume_sma_period': IntParameter(10, 40),
                'ss_volume_spike_multiplier': DecimalParameter(1.5, 4.0, decimals=1),
                
                # 黄昏之星
                'es_hourly_rsi_threshold': IntParameter(50, 70),
                'es_volume_sma_period': IntParameter(10, 40),
                'es_volume_spike_multiplier': DecimalParameter(1.2, 3.5, decimals=1),
            },
            'sell': {
                # V17 修正: 统一参数名称，不再使用 _param 后缀
                'trailing_stop_positive': DecimalParameter(0.01, 0.05, decimals=3), # V14.2: 调整范围
                'trailing_stop_positive_offset': DecimalParameter(0.04, 0.1, decimals=2), # V14.2: 调整范围
                # V14 新增参数
                'atr_period': IntParameter(5, 25),
                'stoploss_atr_multiplier': DecimalParameter(2.0, 5.0, decimals=1),
                # V14.3 最终优化: 移除 exit_rsi_threshold
                # 'exit_rsi_threshold': IntParameter(45, 65), 
            }
        }
    
    @staticmethod
    def generate_roi_table(params: dict) -> dict:
        # V14 改动: 返回一个空的ROI表
        return {}
    
    def populate_buy_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return self.populate_entry_trend(dataframe, metadata)
    
    def populate_sell_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return self.populate_exit_trend(dataframe, metadata) 