# API文档

## 基础信息

- **Base URL**: `http://localhost:8000/api`
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证接口

### 用户注册
```http
POST /auth/register/
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirm": "password123",
  "nickname": "测试用户"
}
```

**响应：**
```json
{
  "message": "注册成功",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "points": 100
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### 用户登录
```http
POST /auth/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 获取用户信息
```http
GET /auth/profile/
Authorization: Bearer <access_token>
```

### 更新用户信息
```http
PUT /auth/profile/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "nickname": "新昵称",
  "bio": "个人简介"
}
```

## 资源接口

### 获取资源列表
```http
GET /resources/?page=1&page_size=20&category=1&search=关键词
```

**查询参数：**
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20，最大100）
- `category`: 分类ID
- `search`: 搜索关键词
- `tags`: 标签名称（逗号分隔）
- `is_free`: 是否免费（true/false）
- `ordering`: 排序方式（-created_at, -view_count, -download_count）

### 获取资源详情
```http
GET /resources/{id}/
```

### 创建资源
```http
POST /resources/create/
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

{
  "title": "资源标题",
  "description": "资源描述",
  "category_id": 1,
  "tag_ids": [1, 2, 3],
  "cover_image": <file>,
  "resource_file": <file>,
  "required_points": 10,
  "is_public": true
}
```

### 下载资源
```http
POST /resources/{id}/download/
Authorization: Bearer <access_token>
```

**响应：**
```json
{
  "download_url": "/api/resources/1/download/abc123/",
  "expires_at": "2024-01-01T12:05:00Z",
  "points_cost": 10,
  "remaining_points": 90
}
```

### 收藏资源
```http
POST /resources/{id}/favorite/
Authorization: Bearer <access_token>
```

### 评分资源
```http
POST /resources/{id}/rating/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "score": 5,
  "comment": "很好的资源"
}
```

## 分类和标签

### 获取分类列表
```http
GET /resources/categories/
```

### 获取标签列表
```http
GET /resources/tags/?search=关键词
```

## 支付接口

### 获取积分套餐
```http
GET /payments/points/packages/
```

### 获取VIP套餐
```http
GET /payments/vip/packages/
```

### 创建订单
```http
POST /payments/orders/create/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "package_id": 1,
  "package_type": "points",
  "payment_method": "alipay"
}
```

### 卡密兑换
```http
POST /payments/cards/redeem/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "code": "ABCD1234EFGH5678"
}
```

## 用户功能

### 签到
```http
POST /auth/signin/
Authorization: Bearer <access_token>
```

### 获取积分历史
```http
GET /auth/points/history/?page=1
Authorization: Bearer <access_token>
```

### 获取用户统计
```http
GET /auth/stats/
Authorization: Bearer <access_token>
```

## 文件上传

### 上传文件
```http
POST /core/upload/file/
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

{
  "file": <file>
}
```

### 上传图片
```http
POST /core/upload/image/
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

{
  "image": <file>
}
```

## 错误码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 错误响应格式

```json
{
  "error": "错误信息",
  "detail": "详细错误描述",
  "code": "ERROR_CODE"
}
```

## 分页响应格式

```json
{
  "count": 100,
  "next": "http://localhost:8000/api/resources/?page=2",
  "previous": null,
  "total_pages": 5,
  "current_page": 1,
  "page_size": 20,
  "results": [...]
}
```

## 限流规则

- API请求：100次/分钟
- 下载请求：10次/分钟
- 登录请求：5次/分钟
- 上传请求：20次/小时

## WebSocket接口

### 实时通知
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/notifications/');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('通知:', data);
};
```

## SDK示例

### JavaScript
```javascript
class CMSClient {
    constructor(baseURL, token) {
        this.baseURL = baseURL;
        this.token = token;
    }
    
    async request(method, endpoint, data = null) {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: data ? JSON.stringify(data) : null
        });
        
        return response.json();
    }
    
    async getResources(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.request('GET', `/resources/?${query}`);
    }
    
    async downloadResource(id) {
        return this.request('POST', `/resources/${id}/download/`);
    }
}

// 使用示例
const client = new CMSClient('http://localhost:8000/api', 'your-token');
const resources = await client.getResources({ category: 1, page: 1 });
```

### Python
```python
import requests

class CMSClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        })
    
    def get_resources(self, **params):
        response = self.session.get(f'{self.base_url}/resources/', params=params)
        return response.json()
    
    def download_resource(self, resource_id):
        response = self.session.post(f'{self.base_url}/resources/{resource_id}/download/')
        return response.json()

# 使用示例
client = CMSClient('http://localhost:8000/api', 'your-token')
resources = client.get_resources(category=1, page=1)
```
