import numpy as np
import pandas as pd
from pandas import DataFrame, Series
from typing import Optional
from datetime import datetime
import math
from functools import reduce

from freqtrade.strategy import (DecimalParameter, IStrategy, IntParameter, informative, CategoricalParameter)
from freqtrade.persistence import Trade, Order
from freqtrade.exchange import timeframe_to_minutes
from freqtrade.strategy import merge_informative_pair

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta

import logging
logger = logging.getLogger(__name__)

# Ignore Performance Warnings
from warnings import simplefilter
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)
simplefilter(action="ignore", category=FutureWarning)


class Jamroly_v003(IStrategy):
    """
    ATR-based DCA strategy with partial exits. Experimental strategy for testing only.
    """
    INTERFACE_VERSION = 3

    def version(self) -> str:
        return "v0.0.3"

    # Strategy settings
    can_short = True
    timeframe = '3m'
    informative_timeframes = ['5m', '15m']
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False
    use_custom_stoploss = False

    ############################################################################
    # Strategy Parameters and Hyperopt Spaces
    ############################################################################

    # Buy hyperspace params
    buy_params = {
        "leverage": 5,
        # 肯特纳通道参数 - 更宽松的区间
        "keltner_pos_upper": 90,
        "keltner_pos_lower": 10,
        "keltner_strength_mult": 1.0,
        
        # 回归通道参数 - 降低斜率要求
        "regression_slope_min": 0.05,
        "regression_dev_mult": 1.5,
        
        # RSI 参数
        "rsi_buy_enter": 30,
        "rsi_buy_exit": 70, # 用于多头止盈参考

        # MACD 参数
        "macd_buy_enabled": True, # 是否启用MACD作为入场条件
    }

    # Sell hyperspace params
    sell_params = {
        # 回归通道参数
        "regression_slope_min": 0.05,  # 最小斜率要求
        "regression_dev_mult": 1.5,  # 通道宽度倍数
        
        # 肯特纳通道参数
        "keltner_pos_lower": 20,  # 下限位置
        "keltner_strength_mult": 1.5,  # 通道宽度倍数
        
        # 止损参数
        "atr_sl_multiplier": 3.0,  # ATR止损倍数
        "trailing_stop": True,
        "trailing_stop_positive": 0.01,  # 1%盈利激活
        "trailing_stop_positive_offset": 0.02,  # 2%回撤触发

        # RSI 参数
        "rsi_sell_enter": 70,
        "rsi_sell_exit": 30, # 用于空头止盈参考

        # MACD 参数
        "macd_sell_enabled": True, # 是否启用MACD作为入场条件
    }

    # ROI table
    minimal_roi = {
        "0": 100,  # Disable ROI
    }

    # Stoploss
    stoploss = -0.03  # 3%止损
    
    # Trailing stop
    trailing_stop = True
    trailing_stop_positive = 0.01  # 1%盈利后激活跟踪止损
    trailing_stop_positive_offset = 0.02  # 回撤2%触发跟踪止损
    trailing_only_offset_is_reached = True  # 只有达到offset才启用跟踪止损

    ############################################################################
    # Hyperopt Parameters
    ############################################################################

    # 杠杆参数
    lev = IntParameter(1, 25, default=buy_params['leverage'], space='buy', optimize=False)
    
    # 肯特纳通道参数优化
    keltner_pos_upper = IntParameter(70, 90, default=buy_params['keltner_pos_upper'], space='buy', optimize=True)
    keltner_pos_lower = IntParameter(10, 30, default=buy_params['keltner_pos_lower'], space='buy', optimize=True)
    keltner_strength_mult = DecimalParameter(1.0, 2.0, default=buy_params['keltner_strength_mult'], space='buy', optimize=True)
    
    # 回归通道参数优化
    regression_slope_min = DecimalParameter(0.05, 0.5, default=buy_params['regression_slope_min'], space='buy', optimize=True)
    regression_dev_mult = DecimalParameter(1.5, 3.0, default=buy_params['regression_dev_mult'], space='buy', optimize=True)
    
    # 市场状态参数优化
    # strong_trend_thresh = IntParameter(20, 40, default=buy_params['strong_trend_thresh'], space='buy', optimize=False)
    # choppy_thresh = IntParameter(10, 30, default=buy_params['choppy_thresh'], space='buy', optimize=False)
    # volume_mult = DecimalParameter(1.0, 2.0, default=buy_params['volume_mult'], space='buy', optimize=False)

    # 新增市场状态和趋势参数
    # choppy_keltner_std = DecimalParameter(10.0, 30.0, default=buy_params['choppy_keltner_std'], space='buy', optimize=True)
    # min_trend_slope = DecimalParameter(0.05, 0.2, default=buy_params['min_trend_slope'], space='buy', optimize=True)
    # max_slope_deviation = DecimalParameter(0.01, 0.05, default=buy_params['max_slope_deviation'], space='buy', optimize=True)
    # min_channel_width = DecimalParameter(0.002, 0.01, default=buy_params['min_channel_width'], space='buy', optimize=True)
    # strong_trend_slope = DecimalParameter(0.1, 0.3, default=buy_params['strong_trend_slope'], space='buy', optimize=True)
    # max_pos_deviation = DecimalParameter(15.0, 35.0, default=buy_params['max_pos_deviation'], space='buy', optimize=True)

    # RSI 参数优化
    rsi_buy_enter = IntParameter(20, 40, default=buy_params['rsi_buy_enter'], space='buy', optimize=True)
    rsi_buy_exit = IntParameter(60, 80, default=buy_params['rsi_buy_exit'], space='buy', optimize=True) # 用于多头止盈参考
    rsi_sell_enter = IntParameter(60, 80, default=sell_params['rsi_sell_enter'], space='sell', optimize=True)
    rsi_sell_exit = IntParameter(20, 40, default=sell_params['rsi_sell_exit'], space='sell', optimize=True) # 用于空头止盈参考

    # MACD 参数优化
    macd_buy_enabled = CategoricalParameter([True, False], default=buy_params['macd_buy_enabled'], space='buy', optimize=True)
    macd_sell_enabled = CategoricalParameter([True, False], default=sell_params['macd_sell_enabled'], space='sell', optimize=True)

    # ATR动态止损/止盈参数
    atr_sl_multiplier = CategoricalParameter([3.0, 4.0, 5.0, 6.0], default=5.0, space='sell', optimize=True)
    atr_tp_long_multiplier = CategoricalParameter([8.0, 10.0, 12.0], default=12.0, space='sell', optimize=True)
    atr_tp_short_multiplier = CategoricalParameter([6.0, 8.0, 10.0], default=8.0, space='sell', optimize=True)
    
    # 仓位管理参数
    risk_per_trade = DecimalParameter(0.01, 0.03, default=0.02, space='buy', optimize=True)
    pos_size_atr = DecimalParameter(2.0, 4.0, default=3.0, space='buy', optimize=True)

    # Custom dict for plotting purposes
    custom_info = {}

    ############################################################################
    # Plot Config
    ############################################################################

    @property
    def plot_config(self):
        """
        Customize indicators plotted on FreqUI
        """
        plot_config = {
            'main_plot': {
                # 价格和突破价格
                'break_even_price': {'color': '#FFD700'},  # 黄金色
                
                # 回归通道
                'regression_upper': {'color': '#FF9999'},  # 上轨(浅红色)
                'regression_middle': {'color': '#666666'},  # 中轨(灰色) 
                'regression_lower': {'color': '#99FF99'},  # 下轨(浅绿色)
                
                # 肯特纳通道
                'keltner_upper': {'color': '#FF4444'},     # 红色
                'keltner_middle': {'color': '#888888'},    # 灰色
                'keltner_lower': {'color': '#44FF44'},     # 绿色
                
                # 多周期指标 - 5分钟
                'regression_middle_5m': {'color': '#0066CC'},  # 蓝色
                'keltner_middle_5m': {'color': '#CC6600'},     # 棕色
                
                # 多周期指标 - 15分钟
                'regression_middle_15m': {'color': '#884DFF'},  # 紫色
                'keltner_middle_15m': {'color': '#FF884D'},     # 橙色
            },
            'subplots': {
                # ATR subplot
                "ATR": {
                    'atr': {'color': '#FF69B4'},       # 粉色
                    'atr_mean': {'color': '#DB7093'},  # 浅紫红色
                    'atr_5m': {'color': '#9370DB'},    # 中紫色
                    'atr_15m': {'color': '#663399'},   # 深紫色
                },
                # Returns subplot
                "Returns": {
                    'returns_roll_mean_cumsum': {'color': '#4169E1'},         # 皇家蓝
                    'returns_roll_mean_cumsum_upper': {'color': '#98FB98'},   # 浅绿色
                    'returns_roll_mean_cumsum_lower': {'color': '#FFA07A'},   # 浅鲑鱼色
                },
                # 肯特纳通道位置指标
                "Keltner Position": {
                    'keltner_pos': {'color': '#2196F3'},       # 蓝色
                    'keltner_pos_5m': {'color': '#FFC107'},    # 琥珀色
                    'keltner_pos_15m': {'color': '#4CAF50'},   # 绿色
                    'keltner_pos_ma': {'color': '#9C27B0'},    # 紫色
                    'keltner_pos_ma_5m': {'color': '#FF5722'}, # 深橙色
                    'keltner_pos_ma_15m': {'color': '#795548'} # 棕色
                },
                # 回归斜率
                "Regression Slope": {
                    'regression_slope': {'color': '#3F51B5'},       # 靛蓝色
                    'regression_slope_5m': {'color': '#E91E63'},    # 粉红色
                    'regression_slope_15m': {'color': '#009688'},   # 水鸭色
                },
                # 趋势和通道状态
                "Channel State": {
                    'channel_alignment': {'color': '#8BC34A'},       # 浅绿色
                    'channel_alignment_5m': {'color': '#FF9800'},    # 橙色
                    'channel_alignment_15m': {'color': '#00BCD4'},   # 青色
                    'trend_continue_after_test': {'color': '#673AB7'}, # 深紫色
                    'trend_continue_after_test_5m': {'color': '#FFEB3B'}, # 黄色
                    'trend_continue_after_test_15m': {'color': '#607D8B'} # 蓝灰色
                },
                # RSI
                "RSI": {
                    'rsi': {'color': '#FFC107'} # Amber
                },
                # MACD
                "MACD": {
                    'macd': {'color': '#2196F3'},      # Blue
                    'macdsignal': {'color': '#FF5722'}, # Deep Orange
                    'macdhist': {'color': '#4CAF50', 'type': 'bar', 'plotly': {'opacity': 0.4}} # Green
                }
            }
        }
        return plot_config

    def informative_pairs(self):
        """
        定义需要获取的信息对
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = []
        for pair in pairs:
            for timeframe in ['5m', '15m']:  # 只需要获取这两个周期的数据，3m是基础周期
                informative_pairs.append((pair, timeframe))
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算多周期指标
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # 计算基础时间周期(3m)的指标
        dataframe = self.calculate_indicators_for_timeframe(dataframe)
        if dataframe is None:
            return DataFrame()  # 返回空DataFrame以防止错误
        
        # 获取5分钟数据
        inf_tf_5m = '5m'
        informative_5m = self.dp.get_pair_dataframe(
            pair=metadata['pair'],
            timeframe=inf_tf_5m
        )
        
        # 处理5分钟数据 - 添加空值检查
        if informative_5m is not None and len(informative_5m) > 0:
            try:
                informative_5m = informative_5m.copy()
                if isinstance(informative_5m.index, pd.DatetimeIndex):
                    informative_5m['date'] = informative_5m.index
                
                # 计算5分钟指标并添加后缀
                informative_5m = self.calculate_indicators_for_timeframe(informative_5m)
                if informative_5m is not None and len(informative_5m) > 0:
                    informative_5m = informative_5m.add_suffix(f'_{inf_tf_5m}')
                    # 确保date列正确
                    if f'date_{inf_tf_5m}' in informative_5m.columns:
                        informative_5m['date'] = informative_5m[f'date_{inf_tf_5m}']
                    
                    # 合并数据
                    dataframe = merge_informative_pair(
                        dataframe, informative_5m,
                        self.timeframe, inf_tf_5m,
                        ffill=True
                    )
            except Exception as e:
                print(f"处理5分钟数据时出错: {e}")
        
        # 处理15分钟数据
        inf_tf_15m = '15m'
        informative_15m = self.dp.get_pair_dataframe(
            pair=metadata['pair'],
            timeframe=inf_tf_15m
        )
        
        # 处理15分钟数据 - 添加空值检查
        if informative_15m is not None and len(informative_15m) > 0:
            try:
                informative_15m = informative_15m.copy()
                if isinstance(informative_15m.index, pd.DatetimeIndex):
                    informative_15m['date'] = informative_15m.index
                
                # 计算15分钟指标并添加后缀
                informative_15m = self.calculate_indicators_for_timeframe(informative_15m)
                if informative_15m is not None and len(informative_15m) > 0:
                    informative_15m = informative_15m.add_suffix(f'_{inf_tf_15m}')
                    # 确保date列正确
                    if f'date_{inf_tf_15m}' in informative_15m.columns:
                        informative_15m['date'] = informative_15m[f'date_{inf_tf_15m}']
                    
                    # 合并数据
                    dataframe = merge_informative_pair(
                        dataframe, informative_15m,
                        self.timeframe, inf_tf_15m,
                        ffill=True
                    )
            except Exception as e:
                print(f"处理15分钟数据时出错: {e}")
        
        return dataframe

    def calculate_indicators_for_timeframe(self, dataframe: DataFrame) -> DataFrame:
        """
        计算单一时间周期的所有指标
        """
        if dataframe is None or len(dataframe) < 20:  # 确保有足够的数据
            return dataframe
        
        try:
            # 创建临时DataFrame以确保不修改原始数据
            temp_df = dataframe.copy()
            
            # 移动平均线
            temp_df['sma_20'] = ta.SMA(temp_df, timeperiod=20)
            temp_df['sma_50'] = ta.SMA(temp_df, timeperiod=50)
            
            # 使用安全的线性回归方法
            window = 20
            if len(temp_df) >= window:
                # 线性回归斜率
                try:
                    # temp_df['regression_slope'] = self.calculate_regression_slope(temp_df, window=window)
                    temp_df['regression_slope'] = ta.LINEARREG_SLOPE(temp_df, timeperiod=window)
                except Exception as e:
                    # 回退到简单差异计算
                    temp_df['regression_slope'] = temp_df['close'].diff(5) / temp_df['close'].shift(5) * 100
            else: # 添加else确保列存在
                temp_df['regression_slope'] = 0.0
            
            # Keltner通道
            temp_df = self.add_keltner_channels(temp_df)
            
            # 通道位置和状态
            temp_df = self.add_channel_positions(temp_df)
            
            # 通道波动测量
            # temp_df = self.add_channel_volatility(temp_df)
            
            # 中线测试和趋势延续指标
            # temp_df = self.add_trend_continuation(temp_df)
            
            # RSI
            temp_df['rsi'] = ta.RSI(temp_df, timeperiod=14)

            # MACD
            macd = ta.MACD(temp_df, fastperiod=12, slowperiod=26, signalperiod=9)
            temp_df['macd'] = macd['macd']
            temp_df['macdsignal'] = macd['macdsignal']
            temp_df['macdhist'] = macd['macdhist']
            
            return temp_df
        
        except Exception as e:
            print(f"计算指标时出错: {str(e)}")
            # 返回原始DataFrame，添加最基本的必要列
            if 'regression_slope' not in dataframe.columns:
                dataframe['regression_slope'] = 0
            if 'keltner_pos' not in dataframe.columns:
                dataframe['keltner_pos'] = 50
            
            # 确保其他重要列也存在
            important_columns = [
                'keltner_upper', 'keltner_middle', 'keltner_lower',
                'keltner_pos_ma', 'keltner_pos_std', 'channel_alignment',
                'keltner_middle_test', 'trend_continue_after_test'
            ]
            
            for col in important_columns:
                if col not in dataframe.columns:
                    if 'pos' in col:
                        dataframe[col] = 50  # 默认位置值
                    elif 'alignment' in col or 'test' in col:
                        dataframe[col] = False  # 默认布尔值
                    else:
                        dataframe[col] = dataframe['close']  # 默认使用收盘价
        
        return dataframe

    def add_keltner_channels(self, dataframe: DataFrame) -> DataFrame:
        """
        添加肯特纳通道指标
        """
        if dataframe is None or len(dataframe) == 0:
            return dataframe
        
        try:
            # 计算基础指标
            typical_price = (dataframe['high'] + dataframe['low'] + dataframe['close']) / 3
            
            # 计算ATR
            dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
            
            # 计算肯特纳通道
            dataframe['keltner_middle'] = ta.EMA(typical_price, timeperiod=20)
            dataframe['keltner_upper'] = dataframe['keltner_middle'] + (dataframe['atr'] * self.keltner_strength_mult.value)
            dataframe['keltner_lower'] = dataframe['keltner_middle'] - (dataframe['atr'] * self.keltner_strength_mult.value)
            
            # 计算中线测试
            dataframe['keltner_middle_test'] = (
                (dataframe['low'] <= dataframe['keltner_middle']) &
                (dataframe['close'] >= dataframe['keltner_middle'])
            )
            
            return dataframe
            
        except Exception as e:
            print(f"计算Keltner通道时出错: {str(e)}")
            # 确保所有必要的列都存在，即使出错
            for col in ['atr', 'keltner_middle', 'keltner_upper', 'keltner_lower', 'keltner_middle_test']:
                if col not in dataframe.columns:
                    if col == 'keltner_middle_test':
                        dataframe[col] = False
                    else:
                        dataframe[col] = dataframe['close']
            return dataframe

    def add_channel_positions(self, dataframe: DataFrame) -> DataFrame:
        """
        添加通道位置和对齐指标
        """
        if dataframe is None or len(dataframe) == 0:
            return dataframe
        
        try:
            # 计算通道位置
            if 'close' in dataframe.columns and 'keltner_upper' in dataframe.columns and 'keltner_lower' in dataframe.columns:
                dataframe['keltner_pos'] = (
                    (dataframe['close'] - dataframe['keltner_lower']) /
                    (dataframe['keltner_upper'] - dataframe['keltner_lower']) * 100
                )
            else:
                dataframe['keltner_pos'] = 50  # 默认值
            
            # 计算通道对齐指标
            if 'regression_slope' in dataframe.columns and 'keltner_pos' in dataframe.columns:
                # 基础时间周期
                dataframe['channel_alignment'] = (
                    (dataframe['regression_slope'] > 0) & (dataframe['keltner_pos'] > 50) |
                    (dataframe['regression_slope'] < 0) & (dataframe['keltner_pos'] < 50)
                )
                
                # 移除5分钟和15分钟的对齐逻辑
                # if 'regression_slope_5m' in dataframe.columns and 'keltner_pos_5m' in dataframe.columns:
                #     dataframe['channel_alignment_5m'] = (
                #         (dataframe['regression_slope_5m'] > 0) & (dataframe['keltner_pos_5m'] > 50) |
                #         (dataframe['regression_slope_5m'] < 0) & (dataframe['keltner_pos_5m'] < 50)
                #     )
                # else:
                #     dataframe['channel_alignment_5m'] = False
                    
                # if 'regression_slope_15m' in dataframe.columns and 'keltner_pos_15m' in dataframe.columns:
                #     dataframe['channel_alignment_15m'] = (
                #         (dataframe['regression_slope_15m'] > 0) & (dataframe['keltner_pos_15m'] > 50) |
                #         (dataframe['regression_slope_15m'] < 0) & (dataframe['keltner_pos_15m'] < 50)
                #     )
                # else:
                #     dataframe['channel_alignment_15m'] = False
            else:
                dataframe['channel_alignment'] = False  # 默认值
                # dataframe['channel_alignment_5m'] = False # 移除
                # dataframe['channel_alignment_15m'] = False # 移除
            
            # 计算位置移动平均
            dataframe['keltner_pos_ma'] = ta.SMA(dataframe['keltner_pos'], timeperiod=20)
            
            # 计算位置标准差
            dataframe['keltner_pos_std'] = ta.STDDEV(dataframe['keltner_pos'], timeperiod=20)
            
            return dataframe
            
        except Exception as e:
            print(f"计算通道位置时出错: {str(e)}")
            # 确保所有必要的列都存在，即使出错
            for col in ['keltner_pos', 'channel_alignment', 'channel_alignment_5m', 'channel_alignment_15m', 'keltner_pos_ma', 'keltner_pos_std']:
                if col not in dataframe.columns:
                    if 'pos' in col:
                        dataframe[col] = 50
                    elif 'alignment' in col:
                        dataframe[col] = False
                    else:
                        dataframe[col] = 0
            return dataframe

    # def add_channel_volatility(self, dataframe: DataFrame) -> DataFrame:
    #     if dataframe is None or len(dataframe) == 0:
    #         return dataframe
        
    #     try:
    #         # 方法实现...
    #         # 注意：确保此方法始终返回有效的DataFrame
    #         return dataframe
    #     except Exception as e:
    #         print(f"计算通道波动时出错: {str(e)}")
    #         return dataframe

    # def add_trend_continuation(self, dataframe: DataFrame) -> DataFrame:
    #     """
    #     添加趋势延续指标
    #     """
    #     if dataframe is None or len(dataframe) == 0:
    #         return dataframe
        
    #     try:
    #         # 初始化趋势延续指标
    #         dataframe['trend_continue_after_test'] = False
            
    #         # 确保必要的列存在
    #         required_columns = ['keltner_middle_test', 'regression_slope', 'keltner_pos', 'keltner_pos_ma']
    #         if not all(col in dataframe.columns for col in required_columns):
    #             return dataframe
            
    #         # 计算趋势延续条件
    #         for i in range(1, len(dataframe)):
    #             if dataframe['keltner_middle_test'].iloc[i-1]:  # 如果前一根K线测试了中线
    #                 # 多头趋势延续条件
    #                 long_continuation = (
    #                     (dataframe['regression_slope'].iloc[i] > 0) &  # 斜率保持正向
    #                     (dataframe['keltner_pos'].iloc[i] > 50) &     # 位置在中线以上
    #                     (dataframe['keltner_pos'].iloc[i] > dataframe['keltner_pos_ma'].iloc[i])  # 位置高于均值
    #                 )
                    
    #                 # 空头趋势延续条件
    #                 short_continuation = (
    #                     (dataframe['regression_slope'].iloc[i] < 0) &  # 斜率保持负向
    #                     (dataframe['keltner_pos'].iloc[i] < 50) &     # 位置在中线以下
    #                     (dataframe['keltner_pos'].iloc[i] < dataframe['keltner_pos_ma'].iloc[i])  # 位置低于均值
    #                 )
                    
    #                 # 设置趋势延续标志
    #                 dataframe['trend_continue_after_test'].iloc[i] = long_continuation | short_continuation
            
    #         return dataframe
            
    #     except Exception as e:
    #         print(f"计算趋势延续指标时出错: {str(e)}")
    #         # 确保趋势延续列存在
    #         if 'trend_continue_after_test' not in dataframe.columns:
    #             dataframe['trend_continue_after_test'] = False
    #         return dataframe

    ############################################################################
    # Entry and Exit Signals
    ############################################################################

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号优化 - 基于当前时间周期的指标
        """
        # -- 做多条件 --
        enter_long_conditions = []
        # 1. 价格突破肯特纳通道下轨，并且回归通道向上，RSI超卖
        enter_long_conditions.append(
            (dataframe['close'] < dataframe['keltner_lower']) &
            (dataframe['regression_slope'] > self.regression_slope_min.value) &
            (dataframe['rsi'] < self.rsi_buy_enter.value)
        )
        # 2. 价格在肯特纳通道中轨附近，回归通道向上，RSI处于上升趋势
        enter_long_conditions.append(
            (dataframe['close'] > dataframe['keltner_middle']) &
            (dataframe['close'] < dataframe['keltner_upper']) & # 避免在通道上轨外开仓
            (dataframe['regression_slope'] > self.regression_slope_min.value) &
            (dataframe['rsi'] < 50) & # RSI < 50 表明仍有上涨空间
            (dataframe['rsi'] > dataframe['rsi'].shift(1)) # RSI 形成上升趋势
        )

        # 可选：MACD 金叉
        if self.macd_buy_enabled.value:
            enter_long_conditions.append(
                (dataframe['macd'] > dataframe['macdsignal']) &
                (dataframe['macd'].shift(1) < dataframe['macdsignal'].shift(1)) # MACD金叉
            )
        
        # 合并做多条件 - 满足任一主要条件，并且（如果启用）满足MACD条件
        if self.macd_buy_enabled.value and len(enter_long_conditions) > 1: # 如果启用了MACD，则需要MACD条件也满足
             dataframe.loc[
                (reduce(lambda x, y: x | y, enter_long_conditions[:-1])) & # 肯特纳/回归/RSI条件满足其一
                (enter_long_conditions[-1]), # MACD条件满足
                'enter_long'] = 1
        elif len(enter_long_conditions) > 0: # 如果未启用MACD或只有一个主要条件
            dataframe.loc[
                reduce(lambda x, y: x | y, enter_long_conditions),
                'enter_long'] = 1


        # -- 做空条件 --
        enter_short_conditions = []
        # 1. 价格突破肯特纳通道上轨，并且回归通道向下，RSI超买
        enter_short_conditions.append(
            (dataframe['close'] > dataframe['keltner_upper']) &
            (dataframe['regression_slope'] < -self.regression_slope_min.value) &
            (dataframe['rsi'] > self.rsi_sell_enter.value)
        )
        # 2. 价格在肯特纳通道中轨附近，回归通道向下，RSI处于下降趋势
        enter_short_conditions.append(
            (dataframe['close'] < dataframe['keltner_middle']) &
            (dataframe['close'] > dataframe['keltner_lower']) & # 避免在通道下轨外开仓
            (dataframe['regression_slope'] < -self.regression_slope_min.value) &
            (dataframe['rsi'] > 50) & # RSI > 50 表明仍有下跌空间
            (dataframe['rsi'] < dataframe['rsi'].shift(1)) # RSI 形成下降趋势
        )

        # 可选：MACD 死叉
        if self.macd_sell_enabled.value:
            enter_short_conditions.append(
                (dataframe['macd'] < dataframe['macdsignal']) &
                (dataframe['macd'].shift(1) > dataframe['macdsignal'].shift(1)) # MACD死叉
            )

        # 合并做空条件 - 满足任一主要条件，并且（如果启用）满足MACD条件
        if self.macd_sell_enabled.value and len(enter_short_conditions) > 1:
            dataframe.loc[
                (reduce(lambda x, y: x | y, enter_short_conditions[:-1])) &
                (enter_short_conditions[-1]),
                'enter_short'] = 1
        elif len(enter_short_conditions) > 0:
            dataframe.loc[
                reduce(lambda x, y: x | y, enter_short_conditions),
                'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号优化 - 基于当前时间周期的指标
        """
        # 初始化空列
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        # -- 做多出场条件 --
        exit_long_conditions = []
        # 1. 价格突破肯特纳通道上轨（止盈信号）
        exit_long_conditions.append(
            dataframe['close'] > dataframe['keltner_upper']
        )
        # 2. RSI 超买
        exit_long_conditions.append(
            dataframe['rsi'] > self.rsi_buy_exit.value # 使用 buy_params 中的 rsi_buy_exit
        )
        # 3. 回归通道向下
        exit_long_conditions.append(
            dataframe['regression_slope'] < -self.regression_slope_min.value # 使用 buy_params 中的 regression_slope_min
        )
        # 可选：MACD 死叉
        if self.macd_buy_enabled.value: # 复用 macd_buy_enabled，或者可以为退出定义新的参数
            exit_long_conditions.append(
                (dataframe['macd'] < dataframe['macdsignal']) &
                (dataframe['macd'].shift(1) > dataframe['macdsignal'].shift(1)) # MACD死叉
            )

        # 合并做多出场条件 - 满足任一条件即可
        if len(exit_long_conditions) > 0:
            dataframe.loc[reduce(lambda x, y: x | y, exit_long_conditions), 'exit_long'] = 1

        # -- 做空出场条件 --
        exit_short_conditions = []
        # 1. 价格突破肯特纳通道下轨（止盈信号）
        exit_short_conditions.append(
            dataframe['close'] < dataframe['keltner_lower']
        )
        # 2. RSI 超卖
        exit_short_conditions.append(
            dataframe['rsi'] < self.rsi_sell_exit.value # 使用 sell_params 中的 rsi_sell_exit
        )
        # 3. 回归通道向上
        exit_short_conditions.append(
            dataframe['regression_slope'] > self.regression_slope_min.value # 使用 sell_params 中的 regression_slope_min
        )
        # 可选：MACD 金叉
        if self.macd_sell_enabled.value: # 复用 macd_sell_enabled
            exit_short_conditions.append(
                (dataframe['macd'] > dataframe['macdsignal']) &
                (dataframe['macd'].shift(1) < dataframe['macdsignal'].shift(1)) # MACD金叉
            )
        
        # 合并做空出场条件 - 满足任一条件即可
        if len(exit_short_conditions) > 0:
            dataframe.loc[reduce(lambda x, y: x | y, exit_short_conditions), 'exit_short'] = 1
            
        return dataframe

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                          proposed_stake: float, min_stake: float, max_stake: float,
                          **kwargs) -> float:
        """
        根据ATR动态调整仓位大小
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) > 0:
            last_candle = dataframe.iloc[-1]
            # 计算基于风险的仓位大小
            atr = last_candle['atr']
            risk_amount = proposed_stake * self.risk_per_trade.value  # 每次交易风险金额
            position_size = risk_amount / (atr * self.pos_size_atr.value)
            return min(max(position_size, min_stake), max_stake)
        return proposed_stake

    def calculate_regression_mid(self, dataframe: DataFrame) -> Series:
        """
        计算回归中线
        """
        return ta.LINEARREG(dataframe['close'], timeperiod=14)

    def calculate_keltner_middle(self, dataframe: DataFrame) -> Series:
        """
        计算肯特纳通道中线
        """
        return ta.EMA(dataframe, timeperiod=34)

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损计算
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 根据交易方向区分止损逻辑
        if trade.is_short:
            # 做空止损逻辑
            if current_profit > 0:
                stop_distance = min(
                    last_candle['keltner_middle'] - current_rate,  
                    last_candle['regression_middle'] - current_rate  
                )
            else:
                stop_distance = last_candle['atr'] * self.atr_sl_multiplier.value
            
            # 转换为百分比
            return stop_distance / current_rate
        else:
            # 做多止损逻辑
            if current_profit > 0:
                stop_distance = min(
                    current_rate - last_candle['keltner_middle'],  
                    current_rate - last_candle['regression_middle']  
                )
            else:
                stop_distance = last_candle['atr'] * self.atr_sl_multiplier.value
            
            # 转换为百分比
            return -1 * (stop_distance / current_rate)

    # def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
    #                 current_profit: float, **kwargs) -> bool:
    #     """
    #     做空出场逻辑
    #     """
    #     dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
    #     # 添加数据验证
    #     if len(dataframe) == 0:
    #         return False
        
    #     last_candle = dataframe.iloc[-1].squeeze()
        
    #     # 1. 趋势反转出场
    #     trend_reversal_short = (
    #         (last_candle['keltner_pos'] > 50) and  # 肯特纳位置突破中值
    #         (last_candle['regression_slope'] > 0) and  # 回归斜率转正
    #         (last_candle['close'] > last_candle['keltner_middle'])  # 价格突破肯特纳中线
    #     )
        
    #     # 2. 分批获利
    #     take_profit_short = (
    #         (current_profit > 0.02) and  # 2%以上获利
    #         (last_candle['keltner_pos'] > last_candle['keltner_pos_ma'])  # 肯特纳位置开始上升
    #     )
        
    #     return trend_reversal_short or take_profit_short