import os
import re
import json
import shutil
from datetime import datetime
import logging

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s',
)

def get_config_from_env():
    """从环境变量中获取并验证配置"""
    project_root = os.getenv("PROJECT_ROOT")
    strategy_path = os.getenv("STRATEGY_PATH")
    config_file_name = os.getenv("CONFIG_FILE_NAME")
    bt_strategy_name = os.getenv("BT_STRATEGY_NAME")

    if not all([project_root, strategy_path, config_file_name, bt_strategy_name]):
        print("错误: 必要的环境变量未设置。请通过主脚本 main.sh 运行。")
        exit(1)
        
    strategy_dir = os.path.join(project_root, "user_data", strategy_path)
    # 输入是Markdown报告
    report_file_path = os.path.join(strategy_dir, f"{bt_strategy_name}_ranked_report.md")
    # 主配置文件
    config_file_path = os.path.join(project_root, "user_data", config_file_name)
    # 策略参数文件，注意名称现在是 ..._Settings.json
    settings_file_path = os.path.join(strategy_dir, f"{bt_strategy_name}_Settings.json")

    for p in [report_file_path, config_file_path, settings_file_path]:
        if not os.path.exists(p):
            print(f"错误: 必需文件未找到 '{p}'。")
            print("请确保之前的步骤已成功执行。")
            exit(1)

    return report_file_path, config_file_path, settings_file_path

def parse_ranked_report(report_path: str) -> list:
    """从Markdown报告中解析出排名最高的交易对列表。"""
    print(f"正在从报告中解析排名交易对: {report_path}")
    try:
        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式从表格中提取被 ` ` 包围的交易对名称
        # 假设它们在表格的第二列
        pairs = re.findall(r'\|\s*\d+\s*\|\s*`([^`]+)`', content)
        
        if not pairs:
            print("警告: 未能从报告的 '综合表现排行榜' 中解析出任何交易对。")

        return pairs
    except Exception as e:
        print(f"解析报告文件时出错: {e}")
        return []

def update_configuration_files(config_file: str, settings_file: str, selected_pairs: list):
    """使用筛选后的交易对列表，同时更新主配置文件和策略参数设置文件。"""
    if not selected_pairs:
        print("没有选择任何交易对，配置文件将不会被修改。")
        return

    # --- 更新主配置文件 (config.json) ---
    try:
        print(f"\n--- 正在更新主配置文件: {os.path.basename(config_file)} ---")
        backup_file = f"{config_file}.backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        shutil.copy2(config_file, backup_file)
        print(f"已创建备份: {backup_file}")

        with open(config_file, 'r+', encoding='utf-8') as f:
            config_data = json.load(f)
            original_whitelist = config_data.get('exchange', {}).get('pair_whitelist', [])
            print(f"原始白名单数量: {len(original_whitelist)}")
            
            # 更新白名单
            config_data['exchange']['pair_whitelist'] = selected_pairs
            print(f"更新后白名单数量: {len(selected_pairs)}")

            f.seek(0)
            json.dump(config_data, f, indent=4)
            f.truncate()
        
        print("成功更新主配置文件。")
    except Exception as e:
        print(f"更新主配置文件时出错: {e}")

    # --- 更新策略参数设置文件 (*_Settings.json) ---
    try:
        print(f"\n--- 正在更新策略参数文件: {os.path.basename(settings_file)} ---")
        backup_settings_file = f"{settings_file}.backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        shutil.copy2(settings_file, backup_settings_file)
        print(f"已创建备份: {backup_settings_file}")
        
        with open(settings_file, 'r+', encoding='utf-8') as f:
            # 读取JSON数据
            settings_data = json.load(f)

            # 健壮性加载：只提取看起来像交易对的条目（键中包含'/'）
            # 以避免被空的 "params": {} 等无效条目干扰。
            original_params = {}
            for key, value in settings_data.items():
                if '/' in key and isinstance(value, dict):
                    original_params[key] = value

            if not original_params:
                logging.warning("在参数文件中未找到任何有效的交易对数据。")
            else:
                logging.info(f"从参数文件中成功加载 {len(original_params)} 个交易对。")

            print(f"原始参数文件中的交易对数量: {len(original_params)}")
            
            # 创建一个新的字典，只包含在 selected_pairs 中的键
            filtered_params = {pair: params for pair, params in original_params.items() if pair in selected_pairs}
            
            print(f"更新后参数文件中的交易对数量: {len(filtered_params)}")
            
            f.seek(0)
            # 始终写回扁平化的格式
            json.dump(filtered_params, f, indent=4)
            f.truncate()
            
        print("成功更新策略参数文件。")
    except Exception as e:
        print(f"更新策略参数文件时出错: {e}")

def main():
    """主执行函数。"""
    report_file, config_file, settings_file = get_config_from_env()
    
    ranked_pairs = parse_ranked_report(report_file)
    if not ranked_pairs:
        return

    print("-" * 50)
    print(f"已从报告中成功解析出 {len(ranked_pairs)} 个排名交易对。")
    print("-" * 50)

    while True:
        try:
            num_to_select_str = input(f"您希望保留前多少个交易对? (输入 1-{len(ranked_pairs)}, 或 'q' 退出): ")
            if num_to_select_str.lower() in ['q', 'quit']:
                print("操作已取消。")
                return
            
            num_to_select = int(num_to_select_str)
            if 0 < num_to_select <= len(ranked_pairs):
                break
            else:
                print(f"错误: 请输入一个介于 1 和 {len(ranked_pairs)} 之间的数字。")
        except ValueError:
            print("错误: 无效输入，请输入一个数字。")

    top_pairs = ranked_pairs[:num_to_select]
    
    print("\n" + "="*50)
    print(f"以下 {len(top_pairs)} 个交易对将被设置为新的白名单:")
    # 为了更美观的打印
    for i in range(0, len(top_pairs), 5):
        print("  ".join(f"`{p}`" for p in top_pairs[i:i+5]))
    print("="*50 + "\n")

    while True:
        confirm = input(f"确认更新配置文件 '{os.path.basename(config_file)}' 和 '{os.path.basename(settings_file)}' 吗? (y/n): ").lower()
        if confirm in ['y', 'yes']:
            update_configuration_files(config_file, settings_file, top_pairs)
            break
        elif confirm in ['n', 'no']:
            print("操作已被用户取消。")
            break
        else:
            print("无效输入，请输入 'y' 或 'n'。")

if __name__ == "__main__":
    print("--- 启动配置更新脚本 (交互模式) ---")
    main()
    print("\n--- 脚本执行完毕 ---") 