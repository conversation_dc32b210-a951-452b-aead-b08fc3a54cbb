"""
核心模型基类
"""
from django.db import models
from django.utils import timezone
import uuid


class TimeStampedModel(models.Model):
    """时间戳基类"""
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        abstract = True


class UUIDModel(models.Model):
    """UUID基类"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    class Meta:
        abstract = True


class SoftDeleteModel(models.Model):
    """软删除基类"""
    is_deleted = models.BooleanField('是否删除', default=False)
    deleted_at = models.DateTimeField('删除时间', null=True, blank=True)
    
    class Meta:
        abstract = True
    
    def delete(self, using=None, keep_parents=False):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save(using=using)
    
    def hard_delete(self, using=None, keep_parents=False):
        """硬删除"""
        super().delete(using=using, keep_parents=keep_parents)


class BaseModel(TimeStampedModel, SoftDeleteModel):
    """基础模型类"""
    
    class Meta:
        abstract = True


class Category(BaseModel):
    """分类模型"""
    name = models.CharField('分类名称', max_length=100, unique=True)
    slug = models.SlugField('URL别名', max_length=100, unique=True)
    description = models.TextField('描述', blank=True)
    icon = models.CharField('图标', max_length=100, blank=True)
    color = models.CharField('颜色', max_length=7, default='#007bff')
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='children',
        verbose_name='父分类'
    )
    sort_order = models.PositiveIntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    
    class Meta:
        verbose_name = '分类'
        verbose_name_plural = '分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name
    
    @property
    def full_name(self):
        """完整分类名称"""
        if self.parent:
            return f"{self.parent.full_name} > {self.name}"
        return self.name


class Tag(BaseModel):
    """标签模型"""
    name = models.CharField('标签名称', max_length=50, unique=True)
    slug = models.SlugField('URL别名', max_length=50, unique=True)
    color = models.CharField('颜色', max_length=7, default='#6c757d')
    usage_count = models.PositiveIntegerField('使用次数', default=0)
    
    class Meta:
        verbose_name = '标签'
        verbose_name_plural = '标签'
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return self.name


class SystemConfig(models.Model):
    """系统配置"""
    key = models.CharField('配置键', max_length=100, unique=True)
    value = models.TextField('配置值')
    description = models.CharField('描述', max_length=200, blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value}"


class OperationLog(models.Model):
    """操作日志"""
    user = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='用户'
    )
    action = models.CharField('操作类型', max_length=50)
    content_type = models.CharField('内容类型', max_length=100, blank=True)
    object_id = models.CharField('对象ID', max_length=100, blank=True)
    description = models.TextField('操作描述')
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    
    class Meta:
        verbose_name = '操作日志'
        verbose_name_plural = '操作日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['action', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user} - {self.action} - {self.created_at}"
