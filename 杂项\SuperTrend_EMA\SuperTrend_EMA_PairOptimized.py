# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these imports ---
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Dict, Optional, Union, Tuple
import logging
from functools import reduce
import json
from pathlib import Path

logger = logging.getLogger(__name__)

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib

# ==========================================
# Supertrend Strategy to Python Bot: 212% Profit (Full Code Inside!)
# https://youtu.be/Z_vIXP0mOJA
# ==========================================

# ================================
# Freqtrade Version
# ================================

"""
freqtrade -V

Operating System:       Linux-5.15.167.4-microsoft-standard-WSL2-x86_64-with-glibc2.36
Python Version:         Python 3.12.9
CCXT Version:           4.4.62

Freqtrade Version:      freqtrade 2025.2
"""

# ================================
# Download Historical Data
# ================================

"""
freqtrade download-data \
    -c user_data/binance_futures_SuperTrend_EMA_PairOptimized.json \
    --timerange 20230101- \
    -t 1m 5m 15m 30m 1h 2h 4h 1d
"""

# ================================
# Backtesting
# ================================

"""
freqtrade backtesting \
    --strategy SuperTrend_EMA_PairOptimized \
    --timeframe 1h \
    --timerange 20240301-20250301 \
    --breakdown month \
    -c user_data/binance_futures_SuperTrend_EMA_PairOptimized.json \
    --max-open-trades 3 \
    --timeframe-detail 5m \
    --cache none 
"""

# ================================
# Start FreqUI Web Interface
# ================================

"""
freqtrade webserver \
    --config user_data/binance_futures_SuperTrend_EMA_PairOptimized.json
"""

class SuperTrend_EMA_PairOptimized(IStrategy):

    def __init__(self, config):
        
        # Initialize the strategy with the given configuration and load pair-specific settings.   
        super().__init__(config)
        self.load_pair_settings()

    def load_pair_settings(self) -> None:
        
        # Get the class name dynamically to locate the appropriate settings file
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            # Attempt to open and load the JSON settings file
            with open(settings_filename, "r") as f:
                self.custom_info = json.load(f)
                logger.info(f"Settings successfully loaded from {settings_filename}.")
                logger.info(f"Settings: {self.custom_info}")

        except FileNotFoundError:
            # Raise an error if the settings file is missing
            raise SystemExit(f"Settings file not found at {settings_filename}. Program will exit.")
        
        except json.JSONDecodeError as e:
            # Raise an error if the JSON file contains invalid data
            raise SystemExit(f"Error decoding JSON from settings file: {e}. Program will exit.")

    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = "1h"
    informative_timeframe = "4h"

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi".
    minimal_roi = {}
    
    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.20

    # Trailing stoploss
    trailing_stop = False
    # trailing_only_offset_is_reached = False
    # trailing_stop_positive = 0.01
    # trailing_stop_positive_offset = 0.0  # Disabled / not configured

    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    @property
    def plot_config(self):
        return {
            "main_plot": {
                "supertrend": {
                    "color": "#4caf50",
                    "type": "line",
                    "fill_to": "close"
                },
                "ema": {
                    "color": "#2962ff",
                    "type": "line"
                }
            },
            "subplots": {
                "ATR": {
                    "atr_percent_4h": {"color": "#b71c1c"},
                    "atr_threshold_low": {"color": "#F7AC06"}
                },
                "ADX": {
                    "adx": {"color": "#f23645"},
                    "dynamic_adx_threshold": {"color": "#F7AC06"}
                }
            }
        }

    def informative_pairs(self):

        # get access to all pairs available in whitelist.
        pairs = self.dp.current_whitelist()

        # Assign tf to each pair so they can be downloaded and cached for strategy.
        informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        
        return informative_pairs
    
    @informative('4h')
    def populate_indicators_4h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_percent"] = (dataframe["atr"] / dataframe["close"]) * 100
        
        return dataframe
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        pair = metadata["pair"]
        
        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
            
            superTrend = pta.supertrend(dataframe['high'], dataframe['low'], dataframe['close'], length=pair_settings["supertrend_length"], multiplier= pair_settings["supertrend_multiplier"])
            dataframe['supertrend'] = superTrend[f'SUPERT_{pair_settings["supertrend_length"]}_{pair_settings["supertrend_multiplier"]}.0']
            dataframe['supertrend_direction'] = superTrend[f'SUPERTd_{pair_settings["supertrend_length"]}_{pair_settings["supertrend_multiplier"]}.0']
            
            dataframe['ema'] = ta.EMA(dataframe, timeperiod=pair_settings["ema_length"])
            dataframe["adx"] = ta.ADX(dataframe)
            
            return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        pair = metadata["pair"]
        
        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
            
            dataframe['atr_threshold_low'] = dataframe['atr_percent_4h'].rolling(window=pair_settings["atr_window"]).quantile(pair_settings["atr_threshold_low_pct"])
            dataframe['dynamic_adx_threshold'] = 20 + (dataframe['atr_percent_4h'] * 2)
            
            ema_value = dataframe['ema']
            distance = ema_value * pair_settings["max_distance_percent"]

            dataframe.loc[
                    (
                        (dataframe['supertrend_direction'] == 1) &
                        (dataframe['close'] > (ema_value + distance)) &
                        (
                            (dataframe['supertrend_direction'].shift(1) == -1) |
                            (dataframe['close'].shift(1) < (ema_value.shift(1) + distance.shift(1)))
                        ) &
                        (dataframe['adx'] > dataframe['dynamic_adx_threshold']) &
                        (dataframe['atr_percent_4h'] >= dataframe['atr_threshold_low']) &
                        (dataframe['volume'] > 0) 
                    ),
                    'enter_long'] = 1
            
            dataframe.loc[
                    (
                        (dataframe['supertrend_direction'] == -1) &
                        (dataframe['close'] < (ema_value - distance)) &
                        (
                            (dataframe['supertrend_direction'].shift(1) == 1) |
                            (dataframe['close'].shift(1) > (ema_value.shift(1) - distance.shift(1)))
                        ) &
                        (dataframe['adx'] > dataframe['dynamic_adx_threshold']) &
                        (dataframe['atr_percent_4h'] >= dataframe['atr_threshold_low']) &
                        (dataframe['volume'] > 0) 
                    ),
                    'enter_short'] = 1
            
            return dataframe
    

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        pair = metadata["pair"]

        # Check if the pair has custom settings defined in self.custom_info
        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
                        
            dataframe.loc[
                (
                    (dataframe['supertrend_direction'] == -1)
                ),
                'exit_long'
            ] = 1
            
            dataframe.loc[
                (
                    (dataframe['supertrend_direction'] == 1)
                ),
                'exit_short'
            ] = 1

            return dataframe

        
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:

        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
            
            return pair_settings["leverage_level"]