'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Layout, Menu, Button, Avatar, Dropdown, Space, Badge, Drawer } from 'antd'
import { 
  HomeOutlined, 
  AppstoreOutlined, 
  UserOutlined, 
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  PlusOutlined,
  MenuOutlined,
  CrownOutlined,
  WalletOutlined
} from '@ant-design/icons'
import { useAuthStore } from '@/store/authStore'
import type { MenuProps } from 'antd'

const { Header: AntHeader } = Layout

const Header = () => {
  const router = useRouter()
  const pathname = usePathname()
  const { user, isAuthenticated, logout, checkAuth } = useAuthStore()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const handleLogout = () => {
    logout()
    router.push('/')
  }

  // 主导航菜单
  const navItems: MenuProps['items'] = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link href="/">首页</Link>,
    },
    {
      key: '/resources',
      icon: <AppstoreOutlined />,
      label: <Link href="/resources">资源库</Link>,
    },
    {
      key: '/categories',
      icon: <AppstoreOutlined />,
      label: <Link href="/categories">分类</Link>,
    },
  ]

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => router.push('/profile'),
    },
    {
      key: 'my-resources',
      icon: <AppstoreOutlined />,
      label: '我的资源',
      onClick: () => router.push('/my-resources'),
    },
    {
      key: 'points',
      icon: <WalletOutlined />,
      label: `积分: ${user?.points || 0}`,
      onClick: () => router.push('/points'),
    },
    {
      type: 'divider',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => router.push('/settings'),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ]

  return (
    <AntHeader className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-8 sticky top-0 z-50">
      <div className="flex items-center justify-between h-full max-w-7xl mx-auto">
        {/* Logo */}
        <div className="flex items-center space-x-8">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">C</span>
            </div>
            <span className="text-xl font-bold text-gray-900 hidden sm:block">
              CMS资源平台
            </span>
          </Link>

          {/* 桌面端导航 */}
          <div className="hidden lg:block">
            <Menu
              mode="horizontal"
              selectedKeys={[pathname]}
              items={navItems}
              className="border-none bg-transparent"
            />
          </div>
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-4">
          {isAuthenticated ? (
            <>
              {/* 上传按钮 */}
              <Link href="/upload">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  className="hidden sm:flex"
                >
                  上传资源
                </Button>
              </Link>

              {/* VIP标识 */}
              {user?.is_vip_active && (
                <Badge.Ribbon text="VIP" color="gold">
                  <div className="w-8 h-6"></div>
                </Badge.Ribbon>
              )}

              {/* 积分显示 */}
              <div className="hidden md:flex items-center space-x-1 text-sm text-gray-600">
                <WalletOutlined />
                <span>{user?.points || 0}</span>
              </div>

              {/* 通知 */}
              <Badge count={0} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  className="text-gray-600"
                />
              </Badge>

              {/* 用户菜单 */}
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space className="cursor-pointer">
                  <Avatar
                    src={user?.avatar}
                    icon={<UserOutlined />}
                    size="default"
                  />
                  <span className="text-gray-700 hidden sm:block">
                    {user?.display_name}
                  </span>
                </Space>
              </Dropdown>
            </>
          ) : (
            <Space>
              <Link href="/login">
                <Button
                  type="text"
                  icon={<LoginOutlined />}
                >
                  登录
                </Button>
              </Link>
              <Link href="/register">
                <Button type="primary">
                  注册
                </Button>
              </Link>
            </Space>
          )}

          {/* 移动端菜单按钮 */}
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuOpen(true)}
            className="lg:hidden"
          />
        </div>
      </div>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="菜单"
        placement="right"
        onClose={() => setMobileMenuOpen(false)}
        open={mobileMenuOpen}
        width={280}
      >
        <Menu
          mode="vertical"
          selectedKeys={[pathname]}
          items={navItems}
          className="border-none"
          onClick={() => setMobileMenuOpen(false)}
        />
        
        {isAuthenticated && (
          <>
            <div className="my-4 border-t border-gray-200"></div>
            <Menu
              mode="vertical"
              items={userMenuItems}
              className="border-none"
              onClick={() => setMobileMenuOpen(false)}
            />
          </>
        )}
      </Drawer>
    </AntHeader>
  )
}

export default Header
