"""
ScoringStrategy6 KAMA Minimal - 极简KAMA震荡过滤版本

基于ADX Slope版本的成功，添加极简的KAMA震荡过滤：
- 完全保持ADX Slope版本的所有成功要素
- 保持VWAP价格合理性验证和ADX斜率分析
- 只添加最核心的KAMA震荡识别功能
- 极简设计，避免过度复杂化
"""

import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy.interface import IStrategy
from freqtrade.strategy import DecimalParameter, IntParameter
from pandas import DataFrame
from typing import Optional
import logging
import traceback
from freqtrade.persistence import Trade
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ScoringStrategy6(IStrategy):
    """
    ScoringStrategy6 KAMA Minimal - 极简KAMA震荡过滤版本
    
    改进策略：
    1. 完全基于ADX Slope版本的成功框架
    2. 保持VWAP价格合理性验证和ADX斜率分析
    3. 只添加最核心的KAMA震荡识别功能
    4. 极简设计，保守权重，避免过度影响
    """
    
    # 策略基本参数 - 完全继承ADX Slope版本
    minimal_roi = {
        "0": 0.05,
        "15": 0.03,
        "30": 0.02,
        "60": 0.01
    }
    
    stoploss = -0.15
    timeframe = '1h'
    startup_candle_count: int = 100
    
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False
    
    # 入场阈值 - 继承ADX Slope版本的成功设置
    entry_score_threshold = DecimalParameter(1.0, 3.0, default=1.5, space="buy", optimize=True)
    exit_score = DecimalParameter(-10.0, -2.0, default=-3.0, space="sell", optimize=True)
    
    # 过滤器参数
    low_volume_threshold = DecimalParameter(0.5, 1.2, default=0.8, space="buy", optimize=True)
    
    # 核心指标参数 - 完全继承ADX Slope版本
    macd_fast = IntParameter(5, 15, default=7, space="buy", optimize=True)
    macd_slow = IntParameter(15, 30, default=26, space="buy", optimize=True)
    macd_signal = IntParameter(5, 20, default=19, space="buy", optimize=True)
    
    adx_period = IntParameter(10, 30, default=26, space="buy", optimize=True)
    adx_threshold = IntParameter(15, 35, default=29, space="buy", optimize=True)
    
    rsi_period = IntParameter(4, 14, default=13, space="buy", optimize=True)
    rsi_oversold = IntParameter(20, 40, default=38, space="buy", optimize=True)
    rsi_overbought = IntParameter(65, 85, default=69, space="buy", optimize=True)
    
    mfi_period = IntParameter(10, 30, default=12, space="buy", optimize=True)
    mfi_oversold = IntParameter(5, 20, default=11, space="buy", optimize=True)
    mfi_overbought = IntParameter(60, 80, default=62, space="buy", optimize=True)
    
    stoch_k = IntParameter(5, 25, default=19, space="buy", optimize=True)
    stoch_d = IntParameter(3, 15, default=13, space="buy", optimize=True)
    stoch_oversold = IntParameter(15, 35, default=28, space="buy", optimize=True)
    stoch_overbought = IntParameter(65, 85, default=80, space="buy", optimize=True)
    
    stoch_rsi_k = IntParameter(1, 10, default=3, space="buy", optimize=True)
    stoch_rsi_d = IntParameter(1, 10, default=2, space="buy", optimize=True)
    stoch_rsi_period = IntParameter(10, 30, default=19, space="buy", optimize=True)
    stoch_rsi_oversold = IntParameter(10, 40, default=40, space="buy", optimize=True)
    stoch_rsi_overbought = IntParameter(75, 95, default=76, space="buy", optimize=True)
    
    bb_period = IntParameter(20, 50, default=38, space="buy", optimize=True)
    bb_std = DecimalParameter(1.5, 3.0, default=2.183, space="buy", optimize=True)
    
    ema_13 = IntParameter(10, 20, default=11, space="buy", optimize=True)
    ema_20 = IntParameter(20, 40, default=34, space="buy", optimize=True)
    ema_34 = IntParameter(30, 50, default=38, space="buy", optimize=True)
    
    # 核心指标权重 - 完全继承ADX Slope版本
    macd_line_cross_weight = DecimalParameter(1.0, 5.0, default=1.373, space="buy", optimize=True)
    macd_hist_weight = DecimalParameter(0.5, 2.0, default=1.371, space="buy", optimize=True)
    adx_di_cross_weight = DecimalParameter(1.0, 4.0, default=1.491, space="buy", optimize=True)
    ema_cross_weight = DecimalParameter(1.0, 4.0, default=1.7, space="buy", optimize=True)
    ema_price_cross_weight = DecimalParameter(0.5, 3.0, default=1.439, space="buy", optimize=True)
    bb_band_weight = DecimalParameter(0.5, 3.0, default=2.999, space="buy", optimize=True)
    bb_middle_cross_weight = DecimalParameter(0.5, 2.0, default=0.911, space="buy", optimize=True)
    rsi_weight = DecimalParameter(0.5, 3.0, default=1.991, space="buy", optimize=True)
    mfi_weight = DecimalParameter(0.5, 3.0, default=0.754, space="buy", optimize=True)
    stoch_weight = DecimalParameter(0.5, 3.0, default=0.694, space="buy", optimize=True)
    stoch_rsi_weight = DecimalParameter(0.5, 3.0, default=1.603, space="buy", optimize=True)
    
    # 继承的验证指标权重
    cmf_weight = DecimalParameter(0.3, 1.0, default=0.5, space="buy", optimize=True)
    structure_weight = DecimalParameter(0.3, 1.0, default=0.5, space="buy", optimize=True)
    vwap_weight = DecimalParameter(0.2, 0.8, default=0.4, space="buy", optimize=True)
    
    # 继承ADX斜率相关参数
    adx_slope_period = IntParameter(3, 8, default=5, space="buy", optimize=True)
    adx_slope_threshold = DecimalParameter(0.3, 1.5, default=0.8, space="buy", optimize=True)
    adx_slope_strengthening_weight = DecimalParameter(0.3, 1.2, default=0.6, space="buy", optimize=True)
    adx_slope_weakening_weight = DecimalParameter(0.3, 1.2, default=0.5, space="buy", optimize=True)
    adx_dynamic_threshold_enabled = True
    adx_slope_multiplier = DecimalParameter(0.5, 2.0, default=1.2, space="buy", optimize=True)
    
    # === 极简KAMA参数 ===
    # 只保留最核心的参数
    kama_period = IntParameter(10, 30, default=20, space="buy", optimize=True)  # 使用talib默认
    kama_oscillation_penalty = DecimalParameter(0.1, 0.4, default=0.2, space="buy", optimize=True)  # 极保守
    kama_oscillation_threshold = DecimalParameter(0.002, 0.008, default=0.005, space="buy", optimize=True)  # 震荡阈值
    
    # 继承的指标参数
    cmf_period = IntParameter(14, 28, default=20, space="buy", optimize=True)
    cmf_threshold = DecimalParameter(0.05, 0.15, default=0.1, space="buy", optimize=True)
    support_resistance_period = IntParameter(15, 35, default=25, space="buy", optimize=True)
    vwap_period = IntParameter(14, 28, default=20, space="buy", optimize=True)
    vwap_deviation_threshold = DecimalParameter(0.015, 0.04, default=0.025, space="buy", optimize=True)
    
    # 协同效应加分参数 - 继承ADX Slope版本
    synergy_macd_rsi_bonus = DecimalParameter(1.0, 4.0, default=3.444, space="buy", optimize=True)
    synergy_adx_di_bonus = DecimalParameter(0.5, 2.0, default=1.574, space="buy", optimize=True)
    synergy_rsi_mfi_bonus = DecimalParameter(0.5, 3.0, default=2.828, space="buy", optimize=True)
    synergy_volume_bonus = DecimalParameter(0.5, 2.0, default=1.867, space="buy", optimize=True)
    synergy_momentum_bonus = DecimalParameter(1.0, 4.0, default=3.411, space="buy", optimize=True)
    synergy_vwap_volume_bonus = DecimalParameter(0.3, 1.0, default=0.5, space="buy", optimize=True)
    synergy_adx_slope_bonus = DecimalParameter(0.3, 1.5, default=0.8, space="buy", optimize=True)
    
    # 过滤器参数 - 继承ADX Slope版本
    low_volume_penalty = DecimalParameter(0.5, 2.0, default=1.0, space="buy", optimize=True)
    adx_weak_penalty = DecimalParameter(0.5, 2.0, default=1.0, space="buy", optimize=True)
    adx_strong_bonus = DecimalParameter(0.5, 2.0, default=1.0, space="buy", optimize=True)
    
    # 风险管理参数 - 继承ADX Slope版本
    atr_period = IntParameter(7, 21, default=14, space="sell", optimize=True)
    atr_multiplier = DecimalParameter(1.5, 2.5, default=2.0, space="sell", optimize=True)
    adx_exit_threshold = IntParameter(15, 30, default=20, space="sell", optimize=True)
    rsi_exit_overbought = IntParameter(50, 75, default=60, space="sell", optimize=True)
    
    # 强化止损参数 - 继承ADX Slope版本
    max_holding_hours = IntParameter(24, 72, default=48, space="sell", optimize=True)
    emergency_stop_loss = DecimalParameter(-0.12, -0.08, default=-0.10, space="sell", optimize=True)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        指标计算 - 基于ADX Slope版本 + 极简KAMA
        """
        # === 完全继承ADX Slope版本的所有指标 ===
        
        # MACD
        macd = ta.MACD(dataframe, 
                       fastperiod=self.macd_fast.value, 
                       slowperiod=self.macd_slow.value, 
                       signalperiod=self.macd_signal.value)
        dataframe['macd'] = macd['macd']
        dataframe['macdsignal'] = macd['macdsignal']
        dataframe['macdhist'] = macd['macdhist']
        
        # ADX & DI
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_period.value)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=self.adx_period.value)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=self.adx_period.value)
        
        # EMA
        dataframe['ema13'] = ta.EMA(dataframe, timeperiod=self.ema_13.value)
        dataframe['ema20'] = ta.EMA(dataframe, timeperiod=self.ema_20.value)
        dataframe['ema34'] = ta.EMA(dataframe, timeperiod=self.ema_34.value)
        
        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), 
                                           window=self.bb_period.value, 
                                           stds=self.bb_std.value)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']
        
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        
        # MFI
        dataframe['mfi'] = ta.MFI(dataframe, timeperiod=self.mfi_period.value)
        
        # Stochastic
        stoch = ta.STOCH(dataframe, 
                        fastk_period=self.stoch_k.value,
                        slowk_period=1,
                        slowk_matype=0,
                        slowd_period=self.stoch_d.value,
                        slowd_matype=0)
        dataframe['slowk'] = stoch['slowk']
        dataframe['slowd'] = stoch['slowd']
        
        # Stochastic RSI
        stoch_rsi = ta.STOCHRSI(dataframe, 
                               timeperiod=self.stoch_rsi_period.value,
                               fastk_period=self.stoch_rsi_k.value,
                               fastd_period=self.stoch_rsi_d.value)
        dataframe['fastk'] = stoch_rsi['fastk']
        dataframe['fastd'] = stoch_rsi['fastd']
        
        # 交易量分析
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_mean']
        
        # ATR
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        
        # === 继承ADX Slope版本的验证指标 ===
        
        # Chaikin Money Flow
        try:
            mfv = ((dataframe['close'] - dataframe['low']) - (dataframe['high'] - dataframe['close'])) / \
                  (dataframe['high'] - dataframe['low']) * dataframe['volume']
            mfv = mfv.fillna(0)
            dataframe['cmf'] = mfv.rolling(self.cmf_period.value).sum() / \
                              dataframe['volume'].rolling(self.cmf_period.value).sum()
            dataframe['cmf'] = dataframe['cmf'].fillna(0)
        except:
            dataframe['cmf'] = 0
        
        # 支撑阻力位
        try:
            period = self.support_resistance_period.value
            dataframe['resistance'] = dataframe['high'].rolling(period).max()
            dataframe['support'] = dataframe['low'].rolling(period).min()
            
            range_val = dataframe['resistance'] - dataframe['support']
            range_val = range_val.replace(0, 1)
            dataframe['price_position'] = (dataframe['close'] - dataframe['support']) / range_val
            dataframe['price_position'] = dataframe['price_position'].fillna(0.5).clip(0, 1)
        except:
            dataframe['price_position'] = 0.5
        
        # === 继承VWAP验证指标 ===
        
        # VWAP计算
        try:
            typical_price = (dataframe['high'] + dataframe['low'] + dataframe['close']) / 3
            vwap_numerator = (typical_price * dataframe['volume']).rolling(self.vwap_period.value).sum()
            vwap_denominator = dataframe['volume'].rolling(self.vwap_period.value).sum()
            dataframe['vwap'] = vwap_numerator / vwap_denominator
            dataframe['vwap'] = dataframe['vwap'].fillna(dataframe['close'])
            
            # VWAP偏离度
            dataframe['vwap_deviation'] = abs(dataframe['close'] - dataframe['vwap']) / dataframe['vwap']
            
            # VWAP信号
            dataframe['vwap_reasonable'] = dataframe['vwap_deviation'] < self.vwap_deviation_threshold.value
            
        except Exception as e:
            logger.warning(f"VWAP计算失败: {e}")
            dataframe['vwap_reasonable'] = True
            dataframe['vwap_deviation'] = 0
        
        # === 继承ADX斜率分析 ===
        self.calculate_adx_slope_indicators(dataframe)
        
        # === 新增：极简KAMA分析 ===
        self.calculate_minimal_kama_indicators(dataframe)
        
        return dataframe

    def calculate_adx_slope_indicators(self, dataframe: DataFrame) -> None:
        """
        ADX斜率分析 - 完全继承ADX Slope版本
        """
        try:
            slope_period = self.adx_slope_period.value
            dataframe['adx_slope'] = dataframe['adx'].diff(slope_period) / slope_period
            dataframe['adx_slope_smooth'] = dataframe['adx_slope'].rolling(3).mean()

            dataframe['adx_strengthening'] = (
                (dataframe['adx'] > 20) &
                (dataframe['adx_slope_smooth'] > self.adx_slope_threshold.value)
            )

            dataframe['adx_weakening'] = (
                (dataframe['adx'] > 15) &
                (dataframe['adx_slope_smooth'] < -self.adx_slope_threshold.value)
            )

            dataframe['adx_stable'] = (
                (dataframe['adx'] > 25) &
                (abs(dataframe['adx_slope_smooth']) <= self.adx_slope_threshold.value * 0.5)
            )

            if self.adx_dynamic_threshold_enabled:
                base_threshold = self.adx_threshold.value
                dynamic_threshold = np.where(
                    dataframe['adx_strengthening'],
                    base_threshold * 0.85,
                    np.where(
                        dataframe['adx_weakening'],
                        base_threshold * 1.15,
                        base_threshold
                    )
                )
                dataframe['adx_dynamic_threshold'] = dynamic_threshold
            else:
                dataframe['adx_dynamic_threshold'] = self.adx_threshold.value

            dataframe['adx_quality'] = 0
            dataframe.loc[dataframe['adx_strengthening'], 'adx_quality'] += 2
            dataframe.loc[dataframe['adx_stable'], 'adx_quality'] += 1
            dataframe.loc[dataframe['adx_weakening'], 'adx_quality'] -= 1

            strong_rising_adx = (
                (dataframe['adx'] > 40) &
                (dataframe['adx_strengthening'])
            )
            dataframe.loc[strong_rising_adx, 'adx_quality'] += 1

            dataframe['strong_trend_confirmed'] = (
                (dataframe['adx'] > 30) &
                (dataframe['adx_strengthening']) &
                (dataframe['plus_di'] > dataframe['minus_di'])
            )

            dataframe['trend_exhaustion_warning'] = (
                (dataframe['adx'] > 35) &
                (dataframe['adx_weakening']) &
                (abs(dataframe['plus_di'] - dataframe['minus_di']) < 5)
            )

        except Exception as e:
            logger.warning(f"ADX斜率计算失败: {e}")
            dataframe['adx_slope'] = 0
            dataframe['adx_slope_smooth'] = 0
            dataframe['adx_strengthening'] = False
            dataframe['adx_weakening'] = False
            dataframe['adx_stable'] = False
            dataframe['adx_dynamic_threshold'] = self.adx_threshold.value
            dataframe['adx_quality'] = 0
            dataframe['strong_trend_confirmed'] = False
            dataframe['trend_exhaustion_warning'] = False

    def calculate_minimal_kama_indicators(self, dataframe: DataFrame) -> None:
        """
        极简KAMA分析 - 只保留最核心的震荡识别功能
        """
        try:
            # === 1. 使用talib计算KAMA ===
            dataframe['kama'] = ta.KAMA(dataframe, timeperiod=self.kama_period.value)

            # === 2. 简单的KAMA斜率计算 ===
            # 只计算基本斜率，不做复杂的平滑处理
            dataframe['kama_slope'] = dataframe['kama'].diff(3) / 3  # 固定3期斜率

            # === 3. 极简震荡识别 ===
            # 只有一个核心功能：识别震荡期
            dataframe['kama_oscillation'] = (
                abs(dataframe['kama_slope']) < self.kama_oscillation_threshold.value
            )

            # === 4. 简单的趋势方向 ===
            # 只用于基本的方向判断
            dataframe['kama_uptrend'] = dataframe['kama_slope'] > 0

            # === 5. 价格与KAMA关系 ===
            # 只保留最基本的位置关系
            dataframe['price_above_kama'] = dataframe['close'] > dataframe['kama']

        except Exception as e:
            logger.warning(f"极简KAMA计算失败: {e}")
            # 设置默认值，确保策略不会因KAMA失败而崩溃
            dataframe['kama'] = dataframe['close']  # 默认等于收盘价
            dataframe['kama_slope'] = 0
            dataframe['kama_oscillation'] = False
            dataframe['kama_uptrend'] = True
            dataframe['price_above_kama'] = True

    def calculate_indicator_signals(self, dataframe: DataFrame) -> DataFrame:
        """
        指标信号计算 - 基于ADX Slope版本，极简KAMA增强
        """
        # 初始化分数列
        dataframe['score'] = 0.0

        # === 完全继承ADX Slope版本的所有信号逻辑 ===

        # MACD 信号
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal'])),
            'score'
        ] += self.macd_line_cross_weight.value

        dataframe.loc[dataframe['macdhist'] > 0, 'score'] += self.macd_hist_weight.value
        dataframe.loc[dataframe['macdhist'] < 0, 'score'] -= self.macd_hist_weight.value

        # === ADX & DI 信号 - 使用动态阈值增强 ===
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['plus_di'], dataframe['minus_di'])) &
            (dataframe['adx'] > dataframe['adx_dynamic_threshold']),
            'score'
        ] += self.adx_di_cross_weight.value

        # EMA 交叉信号
        dataframe.loc[
            qtpylib.crossed_above(dataframe['ema13'], dataframe['ema34']),
            'score'
        ] += self.ema_cross_weight.value

        dataframe.loc[
            qtpylib.crossed_above(dataframe['close'], dataframe['ema20']),
            'score'
        ] += self.ema_price_cross_weight.value

        # 布林带信号
        dataframe.loc[dataframe['close'] < dataframe['bb_lowerband'], 'score'] += self.bb_band_weight.value
        dataframe.loc[dataframe['close'] > dataframe['bb_upperband'], 'score'] -= self.bb_band_weight.value

        dataframe.loc[
            qtpylib.crossed_above(dataframe['close'], dataframe['bb_middleband']),
            'score'
        ] += self.bb_middle_cross_weight.value

        # RSI 信号
        dataframe.loc[dataframe['rsi'] < self.rsi_oversold.value, 'score'] += self.rsi_weight.value
        dataframe.loc[dataframe['rsi'] > self.rsi_overbought.value, 'score'] -= self.rsi_weight.value

        # MFI 信号
        dataframe.loc[dataframe['mfi'] < self.mfi_oversold.value, 'score'] += self.mfi_weight.value
        dataframe.loc[dataframe['mfi'] > self.mfi_overbought.value, 'score'] -= self.mfi_weight.value

        # Stochastic 信号
        dataframe.loc[
            (dataframe['slowk'] < self.stoch_oversold.value) &
            (dataframe['slowd'] < self.stoch_oversold.value),
            'score'
        ] += self.stoch_weight.value

        dataframe.loc[
            (dataframe['slowk'] > self.stoch_overbought.value) &
            (dataframe['slowd'] > self.stoch_overbought.value),
            'score'
        ] -= self.stoch_weight.value

        # Stochastic RSI 信号
        dataframe.loc[
            (dataframe['fastk'] < self.stoch_rsi_oversold.value) &
            (dataframe['fastd'] < self.stoch_rsi_oversold.value),
            'score'
        ] += self.stoch_rsi_weight.value

        dataframe.loc[
            (dataframe['fastk'] > self.stoch_rsi_overbought.value) &
            (dataframe['fastd'] > self.stoch_rsi_overbought.value),
            'score'
        ] -= self.stoch_rsi_weight.value

        # === 继承所有验证指标信号 ===

        # Chaikin Money Flow 信号
        dataframe.loc[dataframe['cmf'] > self.cmf_threshold.value, 'score'] += self.cmf_weight.value
        dataframe.loc[dataframe['cmf'] < -self.cmf_threshold.value, 'score'] -= self.cmf_weight.value

        # 价格位置信号
        dataframe.loc[dataframe['price_position'] < 0.2, 'score'] += self.structure_weight.value
        dataframe.loc[dataframe['price_position'] > 0.8, 'score'] -= self.structure_weight.value

        # VWAP验证信号
        dataframe.loc[
            (dataframe['vwap_reasonable']) &
            (dataframe['close'] > dataframe['vwap']),
            'score'
        ] += self.vwap_weight.value

        dataframe.loc[
            ~dataframe['vwap_reasonable'],
            'score'
        ] -= self.vwap_weight.value * 0.5

        # ADX斜率信号
        dataframe.loc[dataframe['adx_strengthening'], 'score'] += self.adx_slope_strengthening_weight.value
        dataframe.loc[dataframe['adx_weakening'], 'score'] -= self.adx_slope_weakening_weight.value
        dataframe.loc[dataframe['strong_trend_confirmed'], 'score'] += self.adx_slope_strengthening_weight.value * 0.5
        dataframe.loc[dataframe['trend_exhaustion_warning'], 'score'] -= self.adx_slope_weakening_weight.value * 0.8

        # === 极简KAMA信号 - 只有震荡过滤 ===
        # 不添加任何正面信号，只做负面过滤
        # 这是最保守的做法，避免破坏原有逻辑

        return dataframe

    def apply_synergy_bonuses(self, dataframe: DataFrame) -> DataFrame:
        """
        协同效应加分 - 完全继承ADX Slope版本，不添加KAMA协同
        """
        # === 完全继承ADX Slope版本的所有协同效应 ===

        # 看涨MACD交叉 + RSI < 35
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal'])) &
            (dataframe['rsi'] < 35),
            'score'
        ] += self.synergy_macd_rsi_bonus.value

        # ADX > 21 & plusDI > minusDI
        dataframe.loc[
            (dataframe['adx'] > 21) &
            (dataframe['plus_di'] > dataframe['minus_di']),
            'score'
        ] += self.synergy_adx_di_bonus.value

        # RSI < 30 & MFI < 30
        dataframe.loc[
            (dataframe['rsi'] < 30) &
            (dataframe['mfi'] < 30),
            'score'
        ] += self.synergy_rsi_mfi_bonus.value

        # 交易量上升 > 20%
        dataframe.loc[dataframe['volume_ratio'] > 1.2, 'score'] += self.synergy_volume_bonus.value

        # 多个动量指标同时看涨
        dataframe.loc[
            (dataframe['slowk'] < self.stoch_oversold.value) &
            (dataframe['fastk'] < self.stoch_rsi_oversold.value) &
            (dataframe['rsi'] < self.rsi_oversold.value),
            'score'
        ] += self.synergy_momentum_bonus.value

        # VWAP合理性 + 成交量确认
        dataframe.loc[
            (dataframe['vwap_reasonable']) &
            (dataframe['volume_ratio'] > 1.1),
            'score'
        ] += self.synergy_vwap_volume_bonus.value

        # ADX强化 + 动量指标协同
        dataframe.loc[
            (dataframe['adx_strengthening']) &
            (dataframe['rsi'] < self.rsi_oversold.value) &
            (dataframe['plus_di'] > dataframe['minus_di']),
            'score'
        ] += self.synergy_adx_slope_bonus.value

        # ADX强化 + MACD交叉
        dataframe.loc[
            (dataframe['adx_strengthening']) &
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal'])),
            'score'
        ] += self.synergy_adx_slope_bonus.value * 0.8

        # 强趋势确认 + VWAP合理 + 成交量支撑
        perfect_trend_signal = (
            (dataframe['strong_trend_confirmed']) &
            (dataframe['vwap_reasonable']) &
            (dataframe['volume_ratio'] > 1.2)
        )
        dataframe.loc[perfect_trend_signal, 'score'] += 1.2

        # ADX稳定 + 价格突破
        stable_trend_breakout = (
            (dataframe['adx_stable']) &
            (qtpylib.crossed_above(dataframe['close'], dataframe['ema20']))
        )
        dataframe.loc[stable_trend_breakout, 'score'] += self.synergy_adx_slope_bonus.value * 0.6

        # === 不添加任何KAMA协同效应 ===
        # 保持极简设计，避免复杂化

        return dataframe

    def apply_filters(self, dataframe: DataFrame) -> DataFrame:
        """
        过滤器 - 基于ADX Slope版本 + 极简KAMA震荡过滤
        """
        # === 完全继承ADX Slope版本的过滤器 ===

        # 交易量过滤器
        dataframe.loc[
            dataframe['volume_ratio'] < self.low_volume_threshold.value,
            'score'
        ] -= self.low_volume_penalty.value

        # ADX过滤器
        dataframe.loc[dataframe['adx'] < 15, 'score'] -= self.adx_weak_penalty.value
        dataframe.loc[dataframe['adx'] > 35, 'score'] += self.adx_strong_bonus.value

        # 趋势衰竭惩罚
        dataframe.loc[dataframe['trend_exhaustion_warning'], 'score'] -= 0.8

        # ADX质量过滤
        dataframe.loc[dataframe['adx_quality'] < -1, 'score'] -= 0.5
        dataframe.loc[dataframe['adx_quality'] > 2, 'score'] += 0.3

        # === 极简KAMA震荡过滤 ===
        # 只有一个功能：震荡期轻微减分
        dataframe.loc[dataframe['kama_oscillation'], 'score'] -= self.kama_oscillation_penalty.value

        return dataframe

    def calculate_final_score(self, dataframe: DataFrame, pair: str) -> Optional[DataFrame]:
        """
        计算最终分数 - 继承ADX Slope版本逻辑
        """
        try:
            dataframe = self.calculate_indicator_signals(dataframe)
            dataframe = self.apply_synergy_bonuses(dataframe)
            dataframe = self.apply_filters(dataframe)
            return dataframe
        except Exception as e:
            logger.error(f"在为 {pair} 计算分数时发生错误: {type(e).__name__}: {str(e)}")
            logger.error(f"异常详细信息: {traceback.format_exc()}")
            return None

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场逻辑 - 完全继承ADX Slope版本
        """
        pair = metadata['pair']
        dataframe = self.calculate_final_score(dataframe, pair)
        if dataframe is None:
            return dataframe

        conditions = (
            (dataframe['score'] > self.entry_score_threshold.value) &
            (dataframe['volume'] > 0)
        )

        dataframe.loc[conditions, 'enter_long'] = 1
        dataframe.loc[conditions, 'enter_tag'] = 'score_' + dataframe.loc[conditions, 'score'].round(2).astype(str)

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场逻辑 - 完全继承ADX Slope版本，不添加KAMA出场
        """
        if 'score' not in dataframe.columns:
            logger.warning(f"{metadata['pair']} 在退出逻辑中缺少 'score' 列，将重新计算。")
            df_scored = self.calculate_final_score(dataframe, metadata['pair'])
            if df_scored is not None:
                dataframe = df_scored
            else:
                logger.warning(f"无法为 {metadata['pair']} 计算退出分数，将跳过基于分数的退出检查。")
                if 'exit_long' not in dataframe.columns:
                    dataframe['exit_long'] = 0
                return dataframe

        # === 完全继承ADX Slope版本的所有出场逻辑 ===

        # 基于分数的出场
        if 'score' in dataframe.columns:
            dataframe.loc[
                (dataframe['score'] < self.exit_score.value) &
                (dataframe['volume'] > 0),
                'exit_long'
            ] = 1

        # ATR动态止损保护
        if all(c in dataframe.columns for c in ['close', 'volume', 'atr']):
            if len(dataframe) > self.atr_period.value:
                dataframe.loc[
                    (dataframe['close'] < dataframe['close'].shift(1) - dataframe['atr'] * self.atr_multiplier.value) &
                    (dataframe['volume'] > 0),
                    'exit_long'
                ] = 1

        # 关键支撑位破位出场
        if all(c in dataframe.columns for c in ['close', 'ema20', 'ema13', 'plus_di', 'minus_di', 'adx']):
            dataframe.loc[
                (dataframe['close'] < dataframe['ema20']) &
                (dataframe['close'] < dataframe['ema13']) &
                (dataframe['plus_di'] < dataframe['minus_di']) &
                (dataframe['adx'] > self.adx_exit_threshold.value),
                'exit_long'
            ] = 1

        # 布林带上轨回落出场
        if all(c in dataframe.columns for c in ['close', 'bb_middleband', 'bb_upperband', 'rsi']):
            dataframe.loc[
                (dataframe['close'] > dataframe['bb_middleband']) &
                (dataframe['close'].shift(1) > dataframe['bb_upperband']) &
                (dataframe['close'] < dataframe['close'].shift(1)) &
                (dataframe['rsi'] > self.rsi_exit_overbought.value),
                'exit_long'
            ] = 1

        # 趋势衰竭出场
        if 'trend_exhaustion_warning' in dataframe.columns:
            dataframe.loc[dataframe['trend_exhaustion_warning'], 'exit_long'] = 1

        # ADX急剧下降出场
        if 'adx_slope_smooth' in dataframe.columns:
            rapid_adx_decline = (
                (dataframe['adx'] > 30) &
                (dataframe['adx_slope_smooth'] < -1.5)
            )
            dataframe.loc[rapid_adx_decline, 'exit_long'] = 1

        # === 不添加任何KAMA出场信号 ===
        # 保持ADX Slope版本的简洁出场逻辑

        return dataframe

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        动态止损机制 - 完全继承ADX Slope版本
        """
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return self.stoploss

            trade_duration = current_time - trade.open_date_utc

            if current_profit < self.emergency_stop_loss.value:
                logger.warning(f"{pair} 触发紧急止损: {current_profit:.2%}")
                return self.emergency_stop_loss.value

            if trade_duration > timedelta(hours=self.max_holding_hours.value) and current_profit < 0:
                logger.warning(f"{pair} 触发时间止损: 持仓{trade_duration}, 亏损{current_profit:.2%}")
                return -0.01

            if trade_duration < timedelta(hours=1):
                return -0.04
            elif trade_duration < timedelta(hours=6):
                return -0.06
            else:
                if len(dataframe) > self.atr_period.value and 'atr' in dataframe.columns:
                    current_atr = dataframe['atr'].iloc[-1]
                    if not pd.isna(current_atr) and current_atr > 0:
                        atr_stop = -(current_atr * self.atr_multiplier.value) / current_rate
                        return max(atr_stop, self.stoploss)
                return self.stoploss

        except Exception as e:
            logger.error(f"动态止损计算失败 {pair}: {e}")
            return self.stoploss

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                           proposed_stake: float, min_stake: float, max_stake: float,
                           leverage: float, entry_tag: str, side: str, **kwargs) -> float:
        """
        仓位管理 - 完全继承ADX Slope版本，不添加KAMA调整
        """
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return proposed_stake

            current_score = dataframe['score'].iloc[-1] if 'score' in dataframe.columns else 2.0

            if current_score > 4.0:
                multiplier = 1.2
            elif current_score > 3.0:
                multiplier = 1.1
            elif current_score > 2.5:
                multiplier = 1.0
            elif current_score > 2.0:
                multiplier = 0.9
            else:
                multiplier = 0.8

            current_adx = dataframe['adx'].iloc[-1] if 'adx' in dataframe.columns else 25

            if current_adx > 35:
                multiplier *= 1.05
            elif current_adx < 20:
                multiplier *= 0.95

            # === 不添加任何KAMA仓位调整 ===
            # 保持ADX Slope版本的简洁仓位管理

            multiplier = max(0.7, min(multiplier, 1.25))

            final_stake = proposed_stake * multiplier
            final_stake = max(min_stake, min(final_stake, max_stake))

            return final_stake

        except Exception as e:
            logger.error(f"仓位管理计算失败 {pair}: {e}")
            return proposed_stake
