"""
资源管理后台
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from apps.core.models import Category, Tag
from .models import (
    Resource, ResourceImage, ResourceDownload, ResourceRating,
    ResourceComment, Collection, CollectionResource
)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['sort_order', 'name']
    prepopulated_fields = {'slug': ('name',)}


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ['name', 'color', 'usage_count', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name']
    ordering = ['-usage_count', 'name']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['usage_count']


class ResourceImageInline(admin.TabularInline):
    model = ResourceImage
    extra = 1
    fields = ['image', 'caption', 'sort_order']


@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    inlines = [ResourceImageInline]
    list_display = [
        'title', 'category', 'author', 'status', 'is_featured',
        'required_points', 'view_count', 'download_count', 'created_at'
    ]
    list_filter = [
        'status', 'is_public', 'is_featured', 'is_premium',
        'category', 'created_at', 'author'
    ]
    search_fields = ['title', 'description', 'author__username', 'author__email']
    ordering = ['-created_at']
    readonly_fields = [
        'view_count', 'download_count', 'favorite_count',
        'rating_score', 'rating_count', 'file_size', 'file_type',
        'file_format', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': (
                'title', 'description', 'content', 'category', 'tags'
            )
        }),
        ('文件信息', {
            'fields': (
                'cover_image', 'resource_file', 'file_size', 'file_type',
                'file_format', 'external_url', 'download_url'
            )
        }),
        ('权限设置', {
            'fields': (
                'is_public', 'is_featured', 'is_premium',
                'required_points', 'required_vip'
            )
        }),
        ('状态管理', {
            'fields': (
                'status', 'author', 'reviewer', 'reviewed_at', 'review_note'
            )
        }),
        ('SEO设置', {
            'fields': (
                'slug', 'meta_keywords', 'meta_description'
            ),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': (
                'view_count', 'download_count', 'favorite_count',
                'rating_score', 'rating_count', 'published_at',
                'created_at', 'updated_at'
            ),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ['tags']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'category', 'author'
        ).prefetch_related('tags')
    
    actions = ['make_featured', 'remove_featured', 'publish', 'archive']
    
    def make_featured(self, request, queryset):
        queryset.update(is_featured=True)
        self.message_user(request, f"已将 {queryset.count()} 个资源设为推荐")
    make_featured.short_description = "设为推荐"
    
    def remove_featured(self, request, queryset):
        queryset.update(is_featured=False)
        self.message_user(request, f"已将 {queryset.count()} 个资源取消推荐")
    remove_featured.short_description = "取消推荐"
    
    def publish(self, request, queryset):
        queryset.update(status='published')
        self.message_user(request, f"已发布 {queryset.count()} 个资源")
    publish.short_description = "发布资源"
    
    def archive(self, request, queryset):
        queryset.update(status='archived')
        self.message_user(request, f"已归档 {queryset.count()} 个资源")
    archive.short_description = "归档资源"


@admin.register(ResourceDownload)
class ResourceDownloadAdmin(admin.ModelAdmin):
    list_display = [
        'resource', 'user', 'points_cost', 'is_successful',
        'ip_address', 'created_at'
    ]
    list_filter = ['is_successful', 'created_at']
    search_fields = [
        'resource__title', 'user__username', 'user__email', 'ip_address'
    ]
    ordering = ['-created_at']
    readonly_fields = ['created_at']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(ResourceRating)
class ResourceRatingAdmin(admin.ModelAdmin):
    list_display = ['resource', 'user', 'score', 'created_at']
    list_filter = ['score', 'created_at']
    search_fields = ['resource__title', 'user__username', 'comment']
    ordering = ['-created_at']
    readonly_fields = ['created_at']


@admin.register(ResourceComment)
class ResourceCommentAdmin(admin.ModelAdmin):
    list_display = [
        'resource', 'user', 'content_preview', 'is_approved',
        'parent', 'created_at'
    ]
    list_filter = ['is_approved', 'created_at']
    search_fields = ['resource__title', 'user__username', 'content']
    ordering = ['-created_at']
    readonly_fields = ['created_at']
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '内容预览'
    
    actions = ['approve_comments', 'reject_comments']
    
    def approve_comments(self, request, queryset):
        queryset.update(is_approved=True)
        self.message_user(request, f"已审核通过 {queryset.count()} 条评论")
    approve_comments.short_description = "审核通过"
    
    def reject_comments(self, request, queryset):
        queryset.update(is_approved=False)
        self.message_user(request, f"已拒绝 {queryset.count()} 条评论")
    reject_comments.short_description = "拒绝评论"


@admin.register(Collection)
class CollectionAdmin(admin.ModelAdmin):
    list_display = ['title', 'author', 'is_public', 'view_count', 'created_at']
    list_filter = ['is_public', 'created_at']
    search_fields = ['title', 'description', 'author__username']
    ordering = ['-created_at']
    readonly_fields = ['view_count', 'created_at']
