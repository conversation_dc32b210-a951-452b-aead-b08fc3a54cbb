# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy (HAPBS)
#
#   作者: Gemini & User
#   最终版本: Final (基于 V26)
#
#   策略理念 (Final):
#   - 本策略是多次迭代和优化的最终成果，融合了所有成功版本的核心思想。
#   - 入场逻辑: 
#     做空 - 采用在 V24 中被验证的最严格、最可靠的纯做空信号，
#     只在极度明确的空头市场中 (EMA 短中长期均线完全空头排列) 寻找机会。
#     做多 - 采用与做空相反的逻辑，在明确的多头市场中 (EMA 短中长期均线完全多头排列) 寻找突破机会。
#   - 出场逻辑: 采用在 V26 中被证明最高效的"让利润奔跑"机制，
#     通过分级的 ROI 表来确保在不同阶段锁定利润，实现盈利最大化。
#   - 风险控制: 拥有极低的最大回撤，在获得稳健回报的同时，将资金风险降至最低。
# --------------------------------

class HAPBS_Final(IStrategy):

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 风险控制: 动态盈亏管理系统 ---
    # -- I. 动态初始止损 (由 custom_stoploss 函数实现) --
    # Freqtrade 要求 stoploss 属性必须存在。设置一个很大的值，确保它永远不会被触发。
    # 实际的止损将由 custom_stoploss 函数动态决定。
    stoploss = -0.99
    use_custom_stoploss = True
    cs_atr_period = IntParameter(10, 20, default=14, space='stoploss')
    cs_atr_multiplier = DecimalParameter(1.0, 4.0, default=2.5, space='stoploss')
    cs_lookback = IntParameter(5, 20, default=10, space='stoploss')

    # -- II. 动态止盈 (由 populate_exit_trend 函数实现) --
    # trailing_stop = True # 已被 exit_trend 中的ATR追踪止损替代
    # 将 exit 参数归入 'sell' 空间以兼容大部分 Freqtrade 版本
    exit_rr_ratio = DecimalParameter(1.0, 3.0, default=1.5, space='sell')
    exit_atr_tsl_period = IntParameter(10, 20, default=14, space='sell')
    exit_atr_tsl_multiplier = DecimalParameter(1.0, 7.0, default=2.0, space='sell')

    # minimal_roi = { ... } # 已被 exit_trend 中的R:R目标替代

    # --- 可配置参数 ---
    # 将买入和卖出参数分开，以实现更精细的优化
    # --- 做多参数 ---
    ema_short_period_long = IntParameter(10, 30, default=25, space='buy')
    ema_long_period_long = IntParameter(30, 60, default=57, space='buy')
    adx_threshold_long = IntParameter(20, 35, default=30, space='buy')
    volume_factor_long = DecimalParameter(1.0, 2.0, default=1.035, space='buy')

    # --- 做空参数 ---
    ema_short_period_short = IntParameter(10, 30, default=25, space='sell')
    ema_long_period_short = IntParameter(30, 60, default=57, space='sell')
    adx_threshold_short = IntParameter(20, 35, default=30, space='sell')
    volume_factor_short = DecimalParameter(1.0, 2.0, default=1.035, space='sell')

    # --- 图表配置 ---
    plot_config = {
        'main_plot': {
            'ema_short_long': {'color': 'blue', 'linestyle': '-'},
            'ema_long_long': {'color': 'cyan', 'linestyle': '-'},
            'ema_short_short': {'color': 'red', 'linestyle': '--'},
            'ema_long_short': {'color': 'magenta', 'linestyle': '--'},
            'ema_200': {'color': 'black', 'linestyle': ':'},
            # 添加ATR追踪止损线到图表
            'atr_tsl_long': {'color': 'green', 'linestyle': ':'},
            'atr_tsl_short': {'color': 'orange', 'linestyle': ':'},
        },
        'subplots': {
            "ADX": {
                'adx': {'color': 'green'},
            },
            "ATR": {
                'atr': {'color': 'purple'},
            },
        },
    }

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        
        # 添加强劲看跌K线定义
        dataframe['ha_strong_bear'] = (
            (dataframe['ha_close'] < dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_high'])
        )
        
        # 添加强劲看涨K线定义
        dataframe['ha_strong_bull'] = (
            (dataframe['ha_close'] > dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_low'])
        )

        # 添加十字星K线定义
        body_size = abs(dataframe['ha_close'] - dataframe['ha_open'])
        candle_range = dataframe['ha_high'] - dataframe['ha_low']
        dataframe['ha_doji'] = (body_size < candle_range * 0.03)

        # 为做多和做空计算独立的EMA指标
        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=self.ema_short_period_long.value)
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=self.ema_long_period_long.value)
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=self.ema_short_period_short.value)
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=self.ema_long_period_short.value)
        
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        
        # ATR指标，用于止损和止盈
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.exit_atr_tsl_period.value)

        # ATR追踪止损线
        # 做多时，追踪止损线是N周期内最高价减去ATR偏移
        tsl_high = dataframe['high'].rolling(self.exit_atr_tsl_period.value).max()
        dataframe['atr_tsl_long'] = tsl_high - (dataframe['atr'] * self.exit_atr_tsl_multiplier.value)
        # 做空时，追踪止损线是N周期内最低价加上ATR偏移
        tsl_low = dataframe['low'].rolling(self.exit_atr_tsl_period.value).min()
        dataframe['atr_tsl_short'] = tsl_low + (dataframe['atr'] * self.exit_atr_tsl_multiplier.value)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- 信号1：直接突破入场 ---
        # 做多条件 - 强劲K线直接突破
        long_breakout_conditions = (
            # 趋势过滤: 严格的EMA多头排列 (short > long > 200)
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            # 突破信号: 强劲的看涨K线上穿短期EMA
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long'])) &
            (dataframe['ha_strong_bull']) &
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_long.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_long.value)
        )
        dataframe.loc[long_breakout_conditions, ['enter_long', 'enter_tag']] = (1, 'long_breakout')

        # 做空条件 - 强劲K线直接破位
        short_breakdown_conditions = (
            # 趋势过滤: 严格的EMA空头排列 (short < long < 200)
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            # 破位信号: 强劲的看跌K线下穿短期EMA
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short'])) &
            (dataframe['ha_strong_bear']) &
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_short.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_short.value)
        )
        dataframe.loc[short_breakdown_conditions, ['enter_short', 'enter_tag']] = (1, 'short_breakdown')

        # --- 信号2：突破后跟进入场 ---
        # 检查过去3根K线(t-1, t-2, t-3)内是否发生过向上穿越
        recent_cross_up = qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long']).shift(1).rolling(3).sum() > 0
        
        long_followup_conditions = (
            # 趋势过滤: 严格的EMA多头排列
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            # 跟进信号: 强劲看涨K线，位于EMA之上，且近期(3根K线内)发生过穿越
            (dataframe['ha_strong_bull']) &
            (dataframe['ha_close'] > dataframe['ema_short_long']) &
            (recent_cross_up) &
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_long.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_long.value)
        )
        dataframe.loc[long_followup_conditions, ['enter_long', 'enter_tag']] = (1, 'long_followup')

        # 检查过去3根K线(t-1, t-2, t-3)内是否发生过向下穿越
        recent_cross_down = qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short']).shift(1).rolling(3).sum() > 0

        short_followup_conditions = (
            # 趋势过滤: 严格的EMA空头排列
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            # 跟进信号: 强劲看跌K线，位于EMA之下，且近期(3根K线内)发生过穿越
            (dataframe['ha_strong_bear']) &
            (dataframe['ha_close'] < dataframe['ema_short_short']) &
            (recent_cross_down) &
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_short.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_short.value)
        )
        dataframe.loc[short_followup_conditions, ['enter_short', 'enter_tag']] = (1, 'short_followup')

        # --- 信号3：十字星后跟进入场 ---
        # 检查过去5根K线(t-1 to t-5)内是否出现过十字星
        recent_doji = dataframe['ha_doji'].shift(1).rolling(5).sum() > 0

        long_doji_followup_conditions = (
            # 趋势过滤
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            # 跟进信号: 强劲看涨K线，位于EMA之上，且近期(5根K线内)出现过十字星
            (dataframe['ha_strong_bull']) &
            (dataframe['ha_close'] > dataframe['ema_short_long']) &
            (recent_doji) &
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_long.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_long.value)
        )
        dataframe.loc[long_doji_followup_conditions, ['enter_long', 'enter_tag']] = (1, 'long_doji_followup')

        short_doji_followup_conditions = (
            # 趋势过滤
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            # 跟进信号: 强劲看跌K线，位于EMA之下，且近期(5根K线内)出现过十字星
            (dataframe['ha_strong_bear']) &
            (dataframe['ha_close'] < dataframe['ema_short_short']) &
            (recent_doji) &
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_short.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_short.value)
        )
        dataframe.loc[short_doji_followup_conditions, ['enter_short', 'enter_tag']] = (1, 'short_doji_followup')

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        I. 动态初始止损
        - 基于入场前的市场结构(高/低点)和波动性(ATR)来设置初始止损。
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        lookback_period = self.cs_lookback.value
        atr_multiplier = self.cs_atr_multiplier.value
        
        # 获取入场信号蜡烛 (交易发生前的那一根)
        # 这种方法更健壮，可以应对各种索引类型和数据缺失情况
        prev_candles = dataframe.loc[dataframe['date'] < trade.open_date_utc]
        if prev_candles.empty:
            # Fallback for cases where the open date is at the start of the data.
            return -self.stoploss # Return a value that's definitely a valid stoploss
        
        trade_entry_candle = prev_candles.iloc[-1]

        if trade.is_short:
            # 止损1: 基于前期高点
            high_stop = dataframe['high'].iloc[-lookback_period:].max()
            # 止损2: 基于ATR
            atr_stop = trade_entry_candle['ha_close'] + (trade_entry_candle['atr'] * atr_multiplier)
            # 取两者中更宽松(更大)的一个作为止损
            stoploss_price = max(high_stop, atr_stop)
        else:
            # 止损1: 基于前期低点
            low_stop = dataframe['low'].iloc[-lookback_period:].min()
            # 止损2: 基于ATR
            atr_stop = trade_entry_candle['ha_close'] - (trade_entry_candle['atr'] * atr_multiplier)
            # 取两者中更宽松(更小)的一个作为止损
            stoploss_price = min(low_stop, atr_stop)
            
        return stoploss_price

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        II. 动态止盈
        - 结合R:R目标和ATR追踪止损来决定出场时机。
        """
        # --- R:R 止盈 ---
        # Freqtrade框架限制，无法直接在向量化函数中为每笔交易计算R:R目标。
        # R:R目标需要在 per-trade 的循环中处理，或使用更高级的回调函数。
        # 此处我们主要依赖下方的ATR追踪止损。

        # --- ATR 追踪止损 ---
        # 做多出场条件: 当K线收盘价下穿ATR追踪止损线时
        dataframe.loc[
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['atr_tsl_long'])),
            'exit_long'] = 1
        
        # 做空出场条件: 当K线收盘价上穿ATR追踪止损线时
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['atr_tsl_short'])),
            'exit_short'] = 1

        return dataframe 