"""
用户系统管理后台
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import (
    User, UserGroup, UserProfile, PointsHistory, 
    UserFavorite, UserHistory, SignInRecord
)


@admin.register(UserGroup)
class UserGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_vip', 'download_limit', 'upload_limit', 'sort_order']
    list_filter = ['is_vip', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['sort_order', 'name']


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户资料'


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    inlines = [UserProfileInline]
    list_display = [
        'email', 'username', 'display_name', 'user_group',
        'is_vip', 'points', 'is_active', 'date_joined'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'is_vip',
        'user_group', 'date_joined'
    ]
    search_fields = ['email', 'username', 'nickname', 'first_name', 'last_name']
    ordering = ['-date_joined']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('个人信息', {
            'fields': (
                'nickname', 'avatar', 'bio', 'phone', 
                'birthday', 'gender'
            )
        }),
        ('用户状态', {
            'fields': (
                'user_group', 'is_verified', 'is_vip', 'vip_expire_date'
            )
        }),
        ('积分信息', {
            'fields': (
                'points', 'total_points_earned', 'total_points_spent'
            )
        }),
        ('统计信息', {
            'fields': (
                'download_count', 'upload_count', 'login_count',
                'last_login_ip'
            )
        }),
    )
    
    readonly_fields = [
        'total_points_earned', 'total_points_spent',
        'download_count', 'upload_count', 'login_count',
        'last_login_ip', 'date_joined'
    ]
    
    def display_name(self, obj):
        return obj.display_name
    display_name.short_description = '显示名称'


@admin.register(PointsHistory)
class PointsHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'amount', 'balance', 'type', 'reason', 'created_at'
    ]
    list_filter = ['type', 'created_at']
    search_fields = ['user__email', 'user__username', 'reason']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(UserFavorite)
class UserFavoriteAdmin(admin.ModelAdmin):
    list_display = ['user', 'content_type', 'object_id', 'created_at']
    list_filter = ['content_type', 'created_at']
    search_fields = ['user__email', 'user__username']
    ordering = ['-created_at']


@admin.register(UserHistory)
class UserHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'content_type', 'object_id', 'view_count', 'last_viewed_at'
    ]
    list_filter = ['content_type', 'last_viewed_at']
    search_fields = ['user__email', 'user__username']
    ordering = ['-last_viewed_at']


@admin.register(SignInRecord)
class SignInRecordAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'date', 'points_earned', 'consecutive_days', 'created_at'
    ]
    list_filter = ['date', 'created_at']
    search_fields = ['user__email', 'user__username']
    ordering = ['-date']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
