# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these imports ---
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Dict, Optional, Union, Tuple, List

from freqtrade.optimize.space import Categorical, Dimension, Integer, SKDecimal
from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib
from technical.indicators import ichimoku

# ==========================================
# Ultimate Ichimoku Cloud Strategy [2944% Profit Backtest]
# YouTube Link: https://youtu.be/EumlRRIx0WA
# ==========================================


class Ichimoku(IStrategy):
            
    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = "30m"

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi".
    minimal_roi = {}
    
    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.15
    
    # Trailing stoploss
    trailing_stop = True
    trailing_only_offset_is_reached = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.03  
    
    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    # For 30m timeframe
    conversion_line_periods = IntParameter(5, 50, default=12, space="buy")
    base_line_periods = IntParameter(20, 100, default=32, space="buy")
    atr_mult = RealParameter(1.0, 6.0, default=2.5, space="sell")
    converstion_cross_rolling_window = IntParameter(3, 50, default=15, space="buy")

    # ADX trend filter
    adx_threshold = IntParameter(15, 50, default=25, space="buy")

    # ATR based leverage
    atr_mean_period = IntParameter(50, 250, default=100, space="buy")

    leverage_level = IntParameter(1, 10, default=10, space='buy', optimize=True, load=True)

    @property
    def plot_config(self):

        plot_config = {

            'main_plot': {
                f'conversion_line_{self.conversion_line_periods.value}_{self.base_line_periods.value}': {'color': 'rgb(41, 98, 255)', 'style': 'line', 'width': 2},
                f'base_line_{self.conversion_line_periods.value}_{self.base_line_periods.value}': {'color': 'rgb(183, 28, 28)', 'style': 'line', 'width': 2},
                f'upper_{self.conversion_line_periods.value}_{self.base_line_periods.value}': {
                    'color': 'rgb(165, 214, 167)',
                    'fill_to': f'lower_{self.conversion_line_periods.value}_{self.base_line_periods.value}',
                    'fill_label': 'Ichimoku Cloud',
                    'fill_color': 'rgba(67, 160, 71, 0.1)',
                },
                f'lower_{self.conversion_line_periods.value}_{self.base_line_periods.value}': {
                    'color': 'rgb(239, 154, 154)',
                },
            }
        }
        
        return plot_config
    
    
    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pair/interval combinations are non-tradeable, unless they are part
        of the whitelist as well.
        For more information, please consult the documentation
        :return: List of tuples in the format (pair, interval)
            Sample: return [("ETH/USDT", "5m"),
                            ("BTC/USDT", "15m"),
                            ]
        """
        
        # get access to all pairs available in whitelist.
        # pairs = self.dp.current_whitelist()

        # # Assign tf to each pair so they can be downloaded and cached for strategy.
        # informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        
        return []
    
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        # ADX
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)

        new_columns = {}
        for conversion_line_val in self.conversion_line_periods.range:
            for base_line_val in self.base_line_periods.range:
                
                ichi = ichimoku(dataframe,
                                conversion_line_period=conversion_line_val, 
                                base_line_periods=base_line_val)
                
                new_columns[f'conversion_line_{conversion_line_val}_{base_line_val}'] = ichi['tenkan_sen']
                new_columns[f'base_line_{conversion_line_val}_{base_line_val}'] = ichi['kijun_sen']
                new_columns[f'span_a_{conversion_line_val}_{base_line_val}'] = ichi['senkou_span_a']
                new_columns[f'span_b_{conversion_line_val}_{base_line_val}'] = ichi['senkou_span_b']
                new_columns[f'upper_{conversion_line_val}_{base_line_val}'] = np.maximum(ichi['senkou_span_a'], ichi['senkou_span_b'])
                new_columns[f'lower_{conversion_line_val}_{base_line_val}'] = np.minimum(ichi['senkou_span_a'], ichi['senkou_span_b'])

        # ATR
        atr = ta.ATR(dataframe)
        new_columns["atr"] = atr
        new_columns["atr_mean"] = ta.SMA(atr, timeperiod=self.atr_mean_period.value)
        
        new_columns_df = pd.DataFrame(new_columns, index=dataframe.index)
        dataframe = pd.concat([dataframe, new_columns_df], axis=1)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:        
        
        conversion_line_val = self.conversion_line_periods.value
        base_line_val = self.base_line_periods.value

        dataframe.loc[
            (
                # Trend filter: ADX > threshold
                (dataframe['adx'] > self.adx_threshold.value) &

                # Kumo color filter: bullish cloud
                (dataframe[f'span_a_{conversion_line_val}_{base_line_val}'] > dataframe[f'span_b_{conversion_line_val}_{base_line_val}']) &

                # Chikou span filter: price is above the past price
                (dataframe['close'] > dataframe['close'].shift(base_line_val)) &

                # Original conditions
                (dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'] > dataframe[f'base_line_{conversion_line_val}_{base_line_val}']) &
                (dataframe['close'] > dataframe[f'upper_{conversion_line_val}_{base_line_val}']) &
                (dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'] >= dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'].shift(1)) &
                (dataframe[f'base_line_{conversion_line_val}_{base_line_val}'] >= dataframe[f'base_line_{conversion_line_val}_{base_line_val}'].shift(1)) &

                # Checks if conversion line crossed above the base line within the specified rolling window
                (dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'].rolling(window=self.converstion_cross_rolling_window.value).apply(
                    lambda x: any(qtpylib.crossed_above(x, dataframe[f'base_line_{conversion_line_val}_{base_line_val}'].iloc[x.index[0]:x.index[-1]+1])))
                ) &
                
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1
        
        dataframe.loc[
            (
                # Trend filter: ADX > threshold
                (dataframe['adx'] > self.adx_threshold.value) &

                # Kumo color filter: bearish cloud
                (dataframe[f'span_a_{conversion_line_val}_{base_line_val}'] < dataframe[f'span_b_{conversion_line_val}_{base_line_val}']) &

                # Chikou span filter: price is below the past price
                (dataframe['close'] < dataframe['close'].shift(base_line_val)) &

                # Original conditions
                (dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'] < dataframe[f'base_line_{conversion_line_val}_{base_line_val}']) &
                (dataframe['close'] < dataframe[f'lower_{conversion_line_val}_{base_line_val}']) &
                (dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'] <= dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'].shift(1)) &
                (dataframe[f'base_line_{conversion_line_val}_{base_line_val}'] <= dataframe[f'base_line_{conversion_line_val}_{base_line_val}'].shift(1)) &

                # Checks if conversion line crossed below the base line within the specified rolling window
                (dataframe[f'conversion_line_{conversion_line_val}_{base_line_val}'].rolling(window=self.converstion_cross_rolling_window.value).apply(
                    lambda x: any(qtpylib.crossed_below(x, dataframe[f'base_line_{conversion_line_val}_{base_line_val}'].iloc[x.index[0]:x.index[-1]+1])))
                ) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe
    

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        conversion_line_val = self.conversion_line_periods.value
        base_line_val = self.base_line_periods.value
        exit_threshold = dataframe['atr'] * self.atr_mult.value
        
        dataframe.loc[
            (
                (qtpylib.crossed_below(dataframe['close'], (dataframe[f'base_line_{conversion_line_val}_{base_line_val}'] - exit_threshold))) &
                (dataframe['volume'] > 0)
            ),
        'exit_long'] = 1
        
        dataframe.loc[
            (
                (qtpylib.crossed_above(dataframe['close'], (dataframe[f'base_line_{conversion_line_val}_{base_line_val}'] + exit_threshold))) &
                (dataframe['volume'] > 0)
            ),
        'exit_short'] = 1

        return dataframe

    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:

        dataframe, _ = self.dp.get_processed_df(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if (('atr' in last_candle) and ('atr_mean' in last_candle) 
            and (last_candle['atr'] > 0) and (last_candle['atr_mean'] > 0)):
            
            leverage_ratio = last_candle['atr_mean'] / last_candle['atr']
            
            dynamic_leverage = self.leverage_level.value * leverage_ratio
            
            capped_leverage = min(dynamic_leverage, self.leverage_level.value * 2)
            final_leverage = max(1.0, capped_leverage)
            
            return min(final_leverage, max_leverage)

        return self.leverage_level.value