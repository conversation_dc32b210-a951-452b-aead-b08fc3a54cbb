# --- Do not remove these libs ---
from freqtrade.strategy import (
    IStrategy,
    stoploss_from_open,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    informative,
    RealParameter,
)
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime, timedelta
from freqtrade.persistence import Trade


# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging
import pytz

log = logging.getLogger(__name__)

class PumpAndDumpFib15m(IStrategy):
    """
    ## Pump and Dump Fibonacci Strategy (15m Version) V1.12

    **作者:** Gemini 2.5 Pro & User
    **版本:** 1.12
    **核心理念:**
    - **入场逻辑:** 通过止损 "预检查" 机制，确保只有能够成功计算止损的交易才能入场。
    - **出场优化:**
        - **止损 (V1.12 核心改动):** 采用 `find_trade_entry_candle` 辅助函数来精确定位开仓K线，彻底解决了在 `custom_stoploss` 中因索引错位而无法找到信号K线的问题。
        - **止盈:** 基于ATR动态止盈。
    - **风险管理:** 显式禁用移动止损。
    - **ATR自适应杠杆:** 杠杆大小根据市场波动率（ATR）动态调整。

    **时间框架:** 15分钟 (15m)
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True

    # --- 启动和回测所需的数据量 ---
    startup_candle_count: int = 100 # 需要更多数据来计算斐波那契

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }
    
    # --- 止损和止盈设置 ---
    stoploss = -0.99 # 虚拟值，实际止损由 custom_stoploss 控制
    trailing_stop = False # 明确禁用移动止损，以确保 custom_exit 和 custom_stoploss 完全接管
    use_custom_stoploss = True
    use_custom_exit = True

    # Optional plot configuration
    plot_config = {
        'main_plot': {
            'kama': {'color': 'rgba(255, 165, 0, 0.7)'},
        },
        'subplots': {
            "Signals": {
                'climax_candle': {'color': '#FFDD32', 'type': 'bar'},
                'panic_candle': {'color': '#FF00FF', 'type': 'bar'},
            },
            "ATR": {'atr': {'color': 'blue'}},
            "RSI": {'rsi': {'color': 'orange'}},
        }
    }

    # =========================================================================================================
    # --- HYPEROPT PARAMETERS ---
    # =========================================================================================================

    # --- V1.5: ATR-based Exit Parameter ---
    
    # --- KAMA & MSB Filter Parameters (V1.4) ---
    kama_period = IntParameter(10, 50, default=20, space="buy", optimize=True)
    msb_pivot_left = IntParameter(5, 20, default=10, space="buy", optimize=True)
    msb_pivot_right = IntParameter(3, 10, default=5, space="buy", optimize=True)

    # --- 做空 (Short) 参数 ---
    short_consecutive_green_candles = IntParameter(2, 5, default=3, space="buy", optimize=True)
    short_pump_rsi_threshold = IntParameter(60, 90, default=65, space="buy", optimize=True)
    short_upper_wick_body_ratio = DecimalParameter(1.2, 2.0, default=1.3, decimals=1, space="buy", optimize=True)
    short_climax_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    short_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    short_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    short_min_climax_conditions = IntParameter(3, 5, default=5, space="buy", optimize=True)
    
    # --- 做多 (Long) 参数 (与做空相反) ---
    long_consecutive_red_candles = IntParameter(2, 5, default=3, space="buy", optimize=True)
    long_dump_rsi_threshold = IntParameter(10, 40, default=35, space="buy", optimize=True)
    long_lower_wick_body_ratio = DecimalParameter(1.2, 2.0, default=1.3, decimals=1, space="buy", optimize=True)
    long_panic_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    long_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    long_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    long_min_panic_conditions = IntParameter(3, 5, default=5, space="buy", optimize=True)

    # --- 通用风险管理和冷却参数 ---
    cooldown_period = IntParameter(24, 192, default=48, optimize=False)
    
    # --- V1.14: ATR 动态止损参数 (Chandelier Exit) ---
    stoploss_atr_multiplier = DecimalParameter(2.0, 5.0, default=4.0, decimals=1, space="sell", optimize=True)

    # --- V1.16: 基于15m K线的离场参数 ---
    exit_lookback_candles = IntParameter(200, 2000, default=960, space="sell", optimize=True) # 回看K线数量 (960根 ≈ 10天)
    profit_exit_duration_hours = IntParameter(24, 120, default=48, space="sell", optimize=True) # 盈利持仓时间 (小时)
    loss_exit_duration_hours = IntParameter(6, 48, default=24, space="sell", optimize=True)   # 亏损持仓时间 (小时)

    # --- ATR 自适应杠杆参数 ---
    atr_period = IntParameter(10, 20, default=14, optimize=False)
    leverage_target_atr_pct = DecimalParameter(0.02, 0.15, default=0.07, decimals=3, optimize=False)
    leverage_max_atr = DecimalParameter(2.0, 10.0, default=5.0, decimals=1, optimize=False)

    # V1.7: 不再需要 trade_signal_info
    # trade_signal_info: dict = {}
    
    # V1.14: 移除脆弱的辅助函数 `find_trade_entry_candle`

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- KAMA Indicator ---
        dataframe['kama'] = ta.KAMA(dataframe, timeperiod=self.kama_period.value)
        
        # --- ATR ---
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)

        # --- V1.14: 钱德利尔止损所需指标 ---
        dataframe['high_max_rolling'] = dataframe['high'].rolling(window=self.atr_period.value).max()
        dataframe['low_min_rolling'] = dataframe['low'].rolling(window=self.atr_period.value).min()

        # --- Plotting Columns ---
        dataframe['fib_swing_high'] = np.nan
        dataframe['fib_swing_low'] = np.nan
        dataframe['fib_exit_target'] = np.nan

        # --- 原有指标计算 ---
        dataframe['is_green_candle'] = (dataframe['close'] > dataframe['open']).astype(int)
        dataframe['is_red_candle'] = (dataframe['close'] < dataframe['open']).astype(int)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        # --- 做空信号 (Short Signals) ---
        dataframe['volume_is_rising'] = dataframe['volume'] > dataframe['volume'].shift(1)
        dataframe['green_with_rising_vol'] = (dataframe['is_green_candle'] == 1) & (dataframe['volume_is_rising'] == 1)
        dataframe['consecutive_green_rising_vol'] = dataframe['green_with_rising_vol'].rolling(
            window=self.short_consecutive_green_candles.value).sum()
        
        body = abs(dataframe['close'] - dataframe['open'])
        upper_wick = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        dataframe['shooting_star'] = ((upper_wick > body * self.short_upper_wick_body_ratio.value) & (body > 0.000001)).astype('int')
        
        dataframe['range_15m'] = dataframe['high'] - dataframe['low']
        short_range_sma = ta.SMA(dataframe['range_15m'], timeperiod=self.short_climax_sma_period.value)
        short_volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.short_climax_sma_period.value)
        
        dataframe['short_range_spike'] = (dataframe['range_15m'] > short_range_sma * self.short_range_spike_multiplier.value).astype('int')
        dataframe['short_volume_spike'] = (dataframe['volume'] > short_volume_sma * self.short_volume_spike_multiplier.value).astype('int')
        
        short_trend_cond = (dataframe['consecutive_green_rising_vol'] >= self.short_consecutive_green_candles.value).astype('int')
        short_rsi_cond = (dataframe['rsi'] >= self.short_pump_rsi_threshold.value).astype('int')
        
        dataframe['climax_conditions_met'] = (short_trend_cond + short_rsi_cond + dataframe['shooting_star'] + dataframe['short_volume_spike'] + dataframe['short_range_spike'])
        dataframe['climax_candle'] = (dataframe['climax_conditions_met'] >= self.short_min_climax_conditions.value).astype('int')

        is_shrinking_volume = dataframe['volume'] < dataframe['volume'].shift(1)
        is_lower_high = dataframe['high'] <= dataframe['high'].shift(1)
        dataframe['pause_candle_short'] = ((dataframe['is_green_candle'] == 1) & (is_shrinking_volume == 1) & (is_lower_high == 1)).astype('int')

        # --- 做多信号 (Long Signals) ---
        dataframe['red_with_rising_vol'] = (dataframe['is_red_candle'] == 1) & (dataframe['volume_is_rising'] == 1)
        dataframe['consecutive_red_rising_vol'] = dataframe['red_with_rising_vol'].rolling(
            window=self.long_consecutive_red_candles.value).sum()

        lower_wick = np.minimum(dataframe['open'], dataframe['close']) - dataframe['low']
        dataframe['hammer'] = ((lower_wick > body * self.long_lower_wick_body_ratio.value) & (body > 0.000001)).astype('int')

        long_range_sma = ta.SMA(dataframe['range_15m'], timeperiod=self.long_panic_sma_period.value)
        long_volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.long_panic_sma_period.value)

        dataframe['long_range_spike'] = (dataframe['range_15m'] > long_range_sma * self.long_range_spike_multiplier.value).astype('int')
        dataframe['long_volume_spike'] = (dataframe['volume'] > long_volume_sma * self.long_volume_spike_multiplier.value).astype('int')
        
        long_trend_cond = (dataframe['consecutive_red_rising_vol'] >= self.long_consecutive_red_candles.value).astype('int')
        long_rsi_cond = (dataframe['rsi'] <= self.long_dump_rsi_threshold.value).astype('int')

        dataframe['panic_conditions_met'] = (long_trend_cond + long_rsi_cond + dataframe['hammer'] + dataframe['long_volume_spike'] + dataframe['long_range_spike'])
        dataframe['panic_candle'] = (dataframe['panic_conditions_met'] >= self.long_min_panic_conditions.value).astype('int')
        
        is_higher_low = dataframe['low'] >= dataframe['low'].shift(1)
        dataframe['pause_candle_long'] = ((dataframe['is_red_candle'] == 1) & (is_shrinking_volume == 1) & (is_higher_low == 1)).astype('int')

        # --- V1.4: KAMA 资格审查 ---
        dataframe['qualified_climax'] = (dataframe['climax_candle'] == 1) & (dataframe['high'] > dataframe['kama'])
        dataframe['qualified_panic'] = (dataframe['panic_candle'] == 1) & (dataframe['low'] < dataframe['kama'])

        # --- V1.4: MSB (Market Structure Break) Logic ---
        # MSB Logic for Shorts (Break of Swing Low) - Replaced qtpylib.pivot
        is_pivot_low = pd.Series(True, index=dataframe.index)
        for i in range(1, self.msb_pivot_left.value + 1):
            is_pivot_low = is_pivot_low & (dataframe['low'] < dataframe['low'].shift(i))
        for i in range(1, self.msb_pivot_right.value + 1):
            is_pivot_low = is_pivot_low & (dataframe['low'] < dataframe['low'].shift(-i))
        dataframe['pivot_low_price_raw'] = np.where(is_pivot_low, dataframe['low'], np.nan)
        dataframe['last_swing_low_unconfirmed'] = dataframe['pivot_low_price_raw'].ffill()
        dataframe['last_swing_low'] = dataframe['last_swing_low_unconfirmed'].shift(self.msb_pivot_right.value + 1)
        dataframe['short_msb_break'] = dataframe['close'] < dataframe['last_swing_low']

        # MSB Logic for Longs (Break of Swing High) - Replaced qtpylib.pivot
        is_pivot_high = pd.Series(True, index=dataframe.index)
        for i in range(1, self.msb_pivot_left.value + 1):
            is_pivot_high = is_pivot_high & (dataframe['high'] > dataframe['high'].shift(i))
        for i in range(1, self.msb_pivot_right.value + 1):
            is_pivot_high = is_pivot_high & (dataframe['high'] > dataframe['high'].shift(-i))
        dataframe['pivot_high_price_raw'] = np.where(is_pivot_high, dataframe['high'], np.nan)
        dataframe['last_swing_high_unconfirmed'] = dataframe['pivot_high_price_raw'].ffill()
        dataframe['last_swing_high'] = dataframe['last_swing_high_unconfirmed'].shift(self.msb_pivot_right.value + 1)
        dataframe['long_msb_break'] = dataframe['close'] > dataframe['last_swing_high']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        cooldown_seconds = self.cooldown_period.value * 900

        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''

        # --- 冷却期逻辑 ---
        if Trade.get_trades_proxy(pair=pair, is_open=True):
            return dataframe
        
        closed_trades = Trade.get_trades_proxy(pair=pair, is_open=False)
        if closed_trades:
            last_trade = sorted(closed_trades, key=lambda t: t.close_date_utc, reverse=True)[0]
            if (dataframe['date'].iloc[-1].to_pydatetime().replace(tzinfo=pytz.UTC) - last_trade.close_date_utc) < timedelta(seconds=cooldown_seconds):
                return dataframe

        # --- V1.17: 使用 KAMA 替代 EMA 作为趋势过滤器 ---
        trend_filter_short = dataframe['close'] < dataframe['kama']

        # --- V1.14: 简化的入场逻辑 (止损由动态的钱德利尔出口处理) ---

        # Case 1: Climax + Pause candle
        case_1_trigger = (
            (dataframe['qualified_climax'].shift(2) == 1) &
            (dataframe['pause_candle_short'].shift(1) == 1)
        )
        case_1_mask = case_1_trigger & (dataframe['short_msb_break'] == 1)

        # Case 2: Climax candle only
        case_2_trigger = (
            (dataframe['qualified_climax'].shift(1) == 1) &
            (case_1_trigger == False) # 确保 Pause 信号优先
        )
        case_2_mask = case_2_trigger & (dataframe['short_msb_break'] == 1)

        # 合并信号并设置入场
        final_entry_mask = (case_1_mask | case_2_mask) & trend_filter_short
        dataframe.loc[final_entry_mask, 'enter_short'] = 1
        
        # 为分析设置不同的标签
        dataframe.loc[case_1_mask, 'enter_tag'] = 'short_climax_pause'
        dataframe.loc[case_2_mask, 'enter_tag'] = 'short_climax_only'


        # --- 做多 (Long) ---
        trend_filter_long = dataframe['close'] > dataframe['kama']

        # Case 1: Panic + Pause candle
        long_case_1_trigger = (
            (dataframe['qualified_panic'].shift(2) == 1) &
            (dataframe['pause_candle_long'].shift(1) == 1)
        )
        long_case_1_mask = long_case_1_trigger & (dataframe['long_msb_break'] == 1)

        # Case 2: Panic candle only
        long_case_2_trigger = (
            (dataframe['qualified_panic'].shift(1) == 1) &
            (long_case_1_trigger == False) # 确保 Pause 信号优先
        )
        long_case_2_mask = long_case_2_trigger & (dataframe['long_msb_break'] == 1)
        
        # 合并信号并设置入场
        final_long_entry_mask = (long_case_1_mask | long_case_2_mask) & trend_filter_long
        dataframe.loc[final_long_entry_mask, 'enter_long'] = 1
        
        # 为分析设置不同的标签
        dataframe.loc[long_case_1_mask, 'enter_tag'] = 'long_panic_pause'
        dataframe.loc[long_case_2_mask, 'enter_tag'] = 'long_panic_only'


        # V1.7: 移除脆弱的信号信息存储逻辑，止损将在 custom_stoploss 中重新计算
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        return dataframe

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        """
        V1.16: 离场逻辑现在完全基于15分钟K线数据.
        - 基于15分钟K线历史数据的支撑/压力位止盈.
        - 基于盈利/亏损持仓时间的离场.
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        # --- 统一处理时区问题 ---
        current_time_naive = current_time.replace(tzinfo=None)
        trade_open_date_naive = trade.open_date.replace(tzinfo=None)
        
        # --- 1. 基于时间的离场 ---
        # 如果交易持续亏损超过N小时，则离场
        if current_profit < 0 and (current_time_naive - trade_open_date_naive).total_seconds() > self.loss_exit_duration_hours.value * 3600:
            return 'exit_loss_time_limit'
        
        # 如果交易持续盈利超过N小时，则离场
        if current_profit > 0 and (current_time_naive - trade_open_date_naive).total_seconds() > self.profit_exit_duration_hours.value * 3600:
            return 'exit_profit_time_limit'

        # --- 2. 基于15分钟K线关键位的离场 ---
        try:
            # 使用 searchsorted 高效定位交易开仓K线的索引
            trade_open_index = dataframe['date'].searchsorted(trade.open_date_utc, side='left')
        except (KeyError, AttributeError):
            return None # date列不存在或类型错误
        
        if trade_open_index < 1:
            return None # 无法定位交易或交易是第一根K线

        # 确定回看数据的范围
        lookback_candles = self.exit_lookback_candles.value
        start_index = max(0, trade_open_index - lookback_candles)
        pre_trade_data = dataframe.iloc[start_index:trade_open_index]

        if pre_trade_data.empty:
            return None
        
        # 根据交易方向确定目标位
        if trade.is_short:
            # 对于做空交易，目标是下方的支撑位
            support_level = pre_trade_data['low'].min()
            if current_rate <= support_level:
                return 'exit_support_level_hit'
        else: # not trade.is_short
            # 对于做多交易，目标是上方的压力位
            resistance_level = pre_trade_data['high'].max()
            if current_rate >= resistance_level:
                return 'exit_resistance_level_hit'
        
        return None

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        V1.14: 采用钱德利尔出口 (Chandelier Exit) 作为动态止损.
        该方法基于 ATR，稳健可靠，不再需要寻找信号K线。
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return 0.99  # Fallback, 返回一个高值避免意外平仓

        last_candle = dataframe.iloc[-1]

        if not trade.is_short:
            # --- 做多止损 ---
            # 止损位在近期最低价下方
            chandelier_exit_price = last_candle['low_min_rolling'] - last_candle['atr'] * self.stoploss_atr_multiplier.value
            # 返回一个负的百分比
            stoploss_pct = (chandelier_exit_price / trade.open_rate) - 1
            return stoploss_pct
        else:
            # --- 做空止损 ---
            # 止损位在近期最高价上方
            chandelier_exit_price = last_candle['high_max_rolling'] + last_candle['atr'] * self.stoploss_atr_multiplier.value
            # 返回一个正的百分比
            stoploss_pct = (chandelier_exit_price / trade.open_rate) - 1
            return stoploss_pct

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe is None or dataframe.empty or 'atr' not in dataframe.columns:
            return 1.0

        current_atr = dataframe['atr'].iloc[-1]
        if current_rate == 0 or current_atr == 0:
            return 1.0
        
        atr_pct = current_atr / current_rate
        
        # ATR 越高，杠杆越低
        leverage = self.leverage_target_atr_pct.value / atr_pct
        
        # 确保杠杆在合理范围内
        leverage = max(1.0, leverage)
        leverage = min(leverage, self.leverage_max_atr.value, max_leverage)
        
        return leverage

class PumpAndDumpFib15mHyperopt(PumpAndDumpFib15m):
    @staticmethod
    def buy_space():
        return [
            # --- KAMA & MSB Filter Parameters ---
            IntParameter(10, 50, default=20, name='kama_period'),
            IntParameter(5, 20, default=10, name='msb_pivot_left'),
            IntParameter(3, 10, default=5, name='msb_pivot_right'),

            # --- 做空 (Short) 参数 ---
            IntParameter(2, 5, default=3, name='short_consecutive_green_candles'),
            IntParameter(60, 90, default=65, name='short_pump_rsi_threshold'),
            DecimalParameter(1.2, 2.0, default=1.3, decimals=1, name='short_upper_wick_body_ratio'),
            IntParameter(10, 40, default=20, name='short_climax_sma_period'),
            DecimalParameter(1.0, 2.0, default=1.5, decimals=1, name='short_volume_spike_multiplier'),
            DecimalParameter(1.5, 2.5, default=1.5, decimals=1, name='short_range_spike_multiplier'),
            IntParameter(3, 5, default=5, name='short_min_climax_conditions'),
            
            # --- 做多 (Long) 参数 ---
            IntParameter(2, 5, default=3, name='long_consecutive_red_candles'),
            IntParameter(10, 40, default=35, name='long_dump_rsi_threshold'),
            DecimalParameter(1.2, 2.0, default=1.3, decimals=1, name='long_lower_wick_body_ratio'),
            IntParameter(10, 40, default=20, name='long_panic_sma_period'),
            DecimalParameter(1.0, 2.0, default=1.5, decimals=1, name='long_volume_spike_multiplier'),
            DecimalParameter(1.5, 2.5, default=1.5, decimals=1, name='long_range_spike_multiplier'),
            IntParameter(3, 5, default=5, name='long_min_panic_conditions'),
        ]

    @staticmethod
    def sell_space():
        return [
            DecimalParameter(2.0, 5.0, default=4.0, decimals=1, name='stoploss_atr_multiplier'),
            IntParameter(200, 2000, default=960, name='exit_lookback_candles'),
            IntParameter(24, 120, default=48, name='profit_exit_duration_hours'),
            IntParameter(6, 48, default=24, name='loss_exit_duration_hours')
        ] 