# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib
from dataclasses import dataclass
from typing import Dict, List, Tuple


@dataclass
class DynamicStopParams:
    """动态止损参数模型"""
    base_stop_distance: float   # 基础止损距离
    volatility_multiplier: float # 波动性调整倍数
    false_breakout_buffer: float # 假突破缓冲
    delay_conditions: List[str]  # 延迟止损条件
    final_stop_level: float     # 最终止损位


class DynamicStopLossManager:
    """
    动态止损管理器 - V2优化核心组件
    
    主要功能：
    1. 波动性自适应止损调整
    2. 假突破检测和延迟止损
    3. 市场环境相关的止损优化
    """
    
    def __init__(self, config: dict = None):
        self.config = config or {}
        self.volatility_lookback = self.config.get('volatility_lookback', 24)
        self.min_stop_distance = self.config.get('min_stop_distance', 0.015)
        self.max_stop_distance = self.config.get('max_stop_distance', 0.08)
        self.false_breakout_threshold = self.config.get('false_breakout_threshold', 0.6)
    
    def calculate_adaptive_stop_distance(self, dataframe: DataFrame, volatility: float, market_regime: str) -> float:
        """
        计算自适应止损距离
        
        Args:
            dataframe: 价格数据
            volatility: 当前波动性
            market_regime: 市场环境 ('low_vol', 'normal', 'high_vol', 'extreme')
            
        Returns:
            调整后的止损距离
        """
        # 基础止损距离
        base_distance = 0.035  # 3.5%基础止损
        
        # 波动性调整倍数
        volatility_adjustments = {
            'low_vol': 0.7,      # 低波动时收紧止损
            'normal': 1.0,       # 正常波动保持基础
            'high_vol': 1.4,     # 高波动时放宽止损
            'extreme': 1.8       # 极端波动时大幅放宽
        }
        
        volatility_multiplier = volatility_adjustments.get(market_regime, 1.0)
        
        # ATR调整
        if len(dataframe) >= self.volatility_lookback:
            atr = ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=self.volatility_lookback)
            current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
            current_price = dataframe['close'].iloc[-1]
            
            if current_price > 0 and current_atr > 0:
                atr_percentage = current_atr / current_price
                # ATR调整：如果ATR很大，进一步放宽止损
                if atr_percentage > 0.05:  # ATR超过5%
                    volatility_multiplier *= 1.2
        
        # 计算最终止损距离
        adjusted_distance = base_distance * volatility_multiplier
        
        # 确保在合理范围内
        final_distance = max(self.min_stop_distance, min(self.max_stop_distance, adjusted_distance))
        
        return final_distance
    
    def detect_false_breakout(self, dataframe: DataFrame, stop_level: float) -> bool:
        """
        检测假突破情况
        
        Args:
            dataframe: 价格数据
            stop_level: 当前止损位
            
        Returns:
            是否可能是假突破
        """
        if len(dataframe) < 5:
            return False
        
        current_price = dataframe['close'].iloc[-1]
        recent_prices = dataframe['close'].iloc[-5:]
        recent_volumes = dataframe['volume'].iloc[-5:]
        
        # 检查是否刚刚触及止损位
        price_near_stop = abs(current_price - stop_level) / current_price < 0.005  # 0.5%内
        
        if not price_near_stop:
            return False
        
        # 假突破特征检测
        false_breakout_signals = 0
        
        # 1. 成交量萎缩（突破时成交量应该放大）
        avg_volume = recent_volumes.mean()
        current_volume = recent_volumes.iloc[-1]
        if current_volume < avg_volume * 0.8:  # 成交量低于平均80%
            false_breakout_signals += 1
        
        # 2. 快速回拉（突破后快速回到突破位上方）
        if len(dataframe) >= 3:
            prev_price = dataframe['close'].iloc[-2]
            if current_price > stop_level and prev_price <= stop_level:  # 刚突破又回来
                false_breakout_signals += 1
        
        # 3. 上影线较长（表示卖压不强）
        if len(dataframe) >= 1:
            high = dataframe['high'].iloc[-1]
            close = dataframe['close'].iloc[-1]
            open_price = dataframe['open'].iloc[-1]
            
            body_size = abs(close - open_price)
            upper_shadow = high - max(close, open_price)
            
            if body_size > 0 and upper_shadow / body_size > 2:  # 上影线是实体的2倍以上
                false_breakout_signals += 1
        
        # 如果有2个或以上假突破信号，认为可能是假突破
        return false_breakout_signals >= 2
    
    def should_delay_stop_loss(self, dataframe: DataFrame, current_conditions: dict) -> bool:
        """
        判断是否应该延迟止损执行
        
        Args:
            dataframe: 价格数据
            current_conditions: 当前市场条件
            
        Returns:
            是否延迟止损
        """
        delay_reasons = []
        
        # 1. 检测到假突破
        stop_level = current_conditions.get('stop_level', 0)
        if stop_level > 0 and self.detect_false_breakout(dataframe, stop_level):
            delay_reasons.append('false_breakout_detected')
        
        # 2. 市场极端波动期间
        market_regime = current_conditions.get('market_regime', 'normal')
        if market_regime == 'extreme':
            delay_reasons.append('extreme_volatility')
        
        # 3. 重要支撑位附近
        support_level = current_conditions.get('support_level', 0)
        current_price = dataframe['close'].iloc[-1] if len(dataframe) > 0 else 0
        
        if support_level > 0 and current_price > 0:
            distance_to_support = abs(current_price - support_level) / current_price
            if distance_to_support < 0.02:  # 距离支撑位2%以内
                delay_reasons.append('near_support_level')
        
        # 4. 成交量异常低（可能是噪音）
        if len(dataframe) >= 10:
            recent_volume = dataframe['volume'].iloc[-1]
            avg_volume = dataframe['volume'].iloc[-10:].mean()
            if recent_volume < avg_volume * 0.3:  # 成交量低于平均30%
                delay_reasons.append('low_volume_noise')
        
        # 如果有延迟原因，返回True
        return len(delay_reasons) > 0
    
    def get_volatility_adjusted_stop(self, dataframe: DataFrame, base_stop: float, entry_price: float) -> DynamicStopParams:
        """
        获取波动性调整后的止损参数
        
        Args:
            dataframe: 价格数据
            base_stop: 基础止损百分比
            entry_price: 入场价格
            
        Returns:
            动态止损参数对象
        """
        # 计算当前波动性状态
        volatility_state = self._assess_volatility_state(dataframe)
        
        # 计算自适应止损距离
        adaptive_distance = self.calculate_adaptive_stop_distance(dataframe, 0, volatility_state)
        
        # 计算假突破缓冲
        false_breakout_buffer = adaptive_distance * 0.1  # 10%的缓冲
        
        # 最终止损位
        final_stop_level = entry_price * (1 + adaptive_distance)  # 做空时止损在上方
        
        return DynamicStopParams(
            base_stop_distance=base_stop,
            volatility_multiplier=adaptive_distance / base_stop if base_stop > 0 else 1.0,
            false_breakout_buffer=false_breakout_buffer,
            delay_conditions=[],
            final_stop_level=final_stop_level
        )
    
    def _assess_volatility_state(self, dataframe: DataFrame) -> str:
        """
        评估当前波动性状态
        
        Args:
            dataframe: 价格数据
            
        Returns:
            波动性状态 ('low_vol', 'normal', 'high_vol', 'extreme')
        """
        if len(dataframe) < self.volatility_lookback:
            return 'normal'
        
        # 计算ATR
        atr = ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=self.volatility_lookback)
        if atr.isna().all():
            return 'normal'
        
        current_atr = atr.iloc[-1]
        atr_sma = atr.rolling(window=self.volatility_lookback).mean().iloc[-1]
        
        if pd.isna(current_atr) or pd.isna(atr_sma) or atr_sma == 0:
            return 'normal'
        
        # ATR相对于其移动平均的比率
        atr_ratio = current_atr / atr_sma
        
        # 分类波动性状态
        if atr_ratio < 0.7:
            return 'low_vol'
        elif atr_ratio < 1.3:
            return 'normal'
        elif atr_ratio < 2.0:
            return 'high_vol'
        else:
            return 'extreme'


class CryptoShortStrategy(IStrategy):
    """
    Crypto Short Strategy - A multi-timeframe short-only trading strategy
    
    This strategy identifies cryptocurrencies that have experienced significant upward momentum
    over multiple days and positions for potential profit-taking corrections using short positions only.
    
    Strategy operates on:
    - Daily timeframe for trend analysis and market structure identification
    - 1-hour timeframe for precise entry timing
    - Short positions only (no long trades)
    - Fixed leverage application
    - High-liquidity filtering to ensure efficient trade execution
    
    Liquidity Filtering Features:
    - Volume-based liquidity scoring (0-100 scale)
    - Dollar volume analysis for market depth assessment
    - Volume consistency tracking for reliable liquidity
    - Multi-factor liquidity validation before trade entry
    """

    # Strategy interface version
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy
    timeframe = '1h'
    
    # Can our strategy go short?
    can_short: bool = True
    
    # Leverage settings
    leverage_optimize = False
    leverage_num = 3.0  # Fixed leverage multiplier
    
    # ROI table - Faster profit taking to reduce risk
    minimal_roi = {
        "0": 0.08,   # 8% profit target (reduced from 10%)
        "20": 0.05,  # 5% after 20 minutes (faster)
        "40": 0.03,  # 3% after 40 minutes
        "80": 0.015  # 1.5% after 80 minutes
    }

    # Stoploss - Optimized based on backtest results
    stoploss = -0.10  # 10% base stop loss - proven successful configuration

    # Trailing stoploss - More aggressive trailing
    trailing_stop = True
    trailing_stop_positive = 0.008  # Start trailing at 0.8% profit (even earlier)
    trailing_stop_positive_offset = 0.02  # Trail by 2% (tighter)
    trailing_only_offset_is_reached = True

    # Strategy parameters - Comprehensive hyperopt parameter definitions
    
    # === Pump Detection Parameters ===
    pump_threshold_1h = DecimalParameter(0.05, 0.15, default=0.08, space="buy", optimize=True,
                                        load=True)
    pump_threshold_4h = DecimalParameter(0.10, 0.25, default=0.15, space="buy", optimize=True,
                                        load=True)
    
    volume_surge_multiplier = DecimalParameter(2.0, 5.0, default=3.0, space="buy", optimize=True,
                                              load=True)
    
    rsi_overbought_threshold = DecimalParameter(70.0, 85.0, default=75.0, space="buy", optimize=True,
                                               load=True)
    
    # === Reversal Detection Parameters ===
    reversal_confirmation_candles = IntParameter(1, 3, default=2, space="buy", optimize=True,
                                                load=True)
    
    upper_shadow_ratio = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True,
                                         load=True)
    
    pullback_threshold = DecimalParameter(0.02, 0.08, default=0.04, space="buy", optimize=True,
                                         load=True)
    
    # === Liquidity Filtering Parameters ===
    min_liquidity_score = DecimalParameter(25.0, 65.0, default=35.0, space="buy", optimize=True,
                                          load=True)
    liquidity_consistency_threshold = DecimalParameter(0.3, 0.7, default=0.45, space="buy", optimize=True,
                                                      load=True)
    
    # === Hourly Signal Parameters ===
    volume_spike_threshold = DecimalParameter(1.5, 5.0, default=3.0, space="buy", optimize=True,
                                             load=True)
    volume_spike_strength_min = DecimalParameter(0.3, 0.8, default=0.5, space="buy", optimize=True,
                                                load=True)
    
    pattern_confidence_min = DecimalParameter(0.3, 0.8, default=0.5, space="buy", optimize=True,
                                             load=True)
    pattern_confidence_priority = DecimalParameter(0.6, 0.9, default=0.8, space="buy", optimize=True,
                                                  load=True)
    
    # === Signal Strength Parameters ===
    min_signal_strength = DecimalParameter(0.25, 0.6, default=0.35, space="buy", optimize=True,
                                          load=True)
    priority_signal_threshold = DecimalParameter(0.45, 0.7, default=0.55, space="buy", optimize=True,
                                                load=True)
    
    daily_signal_weight = DecimalParameter(0.6, 0.85, default=0.75, space="buy", optimize=True,
                                          load=True)
    hourly_signal_weight = DecimalParameter(0.15, 0.4, default=0.25, space="buy", optimize=True,
                                           load=True)
    
    # === Risk Management Parameters ===
    max_position_risk = DecimalParameter(0.008, 0.03, default=0.02, space="buy", optimize=True,
                                        load=True)
    account_risk_limit = DecimalParameter(0.03, 0.12, default=0.08, space="buy", optimize=True,
                                         load=True)
    
    # === Dynamic Risk Control Parameters ===
    consecutive_loss_limit = IntParameter(2, 5, default=3, space="buy", optimize=True,
                                         load=True)
    risk_reduction_factor = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True,
                                            load=True)
    
    # === Dynamic Trailing Stop Parameters ===
    volatility_lookback = IntParameter(12, 72, default=24, space="sell", optimize=True,
                                      load=True)
    volatility_multiplier = DecimalParameter(1.0, 4.0, default=2.0, space="sell", optimize=True,
                                            load=True)
    
    min_trailing_distance = DecimalParameter(0.015, 0.06, default=0.03, space="sell", optimize=True,
                                            load=True)
    max_trailing_distance = DecimalParameter(0.06, 0.20, default=0.10, space="sell", optimize=True,
                                            load=True)
    
    profit_lock_threshold = DecimalParameter(0.02, 0.10, default=0.05, space="sell", optimize=True,
                                            load=True)
    profit_lock_ratio = DecimalParameter(0.2, 0.8, default=0.5, space="sell", optimize=True,
                                        load=True)
    
    # === Support Level Detection Parameters ===
    support_lookback = IntParameter(20, 100, default=50, space="sell", optimize=True,
                                   load=True)
    support_strength_min = DecimalParameter(0.4, 0.8, default=0.6, space="sell", optimize=True,
                                           load=True)
    
    volume_cluster_periods = IntParameter(30, 100, default=50, space="sell", optimize=True,
                                         load=True)
    consolidation_periods = IntParameter(20, 60, default=30, space="sell", optimize=True,
                                        load=True)
    
    # === Exit Strategy Parameters ===
    quick_profit_pct = DecimalParameter(0.15, 0.35, default=0.25, space="sell", optimize=True,
                                       load=True)
    main_profit_pct = DecimalParameter(0.40, 0.60, default=0.50, space="sell", optimize=True,
                                      load=True)
    
    target_proximity_pct = DecimalParameter(0.01, 0.05, default=0.02, space="sell", optimize=True,
                                           load=True)
    
    emergency_exit_strength = DecimalParameter(0.6, 0.9, default=0.8, space="sell", optimize=True,
                                              load=True)
    
    # === Market Condition Parameters ===
    bullish_avoidance_threshold = DecimalParameter(0.6, 0.9, default=0.7, space="buy", optimize=True,
                                                  load=True)
    
    low_volatility_threshold = DecimalParameter(0.3, 0.7, default=0.5, space="sell", optimize=True,
                                               load=True)
    
    liquidity_exit_threshold = DecimalParameter(0.6, 0.9, default=0.8, space="sell", optimize=True,
                                               load=True)

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 50  # Reduced for better compatibility with new coins
    
    # Strategy configuration and validation
    def __init__(self, config: dict = None) -> None:
        super().__init__(config)
        self.config = config or {}
        
        # 初始化V2优化组件
        self.dynamic_stop_manager = DynamicStopLossManager(self.config)
        
        self.validate_configuration()
        self.setup_logging()
    
    def validate_configuration(self) -> None:
        """
        Validate strategy configuration and parameter boundaries
        Ensures all parameters are within acceptable ranges
        """
        # Validate daily analysis parameters
        # Removed old parameter validation - using simplified pump detection now
        
        if hasattr(self, 'range_expansion_min') and hasattr(self, 'range_expansion_max'):
            if self.range_expansion_min.value >= self.range_expansion_max.value:
                self.logger.warning("range_expansion_min should be less than range_expansion_max")
        
        if hasattr(self, 'volume_expansion_min') and hasattr(self, 'volume_expansion_max'):
            if self.volume_expansion_min.value >= self.volume_expansion_max.value:
                self.logger.warning("volume_expansion_min should be less than volume_expansion_max")
        
        # Validate signal weight parameters
        if hasattr(self, 'daily_signal_weight') and hasattr(self, 'hourly_signal_weight'):
            total_weight = self.daily_signal_weight.value + self.hourly_signal_weight.value
            if abs(total_weight - 1.0) > 0.01:  # Allow small floating point errors
                self.logger.warning(f"Signal weights should sum to 1.0, current sum: {total_weight}")
        
        # Validate trailing stop parameters
        if hasattr(self, 'min_trailing_distance') and hasattr(self, 'max_trailing_distance'):
            if self.min_trailing_distance.value >= self.max_trailing_distance.value:
                self.logger.warning("min_trailing_distance should be less than max_trailing_distance")
        
        # Validate profit taking percentages
        if hasattr(self, 'quick_profit_pct') and hasattr(self, 'main_profit_pct'):
            total_profit_pct = self.quick_profit_pct.value + self.main_profit_pct.value
            if total_profit_pct > 1.0:
                self.logger.warning(f"Total profit taking percentages exceed 100%: {total_profit_pct:.2%}")
        
        # Validate risk management parameters
        if hasattr(self, 'max_position_risk') and hasattr(self, 'account_risk_limit'):
            if self.max_position_risk.value > self.account_risk_limit.value:
                self.logger.warning("max_position_risk should not exceed account_risk_limit")
        
        # Validate liquidity parameters
        if hasattr(self, 'min_liquidity_score'):
            if not (0 <= self.min_liquidity_score.value <= 100):
                self.logger.warning(f"min_liquidity_score should be between 0-100: {self.min_liquidity_score.value}")
    
    def setup_logging(self) -> None:
        """
        Setup enhanced logging for strategy operations
        """
        import logging
        
        # Create strategy-specific logger
        self.strategy_logger = logging.getLogger(f"CryptoShortStrategy.{self.__class__.__name__}")
        
        # Set logging level based on configuration
        log_level = self.config.get('log_level', 'INFO')
        self.strategy_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        
        # Log strategy initialization
        self.strategy_logger.info("CryptoShortStrategy initialized with configuration validation")
    
    def get_strategy_configuration(self) -> dict:
        """
        Get current strategy configuration as dictionary
        Useful for saving optimized parameters
        """
        config = {
            'strategy_name': self.__class__.__name__,
            'version': '1.0.0',
            'timeframe': self.timeframe,
            'can_short': self.can_short,
            'leverage_num': self.leverage_num,
            'startup_candle_count': self.startup_candle_count,
        }
        
        # Add all optimizable parameters
        param_groups = {
            'pump_detection': [
                'pump_threshold_1h', 'pump_threshold_4h',
                'volume_surge_multiplier', 'rsi_overbought_threshold'
            ],
            'liquidity_filtering': [
                'min_liquidity_score', 'liquidity_consistency_threshold'
            ],
            'reversal_detection': [
                'reversal_confirmation_candles', 'upper_shadow_ratio',
                'pullback_threshold'
            ],
            'signal_strength': [
                'min_signal_strength', 'priority_signal_threshold',
                'daily_signal_weight', 'hourly_signal_weight'
            ],
            'risk_management': [
                'max_position_risk', 'account_risk_limit'
            ],
            'trailing_stops': [
                'volatility_lookback', 'volatility_multiplier',
                'min_trailing_distance', 'max_trailing_distance',
                'profit_lock_threshold', 'profit_lock_ratio'
            ],
            'support_detection': [
                'support_lookback', 'support_strength_min',
                'volume_cluster_periods', 'consolidation_periods'
            ],
            'exit_strategy': [
                'quick_profit_pct', 'main_profit_pct',
                'target_proximity_pct', 'emergency_exit_strength'
            ],
            'market_conditions': [
                'bullish_avoidance_threshold', 'low_volatility_threshold',
                'liquidity_exit_threshold'
            ]
        }
        
        for group_name, param_names in param_groups.items():
            config[group_name] = {}
            for param_name in param_names:
                if hasattr(self, param_name):
                    param_obj = getattr(self, param_name)
                    if hasattr(param_obj, 'value'):
                        config[group_name][param_name] = param_obj.value
        
        return config
    
    def load_configuration_from_dict(self, config_dict: dict) -> None:
        """
        Load configuration from dictionary
        Useful for loading optimized parameters
        """
        for group_name, group_params in config_dict.items():
            if isinstance(group_params, dict):
                for param_name, param_value in group_params.items():
                    if hasattr(self, param_name):
                        param_obj = getattr(self, param_name)
                        if hasattr(param_obj, 'value'):
                            # Update parameter value if within bounds
                            if hasattr(param_obj, 'low') and hasattr(param_obj, 'high'):
                                if param_obj.low <= param_value <= param_obj.high:
                                    param_obj.value = param_value
                                else:
                                    self.logger.warning(f"Parameter {param_name} value {param_value} outside bounds [{param_obj.low}, {param_obj.high}]")
        
        # Re-validate after loading
        self.validate_configuration()
    
    def get_parameter_bounds(self) -> dict:
        """
        Get parameter bounds for optimization
        Returns dictionary with parameter ranges
        """
        bounds = {}
        
        # Get all parameter attributes
        for attr_name in dir(self):
            attr = getattr(self, attr_name)
            if hasattr(attr, 'low') and hasattr(attr, 'high') and hasattr(attr, 'value'):
                bounds[attr_name] = {
                    'low': attr.low,
                    'high': attr.high,
                    'default': attr.default,
                    'current': attr.value,
                    'type': type(attr).__name__,
                    'space': getattr(attr, 'space', 'unknown'),
                    'optimize': getattr(attr, 'optimize', False)
                }
        
        return bounds

    # Optional order type mapping
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force
    order_time_in_force = {
        'entry': 'gtc',
        'exit': 'gtc'
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pairs will NOT be traded, but are used for analysis.
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = []
        
        # Add daily timeframe data for all pairs for trend analysis
        for pair in pairs:
            informative_pairs.append((pair, '1d'))
            
        return informative_pairs

    def validate_data_quality(self, dataframe: DataFrame, pair: str) -> DataFrame:
        """
        Validate and clean data quality issues
        Handles missing data, outliers, and erroneous price data
        
        Args:
            dataframe: Raw OHLCV dataframe
            pair: Trading pair symbol
            
        Returns:
            Cleaned dataframe with quality issues addressed
        """
        if len(dataframe) == 0:
            self.strategy_logger.warning(f"Empty dataframe received for {pair}")
            return dataframe
        
        original_length = len(dataframe)
        
        # 1. Check for missing data
        missing_data = dataframe.isnull().sum()
        if missing_data.any():
            # Only log significant missing data issues
            total_missing = missing_data.sum()
            if total_missing > len(dataframe) * 0.1:
                self.strategy_logger.warning(f"Significant missing data in {pair}: {total_missing} values")
            
            # Forward fill missing values for OHLC data
            for col in ['open', 'high', 'low', 'close']:
                if col in dataframe.columns and dataframe[col].isnull().any():
                    dataframe[col] = dataframe[col].fillna(method='ffill').fillna(method='bfill')
            
            # Handle missing volume data
            if 'volume' in dataframe.columns and dataframe['volume'].isnull().any():
                median_volume = dataframe['volume'].median()
                if pd.isna(median_volume) or median_volume <= 0:
                    median_volume = dataframe['volume'].mean()
                if pd.isna(median_volume) or median_volume <= 0:
                    median_volume = 1.0
                dataframe['volume'] = dataframe['volume'].fillna(median_volume)
        
        # 2. Detect and handle price outliers
        for price_col in ['open', 'high', 'low', 'close']:
            if price_col in dataframe.columns and len(dataframe) > 20:
                try:
                    # Calculate rolling statistics for outlier detection
                    rolling_median = dataframe[price_col].rolling(window=20, center=True).median()
                    rolling_std = dataframe[price_col].rolling(window=20, center=True).std()
                    
                    # Skip if we don't have enough valid data
                    if rolling_median.isnull().all() or rolling_std.isnull().all():
                        continue
                    
                    # Define outlier threshold (3 standard deviations from rolling median)
                    outlier_threshold = 3.0
                    upper_bound = rolling_median + (outlier_threshold * rolling_std)
                    lower_bound = rolling_median - (outlier_threshold * rolling_std)
                    
                    # Identify outliers (only where bounds are valid)
                    valid_bounds = ~(upper_bound.isnull() | lower_bound.isnull())
                    outliers = valid_bounds & ((dataframe[price_col] > upper_bound) | (dataframe[price_col] < lower_bound))
                    outlier_count = outliers.sum()
                    
                    if outlier_count > 0:
                        # Only log if significant outliers (more than 1% of data)
                        if outlier_count > len(dataframe) * 0.01:
                            self.strategy_logger.warning(f"Significant outliers in {price_col} for {pair}: {outlier_count}")
                        
                        # Replace outliers with rolling median (only where median is valid)
                        valid_median = ~rolling_median.isnull()
                        outliers_to_fix = outliers & valid_median
                        if outliers_to_fix.any():
                            dataframe.loc[outliers_to_fix, price_col] = rolling_median[outliers_to_fix]
                        
                except Exception as e:
                    self.strategy_logger.warning(f"Error in outlier detection for {price_col} in {pair}: {str(e)}")
        
        # 3. Validate OHLC relationships
        try:
            # Check for basic OHLC validity
            ohlc_cols = ['open', 'high', 'low', 'close']
            if all(col in dataframe.columns for col in ohlc_cols):
                invalid_ohlc = (
                    (dataframe['high'] < dataframe['low']) |
                    (dataframe['high'] < dataframe['open']) |
                    (dataframe['high'] < dataframe['close']) |
                    (dataframe['low'] > dataframe['open']) |
                    (dataframe['low'] > dataframe['close'])
                )
                
                if invalid_ohlc.any():
                    invalid_count = invalid_ohlc.sum()
                    # Only log if significant OHLC issues
                    if invalid_count > len(dataframe) * 0.01:
                        self.strategy_logger.warning(f"OHLC issues in {pair}: {invalid_count} candles")
                    
                    # Fix invalid OHLC by recalculating high/low
                    for idx in dataframe[invalid_ohlc].index:
                        try:
                            open_price = dataframe.loc[idx, 'open']
                            close_price = dataframe.loc[idx, 'close']
                            high_price = dataframe.loc[idx, 'high']
                            low_price = dataframe.loc[idx, 'low']
                            
                            # Ensure high is the maximum and low is the minimum
                            dataframe.loc[idx, 'high'] = max(open_price, close_price, high_price)
                            dataframe.loc[idx, 'low'] = min(open_price, close_price, low_price)
                        except Exception as e:
                            self.strategy_logger.warning(f"Error fixing OHLC for {pair} at {idx}: {str(e)}")
        except Exception as e:
            self.strategy_logger.warning(f"Error in OHLC validation for {pair}: {str(e)}")
        
        # 4. Handle volume outliers
        if 'volume' in dataframe.columns and len(dataframe) > 10:
            try:
                # Volume should be non-negative
                negative_volume = dataframe['volume'] < 0
                if negative_volume.any():
                    self.strategy_logger.error(f"Negative volume detected for {pair}: {negative_volume.sum()} candles")
                    dataframe.loc[negative_volume, 'volume'] = 0
                
                # Detect extremely high volume spikes (potential data errors)
                if len(dataframe) >= 50:
                    volume_median = dataframe['volume'].rolling(window=50).median()
                    volume_mad = (dataframe['volume'] - volume_median).abs().rolling(window=50).median()
                    
                    # Only process where we have valid statistics
                    valid_stats = ~(volume_median.isnull() | volume_mad.isnull()) & (volume_mad > 0)
                    
                    if valid_stats.any():
                        # Modified Z-score for volume outlier detection
                        volume_modified_z = pd.Series(0.0, index=dataframe.index)
                        volume_modified_z[valid_stats] = 0.6745 * (dataframe['volume'][valid_stats] - volume_median[valid_stats]) / volume_mad[valid_stats]
                        extreme_volume_spikes = volume_modified_z > 10  # Very conservative threshold
                        
                        if extreme_volume_spikes.any():
                            spike_count = extreme_volume_spikes.sum()
                            # Only log if very significant volume spikes
                            if spike_count > len(dataframe) * 0.05:
                                self.strategy_logger.warning(f"Many volume spikes in {pair}: {spike_count} candles")
                            
                            # Cap extreme volume spikes at 10x median
                            max_reasonable_volume = volume_median * 10
                            spikes_to_fix = extreme_volume_spikes & ~max_reasonable_volume.isnull()
                            if spikes_to_fix.any():
                                dataframe.loc[spikes_to_fix, 'volume'] = np.minimum(
                                    dataframe.loc[spikes_to_fix, 'volume'],
                                    max_reasonable_volume[spikes_to_fix]
                                )
            except Exception as e:
                self.strategy_logger.warning(f"Error in volume outlier detection for {pair}: {str(e)}")
        
        # 5. Check for data gaps (missing time periods) - Simplified approach
        if len(dataframe) > 1:
            try:
                # Simple gap detection using index differences
                index_values = dataframe.index.values
                if len(index_values) > 1:
                    # Calculate time differences in a more compatible way
                    time_diffs = []
                    for i in range(1, len(index_values)):
                        diff = index_values[i] - index_values[i-1]
                        time_diffs.append(diff)
                    
                    if time_diffs:
                        # Convert to pandas Timedelta for analysis
                        time_diffs_series = pd.Series([pd.Timedelta(diff) for diff in time_diffs])
                        expected_diff = time_diffs_series.median()
                        
                        # Count large gaps (more than 2x expected)
                        large_gaps = sum(1 for diff in time_diffs_series if diff > expected_diff * 2)
                        
                        if large_gaps > 0:
                            self.strategy_logger.warning(f"Data gaps detected for {pair}: {large_gaps} gaps larger than expected")
                        
            except Exception as e:
                # Handle any time-related compatibility issues
                self.strategy_logger.debug(f"Could not check data gaps for {pair}: {str(e)}")
        
        # 6. Final data validation
        final_length = len(dataframe)
        if final_length != original_length:
            self.strategy_logger.info(f"Data cleaning changed length for {pair}: {original_length} -> {final_length}")
        
        # Check data sufficiency (but don't block strategy)
        if final_length < self.startup_candle_count:
            if final_length < 50:  # Only warn if very insufficient
                self.strategy_logger.warning(f"Limited data for {pair}: {final_length} candles (recommended: {self.startup_candle_count})")
            else:
                self.strategy_logger.info(f"Reduced data for {pair}: {final_length} candles")
        
        return dataframe
    
    def detect_data_anomalies(self, dataframe: DataFrame, pair: str) -> dict:
        """
        Detect critical data anomalies only (simplified for compatibility)
        
        Args:
            dataframe: OHLCV dataframe
            pair: Trading pair symbol
            
        Returns:
            Dictionary with anomaly detection results
        """
        anomalies = {
            'pair': pair,
            'total_candles': len(dataframe),
            'anomalies_detected': []
        }
        
        if len(dataframe) == 0:
            anomalies['anomalies_detected'].append('empty_dataframe')
            return anomalies
        
        try:
            # Only check for critical issues that would break the strategy
            
            # Check for completely invalid price data
            price_cols = ['open', 'high', 'low', 'close']
            for col in price_cols:
                if col in dataframe.columns:
                    invalid_prices = (dataframe[col] <= 0).sum()
                    if invalid_prices > len(dataframe) * 0.5:  # More than 50% invalid
                        anomalies['anomalies_detected'].append(f'{col}_mostly_invalid_{invalid_prices}')
            
            # Check for completely missing volume data
            if 'volume' in dataframe.columns:
                zero_volume = (dataframe['volume'] == 0).sum()
                if zero_volume > len(dataframe) * 0.8:  # More than 80% zero volume
                    anomalies['anomalies_detected'].append(f'mostly_zero_volume_{zero_volume}')
            
        except Exception as e:
            # Silently handle any errors in anomaly detection
            pass
        
        return anomalies
    
    def validate_market_conditions(self, dataframe: DataFrame, pair: str) -> dict:
        """
        Validate current market conditions for trading suitability
        
        Args:
            dataframe: OHLCV dataframe with indicators
            pair: Trading pair symbol
            
        Returns:
            Dictionary with market condition validation results
        """
        conditions = {
            'pair': pair,
            'timestamp': dataframe.index[-1] if len(dataframe) > 0 else None,
            'suitable_for_trading': True,
            'warnings': [],
            'blocking_conditions': []
        }
        
        if len(dataframe) < 50:
            conditions['suitable_for_trading'] = False
            conditions['blocking_conditions'].append('insufficient_data')
            return conditions
        
        # Check liquidity conditions
        if 'liquidity_score_1d' in dataframe.columns:
            current_liquidity = dataframe['liquidity_score_1d'].iloc[-1]
            if current_liquidity < self.min_liquidity_score.value:
                conditions['suitable_for_trading'] = False
                conditions['blocking_conditions'].append(f'low_liquidity_{current_liquidity:.1f}')
        
        # Check for extreme volatility (may indicate unstable market)
        if 'close' in dataframe.columns:
            recent_returns = dataframe['close'].pct_change().tail(24)  # Last 24 hours
            volatility = recent_returns.std() * np.sqrt(24)  # Annualized volatility
            
            if volatility > 2.0:  # >200% annualized volatility
                conditions['warnings'].append(f'extreme_volatility_{volatility:.2f}')
            elif volatility > 1.0:  # >100% annualized volatility
                conditions['warnings'].append(f'high_volatility_{volatility:.2f}')
        
        # Check for market manipulation signs (unusual price/volume patterns)
        if 'volume' in dataframe.columns and 'close' in dataframe.columns:
            recent_data = dataframe.tail(24)
            
            # Detect pump and dump patterns
            price_spike = recent_data['close'].pct_change().max()
            volume_spike = (recent_data['volume'] / recent_data['volume'].rolling(10).mean()).max()
            
            if price_spike > 0.3 and volume_spike > 10:  # 30% price spike with 10x volume
                conditions['warnings'].append(f'potential_pump_dump_price_{price_spike:.2f}_volume_{volume_spike:.1f}')
        
        # Check for low volume periods (poor execution conditions)
        if 'volume' in dataframe.columns:
            recent_volume = dataframe['volume'].tail(6).mean()  # Last 6 hours
            historical_volume = dataframe['volume'].tail(168).mean()  # Last week
            
            if recent_volume < historical_volume * 0.3:  # Less than 30% of normal volume
                conditions['warnings'].append(f'low_volume_period_{recent_volume/historical_volume:.2f}')
        
        # Check for weekend/holiday effects (if timestamp available)
        if conditions['timestamp']:
            try:
                # Handle both datetime objects and timestamps
                if hasattr(conditions['timestamp'], 'weekday'):
                    # It's already a datetime object
                    timestamp = conditions['timestamp']
                else:
                    # Convert timestamp to datetime if needed
                    import pandas as pd
                    timestamp = pd.to_datetime(conditions['timestamp'])
                
                weekday = timestamp.weekday()
                hour = timestamp.hour
                
                # Weekend trading (Saturday/Sunday)
                if weekday >= 5:
                    conditions['warnings'].append('weekend_trading')
                
                # Low activity hours (typically 2-6 AM UTC)
                if 2 <= hour <= 6:
                    conditions['warnings'].append('low_activity_hours')
                    
            except Exception as e:
                # Skip time-based checks if timestamp conversion fails
                self.strategy_logger.debug(f"Could not process timestamp for {pair}: {str(e)}")
        
        return conditions
    
    def validate_signal_quality(self, dataframe: DataFrame, pair: str, signal_type: str = 'entry') -> dict:
        """
        Validate signal quality and filter false signals
        
        Args:
            dataframe: OHLCV dataframe with indicators
            pair: Trading pair symbol
            signal_type: Type of signal ('entry' or 'exit')
            
        Returns:
            Dictionary with signal validation results
        """
        validation = {
            'pair': pair,
            'signal_type': signal_type,
            'is_valid': True,
            'confidence': 0.0,
            'strength': 0.0,
            'issues': [],
            'supporting_factors': []
        }
        
        if len(dataframe) < 10:
            validation['is_valid'] = False
            validation['issues'].append('insufficient_data')
            return validation
        
        current_row = dataframe.iloc[-1]
        
        # For entry signals
        if signal_type == 'entry':
            confidence_factors = []
            strength_factors = []
            
            # Daily signal validation
            if 'consecutive_green_days_1d' in dataframe.columns:
                green_days = current_row['consecutive_green_days_1d']
                if green_days >= 2:  # Simplified threshold
                    confidence_factors.append(('consecutive_green_days', min(green_days / 7, 1.0)))
                    validation['supporting_factors'].append(f'green_days_{green_days}')
                else:
                    validation['issues'].append(f'insufficient_green_days_{green_days}')
            
            # Range expansion validation
            if 'range_expansion_count_1d' in dataframe.columns:
                range_expansion = current_row['range_expansion_count_1d']
                if range_expansion >= 2:  # Simplified threshold
                    confidence_factors.append(('range_expansion', min(range_expansion / 5, 1.0)))
                    validation['supporting_factors'].append(f'range_expansion_{range_expansion}')
                else:
                    validation['issues'].append(f'insufficient_range_expansion_{range_expansion}')
            
            # Volume expansion validation
            if 'volume_expansion_count_1d' in dataframe.columns:
                volume_expansion = current_row['volume_expansion_count_1d']
                if volume_expansion >= 2:  # Simplified threshold
                    confidence_factors.append(('volume_expansion', min(volume_expansion / 5, 1.0)))
                    validation['supporting_factors'].append(f'volume_expansion_{volume_expansion}')
                else:
                    validation['issues'].append(f'insufficient_volume_expansion_{volume_expansion}')
            
            # Liquidity validation
            if 'liquidity_score_1d' in dataframe.columns:
                liquidity_score = current_row['liquidity_score_1d']
                if liquidity_score >= self.min_liquidity_score.value:
                    confidence_factors.append(('liquidity', liquidity_score / 100))
                    validation['supporting_factors'].append(f'liquidity_{liquidity_score:.1f}')
                else:
                    validation['issues'].append(f'low_liquidity_{liquidity_score:.1f}')
            
            # Hourly signal validation
            if 'volume_spike' in dataframe.columns and current_row['volume_spike']:
                confidence_factors.append(('volume_spike', 0.8))
                validation['supporting_factors'].append('volume_spike')
            
            if 'pattern_confidence' in dataframe.columns:
                pattern_conf = current_row['pattern_confidence']
                if pattern_conf >= 0.5:  # Simplified threshold
                    confidence_factors.append(('pattern', pattern_conf))
                    validation['supporting_factors'].append(f'pattern_{pattern_conf:.2f}')
            
            # Calculate overall confidence and strength
            if confidence_factors:
                validation['confidence'] = np.mean([factor[1] for factor in confidence_factors])
                validation['strength'] = np.sum([factor[1] for factor in confidence_factors]) / len(confidence_factors)
            
            # Check for signal conflicts or contradictions
            self._check_signal_conflicts(dataframe, validation)
            
            # Minimum confidence threshold
            if validation['confidence'] < self.min_signal_strength.value:
                validation['is_valid'] = False
                validation['issues'].append(f'low_confidence_{validation["confidence"]:.2f}')
        
        # For exit signals
        elif signal_type == 'exit':
            # Exit signal validation logic
            exit_factors = []
            
            # Check if we're near support levels
            if 'support_level' in dataframe.columns and 'close' in dataframe.columns:
                current_price = current_row['close']
                support_level = current_row['support_level']
                
                if support_level > 0:
                    distance_to_support = (current_price - support_level) / support_level
                    if distance_to_support <= self.target_proximity_pct.value:
                        exit_factors.append(('near_support', 0.9))
                        validation['supporting_factors'].append(f'near_support_{distance_to_support:.3f}')
            
            # Check for reversal patterns
            if 'bullish_reversal_signal' in dataframe.columns and current_row['bullish_reversal_signal']:
                exit_factors.append(('bullish_reversal', 0.8))
                validation['supporting_factors'].append('bullish_reversal')
            
            # Calculate exit signal strength
            if exit_factors:
                validation['confidence'] = np.mean([factor[1] for factor in exit_factors])
                validation['strength'] = validation['confidence']
            
        return validation
    
    def _check_signal_conflicts(self, dataframe: DataFrame, validation: dict) -> None:
        """
        Check for conflicting signals that might indicate false positives
        
        Args:
            dataframe: OHLCV dataframe with indicators
            validation: Validation dictionary to update
        """
        if len(dataframe) < 5:
            return
        
        current_row = dataframe.iloc[-1]
        
        # Check for conflicting volume signals
        if 'volume_spike' in dataframe.columns and 'volume_expansion_count_1d' in dataframe.columns:
            has_volume_spike = current_row['volume_spike']
            volume_expansion = current_row['volume_expansion_count_1d']
            
            # Volume spike without expansion might be manipulation
            if has_volume_spike and volume_expansion < 2:
                validation['issues'].append('volume_spike_without_expansion')
        
        # Check for overextended conditions
        if 'consecutive_green_days_1d' in dataframe.columns:
            green_days = current_row['consecutive_green_days_1d']
            if green_days > 10:  # Simplified threshold
                validation['issues'].append(f'overextended_uptrend_{green_days}')
        
        # Check for divergences between price and volume
        if 'close' in dataframe.columns and 'volume' in dataframe.columns:
            recent_data = dataframe.tail(5)
            price_trend = recent_data['close'].iloc[-1] > recent_data['close'].iloc[0]
            volume_trend = recent_data['volume'].iloc[-1] > recent_data['volume'].iloc[0]
            
            if price_trend and not volume_trend:
                validation['issues'].append('price_volume_divergence')
    
    def filter_false_signals(self, dataframe: DataFrame, pair: str) -> DataFrame:
        """
        Apply false signal filtering to entry/exit conditions
        
        Args:
            dataframe: OHLCV dataframe with signals
            pair: Trading pair symbol
            
        Returns:
            DataFrame with filtered signals
        """
        if len(dataframe) == 0:
            return dataframe
        
        # Create copies of original signals
        if 'enter_short' in dataframe.columns:
            dataframe['enter_short_raw'] = dataframe['enter_short'].copy()
        if 'exit_short' in dataframe.columns:
            dataframe['exit_short_raw'] = dataframe['exit_short'].copy()
        
        # Apply signal validation to each potential entry
        for idx in dataframe.index:
            if 'enter_short' in dataframe.columns and dataframe.loc[idx, 'enter_short'] == 1:
                # Get data up to this point for validation
                validation_data = dataframe.loc[:idx]
                validation = self.validate_signal_quality(validation_data, pair, 'entry')
                
                if not validation['is_valid']:
                    dataframe.loc[idx, 'enter_short'] = 0
                    self.strategy_logger.info(f"Filtered false entry signal for {pair} at {idx}: {validation['issues']}")
                else:
                    # Store signal quality metrics
                    dataframe.loc[idx, 'signal_confidence'] = validation['confidence']
                    dataframe.loc[idx, 'signal_strength'] = validation['strength']
        
        # Apply signal validation to each potential exit
        for idx in dataframe.index:
            if 'exit_short' in dataframe.columns and dataframe.loc[idx, 'exit_short'] == 1:
                validation_data = dataframe.loc[:idx]
                validation = self.validate_signal_quality(validation_data, pair, 'exit')
                
                if not validation['is_valid']:
                    dataframe.loc[idx, 'exit_short'] = 0
                    self.strategy_logger.info(f"Filtered false exit signal for {pair} at {idx}: {validation['issues']}")
        
        return dataframe
    
    def validate_market_regime(self, dataframe: DataFrame, pair: str) -> dict:
        """
        Validate current market regime for signal reliability
        
        Args:
            dataframe: OHLCV dataframe with indicators
            pair: Trading pair symbol
            
        Returns:
            Dictionary with market regime validation results
        """
        regime_validation = {
            'pair': pair,
            'regime': 'unknown',
            'suitable_for_shorts': True,
            'volatility_level': 'normal',
            'trend_strength': 0.0,
            'market_stress': False,
            'warnings': []
        }
        
        if len(dataframe) < 50:
            regime_validation['warnings'].append('insufficient_data_for_regime_analysis')
            return regime_validation
        
        # Analyze recent price action (last 20 periods)
        recent_data = dataframe.tail(20)
        
        # Calculate trend strength using price momentum
        price_changes = recent_data['close'].pct_change().dropna()
        positive_changes = (price_changes > 0).sum()
        negative_changes = (price_changes < 0).sum()
        
        if positive_changes > negative_changes * 1.5:
            regime_validation['regime'] = 'bullish'
            regime_validation['trend_strength'] = positive_changes / len(price_changes)
        elif negative_changes > positive_changes * 1.5:
            regime_validation['regime'] = 'bearish'
            regime_validation['trend_strength'] = negative_changes / len(price_changes)
        else:
            regime_validation['regime'] = 'sideways'
            regime_validation['trend_strength'] = 0.5
        
        # Assess volatility level
        recent_volatility = price_changes.std()
        historical_volatility = dataframe['close'].pct_change().tail(100).std()
        
        volatility_ratio = recent_volatility / historical_volatility if historical_volatility > 0 else 1.0
        
        if volatility_ratio > 2.0:
            regime_validation['volatility_level'] = 'high'
            regime_validation['warnings'].append(f'high_volatility_{volatility_ratio:.2f}')
        elif volatility_ratio < 0.5:
            regime_validation['volatility_level'] = 'low'
            regime_validation['warnings'].append(f'low_volatility_{volatility_ratio:.2f}')
        
        # Check for market stress indicators
        if 'volume' in dataframe.columns:
            recent_volume = recent_data['volume'].mean()
            historical_volume = dataframe['volume'].tail(100).mean()
            volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1.0
            
            # Extremely high volume might indicate panic or manipulation
            if volume_ratio > 5.0:
                regime_validation['market_stress'] = True
                regime_validation['warnings'].append(f'extreme_volume_{volume_ratio:.2f}')
        
        # Determine suitability for short positions
        if regime_validation['regime'] == 'bullish' and regime_validation['trend_strength'] > 0.7:
            regime_validation['suitable_for_shorts'] = False
            regime_validation['warnings'].append('strong_bullish_trend_avoid_shorts')
        
        if regime_validation['volatility_level'] == 'high' and regime_validation['market_stress']:
            regime_validation['suitable_for_shorts'] = False
            regime_validation['warnings'].append('high_stress_market_conditions')
        
        return regime_validation
    
    def check_signal_timing(self, dataframe: DataFrame, pair: str) -> dict:
        """
        Check if signal timing is appropriate based on market microstructure
        
        Args:
            dataframe: OHLCV dataframe with indicators
            pair: Trading pair symbol
            
        Returns:
            Dictionary with timing validation results
        """
        timing_validation = {
            'pair': pair,
            'timing_score': 0.0,
            'is_good_timing': True,
            'timing_factors': [],
            'timing_issues': []
        }
        
        if len(dataframe) < 10:
            timing_validation['is_good_timing'] = False
            timing_validation['timing_issues'].append('insufficient_data')
            return timing_validation
        
        current_row = dataframe.iloc[-1]
        recent_data = dataframe.tail(10)
        
        timing_factors = []
        
        # Check volume timing
        if 'volume' in dataframe.columns:
            current_volume = current_row['volume']
            avg_volume = recent_data['volume'].mean()
            
            if current_volume > avg_volume * 1.5:
                timing_factors.append(('volume_confirmation', 0.8))
                timing_validation['timing_factors'].append('high_volume_confirmation')
            elif current_volume < avg_volume * 0.5:
                timing_factors.append(('low_volume_concern', -0.3))
                timing_validation['timing_issues'].append('low_volume_timing')
        
        # Check price action timing
        if 'close' in dataframe.columns and 'open' in dataframe.columns:
            current_candle_direction = 1 if current_row['close'] > current_row['open'] else -1
            
            # For short signals, prefer red candles or late-session weakness
            if current_candle_direction == -1:
                timing_factors.append(('bearish_candle', 0.6))
                timing_validation['timing_factors'].append('bearish_candle_timing')
        
        # Check for recent signal clustering (avoid overtrading)
        if 'enter_short' in dataframe.columns:
            recent_signals = recent_data['enter_short'].sum()
            if recent_signals > 2:
                timing_factors.append(('signal_clustering', -0.5))
                timing_validation['timing_issues'].append(f'recent_signal_clustering_{recent_signals}')
        
        # Check volatility timing
        if len(dataframe) >= 20:
            recent_ranges = (recent_data['high'] - recent_data['low']) / recent_data['close']
            current_range = (current_row['high'] - current_row['low']) / current_row['close']
            avg_range = recent_ranges.mean()
            
            if current_range > avg_range * 1.5:
                timing_factors.append(('high_volatility_timing', 0.7))
                timing_validation['timing_factors'].append('high_volatility_opportunity')
            elif current_range < avg_range * 0.5:
                timing_factors.append(('low_volatility_timing', -0.2))
                timing_validation['timing_issues'].append('low_volatility_timing')
        
        # Calculate overall timing score
        if timing_factors:
            timing_validation['timing_score'] = np.mean([factor[1] for factor in timing_factors])
        
        # Determine if timing is good
        if timing_validation['timing_score'] < -0.2:
            timing_validation['is_good_timing'] = False
        
        return timing_validation
    
    def validate_signal_confluence(self, dataframe: DataFrame, pair: str) -> dict:
        """
        Validate signal confluence across multiple timeframes and indicators
        
        Args:
            dataframe: OHLCV dataframe with indicators
            pair: Trading pair symbol
            
        Returns:
            Dictionary with confluence validation results
        """
        confluence_validation = {
            'pair': pair,
            'confluence_score': 0.0,
            'has_confluence': False,
            'supporting_signals': [],
            'conflicting_signals': []
        }
        
        if len(dataframe) == 0:
            return confluence_validation
        
        current_row = dataframe.iloc[-1]
        confluence_factors = []
        
        # Daily timeframe confluence
        daily_signals = 0
        if 'consecutive_green_days_1d' in dataframe.columns:
            if current_row['consecutive_green_days_1d'] >= self.consecutive_days_min.value:
                daily_signals += 1
                confluence_factors.append(('daily_uptrend', 0.3))
                confluence_validation['supporting_signals'].append('daily_uptrend_exhaustion')
        
        if 'range_expansion_count_1d' in dataframe.columns:
            if current_row['range_expansion_count_1d'] >= self.range_expansion_min.value:
                daily_signals += 1
                confluence_factors.append(('range_expansion', 0.25))
                confluence_validation['supporting_signals'].append('range_expansion')
        
        if 'volume_expansion_count_1d' in dataframe.columns:
            if current_row['volume_expansion_count_1d'] >= self.volume_expansion_min.value:
                daily_signals += 1
                confluence_factors.append(('volume_expansion', 0.25))
                confluence_validation['supporting_signals'].append('volume_expansion')
        
        # Hourly timeframe confluence
        hourly_signals = 0
        if 'volume_spike' in dataframe.columns and current_row['volume_spike']:
            hourly_signals += 1
            confluence_factors.append(('volume_spike', 0.2))
            confluence_validation['supporting_signals'].append('volume_spike')
        
        if 'price_retreat_confirmed' in dataframe.columns and current_row['price_retreat_confirmed']:
            hourly_signals += 1
            confluence_factors.append(('price_retreat', 0.3))
            confluence_validation['supporting_signals'].append('price_retreat')
        
        if 'pattern_confidence' in dataframe.columns:
            pattern_conf = current_row['pattern_confidence']
            if pattern_conf >= self.pattern_confidence_min.value:
                hourly_signals += 1
                confluence_factors.append(('pattern_signal', pattern_conf * 0.2))
                confluence_validation['supporting_signals'].append(f'pattern_{pattern_conf:.2f}')
        
        # Liquidity confluence
        if 'liquidity_score_1d' in dataframe.columns:
            liquidity_score = current_row['liquidity_score_1d']
            if liquidity_score >= self.min_liquidity_score.value:
                confluence_factors.append(('liquidity', liquidity_score / 500))  # Scale to 0-0.2
                confluence_validation['supporting_signals'].append(f'liquidity_{liquidity_score:.1f}')
        
        # Check for conflicting signals
        if 'bullish_reversal_signal' in dataframe.columns and current_row['bullish_reversal_signal']:
            confluence_validation['conflicting_signals'].append('bullish_reversal')
            confluence_factors.append(('bullish_conflict', -0.4))
        
        # Calculate confluence score
        if confluence_factors:
            confluence_validation['confluence_score'] = sum([factor[1] for factor in confluence_factors])
        
        # Determine if we have sufficient confluence
        min_confluence_score = 0.8  # Require strong confluence for shorts
        confluence_validation['has_confluence'] = (
            confluence_validation['confluence_score'] >= min_confluence_score and
            daily_signals >= 2 and  # At least 2 daily signals
            hourly_signals >= 1 and  # At least 1 hourly signal
            len(confluence_validation['conflicting_signals']) == 0  # No conflicts
        )
        
        return confluence_validation
    
    def apply_comprehensive_signal_validation(self, dataframe: DataFrame, pair: str) -> DataFrame:
        """
        Apply comprehensive signal validation including market regime, timing, and confluence
        
        Args:
            dataframe: OHLCV dataframe with signals
            pair: Trading pair symbol
            
        Returns:
            DataFrame with validated signals
        """
        if len(dataframe) == 0:
            return dataframe
        
        # Store original signals for comparison
        if 'enter_short' in dataframe.columns:
            dataframe['enter_short_original'] = dataframe['enter_short'].copy()
        
        # Apply validation to each potential signal
        for idx in dataframe.index:
            if 'enter_short' in dataframe.columns and dataframe.loc[idx, 'enter_short'] == 1:
                # Get data up to this point
                validation_data = dataframe.loc[:idx]
                
                # Market regime validation
                regime_check = self.validate_market_regime(validation_data, pair)
                if not regime_check['suitable_for_shorts']:
                    dataframe.loc[idx, 'enter_short'] = 0
                    dataframe.loc[idx, 'validation_reason'] = f"market_regime_{regime_check['regime']}"
                    self.strategy_logger.info(f"Signal filtered due to market regime for {pair} at {idx}: {regime_check['warnings']}")
                    continue
                
                # Timing validation
                timing_check = self.check_signal_timing(validation_data, pair)
                if not timing_check['is_good_timing']:
                    dataframe.loc[idx, 'enter_short'] = 0
                    dataframe.loc[idx, 'validation_reason'] = f"poor_timing_{timing_check['timing_score']:.2f}"
                    self.strategy_logger.info(f"Signal filtered due to poor timing for {pair} at {idx}: {timing_check['timing_issues']}")
                    continue
                
                # Confluence validation
                confluence_check = self.validate_signal_confluence(validation_data, pair)
                if not confluence_check['has_confluence']:
                    dataframe.loc[idx, 'enter_short'] = 0
                    dataframe.loc[idx, 'validation_reason'] = f"insufficient_confluence_{confluence_check['confluence_score']:.2f}"
                    self.strategy_logger.info(f"Signal filtered due to insufficient confluence for {pair} at {idx}")
                    continue
                
                # If all validations pass, store validation metrics
                dataframe.loc[idx, 'regime_score'] = regime_check['trend_strength']
                dataframe.loc[idx, 'timing_score'] = timing_check['timing_score']
                dataframe.loc[idx, 'confluence_score'] = confluence_check['confluence_score']
                dataframe.loc[idx, 'validation_reason'] = 'validated'
                
                self.strategy_logger.info(f"Signal validated for {pair} at {idx}: "
                                        f"regime={regime_check['regime']}, "
                                        f"timing={timing_check['timing_score']:.2f}, "
                                        f"confluence={confluence_check['confluence_score']:.2f}")
        
        return dataframe
    
    def check_market_conditions_before_trade(self, pair: str, current_time: datetime) -> dict:
        """
        Final market condition check before trade execution
        
        Args:
            pair: Trading pair symbol
            current_time: Current timestamp
            
        Returns:
            Dictionary with market condition check results
        """
        condition_check = {
            'pair': pair,
            'timestamp': current_time,
            'allow_trade': True,
            'conditions_met': [],
            'conditions_failed': [],
            'risk_level': 'normal'
        }
        
        try:
            # Get current dataframe
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) == 0:
                condition_check['allow_trade'] = False
                condition_check['conditions_failed'].append('no_data_available')
                return condition_check
            
            current_row = dataframe.iloc[-1]
            
            # Check liquidity conditions
            if 'liquidity_score_1d' in dataframe.columns:
                liquidity_score = current_row['liquidity_score_1d']
                if liquidity_score >= self.min_liquidity_score.value:
                    condition_check['conditions_met'].append(f'liquidity_ok_{liquidity_score:.1f}')
                else:
                    condition_check['allow_trade'] = False
                    condition_check['conditions_failed'].append(f'low_liquidity_{liquidity_score:.1f}')
            
            # Check for excessive volatility
            if len(dataframe) >= 10:
                recent_volatility = dataframe['close'].pct_change().tail(10).std()
                if recent_volatility > 0.1:  # 10% volatility threshold
                    condition_check['risk_level'] = 'high'
                    condition_check['conditions_failed'].append(f'high_volatility_{recent_volatility:.3f}')
                    # Don't block trade but flag as high risk
                
            # Check for market stress indicators
            if 'volume' in dataframe.columns:
                current_volume = current_row['volume']
                avg_volume = dataframe['volume'].tail(20).mean()
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
                
                if volume_ratio > 10:  # Extreme volume spike
                    condition_check['risk_level'] = 'high'
                    condition_check['conditions_failed'].append(f'extreme_volume_{volume_ratio:.1f}')
                elif volume_ratio > 3:
                    condition_check['conditions_met'].append(f'high_volume_{volume_ratio:.1f}')
            
            # Check for conflicting signals
            if 'bullish_reversal_signal' in dataframe.columns and current_row['bullish_reversal_signal']:
                condition_check['allow_trade'] = False
                condition_check['conditions_failed'].append('bullish_reversal_detected')
            
            # Check position limits (if available)
            # This would integrate with freqtrade's position management
            # For now, we'll add a placeholder
            condition_check['conditions_met'].append('position_limits_ok')
            
        except Exception as e:
            self.strategy_logger.error(f"Error in market condition check for {pair}: {str(e)}")
            condition_check['allow_trade'] = False
            condition_check['conditions_failed'].append(f'check_error_{str(e)[:50]}')
        
        return condition_check
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        Includes data quality validation and cleaning
        
        Performance Note: For maximum performance, consider using pandas vectorization
        or numpy instead of talib.
        """
        
        # Data quality validation and cleaning
        dataframe = self.validate_data_quality(dataframe, metadata['pair'])
        
        # Detect and log critical data anomalies only
        anomalies = self.detect_data_anomalies(dataframe, metadata['pair'])
        if anomalies['anomalies_detected']:
            self.strategy_logger.info(f"Data anomalies detected for {metadata['pair']}: {anomalies['anomalies_detected']}")
        
        # Get informative daily data
        informative_1d = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe='1d')
        
        # Apply data quality validation to daily data as well
        informative_1d = self.validate_data_quality(informative_1d, f"{metadata['pair']}_1d")
        
        # Add daily indicators for trend analysis
        informative_1d = self.populate_daily_indicators(informative_1d, metadata)
        
        # Merge daily data with hourly data
        dataframe = merge_informative_pair(dataframe, informative_1d, self.timeframe, '1d', ffill=True)
        
        # Add hourly indicators for entry signals
        dataframe = self.populate_hourly_indicators(dataframe, metadata)
        
        return dataframe

    def detect_abnormal_pump(self, dataframe: DataFrame) -> DataFrame:
        """
        Task 1.1: Statistical anomaly detection for abnormal price pumps
        
        Implements Z-score based anomaly detection for:
        - 1-hour price changes
        - 4-hour price changes  
        - Volume anomalies
        - Multi-timeframe analysis
        
        Args:
            dataframe: OHLCV dataframe
            
        Returns:
            DataFrame with anomaly detection columns added
        """
        # Ensure we have enough data for statistical analysis
        if len(dataframe) < 50:
            # Return safe defaults for insufficient data
            dataframe['price_1h_zscore'] = pd.Series(0.0, index=dataframe.index)
            dataframe['price_4h_zscore'] = pd.Series(0.0, index=dataframe.index)
            dataframe['volume_zscore'] = pd.Series(0.0, index=dataframe.index)
            dataframe['abnormal_pump_1h'] = pd.Series(False, index=dataframe.index)
            dataframe['abnormal_pump_4h'] = pd.Series(False, index=dataframe.index)
            dataframe['extreme_abnormal_pump'] = pd.Series(False, index=dataframe.index)
            dataframe['abnormal_volume'] = pd.Series(False, index=dataframe.index)
            return dataframe
        
        # Calculate price changes for different timeframes
        # 1-hour equivalent price change (using close-to-close)
        price_change_1h = dataframe['close'].pct_change(1)
        
        # 4-hour equivalent price change (using close-to-close over 4 periods)
        price_change_4h = dataframe['close'].pct_change(4)
        
        # Volume change
        volume_change = dataframe['volume'].pct_change(1)
        
        # Calculate rolling statistics (50-period window for statistical significance)
        rolling_window = 50
        
        # Price change statistics
        price_1h_mean = price_change_1h.rolling(window=rolling_window, min_periods=20).mean()
        price_1h_std = price_change_1h.rolling(window=rolling_window, min_periods=20).std()
        
        price_4h_mean = price_change_4h.rolling(window=rolling_window, min_periods=20).mean()
        price_4h_std = price_change_4h.rolling(window=rolling_window, min_periods=20).std()
        
        # Volume statistics
        volume_mean = dataframe['volume'].rolling(window=rolling_window, min_periods=20).mean()
        volume_std = dataframe['volume'].rolling(window=rolling_window, min_periods=20).std()
        
        # Calculate Z-scores with division by zero protection
        dataframe['price_1h_zscore'] = pd.Series(0.0, index=dataframe.index)
        dataframe['price_4h_zscore'] = pd.Series(0.0, index=dataframe.index)
        dataframe['volume_zscore'] = pd.Series(0.0, index=dataframe.index)
        
        # Calculate Z-scores where standard deviation is not zero
        valid_1h = (price_1h_std > 1e-8) & ~price_1h_std.isna() & ~price_1h_mean.isna()
        valid_4h = (price_4h_std > 1e-8) & ~price_4h_std.isna() & ~price_4h_mean.isna()
        valid_vol = (volume_std > 1e-8) & ~volume_std.isna() & ~volume_mean.isna()
        
        if valid_1h.any():
            dataframe.loc[valid_1h, 'price_1h_zscore'] = (
                (price_change_1h[valid_1h] - price_1h_mean[valid_1h]) / price_1h_std[valid_1h]
            )
        
        if valid_4h.any():
            dataframe.loc[valid_4h, 'price_4h_zscore'] = (
                (price_change_4h[valid_4h] - price_4h_mean[valid_4h]) / price_4h_std[valid_4h]
            )
        
        if valid_vol.any():
            dataframe.loc[valid_vol, 'volume_zscore'] = (
                (dataframe['volume'][valid_vol] - volume_mean[valid_vol]) / volume_std[valid_vol]
            )
        
        # Define anomaly thresholds
        standard_threshold = 2.5  # Standard anomaly (2.5 sigma)
        extreme_threshold = 3.5   # Extreme anomaly (3.5 sigma)
        
        # Detect abnormal pumps (positive Z-scores above thresholds)
        dataframe['abnormal_pump_1h'] = (dataframe['price_1h_zscore'] > standard_threshold)
        dataframe['abnormal_pump_4h'] = (dataframe['price_4h_zscore'] > standard_threshold)
        
        # Extreme abnormal pump requires either timeframe to exceed extreme threshold
        dataframe['extreme_abnormal_pump'] = (
            (dataframe['price_1h_zscore'] > extreme_threshold) |
            (dataframe['price_4h_zscore'] > extreme_threshold)
        )
        
        # Volume anomaly detection (high volume confirmation)
        dataframe['abnormal_volume'] = (dataframe['volume_zscore'] > standard_threshold)
        
        # Combined abnormal pump signal (price anomaly + volume confirmation)
        dataframe['abnormal_pump'] = (
            (dataframe['abnormal_pump_1h'] | dataframe['abnormal_pump_4h']) &
            dataframe['abnormal_volume']
        )
        
        # Fill any remaining NaN values with safe defaults
        dataframe['price_1h_zscore'] = dataframe['price_1h_zscore'].fillna(0.0)
        dataframe['price_4h_zscore'] = dataframe['price_4h_zscore'].fillna(0.0)
        dataframe['volume_zscore'] = dataframe['volume_zscore'].fillna(0.0)
        dataframe['abnormal_pump_1h'] = dataframe['abnormal_pump_1h'].fillna(False)
        dataframe['abnormal_pump_4h'] = dataframe['abnormal_pump_4h'].fillna(False)
        dataframe['extreme_abnormal_pump'] = dataframe['extreme_abnormal_pump'].fillna(False)
        dataframe['abnormal_volume'] = dataframe['abnormal_volume'].fillna(False)
        dataframe['abnormal_pump'] = dataframe['abnormal_pump'].fillna(False)
        
        return dataframe

    def analyze_market_environment(self, dataframe: DataFrame) -> DataFrame:
        """
        Task 3.1: Market environment analysis - Strong uptrend identification
        
        Analyzes current market environment to identify unsuitable conditions for shorting.
        Implements strong uptrend detection based on multiple criteria.
        
        Args:
            dataframe: OHLCV dataframe
            
        Returns:
            DataFrame with market environment analysis columns added
        """
        # Ensure we have enough data for analysis
        if len(dataframe) < 50:
            # Return safe defaults for insufficient data
            dataframe['sma_20'] = pd.Series(dataframe['close'], index=dataframe.index)
            dataframe['sma_50'] = pd.Series(dataframe['close'], index=dataframe.index)
            dataframe['strong_uptrend'] = pd.Series(False, index=dataframe.index)
            dataframe['market_regime'] = pd.Series('sideways', index=dataframe.index)
            return dataframe
        
        # Calculate moving averages for trend identification
        dataframe['sma_20'] = pd.Series(ta.SMA(dataframe['close'], timeperiod=20), index=dataframe.index)
        dataframe['sma_50'] = pd.Series(ta.SMA(dataframe['close'], timeperiod=50), index=dataframe.index)
        
        # Strong uptrend identification based on design document criteria
        dataframe['strong_uptrend'] = (
            (dataframe['close'] > dataframe['sma_20']) &           # Price above 20-day SMA
            (dataframe['sma_20'] > dataframe['sma_50']) &          # 20-day SMA above 50-day SMA
            (dataframe['close'] > dataframe['close'].shift(20) * 1.1)  # Price 10%+ above 20 periods ago
        )
        
        # Additional market regime classification
        # Bullish regime: consistent upward momentum
        bullish_regime = (
            (dataframe['close'] > dataframe['sma_20']) &
            (dataframe['sma_20'] > dataframe['sma_50']) &
            (dataframe['close'] > dataframe['close'].shift(10) * 1.05)  # 5%+ above 10 periods ago
        )
        
        # Bearish regime: consistent downward momentum
        bearish_regime = (
            (dataframe['close'] < dataframe['sma_20']) &
            (dataframe['sma_20'] < dataframe['sma_50']) &
            (dataframe['close'] < dataframe['close'].shift(10) * 0.95)  # 5%+ below 10 periods ago
        )
        
        # Market regime classification
        dataframe['market_regime'] = pd.Series('sideways', index=dataframe.index)
        dataframe.loc[bullish_regime, 'market_regime'] = 'bullish'
        dataframe.loc[bearish_regime, 'market_regime'] = 'bearish'
        dataframe.loc[dataframe['strong_uptrend'], 'market_regime'] = 'strong_bullish'
        
        # Market volatility assessment for dynamic parameter adjustment
        dataframe['price_volatility'] = dataframe['close'].rolling(window=20).std() / dataframe['close'].rolling(window=20).mean()
        dataframe['high_volatility'] = dataframe['price_volatility'] > dataframe['price_volatility'].rolling(window=50).quantile(0.8)
        
        # Volume trend analysis
        dataframe['volume_trend'] = dataframe['volume'].rolling(window=10).mean() / dataframe['volume'].rolling(window=30).mean()
        dataframe['increasing_volume'] = dataframe['volume_trend'] > 1.2
        
        return dataframe

    def calculate_consecutive_green_days(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate consecutive green (bullish) daily candles
        Returns the count of consecutive green days ending at each row
        """
        # Identify green days (close > open)
        green_days = (dataframe['close'] > dataframe['open']).astype(int)
        
        # Calculate consecutive green days using rolling window approach
        consecutive_count = pd.Series(0, index=dataframe.index)
        
        for i in range(len(dataframe)):
            if green_days.iloc[i] == 1:  # Current day is green
                if i == 0:
                    consecutive_count.iloc[i] = 1
                else:
                    # If previous day was also counted, add 1, otherwise start at 1
                    if green_days.iloc[i-1] == 1:
                        consecutive_count.iloc[i] = consecutive_count.iloc[i-1] + 1
                    else:
                        consecutive_count.iloc[i] = 1
            else:
                consecutive_count.iloc[i] = 0  # Reset count on red day
                
        return consecutive_count

    def calculate_range_expansion(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate consecutive days with expanding price ranges
        Returns the count of consecutive range expansion days ending at each row
        """
        # Calculate daily range (high - low)
        daily_range = dataframe['high'] - dataframe['low']
        
        # Calculate range expansion (current range > previous range)
        range_expanding = (daily_range > daily_range.shift(1)).astype(int)
        
        # Calculate consecutive range expansion days
        consecutive_expansion = pd.Series(0, index=dataframe.index)
        
        for i in range(1, len(dataframe)):  # Start from 1 since we need previous day
            if range_expanding.iloc[i] == 1:  # Current day has expanding range
                if range_expanding.iloc[i-1] == 1:
                    consecutive_expansion.iloc[i] = consecutive_expansion.iloc[i-1] + 1
                else:
                    consecutive_expansion.iloc[i] = 1
            else:
                consecutive_expansion.iloc[i] = 0  # Reset count
                
        return consecutive_expansion

    def calculate_volume_expansion(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate consecutive days with expanding volume
        Returns the count of consecutive volume expansion days ending at each row
        """
        # Calculate volume expansion (current volume > previous volume)
        volume_expanding = (dataframe['volume'] > dataframe['volume'].shift(1)).astype(int)
        
        # Calculate consecutive volume expansion days
        consecutive_volume_expansion = pd.Series(0, index=dataframe.index)
        
        for i in range(1, len(dataframe)):  # Start from 1 since we need previous day
            if volume_expanding.iloc[i] == 1:  # Current day has expanding volume
                if volume_expanding.iloc[i-1] == 1:
                    consecutive_volume_expansion.iloc[i] = consecutive_volume_expansion.iloc[i-1] + 1
                else:
                    consecutive_volume_expansion.iloc[i] = 1
            else:
                consecutive_volume_expansion.iloc[i] = 0  # Reset count
                
        return consecutive_volume_expansion

    def calculate_liquidity_score(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate liquidity score based on volume patterns and market participation
        Higher scores indicate better liquidity for trading
        
        Scoring factors:
        - Average daily volume (normalized)
        - Volume consistency (lower volatility = higher score)
        - Recent volume trend
        - Volume-to-market-cap proxy (using price*volume as approximation)
        """
        # Calculate volume-based metrics
        volume_sma_20 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=20), index=dataframe.index)
        volume_sma_5 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=5), index=dataframe.index)
        volume_std_20 = dataframe['volume'].rolling(window=20).std()
        
        # Volume trend factor (recent vs historical average)
        volume_trend_factor = volume_sma_5 / volume_sma_20
        volume_trend_factor = pd.Series(volume_trend_factor, index=dataframe.index).fillna(1.0)
        
        # Volume consistency factor (inverse of coefficient of variation)
        # Lower volatility in volume = more consistent liquidity
        volume_cv = volume_std_20 / volume_sma_20
        volume_consistency_factor = 1 / (1 + volume_cv)
        volume_consistency_factor = pd.Series(volume_consistency_factor, index=dataframe.index).fillna(0.5)
        
        # Dollar volume approximation (price * volume)
        # Higher dollar volume typically indicates better liquidity
        dollar_volume = dataframe['close'] * dataframe['volume']
        dollar_volume_sma = dollar_volume.rolling(window=20).mean()
        
        # Normalize dollar volume to 0-1 scale using rolling percentile
        dollar_volume_percentile = dollar_volume_sma.rolling(window=100).rank(pct=True)
        dollar_volume_percentile = dollar_volume_percentile.fillna(0.5)
        
        # Volume spike resistance (ability to handle large orders)
        # Measured by how often volume exceeds 2x average
        volume_spike_frequency = (dataframe['volume'] > (2 * volume_sma_20)).rolling(window=20).mean()
        volume_spike_frequency = volume_spike_frequency.fillna(0.1)
        
        # Composite liquidity score (0-100 scale)
        liquidity_score = (
            (dollar_volume_percentile * 40) +           # 40% weight on dollar volume
            (volume_consistency_factor * 25) +          # 25% weight on consistency
            (np.clip(volume_trend_factor, 0.5, 2.0) * 12.5) +  # 12.5% weight on trend (capped)
            (volume_spike_frequency * 22.5)             # 22.5% weight on spike handling
        )
        
        # Ensure score is between 0 and 100
        liquidity_score = np.clip(liquidity_score, 0, 100)
        
        return liquidity_score

    def is_high_liquidity_pair(self, dataframe: DataFrame, min_liquidity_score: float = 60.0) -> pd.Series:
        """
        Determine if a trading pair meets high liquidity requirements
        
        Args:
            dataframe: DataFrame with liquidity_score column
            min_liquidity_score: Minimum score required for high liquidity classification
            
        Returns:
            Boolean series indicating high liquidity periods
        """
        # Basic liquidity threshold
        meets_score_threshold = dataframe['liquidity_score'] >= min_liquidity_score
        
        # Additional volume requirements for high liquidity
        volume_sma_20 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=20), index=dataframe.index)
        
        # Require minimum absolute volume (prevents low-volume coins from qualifying)
        # This threshold should be adjusted based on the exchange and market conditions
        min_daily_volume = volume_sma_20.quantile(0.7)  # 70th percentile as minimum
        meets_volume_threshold = dataframe['volume'] >= min_daily_volume
        
        # Require recent volume activity (not stagnant)
        volume_sma_5 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=5), index=dataframe.index)
        has_recent_activity = volume_sma_5 > (volume_sma_20 * 0.5)  # Recent volume > 50% of 20-day average
        
        # Combine all liquidity requirements
        high_liquidity = meets_score_threshold & meets_volume_threshold & has_recent_activity
        
        return high_liquidity

    def validate_liquidity_requirements(self, dataframe: DataFrame, pair: str) -> bool:
        """
        Validate that a trading pair meets minimum liquidity requirements
        This can be used for pair filtering at the strategy level
        
        Args:
            dataframe: DataFrame with liquidity indicators
            pair: Trading pair symbol
            
        Returns:
            Boolean indicating if pair meets liquidity requirements
        """
        if len(dataframe) < 20:  # Need sufficient data for analysis
            return False
            
        # Get recent liquidity metrics
        recent_liquidity_score = dataframe['liquidity_score'].tail(10).mean()
        recent_high_liquidity_ratio = dataframe['high_liquidity'].tail(10).mean()
        
        # Pair must maintain good liquidity over recent period
        meets_score_requirement = recent_liquidity_score >= self.min_liquidity_score.value
        meets_consistency_requirement = recent_high_liquidity_ratio >= 0.7  # 70% of recent periods
        
        # Additional volume-based validation
        recent_volume = dataframe['volume'].tail(10).mean()
        volume_sma_20 = dataframe['volume'].rolling(window=20).mean().iloc[-1]
        
        # Recent volume should be reasonable compared to historical average
        volume_stability = recent_volume >= (volume_sma_20 * 0.3)  # At least 30% of 20-day average
        
        is_valid = meets_score_requirement and meets_consistency_requirement and volume_stability
        
        return is_valid

    def get_liquidity_summary(self, dataframe: DataFrame, pair: str) -> dict:
        """
        Get a summary of liquidity metrics for analysis and debugging
        
        Args:
            dataframe: DataFrame with liquidity indicators
            pair: Trading pair symbol
            
        Returns:
            Dictionary with liquidity summary statistics
        """
        if len(dataframe) == 0:
            return {}
            
        summary = {
            'pair': pair,
            'current_liquidity_score': dataframe['liquidity_score'].iloc[-1] if len(dataframe) > 0 else 0,
            'avg_liquidity_score_20d': dataframe['liquidity_score'].tail(20).mean(),
            'high_liquidity_ratio_20d': dataframe['high_liquidity'].tail(20).mean(),
            'current_volume': dataframe['volume'].iloc[-1] if len(dataframe) > 0 else 0,
            'avg_volume_20d': dataframe['volume'].tail(20).mean(),
            'volume_consistency': 1 / (1 + dataframe['volume'].tail(20).std() / dataframe['volume'].tail(20).mean()),
            'meets_requirements': self.validate_liquidity_requirements(dataframe, pair)
        }
        
        return summary

    def populate_daily_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate indicators for daily timeframe analysis
        """
        
        # Basic daily indicators
        dataframe['daily_range'] = dataframe['high'] - dataframe['low']
        dataframe['daily_volume_sma'] = pd.Series(ta.SMA(dataframe['volume'], timeperiod=10), index=dataframe.index)
        
        # Task 1.1: Statistical anomaly detection system
        dataframe = self.detect_abnormal_pump(dataframe)
        
        # Task 3.1: Market environment analysis - Strong uptrend identification
        dataframe = self.analyze_market_environment(dataframe)
        
        # Task 2.1: Consecutive uptrend detection
        dataframe['consecutive_green_days'] = self.calculate_consecutive_green_days(dataframe)
        
        # Task 2.2: Range expansion detection
        dataframe['range_expansion_count'] = self.calculate_range_expansion(dataframe)
        
        # Task 2.3: Volume expansion analysis
        dataframe['volume_expansion_count'] = self.calculate_volume_expansion(dataframe)
        
        # Task 2.4: Liquidity filtering
        dataframe['liquidity_score'] = self.calculate_liquidity_score(dataframe)
        dataframe['high_liquidity'] = self.is_high_liquidity_pair(dataframe)
        
        # Task 4.2: Support level detection
        dataframe['support_levels'] = self.identify_support_levels(dataframe)
        dataframe['volume_clusters'] = self.identify_volume_clusters(dataframe)
        dataframe['consolidation_zones'] = self.identify_consolidation_zones(dataframe)
        
        return dataframe

    def identify_support_levels(self, dataframe: DataFrame) -> pd.Series:
        """
        Identify support levels using pivot lows and volume analysis
        Returns the nearest significant support level for each period
        """
        if len(dataframe) < 20:
            return pd.Series(0, index=dataframe.index)
        
        # Find pivot lows (local minima)
        low_prices = dataframe['low']
        pivot_window = 5  # Look 5 periods before and after
        
        pivot_lows = []
        pivot_indices = []
        
        for i in range(pivot_window, len(dataframe) - pivot_window):
            current_low = low_prices.iloc[i]
            
            # Check if current low is lower than surrounding lows
            left_lows = low_prices.iloc[i-pivot_window:i]
            right_lows = low_prices.iloc[i+1:i+pivot_window+1]
            
            is_pivot_low = (
                current_low <= left_lows.min() and 
                current_low <= right_lows.min()
            )
            
            if is_pivot_low:
                pivot_lows.append(current_low)
                pivot_indices.append(i)
        
        # Create support level series
        support_levels = pd.Series(0.0, index=dataframe.index)
        
        if not pivot_lows:
            return support_levels
        
        # For each period, find the nearest significant support level below current price
        for i in range(len(dataframe)):
            current_price = dataframe['close'].iloc[i]
            
            # Find pivot lows below current price
            relevant_supports = [
                pivot_lows[j] for j in range(len(pivot_lows))
                if pivot_lows[j] < current_price and pivot_indices[j] <= i
            ]
            
            if relevant_supports:
                # Use the highest support level below current price
                support_levels.iloc[i] = max(relevant_supports)
            else:
                # If no support found, use a percentage below current price
                support_levels.iloc[i] = current_price * 0.95  # 5% below
        
        return support_levels

    def identify_volume_clusters(self, dataframe: DataFrame) -> pd.Series:
        """
        Identify high-volume price clusters that may act as support/resistance
        Returns volume-weighted price levels
        """
        if len(dataframe) < 20:
            return pd.Series(0, index=dataframe.index)
        
        volume_clusters = pd.Series(0.0, index=dataframe.index)
        lookback_period = 50  # Analyze last 50 periods
        
        for i in range(lookback_period, len(dataframe)):
            # Get recent price and volume data
            recent_data = dataframe.iloc[i-lookback_period:i+1]
            
            # Create price bins for volume clustering
            price_min = recent_data['low'].min()
            price_max = recent_data['high'].max()
            price_range = price_max - price_min
            
            if price_range <= 0:
                continue
            
            # Create 20 price bins
            num_bins = 20
            bin_size = price_range / num_bins
            
            # Calculate volume for each price bin
            bin_volumes = {}
            for _, row in recent_data.iterrows():
                # Distribute volume across price range (high to low)
                price_points = [row['low'], row['close'], row['high']]
                volume_per_point = row['volume'] / len(price_points)
                
                for price in price_points:
                    bin_index = int((price - price_min) / bin_size)
                    bin_index = min(bin_index, num_bins - 1)  # Ensure within bounds
                    
                    if bin_index not in bin_volumes:
                        bin_volumes[bin_index] = 0
                    bin_volumes[bin_index] += volume_per_point
            
            # Find the bin with highest volume
            if bin_volumes:
                max_volume_bin = max(bin_volumes.keys(), key=lambda k: bin_volumes[k])
                cluster_price = price_min + (max_volume_bin * bin_size) + (bin_size / 2)
                volume_clusters.iloc[i] = cluster_price
        
        return volume_clusters

    def identify_consolidation_zones(self, dataframe: DataFrame) -> pd.Series:
        """
        Identify price consolidation zones where price has spent significant time
        These zones often act as support/resistance levels
        """
        if len(dataframe) < 30:
            return pd.Series(0, index=dataframe.index)
        
        consolidation_zones = pd.Series(0.0, index=dataframe.index)
        
        for i in range(30, len(dataframe)):
            # Look at recent price action
            recent_highs = dataframe['high'].iloc[i-30:i+1]
            recent_lows = dataframe['low'].iloc[i-30:i+1]
            recent_closes = dataframe['close'].iloc[i-30:i+1]
            
            # Calculate price volatility
            price_std = recent_closes.std()
            price_mean = recent_closes.mean()
            
            if price_std <= 0:
                continue
            
            # Identify consolidation: low volatility relative to price level
            volatility_ratio = price_std / price_mean
            
            # Look for periods where price stayed within a narrow range
            price_range = recent_highs.max() - recent_lows.min()
            range_ratio = price_range / price_mean
            
            # Consolidation criteria
            is_consolidating = (
                volatility_ratio < 0.02 and  # Low volatility (2%)
                range_ratio < 0.05           # Narrow range (5%)
            )
            
            if is_consolidating:
                # Use the middle of the consolidation range
                consolidation_center = (recent_highs.max() + recent_lows.min()) / 2
                consolidation_zones.iloc[i] = consolidation_center
            else:
                # Use previous consolidation zone if available
                if i > 0:
                    consolidation_zones.iloc[i] = consolidation_zones.iloc[i-1]
        
        return consolidation_zones

    def calculate_daily_signal_strength(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate strength of daily timeframe signals (0-1 scale)
        Higher values indicate stronger bearish setup
        """
        signal_strength = pd.Series(0.0, index=dataframe.index)
        
        # Factor 1: Consecutive green days strength
        consecutive_days = dataframe.get('consecutive_green_days_1d', pd.Series(0, index=dataframe.index))
        days_strength = np.clip(consecutive_days / 5.0, 0, 1)  # Normalize to 0-1
        
        # Factor 2: Range expansion strength
        range_expansion = dataframe.get('range_expansion_count_1d', pd.Series(0, index=dataframe.index))
        range_strength = np.clip(range_expansion / 4.0, 0, 1)  # Normalize to 0-1
        
        # Factor 3: Volume expansion strength
        volume_expansion = dataframe.get('volume_expansion_count_1d', pd.Series(0, index=dataframe.index))
        volume_strength = np.clip(volume_expansion / 4.0, 0, 1)  # Normalize to 0-1
        
        # Factor 4: Liquidity strength
        liquidity_score = dataframe.get('liquidity_score_1d', pd.Series(60, index=dataframe.index))
        liquidity_strength = np.clip((liquidity_score - 50) / 50.0, 0, 1)  # Normalize 50-100 to 0-1
        
        # Combine factors with weights
        signal_strength = (
            days_strength * 0.30 +      # 30% weight on consecutive days
            range_strength * 0.25 +     # 25% weight on range expansion
            volume_strength * 0.25 +    # 25% weight on volume expansion
            liquidity_strength * 0.20   # 20% weight on liquidity
        )
        
        return np.clip(signal_strength, 0, 1)

    def calculate_hourly_signal_strength(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate strength of hourly timeframe signals (0-1 scale)
        Higher values indicate stronger reversal setup
        """
        signal_strength = pd.Series(0.0, index=dataframe.index)
        
        # Factor 1: Pattern confidence
        pattern_confidence = dataframe.get('pattern_confidence', pd.Series(0, index=dataframe.index))
        
        # Factor 2: Volume spike strength
        volume_spike_strength = dataframe.get('volume_spike_strength', pd.Series(0, index=dataframe.index))
        
        # Factor 3: Price retreat confirmation
        price_retreat = dataframe.get('price_retreat_confirmed', pd.Series(False, index=dataframe.index))
        retreat_strength = price_retreat.astype(float)
        
        # Factor 4: Spike and retreat pattern
        spike_retreat = dataframe.get('spike_and_retreat', pd.Series(False, index=dataframe.index))
        spike_strength = spike_retreat.astype(float)
        
        # Combine factors with weights
        signal_strength = (
            pattern_confidence * 0.30 +      # 30% weight on candlestick patterns
            volume_spike_strength * 0.30 +   # 30% weight on volume spikes
            retreat_strength * 0.25 +        # 25% weight on price retreat
            spike_strength * 0.15            # 15% weight on spike pattern
        )
        
        return np.clip(signal_strength, 0, 1)

    def coordinate_multi_timeframe_signals(self, dataframe: DataFrame) -> pd.Series:
        """
        Coordinate daily and hourly signals with priority weighting
        Returns combined signal strength (0-1 scale)
        """
        # Calculate individual timeframe strengths
        daily_strength = self.calculate_daily_signal_strength(dataframe)
        hourly_strength = self.calculate_hourly_signal_strength(dataframe)
        
        # Signal coordination rules
        # Both timeframes must show some strength for a valid signal
        min_daily_strength = 0.4   # Minimum daily signal strength
        min_hourly_strength = 0.3  # Minimum hourly signal strength
        
        # Check minimum requirements
        daily_valid = daily_strength >= min_daily_strength
        hourly_valid = hourly_strength >= min_hourly_strength
        
        # Combined signal strength calculation
        # Daily signals have higher weight as they indicate the main trend
        combined_strength = (
            daily_strength * 0.65 +    # 65% weight on daily signals
            hourly_strength * 0.35     # 35% weight on hourly signals
        )
        
        # Only valid signals where both timeframes meet minimum requirements
        valid_signals = daily_valid & hourly_valid
        final_strength = np.where(valid_signals, combined_strength, 0)
        
        return pd.Series(final_strength, index=dataframe.index)

    def check_signal_priority_requirements(self, dataframe: DataFrame) -> pd.Series:
        """
        Check high-priority signal requirements for immediate entry
        Returns boolean series indicating priority signals
        """
        # High priority conditions
        strong_daily = self.calculate_daily_signal_strength(dataframe) >= 0.7
        strong_hourly = self.calculate_hourly_signal_strength(dataframe) >= 0.6
        
        # Volume confirmation for priority signals
        volume_spike = dataframe.get('volume_spike', pd.Series(False, index=dataframe.index))
        high_volume_spike_strength = dataframe.get('volume_spike_strength', pd.Series(0, index=dataframe.index)) >= 0.7
        
        # Pattern confirmation for priority signals
        strong_reversal_pattern = dataframe.get('pattern_confidence', pd.Series(0, index=dataframe.index)) >= 0.8
        
        # Priority signal criteria (any of these conditions)
        priority_signals = (
            (strong_daily & strong_hourly) |                    # Both timeframes very strong
            (strong_daily & volume_spike & high_volume_spike_strength) |  # Strong daily + volume spike
            (strong_hourly & strong_reversal_pattern)           # Strong hourly + pattern
        )
        
        return priority_signals

    def validate_entry_conditions(self, dataframe: DataFrame) -> pd.Series:
        """
        Comprehensive entry condition validation
        Returns boolean series indicating valid entry points
        """
        # Basic requirement checks
        daily_conditions = (
            (dataframe['consecutive_green_days_1d'] >= self.consecutive_days_min.value) &
            (dataframe['range_expansion_count_1d'] >= self.range_expansion_min.value) &
            (dataframe['volume_expansion_count_1d'] >= self.volume_expansion_min.value)
        )
        
        # Liquidity requirements
        liquidity_conditions = (
            (dataframe['liquidity_score_1d'] >= self.min_liquidity_score.value) &
            (dataframe['high_liquidity_1d'] == True)
        )
        
        # Hourly signal requirements
        hourly_conditions = (
            (dataframe['volume_spike'] == True) &
            (dataframe['price_retreat_confirmed'] == True)
        )
        
        # Multi-timeframe coordination
        signal_strength = self.coordinate_multi_timeframe_signals(dataframe)
        strong_combined_signal = signal_strength >= 0.5
        
        # Additional validation filters
        
        # Market structure filter - avoid entries during strong downtrends
        # (We want to short after uptrends, not during existing downtrends)
        close_prices = dataframe['close']
        sma_20 = pd.Series(ta.SMA(close_prices, timeperiod=20), index=dataframe.index)
        sma_50 = pd.Series(ta.SMA(close_prices, timeperiod=50), index=dataframe.index)
        
        # Price should be above moving averages (indicating uptrend to reverse)
        uptrend_structure = (close_prices > sma_20) & (sma_20 > sma_50)
        
        # RSI filter - avoid extremely oversold conditions
        rsi = ta.RSI(dataframe, timeperiod=14)
        rsi_not_oversold = rsi > 30  # Avoid oversold conditions
        
        # Volume validation - ensure sufficient recent volume
        volume_adequate = dataframe['volume'] > (dataframe['volume'].rolling(window=24).mean() * 0.5)
        
        # Combine all conditions
        valid_entries = (
            daily_conditions &
            liquidity_conditions &
            hourly_conditions &
            strong_combined_signal &
            uptrend_structure &
            rsi_not_oversold &
            volume_adequate
        )
        
        return valid_entries

    def calculate_signal_confidence_score(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate overall signal confidence score (0-1 scale)
        Higher scores indicate higher confidence in the trade setup
        """
        # Get component scores
        daily_strength = self.calculate_daily_signal_strength(dataframe)
        hourly_strength = self.calculate_hourly_signal_strength(dataframe)
        combined_strength = self.coordinate_multi_timeframe_signals(dataframe)
        
        # Additional confidence factors
        
        # Factor 1: Pattern quality
        pattern_confidence = dataframe.get('pattern_confidence', pd.Series(0, index=dataframe.index))
        
        # Factor 2: Volume confirmation quality
        volume_spike_strength = dataframe.get('volume_spike_strength', pd.Series(0, index=dataframe.index))
        
        # Factor 3: Market structure alignment
        close_prices = dataframe['close']
        high_prices = dataframe['high']
        
        # Distance from recent highs (closer = higher confidence for shorts)
        recent_high = high_prices.rolling(window=24).max()
        distance_from_high = (recent_high - close_prices) / recent_high
        proximity_score = 1 - np.clip(distance_from_high * 10, 0, 1)  # Closer to high = higher score
        
        # Factor 4: Momentum divergence
        rsi = ta.RSI(dataframe, timeperiod=14)
        price_momentum = (close_prices - close_prices.shift(5)) / close_prices.shift(5)
        
        # Look for bearish divergence (price up, RSI down)
        rsi_declining = rsi < rsi.shift(3)
        price_rising = price_momentum > 0
        bearish_divergence = (price_rising & rsi_declining).astype(float)
        
        # Combine confidence factors
        confidence_score = (
            combined_strength * 0.35 +      # 35% weight on combined signal strength
            pattern_confidence * 0.20 +     # 20% weight on pattern quality
            volume_spike_strength * 0.20 +  # 20% weight on volume confirmation
            proximity_score * 0.15 +        # 15% weight on proximity to highs
            bearish_divergence * 0.10       # 10% weight on momentum divergence
        )
        
        return np.clip(confidence_score, 0, 1)

    def detect_doji_pattern(self, dataframe: DataFrame) -> pd.Series:
        """
        Detect doji candlestick patterns
        Doji occurs when open and close prices are very close, indicating indecision
        """
        # Calculate body size as percentage of total range
        body_size = abs(dataframe['close'] - dataframe['open'])
        total_range = dataframe['high'] - dataframe['low']
        
        # Avoid division by zero
        body_ratio = np.where(total_range > 0, body_size / total_range, 0)
        
        # Doji: body is less than 5% of total range
        doji_threshold = 0.05
        is_doji = body_ratio <= doji_threshold
        
        # Additional filter: require minimum range to avoid noise
        min_range_pct = 0.001  # 0.1% minimum range
        price_avg = (dataframe['high'] + dataframe['low']) / 2
        min_range = price_avg * min_range_pct
        has_sufficient_range = total_range >= min_range
        
        return is_doji & has_sufficient_range

    def detect_spinning_top_pattern(self, dataframe: DataFrame) -> pd.Series:
        """
        Detect spinning top candlestick patterns
        Spinning top has small body with long upper and lower shadows
        """
        # Calculate components
        body_size = abs(dataframe['close'] - dataframe['open'])
        upper_shadow = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        lower_shadow = np.minimum(dataframe['open'], dataframe['close']) - dataframe['low']
        total_range = dataframe['high'] - dataframe['low']
        
        # Avoid division by zero
        body_ratio = np.where(total_range > 0, body_size / total_range, 0)
        upper_shadow_ratio = np.where(total_range > 0, upper_shadow / total_range, 0)
        lower_shadow_ratio = np.where(total_range > 0, lower_shadow / total_range, 0)
        
        # Spinning top criteria:
        # 1. Small body (less than 30% of range)
        # 2. Both shadows should be significant (at least 20% each)
        # 3. Shadows should be roughly equal (within 50% of each other)
        small_body = body_ratio <= 0.30
        significant_upper_shadow = upper_shadow_ratio >= 0.20
        significant_lower_shadow = lower_shadow_ratio >= 0.20
        
        # Check shadow balance
        shadow_ratio = np.where(
            (upper_shadow > 0) & (lower_shadow > 0),
            np.minimum(upper_shadow, lower_shadow) / np.maximum(upper_shadow, lower_shadow),
            0
        )
        balanced_shadows = shadow_ratio >= 0.50
        
        # Minimum range filter
        min_range_pct = 0.002  # 0.2% minimum range
        price_avg = (dataframe['high'] + dataframe['low']) / 2
        min_range = price_avg * min_range_pct
        has_sufficient_range = total_range >= min_range
        
        return small_body & significant_upper_shadow & significant_lower_shadow & balanced_shadows & has_sufficient_range

    def detect_shooting_star_pattern(self, dataframe: DataFrame) -> pd.Series:
        """
        Detect shooting star candlestick patterns
        Shooting star has small body near the low with long upper shadow
        """
        # Calculate components
        body_size = abs(dataframe['close'] - dataframe['open'])
        upper_shadow = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        lower_shadow = np.minimum(dataframe['open'], dataframe['close']) - dataframe['low']
        total_range = dataframe['high'] - dataframe['low']
        
        # Avoid division by zero
        body_ratio = np.where(total_range > 0, body_size / total_range, 0)
        upper_shadow_ratio = np.where(total_range > 0, upper_shadow / total_range, 0)
        lower_shadow_ratio = np.where(total_range > 0, lower_shadow / total_range, 0)
        
        # Shooting star criteria:
        # 1. Small body (less than 25% of range)
        # 2. Long upper shadow (at least 60% of range)
        # 3. Short or no lower shadow (less than 15% of range)
        # 4. Body should be in lower half of the range
        small_body = body_ratio <= 0.25
        long_upper_shadow = upper_shadow_ratio >= 0.60
        short_lower_shadow = lower_shadow_ratio <= 0.15
        
        # Body position check - should be in lower portion
        body_high = np.maximum(dataframe['open'], dataframe['close'])
        body_position = np.where(total_range > 0, (body_high - dataframe['low']) / total_range, 0)
        body_in_lower_half = body_position <= 0.50
        
        # Minimum range filter
        min_range_pct = 0.003  # 0.3% minimum range for shooting star
        price_avg = (dataframe['high'] + dataframe['low']) / 2
        min_range = price_avg * min_range_pct
        has_sufficient_range = total_range >= min_range
        
        return small_body & long_upper_shadow & short_lower_shadow & body_in_lower_half & has_sufficient_range

    def calculate_pattern_confidence(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate confidence score for candlestick patterns
        Higher confidence indicates stronger reversal signal
        """
        # Get pattern detections
        doji = self.detect_doji_pattern(dataframe)
        spinning_top = self.detect_spinning_top_pattern(dataframe)
        shooting_star = self.detect_shooting_star_pattern(dataframe)
        
        # Base confidence scores
        confidence = pd.Series(0.0, index=dataframe.index)
        
        # Assign base confidence scores
        confidence.loc[doji] = 0.6  # Moderate confidence for doji
        confidence.loc[spinning_top] = 0.7  # Higher confidence for spinning top
        confidence.loc[shooting_star] = 0.8  # Highest confidence for shooting star
        
        # Volume confirmation boost
        volume_sma = pd.Series(ta.SMA(dataframe['volume'], timeperiod=24), index=dataframe.index)
        volume_above_average = dataframe['volume'] > volume_sma
        confidence.loc[volume_above_average] += 0.1
        
        # Recent trend context boost
        # Patterns are more significant after strong moves
        price_change_5 = (dataframe['close'] - dataframe['close'].shift(5)) / dataframe['close'].shift(5)
        strong_recent_move = abs(price_change_5) > 0.02  # 2% move in last 5 hours
        confidence.loc[strong_recent_move] += 0.1
        
        # Cap confidence at 1.0
        confidence = np.clip(confidence, 0, 1.0)
        
        return confidence

    def detect_volume_spike(self, dataframe: DataFrame) -> pd.Series:
        """
        Detect volume spikes that exceed historical averages
        Volume spikes often precede significant price movements
        """
        # Calculate multiple volume averages for comparison
        volume_sma_24 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=24), index=dataframe.index)  # 24-hour average
        volume_sma_12 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=12), index=dataframe.index)  # 12-hour average
        volume_sma_6 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=6), index=dataframe.index)    # 6-hour average
        
        # Calculate volume standard deviation for dynamic thresholds
        volume_std_24 = dataframe['volume'].rolling(window=24).std()
        
        # Multiple spike detection criteria
        current_volume = dataframe['volume']
        
        # Criterion 1: Current volume exceeds configurable threshold times 24h average
        spike_vs_24h = current_volume > (volume_sma_24 * self.volume_spike_threshold.value)
        
        # Criterion 2: Current volume exceeds 2 standard deviations above mean
        volume_z_score = np.where(
            volume_std_24 > 0,
            (current_volume - volume_sma_24) / volume_std_24,
            0
        )
        spike_vs_std = volume_z_score > 2.0
        
        # Criterion 3: Progressive volume increase (current > 12h > 6h averages)
        progressive_increase = (
            (current_volume > volume_sma_6) &
            (volume_sma_6 > volume_sma_12) &
            (volume_sma_12 > volume_sma_24 * 0.8)  # Allow some tolerance
        )
        
        # Criterion 4: Volume exceeds recent maximum
        volume_max_12h = dataframe['volume'].rolling(window=12).max().shift(1)  # Exclude current candle
        exceeds_recent_max = current_volume > (volume_max_12h * 1.2)  # 20% above recent max
        
        # Combine criteria - any two must be true for spike confirmation
        criteria_count = (
            spike_vs_24h.astype(int) +
            spike_vs_std.astype(int) +
            progressive_increase.astype(int) +
            exceeds_recent_max.astype(int)
        )
        
        # Require at least 2 criteria to be met
        volume_spike = criteria_count >= 2
        
        # Additional filter: minimum absolute volume to avoid low-volume noise
        min_volume_threshold = volume_sma_24 * 0.1  # At least 10% of average
        sufficient_volume = current_volume >= min_volume_threshold
        
        return volume_spike & sufficient_volume

    def calculate_volume_spike_strength(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate the strength of volume spikes (0-1 scale)
        Higher values indicate stronger volume anomalies
        """
        volume_sma_24 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=24), index=dataframe.index)
        volume_std_24 = dataframe['volume'].rolling(window=24).std()
        current_volume = dataframe['volume']
        
        # Calculate multiple strength factors
        
        # Factor 1: Ratio to 24h average (normalized)
        volume_ratio = current_volume / volume_sma_24
        ratio_strength = np.clip((volume_ratio - 1) / 4, 0, 1)  # Scale 1-5x ratio to 0-1
        
        # Factor 2: Z-score strength (normalized)
        volume_z_score = np.where(
            volume_std_24 > 0,
            (current_volume - volume_sma_24) / volume_std_24,
            0
        )
        z_strength = np.clip(volume_z_score / 5, 0, 1)  # Scale 0-5 z-score to 0-1
        
        # Factor 3: Percentile ranking strength
        volume_percentile = current_volume.rolling(window=100).rank(pct=True)
        percentile_strength = np.clip((volume_percentile - 0.8) / 0.2, 0, 1)  # Scale 80-100% to 0-1
        
        # Factor 4: Consistency across timeframes
        volume_sma_6 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=6), index=dataframe.index)
        volume_sma_12 = pd.Series(ta.SMA(dataframe['volume'], timeperiod=12), index=dataframe.index)
        
        consistency_score = 0
        if len(dataframe) > 12:
            # Check if volume is elevated across multiple timeframes
            elevated_6h = volume_sma_6 > volume_sma_24
            elevated_12h = volume_sma_12 > volume_sma_24
            consistency_score = (elevated_6h.astype(float) + elevated_12h.astype(float)) / 2
        
        # Combine factors with weights
        spike_strength = (
            ratio_strength * 0.35 +      # 35% weight on ratio
            z_strength * 0.30 +          # 30% weight on z-score
            percentile_strength * 0.25 + # 25% weight on percentile
            consistency_score * 0.10     # 10% weight on consistency
        )
        
        return np.clip(spike_strength, 0, 1)

    def detect_spike_and_retreat_pattern(self, dataframe: DataFrame) -> pd.Series:
        """
        Detect spike-and-retreat patterns where price spikes up then retreats
        This pattern often indicates exhaustion and potential reversal
        """
        # Look for price spikes followed by retreats over multiple candles
        high_prices = dataframe['high']
        close_prices = dataframe['close']
        
        # Calculate rolling maximum highs over different periods
        high_3h = high_prices.rolling(window=3).max()
        high_6h = high_prices.rolling(window=6).max()
        high_12h = high_prices.rolling(window=12).max()
        
        # Detect recent price spike (current high near recent maximum)
        current_high = dataframe['high']
        near_recent_high = current_high >= (high_6h * 0.995)  # Within 0.5% of 6h high
        
        # Detect retreat from spike
        # Price should be retreating from the high
        retreat_from_high = close_prices < (current_high * 0.99)  # Close below 99% of high
        
        # Additional retreat confirmation - compare with previous candles
        price_declining = (
            (close_prices < close_prices.shift(1)) &  # Current close < previous close
            (close_prices.shift(1) < close_prices.shift(2))  # Previous close < 2 candles ago
        )
        
        # Volume confirmation - retreat should have reasonable volume
        volume_sma = pd.Series(ta.SMA(dataframe['volume'], timeperiod=12), index=dataframe.index)
        adequate_volume = dataframe['volume'] > (volume_sma * 0.5)  # At least 50% of average
        
        # Combine conditions
        spike_and_retreat = (
            near_recent_high &
            retreat_from_high &
            price_declining &
            adequate_volume
        )
        
        return spike_and_retreat

    def confirm_price_reversal(self, dataframe: DataFrame) -> pd.Series:
        """
        Confirm price reversal using multiple technical indicators
        Combines candlestick patterns, volume, and momentum indicators
        """
        # Get previously calculated indicators
        reversal_patterns = dataframe.get('reversal_pattern', pd.Series(False, index=dataframe.index))
        volume_spikes = dataframe.get('volume_spike', pd.Series(False, index=dataframe.index))
        spike_retreat = dataframe.get('spike_and_retreat', pd.Series(False, index=dataframe.index))
        
        # Momentum indicators for reversal confirmation
        # RSI divergence - price makes higher high but RSI doesn't
        rsi = ta.RSI(dataframe, timeperiod=14)
        rsi_overbought = rsi > 70
        
        # Price momentum using rate of change
        price_roc_3 = ((dataframe['close'] - dataframe['close'].shift(3)) / dataframe['close'].shift(3)) * 100
        price_roc_6 = ((dataframe['close'] - dataframe['close'].shift(6)) / dataframe['close'].shift(6)) * 100
        
        # Momentum divergence - recent momentum weaker than earlier momentum
        momentum_weakening = price_roc_3 < price_roc_6
        
        # Volume-price divergence
        # Price up but volume declining suggests weakness
        price_up = dataframe['close'] > dataframe['close'].shift(3)
        volume_declining = dataframe['volume'] < dataframe['volume'].shift(3)
        volume_price_divergence = price_up & volume_declining
        
        # Combine reversal signals
        # Require at least 2 of the following conditions:
        conditions = [
            reversal_patterns,
            volume_spikes,
            spike_retreat,
            rsi_overbought,
            momentum_weakening,
            volume_price_divergence
        ]
        
        # Count how many conditions are met
        condition_count = sum(cond.astype(int) for cond in conditions)
        
        # Require at least 2 conditions for reversal confirmation
        reversal_confirmed = condition_count >= 2
        
        return reversal_confirmed


    



    
    def detect_trend_exhaustion(self, dataframe: DataFrame) -> DataFrame:
        """
        Multi-angle verification of trend exhaustion
        Core insight: 不是单纯红K线，而是多角度验证上涨无力，识别上涨趋势终结
        """
        # 1. Momentum exhaustion - RSI divergence
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=14)
        dataframe['rsi_declining'] = dataframe['rsi'] < dataframe['rsi'].shift(1)
        dataframe['rsi_overbought_declining'] = (dataframe['rsi'] > 70) & dataframe['rsi_declining']
        
        # 2. Volume exhaustion - decreasing volume on up moves
        dataframe['price_up'] = dataframe['close'] > dataframe['close'].shift(1)
        dataframe['volume_declining'] = dataframe['volume'] < dataframe['volume'].shift(1)
        dataframe['volume_exhaustion'] = dataframe['price_up'] & dataframe['volume_declining']
        
        # 3. Price action exhaustion - smaller candle bodies, longer wicks
        dataframe['body_size'] = abs(dataframe['close'] - dataframe['open'])
        dataframe['total_range'] = dataframe['high'] - dataframe['low']
        dataframe['body_ratio'] = dataframe['body_size'] / (dataframe['total_range'] + 1e-8)
        dataframe['weak_bodies'] = dataframe['body_ratio'] < 0.5  # Small bodies indicate indecision
        
        # 4. Failed breakout - price makes new high but fails to sustain
        dataframe['recent_high'] = dataframe['high'].rolling(window=10).max()
        dataframe['new_high'] = dataframe['high'] >= dataframe['recent_high']
        dataframe['failed_breakout'] = (
            dataframe['new_high'] & 
            (dataframe['close'] < dataframe['high'] * 0.98)  # Close below 98% of high
        )
        
        # 5. Multi-timeframe weakness
        dataframe['close_4h_ago'] = dataframe['close'].shift(4)
        dataframe['higher_4h'] = dataframe['close'] > dataframe['close_4h_ago']
        dataframe['volume_4h_declining'] = (
            dataframe['volume'].rolling(4).mean() < 
            dataframe['volume'].rolling(4).mean().shift(4)
        )
        dataframe['momentum_weakness'] = dataframe['higher_4h'] & dataframe['volume_4h_declining']
        
        # Combined trend exhaustion score (0-5 scale)
        dataframe['exhaustion_signals'] = (
            dataframe['rsi_overbought_declining'].astype(int) +
            dataframe['volume_exhaustion'].astype(int) +
            dataframe['weak_bodies'].astype(int) +
            dataframe['failed_breakout'].astype(int) +
            dataframe['momentum_weakness'].astype(int)
        )
        
        # More strict trend exhaustion confirmation (require more signals)
        dataframe['trend_exhaustion'] = dataframe['exhaustion_signals'] >= 3  # Increased from 2 to 3
        dataframe['strong_trend_exhaustion'] = dataframe['exhaustion_signals'] >= 4  # Increased from 3 to 4
        
        # More conservative reversal confirmation (wait for sustained weakness)
        dataframe['sustained_weakness'] = (
            (dataframe['close'] < dataframe['open']) &  # Current red candle
            (dataframe['close'].shift(1) < dataframe['open'].shift(1)) &  # Previous red candle
            (dataframe.get('failed_breakout', pd.Series(False, index=dataframe.index))) &  # Failed to sustain high
            (dataframe.get('rsi_overbought_declining', pd.Series(False, index=dataframe.index)))  # RSI confirmation
        )
        
        # Immediate reversal for quick entry (more strict)
        dataframe['immediate_reversal'] = (
            dataframe.get('sustained_weakness', pd.Series(False, index=dataframe.index)) &
            (dataframe.get('abnormal_volume', pd.Series(False, index=dataframe.index)))  # Volume confirmation
        )
        
        # Fill any remaining NaN values with safe defaults
        dataframe['rsi'] = dataframe['rsi'].fillna(50.0)
        dataframe['rsi_declining'] = dataframe['rsi_declining'].fillna(False)
        dataframe['rsi_overbought_declining'] = dataframe['rsi_overbought_declining'].fillna(False)
        dataframe['price_up'] = dataframe['price_up'].fillna(False)
        dataframe['volume_declining'] = dataframe['volume_declining'].fillna(False)
        dataframe['volume_exhaustion'] = dataframe['volume_exhaustion'].fillna(False)
        dataframe['body_size'] = dataframe['body_size'].fillna(0.0)
        dataframe['total_range'] = dataframe['total_range'].fillna(0.0)
        dataframe['body_ratio'] = dataframe['body_ratio'].fillna(0.5)
        dataframe['weak_bodies'] = dataframe['weak_bodies'].fillna(False)
        dataframe['recent_high'] = dataframe['recent_high'].fillna(dataframe['high'])
        dataframe['new_high'] = dataframe['new_high'].fillna(False)
        dataframe['failed_breakout'] = dataframe['failed_breakout'].fillna(False)
        dataframe['close_4h_ago'] = dataframe['close_4h_ago'].fillna(dataframe['close'])
        dataframe['higher_4h'] = dataframe['higher_4h'].fillna(False)
        dataframe['volume_4h_declining'] = dataframe['volume_4h_declining'].fillna(False)
        dataframe['momentum_weakness'] = dataframe['momentum_weakness'].fillna(False)
        dataframe['exhaustion_signals'] = dataframe['exhaustion_signals'].fillna(0)
        dataframe['trend_exhaustion'] = dataframe['trend_exhaustion'].fillna(False)
        dataframe['strong_trend_exhaustion'] = dataframe['strong_trend_exhaustion'].fillna(False)
        dataframe['sustained_weakness'] = dataframe['sustained_weakness'].fillna(False)
        dataframe['immediate_reversal'] = dataframe['immediate_reversal'].fillna(False)
        
        return dataframe

    def detect_sustained_weakness_confirmation(self, dataframe: DataFrame) -> DataFrame:
        """
        Task 4.1: Sustained weakness confirmation mechanism
        
        Implements strict confirmation requirements to avoid premature entries:
        - Wait for at least 2 consecutive weakness signals
        - Confirm with multiple timeframe analysis
        - Verify volume confirmation
        - Check for failed bounce attempts
        """
        # Initialize confirmation columns
        dataframe['sustained_weakness_confirmed'] = pd.Series(False, index=dataframe.index)
        dataframe['weakness_confirmation_score'] = pd.Series(0, index=dataframe.index)
        dataframe['multi_timeframe_weakness'] = pd.Series(False, index=dataframe.index)
        
        if len(dataframe) < 10:
            return dataframe
        
        # 1. Consecutive red candles confirmation
        dataframe['red_candle'] = dataframe['close'] < dataframe['open']
        dataframe['consecutive_red'] = pd.Series(0, index=dataframe.index)
        
        # Calculate consecutive red candles using proper pandas assignment
        consecutive_red_values = [0] * len(dataframe)
        for i in range(1, len(dataframe)):
            if dataframe['red_candle'].iloc[i]:
                if dataframe['red_candle'].iloc[i-1]:
                    consecutive_red_values[i] = consecutive_red_values[i-1] + 1
                else:
                    consecutive_red_values[i] = 1
            else:
                consecutive_red_values[i] = 0
        
        dataframe['consecutive_red'] = consecutive_red_values
        
        # 2. Failed bounce detection using proper pandas assignment
        failed_bounce_values = [False] * len(dataframe)
        for i in range(2, len(dataframe)):
            # Look for pattern: down -> small up -> down again
            if (dataframe['close'].iloc[i] < dataframe['close'].iloc[i-1] and
                dataframe['close'].iloc[i-1] > dataframe['close'].iloc[i-2] and
                dataframe['close'].iloc[i] < dataframe['close'].iloc[i-2]):
                # Ensure the bounce was weak (less than 50% recovery)
                down_move = dataframe['close'].iloc[i-2] - dataframe['low'].iloc[i-2:i].min()
                bounce_move = dataframe['close'].iloc[i-1] - dataframe['low'].iloc[i-2:i].min()
                if down_move > 0 and bounce_move / down_move < 0.5:
                    failed_bounce_values[i] = True
        
        dataframe['failed_bounce'] = failed_bounce_values
        
        # 3. Volume confirmation during weakness
        dataframe['volume_confirms_weakness'] = pd.Series(False, index=dataframe.index)
        volume_sma = dataframe['volume'].rolling(window=20).mean()
        
        # Volume should be above average during selling pressure
        dataframe['volume_confirms_weakness'] = (
            (dataframe['red_candle']) &
            (dataframe['volume'] > volume_sma * 1.1)  # 10% above average
        )
        
        # 4. Multi-timeframe weakness (4-hour perspective)
        dataframe['price_4h_declining'] = dataframe['close'] < dataframe['close'].shift(4)
        dataframe['volume_4h_declining'] = (
            dataframe['volume'].rolling(4).mean() < 
            dataframe['volume'].rolling(4).mean().shift(4)
        )
        
        # Multi-timeframe weakness: price declining with volume confirmation
        dataframe['multi_timeframe_weakness'] = (
            dataframe['price_4h_declining'] &
            (dataframe['volume_4h_declining'] | dataframe['volume_confirms_weakness'])
        )
        
        # 5. RSI confirmation of weakness
        if 'rsi' in dataframe.columns:
            dataframe['rsi_confirms_weakness'] = (
                (dataframe['rsi'] < 60) &  # Not overbought anymore
                (dataframe['rsi'] < dataframe['rsi'].shift(1))  # Declining RSI
            )
        else:
            dataframe['rsi_confirms_weakness'] = pd.Series(False, index=dataframe.index)
        
        # Calculate weakness confirmation score (0-5 scale)
        dataframe['weakness_confirmation_score'] = (
            (dataframe['consecutive_red'] >= 2).astype(int) +  # At least 2 consecutive red candles
            dataframe['failed_bounce'].astype(int) +           # Failed bounce attempt
            dataframe['volume_confirms_weakness'].astype(int) + # Volume confirmation
            dataframe['multi_timeframe_weakness'].astype(int) + # Multi-timeframe weakness
            dataframe['rsi_confirms_weakness'].astype(int)      # RSI confirmation
        )
        
        # Sustained weakness confirmed: need at least 3 out of 5 confirmations
        dataframe['sustained_weakness_confirmed'] = dataframe['weakness_confirmation_score'] >= 3
        
        # Additional strict confirmation: wait for sustained pattern
        dataframe['sustained_weakness_strict'] = (
            dataframe['sustained_weakness_confirmed'] &
            (dataframe['sustained_weakness_confirmed'].shift(1) == True)  # Confirmed for 2 periods
        )
        
        # Fill NaN values
        dataframe['sustained_weakness_confirmed'] = dataframe['sustained_weakness_confirmed'].fillna(False)
        dataframe['weakness_confirmation_score'] = dataframe['weakness_confirmation_score'].fillna(0)
        dataframe['multi_timeframe_weakness'] = dataframe['multi_timeframe_weakness'].fillna(False)
        dataframe['sustained_weakness_strict'] = dataframe['sustained_weakness_strict'].fillna(False)
        
        return dataframe

    def calculate_signal_quality_score(self, dataframe: DataFrame) -> DataFrame:
        """
        回滚到V1成功配置 - V2/V2.1优化失败
        
        V1成功表现:
        - 总收益: +55.73%
        - 胜率: 72.0%
        - Emergency信号: 1442次，73.2%胜率
        - 止损率: 19.1%
        
        V2/V2.1失败原因:
        - 过度优化导致信号质量下降
        - 动态止损管理器增加了止损频率
        - 信号分级逻辑错误
        """
        # 完全回滚到V1成功配置 - V2/V2.1优化彻底失败
        
        if len(dataframe) < 10:
            return dataframe
        
        # V1原始评分逻辑 - 已验证成功 (55.73%收益，72%胜率)
        dataframe['signal_quality_score'] = pd.Series(0.0, index=dataframe.index)
        
        # 1. 异常强度评分 (0-25分) - V1原始配置
        anomaly_score = pd.Series(0.0, index=dataframe.index)
        
        # Z-score基础评分 - V1配置
        price_1h_zscore = dataframe.get('price_1h_zscore', pd.Series(0, index=dataframe.index))
        price_4h_zscore = dataframe.get('price_4h_zscore', pd.Series(0, index=dataframe.index))
        volume_zscore = dataframe.get('volume_zscore', pd.Series(0, index=dataframe.index))
        
        # V1宽松评分配置
        anomaly_score += np.clip(price_1h_zscore * 5, 0, 15)  # 最大15分
        anomaly_score += np.clip(price_4h_zscore * 4, 0, 12)  # 最大12分
        anomaly_score += np.clip(volume_zscore * 3, 0, 10)    # 最大10分
        
        # 异常pump检测奖励 - V1配置
        if 'abnormal_pump_1h' in dataframe.columns:
            anomaly_score += np.where(dataframe['abnormal_pump_1h'], 8, 0)
        if 'abnormal_pump_4h' in dataframe.columns:
            anomaly_score += np.where(dataframe['abnormal_pump_4h'], 8, 0)
        
        # 2. 疲劳确认评分 (0-30分) - V1配置
        exhaustion_score = pd.Series(0.0, index=dataframe.index)
        
        exhaustion_signals = dataframe.get('exhaustion_signals', pd.Series(0, index=dataframe.index))
        exhaustion_score += np.clip(exhaustion_signals * 6, 0, 30)  # 每个信号6分
        
        # 个别疲劳信号奖励 - V1配置
        if 'rsi_overbought_declining' in dataframe.columns:
            exhaustion_score += np.where(dataframe['rsi_overbought_declining'], 8, 0)
        if 'volume_exhaustion' in dataframe.columns:
            exhaustion_score += np.where(dataframe['volume_exhaustion'], 6, 0)
        if 'failed_breakout' in dataframe.columns:
            exhaustion_score += np.where(dataframe['failed_breakout'], 7, 0)
        
        # 3. 弱势持续评分 (0-30分) - V1配置
        weakness_score = pd.Series(0.0, index=dataframe.index)
        
        weakness_confirmation_score = dataframe.get('weakness_confirmation_score', pd.Series(0, index=dataframe.index))
        consecutive_red = dataframe.get('consecutive_red', pd.Series(0, index=dataframe.index))
        
        weakness_score += np.clip(weakness_confirmation_score * 6, 0, 25)  # 最大25分
        weakness_score += np.clip(consecutive_red * 4, 0, 10)             # 最大10分
        
        # 失败反弹奖励 - V1配置
        if 'failed_bounce' in dataframe.columns:
            weakness_score += np.where(dataframe['failed_bounce'], 5, 0)
        
        # 4. 市场环境评分 (0-20分) - V1配置
        environment_score = pd.Series(20.0, index=dataframe.index)  # 起始20分
        
        # V1环境评分逻辑
        strong_uptrend = dataframe.get('strong_uptrend', pd.Series(False, index=dataframe.index))
        environment_score = np.where(strong_uptrend, 5, environment_score)  # 强势上涨减分
        
        market_regime = dataframe.get('market_regime', pd.Series('sideways', index=dataframe.index))
        environment_score = np.where(market_regime == 'bearish', environment_score + 5, environment_score)  # 熊市奖励
        environment_score = np.clip(environment_score, 5, 25)  # 最少5分
        
        # 5. 成交量确认评分 (0-15分) - V1配置
        volume_score = pd.Series(0.0, index=dataframe.index)
        
        abnormal_volume = dataframe.get('abnormal_volume', pd.Series(False, index=dataframe.index))
        volume_confirms_weakness = dataframe.get('volume_confirms_weakness', pd.Series(False, index=dataframe.index))
        
        volume_score += np.where(abnormal_volume, 8, 0)  # V1配置
        volume_score += np.where(volume_confirms_weakness, 6, 0)  # V1配置
        
        # V1基础成交量分数
        volume_score += 3  # 基础3分
        
        # V1最终评分合并
        dataframe['signal_quality_score'] = (
            anomaly_score + 
            exhaustion_score + 
            weakness_score + 
            environment_score + 
            volume_score
        )
        
        # V1分数范围
        dataframe['signal_quality_score'] = np.clip(dataframe['signal_quality_score'], 0, 100)
        
        # 完全回滚到V1分级配置
        dataframe['signal_quality_tier'] = pd.Series('low', index=dataframe.index)
        dataframe['signal_quality_tier'] = np.where(
            dataframe['signal_quality_score'] >= 80, 'excellent',
            np.where(dataframe['signal_quality_score'] >= 65, 'high',
                    np.where(dataframe['signal_quality_score'] >= 50, 'medium',
                            np.where(dataframe['signal_quality_score'] >= 35, 'fair', 'low')))
        )
        
        return dataframe

    def populate_hourly_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate indicators for hourly timeframe analysis
        """
        
        # Basic hourly indicators
        dataframe['volume_sma'] = pd.Series(ta.SMA(dataframe['volume'], timeperiod=24), index=dataframe.index)  # 24-hour volume average
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # Task 3.1: Candlestick pattern recognition
        dataframe['doji_signal'] = self.detect_doji_pattern(dataframe)
        dataframe['spinning_top'] = self.detect_spinning_top_pattern(dataframe)
        dataframe['shooting_star'] = self.detect_shooting_star_pattern(dataframe)
        dataframe['pattern_confidence'] = self.calculate_pattern_confidence(dataframe)
        
        # Combine reversal patterns
        dataframe['reversal_pattern'] = (
            dataframe['doji_signal'] | 
            dataframe['spinning_top'] | 
            dataframe['shooting_star']
        )
        
        # Task 3.2: Volume spike detection
        dataframe['volume_spike'] = self.detect_volume_spike(dataframe)
        dataframe['volume_spike_strength'] = self.calculate_volume_spike_strength(dataframe)
        
        # Task 3.3: Price action confirmation
        dataframe['spike_and_retreat'] = self.detect_spike_and_retreat_pattern(dataframe)
        dataframe['price_retreat_confirmed'] = self.confirm_price_reversal(dataframe)
        
        return dataframe

    def calculate_reversal_strength(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate the strength of reversal signals (0-1 scale)
        Higher values indicate stronger reversal probability
        """
        # Initialize strength score
        strength = pd.Series(0.0, index=dataframe.index)
        
        # Factor 1: Candlestick pattern confidence
        pattern_confidence = dataframe.get('pattern_confidence', pd.Series(0.0, index=dataframe.index))
        strength += pattern_confidence * 0.25  # 25% weight
        
        # Factor 2: Volume spike strength
        volume_spike_strength = dataframe.get('volume_spike_strength', pd.Series(0.0, index=dataframe.index))
        strength += volume_spike_strength * 0.25  # 25% weight
        
        # Factor 3: RSI overbought level
        rsi = ta.RSI(dataframe, timeperiod=14)
        rsi_strength = np.clip((rsi - 70) / 30, 0, 1)  # Scale 70-100 RSI to 0-1
        strength += rsi_strength * 0.20  # 20% weight
        
        # Factor 4: Price extension from moving average
        sma_20 = pd.Series(ta.SMA(dataframe['close'], timeperiod=20), index=dataframe.index)
        price_extension = (dataframe['close'] - sma_20) / sma_20
        extension_strength = np.clip(price_extension / 0.1, 0, 1)  # Scale 0-10% extension to 0-1
        strength += extension_strength * 0.15  # 15% weight
        
        # Factor 5: Volume-price divergence strength
        price_change_3 = (dataframe['close'] - dataframe['close'].shift(3)) / dataframe['close'].shift(3)
        volume_change_3 = (dataframe['volume'] - dataframe['volume'].shift(3)) / dataframe['volume'].shift(3)
        
        # Divergence when price up but volume down (or vice versa)
        divergence_strength = np.where(
            (price_change_3 > 0) & (volume_change_3 < 0),
            np.clip(abs(volume_change_3), 0, 0.5) * 2,  # Scale to 0-1
            0
        )
        strength += divergence_strength * 0.15  # 15% weight
        
        # Ensure strength is between 0 and 1
        return np.clip(strength, 0, 1)



    def identify_bullish_conditions(self, dataframe: DataFrame) -> pd.Series:
        """
        Identify bullish market conditions that should prevent short entries
        Returns boolean series indicating bullish periods to avoid
        """
        # Strong uptrend indicators that suggest avoiding shorts
        bullish_conditions = pd.Series(False, index=dataframe.index)
        
        # 1. Strong daily uptrend (multiple consecutive green days beyond threshold)
        excessive_green_days = dataframe.get('consecutive_green_days_1d', pd.Series(0, index=dataframe.index)) > 7
        
        # 2. Parabolic price movement (extreme range expansion)
        extreme_range_expansion = dataframe.get('range_expansion_count_1d', pd.Series(0, index=dataframe.index)) > 5
        
        # 3. Volume explosion without retreat (FOMO buying)
        volume_explosion = dataframe.get('volume_expansion_count_1d', pd.Series(0, index=dataframe.index)) > 6
        
        # 4. Hourly momentum still very strong (no reversal signals)
        strong_hourly_momentum = (
            (dataframe.get('pattern_confidence', pd.Series(0, index=dataframe.index)) < 0.3) &
            (dataframe.get('volume_spike_strength', pd.Series(0, index=dataframe.index)) < 0.4)
        )
        
        # 5. Price above key resistance levels (breakout continuation)
        # This would be implemented with resistance level detection
        # For now, use a simple price momentum filter
        recent_high = dataframe['high'].rolling(window=24).max()  # 24-hour high
        price_near_highs = dataframe['close'] > (recent_high * 0.98)  # Within 2% of recent high
        
        # Combine bullish conditions (any of these suggests avoiding shorts)
        bullish_conditions = (
            excessive_green_days |
            extreme_range_expansion |
            volume_explosion |
            (strong_hourly_momentum & price_near_highs)
        )
        
        return bullish_conditions

    def validate_abnormal_pump_conditions(self, dataframe: DataFrame) -> pd.Series:
        """
        Validation for abnormal pump + trend exhaustion strategy
        Core principle: Statistical outliers + multi-angle trend exhaustion confirmation
        """
        # 1. Abnormal pump detected (statistical outlier)
        abnormal_pump_detected = (
            (dataframe.get('abnormal_pump', pd.Series(False, index=dataframe.index)) == True) |
            (dataframe.get('extreme_abnormal_pump', pd.Series(False, index=dataframe.index)) == True)
        )
        
        # 2. Trend exhaustion signals (stricter multi-angle confirmation)
        exhaustion_confirmed = (
            (dataframe.get('trend_exhaustion', pd.Series(False, index=dataframe.index)) == True) &
            ~(dataframe.get('strong_uptrend', pd.Series(False, index=dataframe.index)) == True)  # Not in strong uptrend
        )
        
        # 3. Volume supporting the thesis (abnormal volume during pump)
        volume_support = (
            (dataframe.get('abnormal_volume', pd.Series(False, index=dataframe.index)) == True) |
            (dataframe.get('volume_zscore', pd.Series(0, index=dataframe.index)) > 1.5)
        )
        
        # 4. Basic market conditions
        basic_conditions = (
            (dataframe.get('liquidity_score_1d', pd.Series(50, index=dataframe.index)) >= self.min_liquidity_score.value) &
            (dataframe['volume'] > 0)
        )
        
        # All conditions must be met
        valid_conditions = abnormal_pump_detected & exhaustion_confirmed & volume_support & basic_conditions
        
        return valid_conditions

    def calculate_short_signal_strength(self, dataframe: DataFrame) -> pd.Series:
        """
        Calculate the strength of short signals (0-1 scale)
        Higher values indicate stronger short opportunities
        """
        # Get multi-timeframe signal coordination
        combined_strength = self.coordinate_multi_timeframe_signals(dataframe)
        
        # Apply pump-dump specific adjustments
        short_conditions_valid = self.validate_pump_dump_conditions(dataframe)
        
        # Reduce signal strength in bullish conditions
        bullish_penalty = self.identify_bullish_conditions(dataframe)
        
        # Calculate final short signal strength
        short_strength = combined_strength.copy()
        
        # Zero out signals where short conditions are not valid
        short_strength = np.where(short_conditions_valid, short_strength, 0)
        
        # Apply bullish penalty (reduce strength by 50% in bullish conditions)
        short_strength = np.where(bullish_penalty, short_strength * 0.5, short_strength)
        
        return pd.Series(short_strength, index=dataframe.index)

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        SHORT-ONLY STRATEGY - Pump & Dump focused strategy
        
        Entry Logic:
        1. Detect pump (sudden price surge) signals
        2. Identify reversal confirmation after pump
        3. Enter short positions at optimal reversal points
        4. Apply liquidity and risk management filters
        """
        
        # Add abnormal pump detection (based on statistical analysis)
        dataframe = self.detect_abnormal_pump(dataframe)
        
        # Add trend exhaustion detection (multi-angle verification)
        dataframe = self.detect_trend_exhaustion(dataframe)
        
        # Task 4.1: Add sustained weakness confirmation
        dataframe = self.detect_sustained_weakness_confirmation(dataframe)
        
        # Add signal quality scoring system
        dataframe = self.calculate_signal_quality_score(dataframe)
        
        # Skip legacy signal strength calculation to avoid old parameter references
        
        # Minimum signal strength threshold for entry
        min_signal_strength_value = self.min_signal_strength.value
        
        # Priority signal detection for immediate entries
        priority_signals = self.check_signal_priority_requirements(dataframe)
        
        # Balanced Standard short conditions - 平衡质量和数量
        main_short_conditions = (
            # 1. 放宽的异常检测 (More accessible anomaly detection)
            (
                (dataframe.get('abnormal_pump', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('abnormal_pump_1h', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('abnormal_pump_4h', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('abnormal_volume', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('price_1h_zscore', pd.Series(0, index=dataframe.index)) > 1.5) |
                (dataframe.get('price_4h_zscore', pd.Series(0, index=dataframe.index)) > 1.5)
            ) &
            
            # 2. 放宽的趋势疲劳确认 (More accessible exhaustion confirmation)
            (
                (dataframe.get('trend_exhaustion', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('exhaustion_signals', pd.Series(0, index=dataframe.index)) >= 2) |
                (dataframe.get('rsi_overbought_declining', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('volume_exhaustion', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('failed_breakout', pd.Series(False, index=dataframe.index)) == True)
            ) &
            
            # 3. 基础弱势确认 (Basic weakness confirmation)
            (
                (dataframe.get('sustained_weakness_confirmed', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('weakness_confirmation_score', pd.Series(0, index=dataframe.index)) >= 2) |
                (dataframe.get('consecutive_red', pd.Series(0, index=dataframe.index)) >= 1) |
                (dataframe.get('failed_bounce', pd.Series(False, index=dataframe.index)) == True)
            ) &
            
            # 4. 回滚到V1: Standard信号质量要求
            (dataframe.get('signal_quality_score', pd.Series(0, index=dataframe.index)) >= 35) &
            
            # 5. 避免强势上涨趋势 (Avoid strong uptrends)
            ~(dataframe.get('strong_uptrend', pd.Series(False, index=dataframe.index)) == True) &
            
            # 6. 放宽流动性要求 (Relaxed liquidity requirement)
            (dataframe.get('liquidity_score_1d', pd.Series(50, index=dataframe.index)) >= (self.min_liquidity_score.value * 0.7)) &
            
            # 7. 基础成交量确认 (Basic volume confirmation)
            (dataframe['volume'] > 0)
        )
        
        # Priority short conditions - 高质量但可达到的信号
        priority_short_conditions = (
            # 1. 较强异常信号 (Moderately strong abnormal signals)
            (
                (dataframe.get('extreme_abnormal_pump', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('abnormal_pump', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('price_1h_zscore', pd.Series(0, index=dataframe.index)) > 2.0) |
                (
                    (dataframe.get('abnormal_volume', pd.Series(False, index=dataframe.index)) == True) &
                    (dataframe.get('price_1h_zscore', pd.Series(0, index=dataframe.index)) > 1.5)
                )
            ) &
            
            # 2. 较强趋势疲劳信号 (Moderately strong exhaustion signals)
            (
                (dataframe.get('strong_trend_exhaustion', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('trend_exhaustion', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('exhaustion_signals', pd.Series(0, index=dataframe.index)) >= 3) |
                (
                    (dataframe.get('exhaustion_signals', pd.Series(0, index=dataframe.index)) >= 2) &
                    (dataframe.get('rsi_overbought_declining', pd.Series(False, index=dataframe.index)) == True)
                )
            ) &
            
            # 3. 弱势确认 (Weakness confirmation)
            (
                (dataframe.get('sustained_weakness_confirmed', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('weakness_confirmation_score', pd.Series(0, index=dataframe.index)) >= 2) |
                (dataframe.get('failed_breakout', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('consecutive_red', pd.Series(0, index=dataframe.index)) >= 2)
            ) &
            
            # 4. 回滚到V1: Priority信号质量要求
            (dataframe.get('signal_quality_score', pd.Series(0, index=dataframe.index)) >= 50) &
            
            # 5. 避免强势上涨趋势 (Avoid strong uptrends)
            ~(dataframe.get('strong_uptrend', pd.Series(False, index=dataframe.index)) == True) &
            
            # 6. 基本要求 (Basic requirements)
            (dataframe.get('liquidity_score_1d', pd.Series(50, index=dataframe.index)) >= (self.min_liquidity_score.value * 0.6)) &
            (dataframe['volume'] > 0)
        )
        
        # Accessible backup conditions - 确保有足够的交易信号
        backup_short_conditions = (
            # 基础异常检测 (Basic anomaly detection)
            (
                (dataframe.get('price_1h_zscore', pd.Series(0, index=dataframe.index)) > 1.2) |
                (dataframe.get('price_4h_zscore', pd.Series(0, index=dataframe.index)) > 1.2) |
                (dataframe.get('volume_zscore', pd.Series(0, index=dataframe.index)) > 1.5) |
                (dataframe.get('abnormal_pump_1h', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('abnormal_pump_4h', pd.Series(False, index=dataframe.index)) == True)
            ) &
            
            # 基础反转信号确认 (Basic reversal signal confirmation)
            (
                (dataframe.get('rsi_overbought_declining', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('volume_exhaustion', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('failed_breakout', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('weak_bodies', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('exhaustion_signals', pd.Series(0, index=dataframe.index)) >= 1)
            ) &
            
            # 最基础的弱势确认 (Most basic weakness confirmation)
            (
                (dataframe.get('weakness_confirmation_score', pd.Series(0, index=dataframe.index)) >= 1) |
                (dataframe.get('consecutive_red', pd.Series(0, index=dataframe.index)) >= 1) |
                (dataframe.get('failed_bounce', pd.Series(False, index=dataframe.index)) == True)
            ) &
            
            # 回滚到V1: Backup信号质量要求
            (dataframe.get('signal_quality_score', pd.Series(0, index=dataframe.index)) >= 25) &
            
            # Not in strong uptrend
            ~(dataframe.get('strong_uptrend', pd.Series(False, index=dataframe.index)) == True) &
            
            # Basic volume requirement
            (dataframe['volume'] > 0)
        )
        
        # Emergency fallback conditions - 确保能产生一些信号
        emergency_short_conditions = (
            # 回滚到V1: Emergency信号无质量要求 (这是V1成功的关键)
            
            # 基本异常检测 - V1配置
            (
                (dataframe.get('price_1h_zscore', pd.Series(0, index=dataframe.index)) > 1.0) |
                (dataframe.get('volume_zscore', pd.Series(0, index=dataframe.index)) > 1.0)
            ) &
            
            # 基本反转信号 - V1配置
            (
                (dataframe.get('rsi_overbought_declining', pd.Series(False, index=dataframe.index)) == True) |
                (dataframe.get('consecutive_red', pd.Series(0, index=dataframe.index)) >= 1) |
                (dataframe.get('volume_exhaustion', pd.Series(False, index=dataframe.index)) == True)
            ) &
            
            # 不在强势上涨中 - V1配置
            ~(dataframe.get('strong_uptrend', pd.Series(False, index=dataframe.index)) == True) &
            
            # 基本成交量 - V1配置
            (dataframe['volume'] > 0)
        )
        
        # Combine entry conditions (priority > main > backup > emergency)
        final_short_conditions = priority_short_conditions | main_short_conditions | backup_short_conditions | emergency_short_conditions
        
        # Set short entry signals with appropriate tags (priority order)
        dataframe.loc[priority_short_conditions, ['enter_short', 'enter_tag']] = (1, 'priority_short')
        dataframe.loc[main_short_conditions & ~priority_short_conditions, ['enter_short', 'enter_tag']] = (1, 'standard_short')
        dataframe.loc[backup_short_conditions & ~main_short_conditions & ~priority_short_conditions, ['enter_short', 'enter_tag']] = (1, 'backup_short')
        dataframe.loc[emergency_short_conditions & ~backup_short_conditions & ~main_short_conditions & ~priority_short_conditions, ['enter_short', 'enter_tag']] = (1, 'emergency_short')
        
        # Simplified validation - focus on core logic
        # Remove complex validation systems that rely on old parameters
        
        # Basic market condition check - simplified
        if len(dataframe) > 0 and dataframe['volume'].iloc[-1] > 0:
            # Allow trading if basic conditions are met
            pass
        
        # Explicitly prevent any long entries (short-only strategy)
        dataframe['enter_long'] = 0
        
        return dataframe

    def calculate_support_based_targets(self, dataframe: DataFrame) -> dict:
        """
        Calculate profit targets based on identified support levels
        Returns dictionary with multiple target levels
        """
        current_price = dataframe['close'].iloc[-1]
        
        # Get support levels
        support_level = dataframe.get('support_levels_1d', pd.Series(current_price * 0.95, index=dataframe.index)).iloc[-1]
        volume_cluster = dataframe.get('volume_clusters_1d', pd.Series(current_price * 0.97, index=dataframe.index)).iloc[-1]
        consolidation_zone = dataframe.get('consolidation_zones_1d', pd.Series(current_price * 0.96, index=dataframe.index)).iloc[-1]
        
        # Calculate target levels (for short positions, targets are below current price)
        targets = {}
        
        # Target 1: Conservative target at nearest support
        nearest_support = max(support_level, volume_cluster, consolidation_zone)
        if nearest_support < current_price:
            targets['target_1'] = {
                'price': nearest_support,
                'profit_pct': (current_price - nearest_support) / current_price,
                'confidence': 0.8,
                'description': 'Nearest Support Level'
            }
        
        # Target 2: Aggressive target at strongest support
        strongest_support = min(support_level, volume_cluster, consolidation_zone)
        if strongest_support < current_price and strongest_support != nearest_support:
            targets['target_2'] = {
                'price': strongest_support,
                'profit_pct': (current_price - strongest_support) / current_price,
                'confidence': 0.6,
                'description': 'Strong Support Level'
            }
        
        # Target 3: Extended target based on daily range
        daily_range = dataframe['daily_range_1d'].tail(10).mean()
        extended_target = current_price - (daily_range * 1.5)
        if extended_target > 0:
            targets['target_3'] = {
                'price': extended_target,
                'profit_pct': (current_price - extended_target) / current_price,
                'confidence': 0.4,
                'description': 'Extended Range Target'
            }
        
        return targets

    def check_profit_target_conditions(self, dataframe: DataFrame) -> pd.Series:
        """
        Check if current conditions warrant taking profits at support levels
        Returns boolean series indicating profit-taking opportunities
        """
        current_price = dataframe['close']
        
        # Get support levels
        support_level = dataframe.get('support_levels_1d', current_price * 0.95)
        volume_cluster = dataframe.get('volume_clusters_1d', current_price * 0.97)
        
        # Profit target conditions
        profit_conditions = pd.Series(False, index=dataframe.index)
        
        # Condition 1: Price approaching support level
        near_support = (
            (current_price <= support_level * 1.02) |  # Within 2% of support
            (current_price <= volume_cluster * 1.02)   # Within 2% of volume cluster
        )
        
        # Condition 2: Volume increase at support (potential bounce)
        volume_sma = dataframe['volume'].rolling(window=24).mean()
        volume_increase = dataframe['volume'] > (volume_sma * 1.5)
        
        # Condition 3: Reversal patterns forming at support
        reversal_patterns = (
            (dataframe.get('doji_signal', pd.Series(False, index=dataframe.index)) == True) |
            (dataframe.get('spinning_top', pd.Series(False, index=dataframe.index)) == True)
        )
        
        # Condition 4: RSI oversold at support (bounce potential)
        # This would require RSI calculation - using placeholder
        oversold_condition = pd.Series(True, index=dataframe.index)  # Placeholder
        
        # Combine profit target conditions
        profit_conditions = near_support & (volume_increase | reversal_patterns | oversold_condition)
        
        return profit_conditions

    def calculate_multiple_exit_levels(self, dataframe: DataFrame) -> dict:
        """
        Calculate multiple exit levels for staged profit taking
        Returns dictionary with exit levels and their conditions
        """
        targets = self.calculate_support_based_targets(dataframe)
        profit_conditions = self.check_profit_target_conditions(dataframe)
        
        exit_levels = {}
        
        # Level 1: Quick profit (25% of position)
        if 'target_1' in targets:
            exit_levels['quick_profit'] = {
                'trigger_condition': profit_conditions & (dataframe['close'] <= targets['target_1']['price']),
                'position_pct': 0.25,
                'description': f"Quick profit at {targets['target_1']['description']}",
                'tag': 'quick_profit'
            }
        
        # Level 2: Main profit (50% of position)
        if 'target_2' in targets:
            exit_levels['main_profit'] = {
                'trigger_condition': profit_conditions & (dataframe['close'] <= targets['target_2']['price']),
                'position_pct': 0.50,
                'description': f"Main profit at {targets['target_2']['description']}",
                'tag': 'main_profit'
            }
        
        # Level 3: Extended profit (remaining 25% of position)
        if 'target_3' in targets:
            exit_levels['extended_profit'] = {
                'trigger_condition': profit_conditions & (dataframe['close'] <= targets['target_3']['price']),
                'position_pct': 0.25,
                'description': f"Extended profit at {targets['target_3']['description']}",
                'tag': 'extended_profit'
            }
        
        return exit_levels

    def check_emergency_exit_conditions(self, dataframe: DataFrame) -> pd.Series:
        """
        Check for emergency exit conditions that override profit targets
        Returns boolean series indicating emergency exit situations
        """
        emergency_conditions = pd.Series(False, index=dataframe.index)
        
        # Emergency Condition 1: Strong reversal pattern against position
        strong_bullish_reversal = (
            (dataframe.get('consecutive_green_days_1d', pd.Series(0, index=dataframe.index)) >= 2) &
            (dataframe.get('volume_expansion_count_1d', pd.Series(0, index=dataframe.index)) >= 2) &
            (dataframe.get('volume_spike_strength', pd.Series(0, index=dataframe.index)) >= 0.8)
        )
        
        # Emergency Condition 2: Breaking above key resistance
        recent_high = dataframe['high'].rolling(window=48).max()  # 48-hour high
        breaking_resistance = dataframe['close'] > (recent_high * 1.01)  # 1% above recent high
        
        # Emergency Condition 3: Extreme volume spike with price reversal
        volume_sma = dataframe['volume'].rolling(window=24).mean()
        extreme_volume = dataframe['volume'] > (volume_sma * 3)
        price_reversal = dataframe['close'] > dataframe['open']
        
        # Combine emergency conditions
        emergency_conditions = (
            strong_bullish_reversal |
            breaking_resistance |
            (extreme_volume & price_reversal)
        )
        
        return emergency_conditions

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        Implements support level-based profit targets with multiple exit levels
        """
        
        # Calculate multiple exit levels
        exit_levels = self.calculate_multiple_exit_levels(dataframe)
        
        # Check emergency exit conditions
        emergency_exit = self.check_emergency_exit_conditions(dataframe)
        
        # Initialize exit signals
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = ''
        
        # Emergency exits (highest priority)
        emergency_conditions = emergency_exit & (dataframe['volume'] > 0)
        dataframe.loc[emergency_conditions, ['exit_short', 'exit_tag']] = (1, 'emergency_exit')
        
        # Profit target exits (staged approach)
        for level_name, level_info in exit_levels.items():
            if 'trigger_condition' in level_info:
                # Only trigger if not already in emergency exit
                level_conditions = level_info['trigger_condition'] & ~emergency_conditions
                
                # Set exit signal for this level
                dataframe.loc[level_conditions, ['exit_short', 'exit_tag']] = (1, level_info['tag'])
        
        # Additional exit conditions
        
        # Time-based exit (prevent holding too long)
        # This would require trade duration tracking - placeholder for now
        
        # Volatility-based exit (exit if volatility drops too much - trend exhaustion)
        low_volatility = dataframe.get('daily_range_1d', pd.Series(1, index=dataframe.index)) < (
            dataframe.get('daily_range_1d', pd.Series(1, index=dataframe.index)).rolling(window=20).mean() * 0.5
        )
        
        stagnant_conditions = low_volatility & ~emergency_conditions & (dataframe['volume'] > 0)
        dataframe.loc[stagnant_conditions, ['exit_short', 'exit_tag']] = (1, 'low_volatility_exit')
        
        # Liquidity-based exit (exit if liquidity deteriorates)
        poor_liquidity = (
            (dataframe.get('liquidity_score_1d', pd.Series(60, index=dataframe.index)) < (self.min_liquidity_score.value * 0.8)) |
            (dataframe.get('high_liquidity_1d', pd.Series(True, index=dataframe.index)) == False)
        )
        
        liquidity_exit_conditions = poor_liquidity & ~emergency_conditions & (dataframe['volume'] > 0)
        dataframe.loc[liquidity_exit_conditions, ['exit_short', 'exit_tag']] = (1, 'liquidity_exit')
        
        return dataframe

    def calculate_position_size(self, pair: str, current_rate: float, 
                               account_balance: float) -> float:
        """
        Calculate position size based on risk management rules
        
        Args:
            pair: Trading pair
            current_rate: Current price
            account_balance: Available account balance
            
        Returns:
            Position size as percentage of balance
        """
        try:
            # Get current dataframe for volatility assessment
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) == 0:
                return self.max_position_risk.value
            
            # Calculate recent volatility using ATR
            atr = ta.ATR(dataframe, timeperiod=14)
            current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
            
            if current_atr <= 0:
                return self.max_position_risk.value
            
            # Calculate volatility as percentage of price
            volatility_pct = current_atr / current_rate
            
            # Adjust position size based on volatility
            # Higher volatility = smaller position size
            if volatility_pct > 0.05:  # High volatility (>5%)
                risk_multiplier = 0.5
            elif volatility_pct > 0.03:  # Medium volatility (3-5%)
                risk_multiplier = 0.75
            else:  # Low volatility (<3%)
                risk_multiplier = 1.0
            
            # Calculate base position size
            base_position_risk = self.max_position_risk.value * risk_multiplier
            
            # Additional risk adjustment based on liquidity
            liquidity_score = dataframe.get('liquidity_score_1d', pd.Series(60)).iloc[-1]
            if liquidity_score < 50:  # Low liquidity
                liquidity_multiplier = 0.5
            elif liquidity_score < 70:  # Medium liquidity
                liquidity_multiplier = 0.8
            else:  # High liquidity
                liquidity_multiplier = 1.0
            
            # Final position size calculation
            final_position_risk = base_position_risk * liquidity_multiplier
            
            # Ensure within bounds
            final_position_risk = np.clip(final_position_risk, 0.01, self.max_position_risk.value)
            
            return final_position_risk
            
        except Exception as e:
            self.logger.warning(f"Position size calculation failed for {pair}: {e}")
            return self.max_position_risk.value

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], 
                 side: str, **kwargs) -> float:
        """
        Customize leverage for each new trade with risk-adjusted sizing
        
        Features:
        - Fixed base leverage as configured
        - Risk adjustment based on volatility
        - Liquidity-based leverage modification
        - Maximum leverage limits
        """
        try:
            # Get current dataframe for risk assessment
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) == 0:
                return self.leverage_num
            
            # Base leverage
            base_leverage = self.leverage_num
            
            # Volatility adjustment
            atr = ta.ATR(dataframe, timeperiod=14)
            current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
            
            if current_atr > 0:
                volatility_pct = current_atr / current_rate
                
                # Reduce leverage in high volatility conditions
                if volatility_pct > 0.06:  # Very high volatility
                    volatility_multiplier = 0.6
                elif volatility_pct > 0.04:  # High volatility
                    volatility_multiplier = 0.8
                else:  # Normal volatility
                    volatility_multiplier = 1.0
                
                adjusted_leverage = base_leverage * volatility_multiplier
            else:
                adjusted_leverage = base_leverage
            
            # Liquidity adjustment
            liquidity_score = dataframe.get('liquidity_score_1d', pd.Series(60)).iloc[-1]
            if liquidity_score < 50:  # Low liquidity - reduce leverage
                liquidity_multiplier = 0.7
            elif liquidity_score < 70:  # Medium liquidity
                liquidity_multiplier = 0.9
            else:  # High liquidity - full leverage
                liquidity_multiplier = 1.0
            
            final_leverage = adjusted_leverage * liquidity_multiplier
            
            # Ensure leverage is within acceptable bounds
            final_leverage = np.clip(final_leverage, 1.0, min(max_leverage, 5.0))
            
            return final_leverage
            
        except Exception as e:
            self.logger.warning(f"Leverage calculation failed for {pair}: {e}")
            return self.leverage_num

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                           proposed_stake: float, min_stake: Optional[float], max_stake: float,
                           leverage: float, entry_tag: Optional[str], side: str,
                           **kwargs) -> float:
        """
        Customize stake amount based on risk management and position sizing rules
        
        This method calculates the optimal stake amount considering:
        - Account risk limits
        - Volatility-adjusted position sizing
        - Liquidity considerations
        - Leverage effects
        """
        try:
            # Get account balance (approximate from max_stake)
            estimated_balance = max_stake * 10  # Rough estimate
            
            # Calculate risk-adjusted position size
            position_risk_pct = self.calculate_position_size(pair, current_rate, estimated_balance)
            
            # Calculate stake amount based on risk percentage
            risk_based_stake = estimated_balance * position_risk_pct
            
            # Adjust for leverage (higher leverage = smaller base stake needed)
            leverage_adjusted_stake = risk_based_stake / leverage
            
            # Ensure within platform limits
            if min_stake:
                final_stake = max(leverage_adjusted_stake, min_stake)
            else:
                final_stake = leverage_adjusted_stake
            
            final_stake = min(final_stake, max_stake)
            
            return final_stake
            
        except Exception as e:
            self.logger.warning(f"Stake amount calculation failed for {pair}: {e}")
            return proposed_stake

    def calculate_volatility_adjusted_stop(self, dataframe: DataFrame, current_rate: float) -> float:
        """
        Task 5.1: Enhanced volatility-adjusted stop loss with market condition awareness
        
        Features:
        - ATR-based volatility measurement
        - Market regime adjustment
        - Consecutive loss protection
        - Volume-based stop adjustment
        """
        if len(dataframe) < self.volatility_lookback.value:
            return self.stoploss
        
        # Calculate ATR for volatility measurement
        atr = ta.ATR(dataframe, timeperiod=self.volatility_lookback.value)
        current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
        
        if current_atr <= 0:
            return self.stoploss
        
        # Calculate ATR as percentage of current price
        atr_percentage = current_atr / current_rate
        
        # Base volatility adjustment
        base_distance = atr_percentage * self.volatility_multiplier.value
        
        # Market condition adjustments
        current_data = dataframe.iloc[-1]
        
        # 1. High volatility adjustment - widen stops in volatile markets
        recent_volatility = dataframe['close'].rolling(window=10).std() / dataframe['close'].rolling(window=10).mean()
        volatility_factor = 1.0
        if len(recent_volatility) > 0 and not pd.isna(recent_volatility.iloc[-1]):
            vol_percentile = recent_volatility.iloc[-1]
            if vol_percentile > 0.05:  # High volatility (>5%)
                volatility_factor = 1.5  # Widen stops by 50%
            elif vol_percentile > 0.03:  # Medium volatility (3-5%)
                volatility_factor = 1.2  # Widen stops by 20%
        
        # 2. Volume confirmation adjustment - tighten stops on low volume
        volume_factor = 1.0
        if 'volume' in dataframe.columns:
            recent_volume = dataframe['volume'].rolling(window=20).mean()
            current_volume = current_data.get('volume', 0)
            if len(recent_volume) > 0 and not pd.isna(recent_volume.iloc[-1]) and recent_volume.iloc[-1] > 0:
                volume_ratio = current_volume / recent_volume.iloc[-1]
                if volume_ratio < 0.5:  # Low volume
                    volume_factor = 0.8  # Tighten stops by 20%
                elif volume_ratio > 2.0:  # High volume
                    volume_factor = 1.1  # Slightly widen stops
        
        # 3. Market regime adjustment
        regime_factor = 1.0
        if current_data.get('strong_uptrend', False):
            regime_factor = 1.3  # Wider stops in strong uptrends (avoid premature exits)
        elif current_data.get('market_regime', 'sideways') == 'bearish':
            regime_factor = 0.9  # Tighter stops in bearish markets
        
        # Apply all adjustments
        adjusted_distance = base_distance * volatility_factor * volume_factor * regime_factor
        
        # Apply min/max bounds with dynamic adjustment
        min_distance = self.min_trailing_distance.value
        max_distance = self.max_trailing_distance.value
        
        # Dynamic bounds based on market conditions
        if volatility_factor > 1.2:  # High volatility
            max_distance *= 1.2  # Allow wider stops
        
        stop_distance = np.clip(adjusted_distance, min_distance, max_distance)
        
        # Return as negative value (stop loss)
        return -stop_distance

    def calculate_profit_lock_level(self, current_profit: float) -> float:
        """
        Task 5.1: Enhanced profit locking with dynamic protection levels
        
        Features:
        - Progressive profit protection
        - Time-based profit locking
        - Market condition awareness
        """
        if current_profit <= 0:
            return self.stoploss
        
        # Progressive profit locking with enhanced levels
        if current_profit >= self.profit_lock_threshold.value:
            # More aggressive profit locking to reduce stop loss frequency
            if current_profit >= 0.15:  # 15% profit - very aggressive locking
                lock_ratio = 0.8  # Lock 80% of profits
            elif current_profit >= 0.10:  # 10% profit - aggressive locking
                lock_ratio = 0.7  # Lock 70% of profits
            elif current_profit >= 0.06:  # 6% profit - moderate locking
                lock_ratio = 0.6  # Lock 60% of profits
            elif current_profit >= 0.03:  # 3% profit - conservative locking
                lock_ratio = 0.4  # Lock 40% of profits
            else:  # Small profits
                lock_ratio = 0.2  # Lock 20% of profits
            
            # Calculate locked profit level
            locked_profit = current_profit * lock_ratio
            
            # Ensure we don't lock in less than break-even
            min_lock_level = max(-0.01, self.stoploss * 0.5)  # At least break-even or half original stop
            profit_lock_stop = max(min_lock_level, -(abs(self.stoploss) - locked_profit))
            
            return profit_lock_stop
        
        return self.stoploss

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        回滚到V1成功配置 - 禁用V2动态止损管理器
        
        V1成功表现: 19.1%止损率
        V2失败表现: 54.3%止损率 
        V2.1更糟: 58.9%止损率
        
        结论: 动态止损管理器增加了止损频率，完全回滚
        """
        try:
            # 获取当前数据框进行分析
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) == 0:
                return self.stoploss
            
            # V1原始止损逻辑 - 已验证成功
            volatility_stop = self.calculate_volatility_adjusted_stop(dataframe, current_rate)
            profit_lock_stop = self.calculate_profit_lock_level(current_profit)
            
            # V1时间调整 - 给新交易更多空间
            trade_duration = current_time - trade.open_date_utc
            time_factor = 1.0
            
            if trade_duration.total_seconds() < 600:  # 10分钟内
                time_factor = 1.4  # 40%更宽松
            elif trade_duration.total_seconds() < 1800:  # 30分钟内
                time_factor = 1.2  # 20%更宽松
            elif trade_duration.total_seconds() < 3600:  # 1小时内
                time_factor = 1.1  # 10%更宽松
            
            # V1信号质量调整
            current_data = dataframe.iloc[-1]
            signal_factor = 1.0
            
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            if entry_tag in ['priority_short', 'standard_short']:
                signal_factor = 1.3  # 30%更宽松
            
            # V1市场动量调整
            momentum_factor = 1.0
            
            if len(dataframe) >= 3:
                recent_closes = dataframe['close'].tail(3)
                if len(recent_closes) >= 3:
                    price_trend = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / recent_closes.iloc[0]
                    if price_trend < -0.01:  # 价格下跌有利
                        momentum_factor = 1.2  # 20%更宽松
                    elif price_trend > 0.02:  # 价格上涨不利
                        momentum_factor = 0.9  # 10%更紧
            
            # V1调整应用
            adjusted_volatility_stop = volatility_stop * time_factor * signal_factor * momentum_factor
            
            # V1紧急调整 - 更保守
            emergency_factor = 1.0
            
            if (current_data.get('strong_uptrend', False) and 
                current_profit < -0.025 and 
                trade_duration.total_seconds() > 1800):
                emergency_factor = 0.85  # 仅在极端情况下收紧
            
            final_volatility_stop = adjusted_volatility_stop * emergency_factor
            
            # V1利润状态选择
            if current_profit > 0.01:  # 盈利1%以上
                dynamic_stop = max(profit_lock_stop, final_volatility_stop * 0.9)
            elif current_profit > 0:  # 小幅盈利
                dynamic_stop = max(profit_lock_stop * 0.8, final_volatility_stop)
            else:  # 亏损状态
                dynamic_stop = final_volatility_stop
            
            # V1最终止损确保
            final_stop = max(dynamic_stop, self.stoploss)
            
            # V1最大允许范围
            max_allowed_stop = self.stoploss * 2.5
            final_stop = max(final_stop, max_allowed_stop)
            
            return final_stop
            
        except Exception as e:
            # V1回退逻辑
            self.logger.warning(f"V1止损计算失败 {pair}: {e}")
            return self.stoploss
    
    def _assess_market_regime(self, dataframe: DataFrame) -> str:
        """评估当前市场环境"""
        return self.dynamic_stop_manager._assess_volatility_state(dataframe)
    
    def _find_nearest_support(self, dataframe: DataFrame, current_price: float) -> float:
        """寻找最近的支撑位"""
        if len(dataframe) < 20:
            return current_price * 0.95  # 默认5%下方作为支撑
        
        # 简单的支撑位检测 - 寻找近期低点
        recent_lows = dataframe['low'].tail(20)
        support_candidates = recent_lows[recent_lows < current_price * 0.98]
        
        if len(support_candidates) > 0:
            return support_candidates.max()  # 最高的支撑位
        else:
            return current_price * 0.95
    
    def _calculate_time_factor(self, trade_duration) -> float:
        """计算基于交易时长的调整因子"""
        seconds = trade_duration.total_seconds()
        
        if seconds < 300:  # 5分钟内
            return 1.5  # 50%更宽松
        elif seconds < 900:  # 15分钟内
            return 1.3  # 30%更宽松
        elif seconds < 1800:  # 30分钟内
            return 1.2  # 20%更宽松
        elif seconds < 3600:  # 1小时内
            return 1.1  # 10%更宽松
        else:
            return 1.0  # 正常
    
    def _calculate_signal_factor(self, entry_tag: str) -> float:
        """计算基于信号质量的调整因子"""
        signal_factors = {
            'priority_short': 1.4,    # 优先信号 - 40%更宽松
            'standard_short': 1.2,    # 标准信号 - 20%更宽松
            'backup_short': 1.1,      # 备用信号 - 10%更宽松
            'emergency_short': 1.0    # 紧急信号 - 正常
        }
        return signal_factors.get(entry_tag, 1.0)
    
    def _calculate_momentum_factor(self, dataframe: DataFrame) -> float:
        """计算基于市场动量的调整因子"""
        if len(dataframe) < 5:
            return 1.0
        
        # 计算近期价格趋势
        recent_closes = dataframe['close'].tail(5)
        price_change = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / recent_closes.iloc[0]
        
        # 对于做空，价格下跌是有利的
        if price_change < -0.02:  # 价格下跌超过2%
            return 1.3  # 30%更宽松 - 趋势有利
        elif price_change < -0.01:  # 价格下跌1-2%
            return 1.1  # 10%更宽松
        elif price_change > 0.02:  # 价格上涨超过2%
            return 0.9  # 10%更紧 - 趋势不利
        else:
            return 1.0  # 正常

    def log_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                       entry_tag: Optional[str], side: str, current_time: datetime,
                       market_data: dict = None) -> None:
        """
        Log detailed trade entry information
        """
        try:
            # Get current market data for logging
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) > 0:
                current_data = dataframe.iloc[-1]
                
                # Prepare comprehensive log entry
                log_entry = {
                    'timestamp': current_time.isoformat(),
                    'pair': pair,
                    'action': 'ENTRY',
                    'side': side,
                    'order_type': order_type,
                    'amount': amount,
                    'rate': rate,
                    'entry_tag': entry_tag,
                    'market_data': {
                        'liquidity_score': current_data.get('liquidity_score_1d', 0),
                        'consecutive_green_days': current_data.get('consecutive_green_days_1d', 0),
                        'range_expansion': current_data.get('range_expansion_count_1d', 0),
                        'volume_expansion': current_data.get('volume_expansion_count_1d', 0),
                        'volume_spike': current_data.get('volume_spike', False),
                        'pattern_confidence': current_data.get('pattern_confidence', 0),
                        'signal_confidence': current_data.get('signal_confidence', 0),
                        'current_price': current_data.get('close', rate),
                        'volume': current_data.get('volume', 0)
                    }
                }
                
                # Log entry with structured data
                self.strategy_logger.info(f"TRADE_ENTRY: {pair} | "
                                        f"Side: {side} | Amount: {amount:.6f} | Rate: {rate:.8f} | "
                                        f"Tag: {entry_tag} | "
                                        f"Liquidity: {log_entry['market_data']['liquidity_score']:.1f} | "
                                        f"Green Days: {log_entry['market_data']['consecutive_green_days']} | "
                                        f"Signal Conf: {log_entry['market_data']['signal_confidence']:.3f}")
                
                # Store detailed log for analysis
                if not hasattr(self, 'trade_logs'):
                    self.trade_logs = []
                self.trade_logs.append(log_entry)
                
        except Exception as e:
            self.strategy_logger.error(f"Error logging trade entry for {pair}: {str(e)}")

    def log_trade_exit(self, pair: str, trade: 'Trade', order_type: str, amount: float,
                      rate: float, exit_reason: str, current_time: datetime) -> None:
        """
        Log detailed trade exit information
        """
        try:
            # Calculate trade performance
            entry_rate = trade.open_rate if trade else rate
            profit_pct = ((entry_rate - rate) / entry_rate) * 100 if trade and trade.is_short else 0
            duration = (current_time - trade.open_date).total_seconds() / 3600 if trade else 0
            
            # Get current market data
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            current_data = dataframe.iloc[-1] if len(dataframe) > 0 else {}
            
            # Prepare comprehensive log entry
            log_entry = {
                'timestamp': current_time.isoformat(),
                'pair': pair,
                'action': 'EXIT',
                'order_type': order_type,
                'amount': amount,
                'exit_rate': rate,
                'exit_reason': exit_reason,
                'trade_performance': {
                    'entry_rate': entry_rate,
                    'profit_pct': profit_pct,
                    'duration_hours': duration,
                    'trade_id': trade.id if trade else None
                },
                'market_data': {
                    'current_price': current_data.get('close', rate),
                    'volume': current_data.get('volume', 0),
                    'liquidity_score': current_data.get('liquidity_score_1d', 0)
                }
            }
            
            # Log exit with performance data
            self.strategy_logger.info(f"TRADE_EXIT: {pair} | "
                                    f"Reason: {exit_reason} | Amount: {amount:.6f} | Rate: {rate:.8f} | "
                                    f"Profit: {profit_pct:.2f}% | Duration: {duration:.1f}h")
            
            # Store detailed log for analysis
            if not hasattr(self, 'trade_logs'):
                self.trade_logs = []
            self.trade_logs.append(log_entry)
            
        except Exception as e:
            self.strategy_logger.error(f"Error logging trade exit for {pair}: {str(e)}")

    def log_signal_generation(self, pair: str, signal_type: str, signal_data: dict,
                             current_time: datetime) -> None:
        """
        Log signal generation details for analysis
        """
        try:
            log_entry = {
                'timestamp': current_time.isoformat(),
                'pair': pair,
                'action': 'SIGNAL_GENERATED',
                'signal_type': signal_type,
                'signal_data': signal_data
            }
            
            self.strategy_logger.info(f"SIGNAL: {pair} | Type: {signal_type} | "
                                    f"Strength: {signal_data.get('strength', 0):.3f} | "
                                    f"Confidence: {signal_data.get('confidence', 0):.3f}")
            
            # Store signal log
            if not hasattr(self, 'signal_logs'):
                self.signal_logs = []
            self.signal_logs.append(log_entry)
            
        except Exception as e:
            self.strategy_logger.error(f"Error logging signal for {pair}: {str(e)}")

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                           side: str, **kwargs) -> bool:
        """
        Called right before placing a entry order.
        Comprehensive entry validation with detailed logging
        """
        try:
            # Get current dataframe for validation
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) == 0:
                self.strategy_logger.warning(f"No data available for trade entry validation: {pair}")
                return False
            
            # Validate market conditions
            market_conditions = self.validate_market_conditions(dataframe, pair)
            
            # Additional liquidity validation at entry time
            current_liquidity_score = dataframe['liquidity_score_1d'].iloc[-1] if 'liquidity_score_1d' in dataframe.columns else 0
            current_high_liquidity = dataframe['high_liquidity_1d'].iloc[-1] if 'high_liquidity_1d' in dataframe.columns else False
            
            # Check liquidity requirements
            liquidity_check = (
                current_liquidity_score >= self.min_liquidity_score.value and
                current_high_liquidity == True
            )
            
            # Validate signal quality at entry time
            signal_validation = self.validate_signal_quality(dataframe, pair, 'entry')
            
            # Decision logic
            allow_entry = (
                market_conditions['suitable_for_trading'] and
                liquidity_check and
                signal_validation['is_valid']
            )
            
            # Log entry attempt with detailed information
            self.log_trade_entry(pair, order_type, amount, rate, entry_tag, side, current_time, {
                'market_conditions': market_conditions,
                'liquidity_check': liquidity_check,
                'signal_validation': signal_validation,
                'decision': allow_entry
            })
            
            # Log rejection reasons if entry is denied
            if not allow_entry:
                rejection_reasons = []
                if not market_conditions['suitable_for_trading']:
                    rejection_reasons.extend(market_conditions['blocking_conditions'])
                if not liquidity_check:
                    rejection_reasons.append(f'liquidity_insufficient_{current_liquidity_score:.1f}')
                if not signal_validation['is_valid']:
                    rejection_reasons.extend(signal_validation['issues'])
                
                self.strategy_logger.info(f"Trade entry rejected for {pair}: {rejection_reasons}")
            
            return allow_entry
            
        except Exception as e:
            self.strategy_logger.error(f"Error in trade entry confirmation for {pair}: {str(e)}")
            return False

    def confirm_trade_exit(self, pair: str, trade: 'Trade', order_type: str, amount: float,
                          rate: float, time_in_force: str, exit_reason: str,
                          current_time: datetime, **kwargs) -> bool:
        """
        Called right before placing a regular exit order.
        Comprehensive exit validation with detailed logging
        """
        try:
            # Get current dataframe for validation
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) == 0:
                self.strategy_logger.warning(f"No data available for trade exit validation: {pair}")
                return True  # Allow exit if no data (safety measure)
            
            # Validate exit conditions
            exit_validation = self.validate_signal_quality(dataframe, pair, 'exit')
            market_conditions = self.validate_market_conditions(dataframe, pair)
            
            # Calculate trade performance for logging
            entry_rate = trade.open_rate if trade else rate
            profit_pct = ((entry_rate - rate) / entry_rate) * 100 if trade and trade.is_short else 0
            duration = (current_time - trade.open_date).total_seconds() / 3600 if trade else 0
            
            # Log exit attempt with detailed information
            self.log_trade_exit(pair, trade, order_type, amount, rate, exit_reason, current_time)
            
            # Additional exit validation based on reason
            allow_exit = True
            
            if exit_reason in ['roi', 'trailing_stop_loss']:
                # Profit-taking exits - always allow
                self.strategy_logger.info(f"Profit exit confirmed for {pair}: {exit_reason} | "
                                        f"Profit: {profit_pct:.2f}% | Duration: {duration:.1f}h")
            
            elif exit_reason == 'stop_loss':
                # Stop loss exits - always allow for risk management
                self.strategy_logger.warning(f"Stop loss exit for {pair}: "
                                           f"Loss: {profit_pct:.2f}% | Duration: {duration:.1f}h")
            
            elif exit_reason == 'force_exit':
                # Forced exits - always allow
                self.strategy_logger.warning(f"Forced exit for {pair}: {exit_reason}")
            
            else:
                # Signal-based exits - validate conditions
                if not exit_validation['is_valid']:
                    self.strategy_logger.info(f"Exit signal validation failed for {pair}: "
                                            f"{exit_validation['issues']}")
                    # Still allow exit for safety, but log the issue
                
                self.strategy_logger.info(f"Signal-based exit for {pair}: {exit_reason} | "
                                        f"Confidence: {exit_validation['confidence']:.3f}")
            
            return allow_exit
            
        except Exception as e:
            self.strategy_logger.error(f"Error in trade exit confirmation for {pair}: {str(e)}")
            return True  # Allow exit on error for safety
    
    def get_trade_logs(self, pair: str = None, action: str = None) -> list:
        """
        Get trade logs for analysis
        
        Args:
            pair: Filter by trading pair (optional)
            action: Filter by action type (optional)
            
        Returns:
            List of trade log entries
        """
        if not hasattr(self, 'trade_logs'):
            return []
        
        logs = self.trade_logs
        
        if pair:
            logs = [log for log in logs if log.get('pair') == pair]
        
        if action:
            logs = [log for log in logs if log.get('action') == action]
        
        return logs
    
    def get_signal_logs(self, pair: str = None, signal_type: str = None) -> list:
        """
        Get signal logs for analysis
        
        Args:
            pair: Filter by trading pair (optional)
            signal_type: Filter by signal type (optional)
            
        Returns:
            List of signal log entries
        """
        if not hasattr(self, 'signal_logs'):
            return []
        
        logs = self.signal_logs
        
        if pair:
            logs = [log for log in logs if log.get('pair') == pair]
        
        if signal_type:
            logs = [log for log in logs if log.get('signal_type') == signal_type]
        
        return logs
    
    def export_logs_to_file(self, filename: str = None) -> str:
        """
        Export all logs to a JSON file for analysis
        
        Args:
            filename: Output filename (optional)
            
        Returns:
            Path to the exported file
        """
        import json
        from datetime import datetime
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"crypto_short_strategy_logs_{timestamp}.json"
        
        try:
            all_logs = {
                'strategy_name': self.__class__.__name__,
                'export_timestamp': datetime.now().isoformat(),
                'trade_logs': getattr(self, 'trade_logs', []),
                'signal_logs': getattr(self, 'signal_logs', []),
                'configuration': self.get_strategy_configuration()
            }
            
            with open(filename, 'w') as f:
                json.dump(all_logs, f, indent=2, default=str)
            
            self.strategy_logger.info(f"Logs exported to {filename}")
            return filename
            
        except Exception as e:
            self.strategy_logger.error(f"Error exporting logs: {str(e)}")
            return ""