import logging
import json
from pathlib import Path
import numpy as np
import pandas as pd
import talib.abstract as ta
from pandas import DataFrame
from typing import Optional, Dict, List
from datetime import datetime, timedelta

from freqtrade.strategy import (IStrategy, informative)
from freqtrade.persistence import Trade
from freqtrade.exchange import timeframe_to_minutes

# 忽略性能警告
from warnings import simplefilter
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)

logger = logging.getLogger(__name__)

class TRDV3_PairOptimized(IStrategy):
    """
    Trend Recognition Dual Version 3
    - Multi-Timeframe (1h for trend, 15m for entry)
    - Core Trend: SuperTrend
    - Auxiliary/Confirmation: KAMA
    - Market Regime Detection (Initial)
    - ATR based position sizing and trailing stop loss
    - Pair-specific parameters from JSON
    """
    INTERFACE_VERSION: int = 3

    # --- 策略核心配置 ---
    timeframe = '15m'  # 交易执行时间周期
    # info_timeframe = '1h' # 主趋势判断时间周期 - now handled by @informative

    # 启用做空 (根据您的需求，可以设为True或False)
    can_short: bool = True

    # 同时开启的最大交易数量 (根据优化结果设置)
    max_open_trades: int = 4

    # ROI表 (初期可以简单设置，后续根据回测优化)
    minimal_roi = {
        "0": 0.10, # 10%
        "30": 0.05, # 30分钟后5%
        "60": 0.025, # 60分钟后2.5%
        "120": 0.01 # 120分钟后1%
    }

    # 固定止损 (作为ATR止损的最后防线，可以设得宽一些)
    stoploss = -0.10  # 从 -0.20 修改为 -0.10

    # 跟踪止损设置
    trailing_stop = True
    trailing_stop_positive = 0.02  # 当盈利达到2%时激活跟踪止损
    trailing_stop_positive_offset = 0.05  # 从盈利高点回撤5%时触发止损
    trailing_only_offset_is_reached = True # 只有当盈利达到 trailing_stop_positive 时才开始跟踪

    # 运行配置
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False # 出场信号触发时，即使亏损也退出
    ignore_roi_if_entry_signal = False # 如果有新的入场信号，旧的ROI仍然有效

    # --- 可优化的核心参数 ---
    # 参数现在从 JSON 文件加载，此处不再定义

    # --- 高时间周期 (1h) 指标参数 (通常不直接优化，或者与执行周期参数联动) ---
    # SuperTrend (1h - 主趋势周期)
    st_long_atr_period = 14 # 1h Supertrend ATR 周期
    st_long_multiplier = 3.5 # 1h Supertrend 乘数

    # KAMA (1h - 主趋势周期)
    kama_long_period = 30 # 1h KAMA 周期
    kama_long_slope_period = 8 # 1h KAMA 斜率周期
    kama_long_slope_threshold = 0.00005 # 1h KAMA 斜率阈值，用于判断趋势有效性


    def __init__(self, config: dict):
        super().__init__(config)
        self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                self.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it to function. Please create it.")
            self.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}. Strategy may not function correctly.")
            self.custom_info = {}

    # --- 辅助方法 ---
    def calculate_supertrend(self, dataframe: DataFrame, period: int, multiplier: float) -> DataFrame:
        """
        Calculates SuperTrend indicator
        """
        df = dataframe.copy()
        df['TR'] = ta.TRANGE(df)
        df['ATR'] = ta.SMA(df['TR'], period)

        df['basic_ub'] = (df['high'] + df['low']) / 2 + multiplier * df['ATR']
        df['basic_lb'] = (df['high'] + df['low']) / 2 - multiplier * df['ATR']

        df['final_ub'] = 0.00
        df['final_lb'] = 0.00

        for i in range(period, len(df)):
            df.loc[i, 'final_ub'] = (df.loc[i, 'basic_ub'] if df.loc[i, 'basic_ub'] < df.loc[i-1, 'final_ub'] or df.loc[i-1, 'close'] > df.loc[i-1, 'final_ub'] else df.loc[i-1, 'final_ub'])
            df.loc[i, 'final_lb'] = (df.loc[i, 'basic_lb'] if df.loc[i, 'basic_lb'] > df.loc[i-1, 'final_lb'] or df.loc[i-1, 'close'] < df.loc[i-1, 'final_lb'] else df.loc[i-1, 'final_lb'])

        df['ST'] = np.nan # Initialize with NaN
        df['STX'] = ''

        # Set initial SuperTrend value at the 'period' index
        if period < len(df): # Ensure dataframe is long enough
            if df.loc[period, 'close'] > df.loc[period, 'final_lb']: # Initial guess: if close > lower band, trend is up
                df.loc[period, 'ST'] = df.loc[period, 'final_lb']
            elif df.loc[period, 'close'] < df.loc[period, 'final_ub']: # Initial guess: if close < upper band, trend is down
                df.loc[period, 'ST'] = df.loc[period, 'final_ub']
            # else: ST remains NaN if close is exactly on a band or between basic_ub/lb without clear direction yet.
            # This NaN will be forward-filled by subsequent logic or bfill at the end if it's the only point.

        for i in range(period + 1, len(df)): # Start loop from period + 1
            prev_st = df.loc[i-1, 'ST']
            # If previous ST is NaN (can happen if initial ST was not set), try to set it based on current price vs final bands
            if pd.isna(prev_st):
                if df.loc[i, 'close'] > df.loc[i, 'final_lb']:
                    df.loc[i, 'ST'] = df.loc[i, 'final_lb']
                elif df.loc[i, 'close'] < df.loc[i, 'final_ub']:
                    df.loc[i, 'ST'] = df.loc[i, 'final_ub']
                # If still can't determine, ST remains NaN for this point too
                continue # Skip to next iteration if ST can't be determined

            if prev_st == df.loc[i-1, 'final_ub']:
                df.loc[i, 'ST'] = df.loc[i, 'final_ub'] if df.loc[i, 'close'] <= df.loc[i, 'final_ub'] else df.loc[i, 'final_lb']
            elif prev_st == df.loc[i-1, 'final_lb']:
                df.loc[i, 'ST'] = df.loc[i, 'final_lb'] if df.loc[i, 'close'] >= df.loc[i, 'final_lb'] else df.loc[i, 'final_ub']
            # else: This case should ideally not be reached if prev_st was a valid final_ub or final_lb

        df.loc[df['close'] > df['ST'], 'STX'] = 'up'
        df.loc[df['close'] < df['ST'], 'STX'] = 'down'
        df.loc[df['STX'] == '', 'STX'] = pd.NA # Set to NA if no trend determined

        df['ST'] = df['ST'].bfill().ffill() # Fill any remaining NaNs robustly
        df['STX'] = df['STX'].bfill().ffill()

        return df[['ST', 'STX']]

    # --- Informative Timeframe (1h) ---
    @informative('1h')
    def populate_info_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populates indicators for the 1-hour informative timeframe
        """
        # 1h SuperTrend
        supertrend_long = self.calculate_supertrend(dataframe.copy(), period=self.st_long_atr_period, multiplier=self.st_long_multiplier)
        dataframe[f'st_long'] = supertrend_long['ST'] 
        dataframe[f'stx_long'] = supertrend_long['STX'] 

        # 1h KAMA
        dataframe[f'kama_long'] = ta.KAMA(dataframe, timeperiod=self.kama_long_period) 
        dataframe[f'kama_long_slope'] = ta.LINEARREG_SLOPE(dataframe[f'kama_long'], timeperiod=self.kama_long_slope_period)
        
        # 1h EMA Filter (for L1 proposal)
        dataframe[f'ema_long_filter'] = ta.EMA(dataframe, timeperiod=50) 
        # For M2 proposal: Slope of the 1h EMA filter
        dataframe[f'ema_long_filter_slope'] = ta.LINEARREG_SLOPE(dataframe[f'ema_long_filter'], timeperiod=5) # 5-period slope of EMA50

        return dataframe

    # --- 主指标计算 (15m) ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several indicators to the main timeframe (15m) dataframe.
        Informative indicators from 1h are automatically merged by Freqtrade.
        """
        pair = metadata['pair']
        if pair not in self.custom_info:
            return dataframe
        
        pair_settings = self.custom_info[pair]

        # SuperTrend (15m - 执行周期)
        supertrend_short = self.calculate_supertrend(dataframe.copy(), period=pair_settings['st_short_atr_period'], multiplier=pair_settings['st_short_multiplier'])
        dataframe['st_short'] = supertrend_short['ST']
        dataframe['stx_short'] = supertrend_short['STX']

        # KAMA (15m - 执行周期)
        dataframe['kama_short'] = ta.KAMA(dataframe, timeperiod=pair_settings['kama_short_period'])
        dataframe['kama_short_slope'] = ta.LINEARREG_SLOPE(dataframe['kama_short'], timeperiod=pair_settings['kama_short_slope_period'])

        # ATR (15m - 用于仓位和止损)
        dataframe['atr_pos_size'] = ta.ATR(dataframe, timeperiod=pair_settings['atr_period_pos_size'])

        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        return dataframe

    # --- 入场信号逻辑 ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        if pair not in self.custom_info:
            return dataframe
        pair_settings = self.custom_info[pair]

        required_cols_1h = [
            f'stx_long_1h', f'kama_long_slope_1h', 
            f'ema_long_filter_1h', f'close_1h', f'ema_long_filter_slope_1h' # Added slope
        ]
        if not all(col in dataframe.columns for col in required_cols_1h):
            # logger.warning(f"Missing informative columns for pair {metadata['pair']} in entry. Skipping signal generation.")
            return dataframe

        # --- 做多入场条件 ---
        long_condition_1h_strong_trend_up = (
            (dataframe[f'stx_long_1h'] == 'up') &
            (dataframe[f'kama_long_slope_1h'] > self.kama_long_slope_threshold) &
            (dataframe[f'close_1h'] > dataframe[f'ema_long_filter_1h']) & # Price above 1h EMA50
            (dataframe[f'ema_long_filter_slope_1h'] > 0)  # And 1h EMA50 is sloping upwards
        )
        long_condition_15m_trend_up = (dataframe['stx_short'] == 'up')
        long_condition_15m_kama_confirm = (dataframe['kama_short_slope'] > pair_settings['kama_short_slope_threshold_entry'])

        dataframe.loc[
            long_condition_1h_strong_trend_up &
            long_condition_15m_trend_up &
            long_condition_15m_kama_confirm &
            (dataframe['volume'] > 0), 
            'enter_long'] = 1

        # --- 做空入场条件 ---
        short_condition_1h_strong_trend_down = (
            (dataframe[f'stx_long_1h'] == 'down') &
            (dataframe[f'kama_long_slope_1h'] < -self.kama_long_slope_threshold) &
            (dataframe[f'close_1h'] < dataframe[f'ema_long_filter_1h']) & # Price below 1h EMA50
            (dataframe[f'ema_long_filter_slope_1h'] < 0)  # And 1h EMA50 is sloping downwards
        )
        short_condition_15m_trend_down = (dataframe['stx_short'] == 'down')
        short_condition_15m_kama_confirm = (dataframe['kama_short_slope'] < -pair_settings['kama_short_slope_threshold_entry'])

        if self.can_short:
            dataframe.loc[
                short_condition_1h_strong_trend_down &
                short_condition_15m_trend_down &
                short_condition_15m_kama_confirm &
                (dataframe['volume'] > 0),
                'enter_short'] = 1
            
        return dataframe

    # --- 出场信号逻辑 ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        required_cols = [
            f'stx_long_1h' # Only need 1h SuperTrend status
        ]
        if not all(col in dataframe.columns for col in required_cols):
            # logger.warning(f"Missing informative column stx_long_1h for pair {metadata['pair']} in exit. Skipping signal generation.")
            dataframe['exit_long'] = 0
            dataframe['exit_short'] = 0
            return dataframe

        if 'exit_long' not in dataframe.columns:
            dataframe['exit_long'] = 0
        if 'exit_short' not in dataframe.columns:
            dataframe['exit_short'] = 0

        # --- 做多出场条件 ---
        # Exit if 1-hour SuperTrend flips to 'down'
        # Ensure we are checking for a flip: previous was 'up', current is 'down'
        exit_long_condition = (
            (dataframe[f'stx_long_1h'] == 'down') &
            (dataframe[f'stx_long_1h'].shift(1) == 'up') 
        )
        dataframe.loc[exit_long_condition, 'exit_long'] = 1
        
        # --- 做空出场条件 ---
        if self.can_short:
            # Exit if 1-hour SuperTrend flips to 'up'
            # Ensure we are checking for a flip: previous was 'down', current is 'up'
            exit_short_condition = (
                (dataframe[f'stx_long_1h'] == 'up') &
                (dataframe[f'stx_long_1h'].shift(1) == 'down')
            )
            dataframe.loc[exit_short_condition, 'exit_short'] = 1
            
        return dataframe

    # --- 动态仓位管理 ---
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            entry_tag: Optional[str], **kwargs) -> float:
        
        if pair not in self.custom_info:
            return proposed_stake
        
        pair_settings = self.custom_info[pair]

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return proposed_stake
        
        last_candle = dataframe.iloc[-1]
        
        atr_value = last_candle.get('atr_pos_size') # Use .get for safety

        if atr_value is None or atr_value == 0 or pair_settings.get('atr_multiplier_pos_size', 0) == 0:
            logger.warning(f"ATR or multiplier for position size is 0 or None for {pair} at {current_time}. Using proposed_stake: {proposed_stake}")
            return proposed_stake
        
        try:
            current_balance = self.wallets.get_total_stake_amount()
        except Exception as e:
            logger.warning(f"Could not get total stake amount: {e}. Defaulting to a calculated balance if possible or proposed_stake.")
            # Fallback if wallet info is not available (e.g. during plotting or some backtest modes)
            risk_per_trade = pair_settings.get('risk_per_trade', 0.01)
            current_balance = proposed_stake / (risk_per_trade if risk_per_trade > 0 else 0.01) # Estimate total equity

        if current_balance <= 0: # Ensure current_balance is positive
             logger.warning(f"Current balance is {current_balance}. Using proposed_stake.")
             return proposed_stake

        risk_amount_per_trade = current_balance * pair_settings['risk_per_trade']
        
        stop_loss_distance = atr_value * pair_settings['atr_multiplier_pos_size'] # This is in quote currency if ATR is from quote asset
        
        if stop_loss_distance == 0:
            logger.warning(f"Stop loss distance is 0 for {pair} (ATR or multiplier is zero). Using proposed_stake.")
            return proposed_stake
            
        position_size_in_asset = risk_amount_per_trade / stop_loss_distance
        
        calculated_stake = position_size_in_asset * current_rate
        
        final_stake = max(min_stake, calculated_stake)
        final_stake = min(max_stake, final_stake)
        
        # logger.info(f"Pair: {pair}, Rate: {current_rate}, ATR: {atr_value:.4f}, SL Dist: {stop_loss_distance:.4f}, Risk Amt: {risk_amount_per_trade:.2f}, Calc Stake: {calculated_stake:.2f}, Final Stake: {final_stake:.2f}")

        return final_stake

    # --- Freqtrade 回调函数 ---
    def version(self) -> str:
        """
        Returns strategy version.
        """
        return "TRDV3_PairOptimized_1.0" # Incremented version

    def bot_loop_start(self, **kwargs) -> None:
        """
        Called at the start of the bot iteration (one loop).
        """
        # For informative decorator, the suffix is automatically determined by Freqtrade
        # self.info_timeframe_suffix = timeframe_to_minutes(self.info_timeframe) # Not needed like this
        logger.info(f"Starting Loop for {self.version()} on {self.timeframe} timeframe. ")
        return super().bot_loop_start(**kwargs)

    # --- 绘图配置 (可选) ---
    plot_config = {
        'main_plot': {
            'st_short': {'color': 'blue', 'type': 'line'},
            'st_long_1h': {'color': 'purple', 'type': 'line'}, 
            'kama_short': {'color': 'green'},
            'kama_long_1h': {'color': 'orange'},
            'ema_long_filter_1h': {'color': 'cyan', 'type': 'line'},
        },
        'subplots': {
            "ATR": {
                'atr_pos_size': {'color': 'brown'},
            },
            "KAMA_SLOPE":{
                'kama_short_slope': {'color': 'red'},
                'kama_long_slope_1h': {'color': 'magenta'}
            },
            "STX_1H": { # Example for visualizing 1h trend direction
                 'stx_long_1h': {'color': 'black', 'type': 'bar'} # May need conversion to 1 for up, -1 for down
            },
            "EMA_SLOPE_1H": { 'ema_long_filter_slope_1h': {'color': 'orange'} }
        }
    }
