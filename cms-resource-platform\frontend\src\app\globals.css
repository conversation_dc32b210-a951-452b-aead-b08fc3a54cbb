@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
  }
  
  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* 组件样式 */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:ring-gray-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-md {
    @apply px-4 py-2 text-sm;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-200 hover:shadow-md hover:border-gray-300;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500;
  }
  
  .textarea {
    @apply input resize-none;
  }
  
  .select {
    @apply input pr-10 cursor-pointer;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 lg:py-20;
  }
  
  /* 加载动画 */
  .loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
  }
  
  @keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }
  
  /* 骨架屏动画 */
  .skeleton {
    @apply bg-gray-200 animate-pulse rounded;
  }
  
  .skeleton-text {
    @apply skeleton h-4 w-full mb-2;
  }
  
  .skeleton-title {
    @apply skeleton h-6 w-3/4 mb-4;
  }
  
  .skeleton-avatar {
    @apply skeleton h-10 w-10 rounded-full;
  }
  
  .skeleton-image {
    @apply skeleton aspect-video w-full;
  }
  
  /* 响应式网格 */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  .grid-responsive-large {
    @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8;
  }
  
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }
  
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 悬浮效果 */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
  
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  /* 焦点环 */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  /* 分隔线 */
  .divider {
    @apply border-t border-gray-200 my-6;
  }
  
  .divider-vertical {
    @apply border-l border-gray-200 mx-4;
  }
}

/* 工具样式 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: transparent;
}

.ant-menu {
  background: transparent;
  border: none;
}

.ant-menu-item {
  @apply rounded-lg;
}

.ant-menu-item:hover {
  @apply bg-gray-100;
}

.ant-menu-item-selected {
  @apply bg-primary-50 text-primary-600;
}

.ant-btn-primary {
  @apply bg-primary-500 border-primary-500 hover:bg-primary-600 hover:border-primary-600;
}

.ant-input:focus,
.ant-input-focused {
  @apply border-primary-500;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.ant-upload-drag:hover {
  @apply border-primary-400;
}

.ant-upload-drag.ant-upload-drag-hover {
  @apply border-primary-500;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
