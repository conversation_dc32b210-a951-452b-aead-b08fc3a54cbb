import os
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

"""
HeikinAshiTrendFollowerV7_Pair - A Trend Following Strategy with Pair-Specific Settings

* Changelog V7-Pair:
    - Forked from HeikinAshiTrendFollowerV7.
    - Removed hyperoptable parameters.
    - Added functionality to load all strategy parameters from an external JSON file.
    - Each trading pair can have its own set of parameters, with a 'DEFAULT' fallback.
"""

import logging
import numpy as np
import talib.abstract as ta
from datetime import datetime, timezone
from freqtrade.strategy import IStrategy
from freqtrade.exchange import timeframe_to_seconds
from pandas import DataFrame
import pandas as pd
from typing import Optional, Any
import json
from pathlib import Path

logger = logging.getLogger(__name__)

# --- Helper Functions (unchanged) ---
def merge_informative_pair(dataframe: pd.DataFrame, informative: pd.DataFrame, 
                          timeframe: str, timeframe_inf: str, 
                          ffill: bool = True) -> pd.DataFrame:
    if not isinstance(dataframe.index, pd.DatetimeIndex):
        dataframe = dataframe.set_index('date', drop=False)
    if not isinstance(informative.index, pd.DatetimeIndex):
        informative = informative.set_index('date', drop=False)
    
    informative_cols = informative.columns
    informative_cols_names = [f"{col}_{timeframe_inf}" for col in informative_cols]
    informative.columns = informative_cols_names
    
    resampled = informative.copy()
    resampled = resampled.reindex(dataframe.index, method='ffill')
    dataframe = pd.concat([dataframe, resampled], axis=1)
    
    if ffill:
        dataframe = dataframe.ffill()
    
    return dataframe

# --- Strategy Class ---
class HeikinAshiTrendFollowerV7_Pair(IStrategy):
    # --- Strategy Configuration ---
    INTERFACE_VERSION: int = 4
    
    timeframe = "15m"
    startup_candle_count = 200
    higher_tf = '1h'
    can_short: bool = False
    process_only_new_candles = True # Recommended for performance

    stoploss = -0.99  # Use a large stoploss as custom_stoploss is primary
    use_custom_stoploss = True
    cooldown_lookback = 3
    
    def __init__(self, config: dict):
        super().__init__(config)
        self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                self.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it.")
            self.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}.")
            self.custom_info = {}
            
    def _get_pair_settings(self, pair: str) -> Optional[dict]:
        """ Helper to get settings for a pair, with a fallback to DEFAULT """
        if not self.custom_info:
            logger.warning("Strategy settings are not loaded. Cannot get pair settings.")
            return None
            
        pair_settings = self.custom_info.get(pair)
        if pair_settings is None:
            pair_settings = self.custom_info.get("DEFAULT")
            if pair_settings is None:
                 logger.warning(f"No settings found for pair {pair} and no DEFAULT settings defined.")
                 return None
        return pair_settings

    # --- Plotting Configuration ---
    @property
    def plot_config(self):
        return {
            "main_plot": {
                "ha_high": {"color": "green"}, "ha_low": {"color": "red"},
                "pivot_zone_low": {"color": "green", "type": "line", "linestyle": "--"},
                "pivot_zone_high": {"color": "red", "type": "line", "linestyle": "--"},
                "ema_short": {"color": "blue"}, "ema200": {"color": "orange"},
            },
            "subplots": {
                "ADX": {"adx": {"color": "brown"}}, "RSI": {"rsi": {"color": "purple"}},
                "OBV": {"obv": {"color": "blue"}, "obv_ema": {"color": "orange"}},
            }
        }

    # --- Informative Pairs ---
    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        return [(pair, self.higher_tf) for pair in pairs]

    # --- Indicator Population ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair_settings = self._get_pair_settings(metadata['pair'])
        if pair_settings is None:
            return dataframe
        
        # --- Higher Timeframe (MTA) ---
        inf_tf = self.higher_tf
        try:
            informative = self.dp.get_pair_dataframe(metadata['pair'], inf_tf)
            informative['ema50'] = ta.EMA(informative, timeperiod=50)
            informative['ema200'] = ta.EMA(informative, timeperiod=200)
            informative['higher_trend'] = 0
            informative.loc[(informative['close'] > informative['ema50']) & (informative['ema50'] > informative['ema200']), 'higher_trend'] = 1
            informative.loc[(informative['close'] < informative['ema50']) & (informative['ema50'] < informative['ema200']), 'higher_trend'] = -1
            dataframe = merge_informative_pair(dataframe, informative, self.timeframe, inf_tf, ffill=True)
            dataframe['higher_trend_val'] = dataframe[f"higher_trend_{inf_tf}"]
        except Exception as e:
            dataframe['higher_trend_val'] = 0
            print(f"Could not get higher timeframe data: {e}")
        
        # --- Heikin Ashi Candles ---
        heikin_ashi = dataframe.copy()
        heikin_ashi['ha_close'] = (dataframe['open'] + dataframe['high'] + dataframe['low'] + dataframe['close']) / 4
        
        first_idx = dataframe.index[0] if len(dataframe) > 0 else None
        if first_idx is not None:
            heikin_ashi.at[first_idx, 'ha_open'] = (dataframe.at[first_idx, 'open'] + dataframe.at[first_idx, 'close']) / 2
            for i in range(1, len(dataframe)):
                current_idx = dataframe.index[i]
                prev_idx = dataframe.index[i-1]
                heikin_ashi.at[current_idx, 'ha_open'] = (heikin_ashi.at[prev_idx, 'ha_open'] + heikin_ashi.at[prev_idx, 'ha_close']) / 2

        heikin_ashi['ha_high'] = heikin_ashi[['ha_open', 'ha_close']].join(dataframe['high']).max(axis=1)
        heikin_ashi['ha_low'] = heikin_ashi[['ha_open', 'ha_close']].join(dataframe['low']).min(axis=1)

        dataframe['ha_open'] = heikin_ashi['ha_open']
        dataframe['ha_high'] = heikin_ashi['ha_high']
        dataframe['ha_low'] = heikin_ashi['ha_low']
        dataframe['ha_close'] = heikin_ashi['ha_close']
        
        dataframe['ha_strong_bull'] = (dataframe['ha_close'] > dataframe['ha_open']) & (dataframe['ha_low'] == dataframe['ha_open'])
        dataframe['ha_strong_bear'] = (dataframe['ha_close'] < dataframe['ha_open']) & (dataframe['ha_high'] == dataframe['ha_open'])
        
        # --- Core Indicators ---
        dataframe['pivot_zone_high'] = dataframe['high'].shift(1).rolling(pair_settings.get('donchian_period', 20)).max()
        dataframe['pivot_zone_low'] = dataframe['low'].shift(1).rolling(pair_settings.get('donchian_period', 20)).min()
        dataframe['pivot_zone_mid'] = (dataframe['pivot_zone_high'] + dataframe['pivot_zone_low']) / 2
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['ema50'] = ta.EMA(dataframe, timeperiod=50)
        dataframe['ema200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_ema'] = ta.EMA(dataframe['obv'], timeperiod=20)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=pair_settings.get('rsi_period', 14))
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=pair_settings.get('atr_period', 14))
        dataframe['atr_pct'] = dataframe['atr'] / dataframe['close']
        dataframe['volatility_factor'] = np.clip(dataframe['atr_pct'] * 100, 0.5, 2.0)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        
        # --- Trend Identification ---
        dataframe['trend'] = 0
        uptrend_condition = (
            (dataframe['close'] > dataframe['pivot_zone_mid']) & 
            (dataframe['close'] > dataframe['ema50']) & 
            (dataframe['ema50'] > dataframe['ema200']) &
            (dataframe['higher_trend_val'] >= 0)
        )
        downtrend_condition = (
            (dataframe['close'] < dataframe['pivot_zone_mid']) & 
            (dataframe['close'] < dataframe['ema50']) & 
            (dataframe['ema50'] < dataframe['ema200']) &
            (dataframe['higher_trend_val'] <= 0)
        )
        dataframe.loc[uptrend_condition, 'trend'] = 1
        dataframe.loc[downtrend_condition, 'trend'] = -1
        
        return dataframe

    # --- Entry Logic ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair_settings = self._get_pair_settings(metadata['pair'])
        if pair_settings is None:
            return dataframe
            
        trend_long_conditions = (
            (dataframe['close'] > dataframe['ema200']) &
            (dataframe['ema200'] > dataframe['ema200'].shift(20)) &
            (dataframe['close'] > dataframe['ema50']) &
            (dataframe['adx'] > pair_settings.get('adx_threshold', 25)) &
            dataframe['ha_strong_bull'] &
            (dataframe['obv'] > dataframe['obv_ema'])
        )
        dataframe.loc[trend_long_conditions, ['enter_long', 'enter_tag']] = (1, 'trend_long')
        return dataframe

    def custom_position_size(self, pair: str, current_time: datetime, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return 1.0
        
        last_candle = dataframe.iloc[-1]
        market_volatility = last_candle.get('atr_pct', 0.0)
        base_position = 1.0
        position_size = base_position * (1.0 - min(0.5, market_volatility * 10))
        return max(0.3, position_size)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float, 
                           time_in_force: str, current_time: datetime, **kwargs) -> bool:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty or len(dataframe) < self.cooldown_lookback: return True
            
        last_candles = dataframe.iloc[-self.cooldown_lookback:]
        exit_tags = ['exit_profit_rsi', 'exit_profit_medium', 'exit_profit_large']
        if last_candles['exit_tag'].isin(exit_tags).any():
            return False
        return True
    
    # --- Exit Logic ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair_settings = self._get_pair_settings(metadata['pair'])
        if pair_settings is None: return dataframe
        
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        dataframe.loc[
            (
                dataframe['ha_strong_bear'] & 
                dataframe['ha_strong_bear'].shift(1) &
                (dataframe['close'] < dataframe['ema_short'] * 0.98) &
                (dataframe['adx'] > pair_settings.get('adx_threshold', 25) * 1.2) &
                (dataframe['volume'] > dataframe['volume_mean'])
            ),
            ['exit_long', 'exit_tag']] = (1, 'strong_reversal')
        return dataframe

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime,
                    current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        pair_settings = self._get_pair_settings(pair)
        if pair_settings is None: return None

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return None
        last_candle = dataframe.iloc[-1]
        
        if current_profit > 0:
            if current_profit >= 0.05: return 'exit_profit_large'
            if current_profit >= 0.02 and last_candle['close'] > last_candle['pivot_zone_mid']: return 'exit_profit_medium'
            if current_profit > 0.01 and last_candle['rsi'] > pair_settings.get('rsi_overbought_threshold', 75): return 'exit_profit_rsi'

        if current_profit > 0.01 and last_candle['trend'] == -1:
            return 'exit_trend_reversal'
            
        trade_duration_candles = (current_time.replace(tzinfo=timezone.utc) - trade.open_date_utc).total_seconds() / timeframe_to_seconds(self.timeframe)
        if current_profit < 0 and trade_duration_candles > pair_settings.get('max_trade_duration_candles', 200):
            return 'exit_stop_loss_timed'
        
        return None

    def confirm_trade_exit(self, pair: str, trade: 'Trade', order_type: str, amount: float,
                         rate: float, time_in_force: str, exit_reason: str,
                         current_time: datetime, **kwargs) -> bool:
        if exit_reason == 'trailing_stop_loss': return False
        return True

    # --- Custom Stoploss ---
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return -0.99

        open_candle_df = dataframe.loc[dataframe['date'] < trade.open_date_utc]
        if open_candle_df.empty: return -0.99
        
        open_candle = open_candle_df.iloc[-1]
        atr_value = open_candle.get('atr', 0)
        volatility_factor = open_candle.get('volatility_factor', 1.0)
        pivot_zone_low = open_candle.get('pivot_zone_low', 0)
        
        if current_profit > 0.03:
            stop_price = trade.open_rate * (1 + (current_profit * 0.5))
            return (stop_price / current_rate) - 1
        elif current_profit > 0.01:
            stop_price = trade.open_rate * 1.001
            return (stop_price / current_rate) - 1
        
        adjusted_multiplier = 1.8 * volatility_factor
        stoploss_price = pivot_zone_low - (atr_value * adjusted_multiplier)
        return (stoploss_price / current_rate) - 1 