#!/bin/bash
#
# 该脚本为 SuperTrend_MACD_RSI_PairOptimized 策略提供一个独立的回测及分析工作流：
# 1. 运行所有交易对的回测并将原始输出记录到一个日志文件中。
# 2. 从完整的日志文件中解析出每个交易对的关键 "Strategy Summary" 信息。
# 3. 调用Python脚本对摘要报告进行深度分析，生成最终的排名报告。

# --- VPS 绝对路径配置 ---
# Freqtrade 项目根目录 (必须是 docker-compose.yml 文件所在的目录)
PROJECT_ROOT="/root/ft"
# 用于回测的策略名称
STRATEGY_FOR_BACKTEST="SuperTrend_MACD_RSI_PairOptimized"
# Freqtrade 主配置文件路径
CONFIG_FILE="${PROJECT_ROOT}/user_data/SuperTrend_config.json"
# 初始回测报告文件
BACKTEST_SUMMARY_FILE="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_backtest_summary.txt"
# 最终排名报告文件
RANKED_REPORT_FILE="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_ranked_report.md"
# Python分析脚本路径
ANALYSIS_SCRIPT="${PROJECT_ROOT}/user_data/strategies/SuperTrend/3_SuperTrend_analyze_backtest_results.py"
# 原始日志文件
RAW_LOG_FILE="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_backtest_raw_log.txt"
# 策略参数文件的主机路径 (用于检查文件是否存在)
SETTINGS_FILE_HOST="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_MACD_RSI_PairOptimized_Settings.json"
# 策略参数文件的容器内路径 (传递给策略)
SETTINGS_FILE_CONTAINER="/freqtrade/user_data/strategies/SuperTrend/SuperTrend_MACD_RSI_PairOptimized_Settings.json"
# 回测时间范围
TIMERANGE="20240401-20250430"
# 回测时间周期
TIMEFRAME="1h"

# --- 函数定义 ---
# 定义一个函数，用于递归设置 user_data 目录的读取权限
set_permissions() {
    echo "正在更新项目文件权限以兼容Docker容器..."
    if [ -d "${PROJECT_ROOT}/user_data" ]; then
        # 此命令确保容器内的非root用户可以读取主机上的策略和配置文件
        # a+rX 确保文件可读，目录可访问
        # 脚本执行者需要有sudo免密权限，或者以root身份运行
        sudo chmod -R a+rX "${PROJECT_ROOT}/user_data"
        echo "权限更新完成。"
    else
        echo "警告: '${PROJECT_ROOT}/user_data' 目录不存在，跳过权限设置。"
    fi
}

# --- 脚本开始 ---

echo "启动 SuperTrend 独立回测及分析工作流..."

# --- 步骤 0: 权限及环境准备 ---
set_permissions # 脚本开始时，设置初始权限

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 '$CONFIG_FILE' 未找到。"
    exit 1
fi
if ! command -v jq &> /dev/null; then
    echo "错误: jq 未安装。请先安装。"
    exit 1
fi

mkdir -p "$(dirname "$BACKTEST_SUMMARY_FILE")"
touch "$BACKTEST_SUMMARY_FILE"

# --- 步骤 1: 运行回测 ---
echo "正在为所有交易对运行回测..."
PAIRS=$(jq -r '.exchange.pair_whitelist[]' ${CONFIG_FILE})
if [ -z "$PAIRS" ]; then
    echo "错误：无法从 ${CONFIG_FILE} 中读取交易对列表。"
    exit 1
fi

echo "SuperTrend 原始回测日志 - $(date)" > "$RAW_LOG_FILE"
TOTAL_PAIRS=$(echo "$PAIRS" | wc -w)
CURRENT_PAIR_NUM=0

for PAIR in $PAIRS; do
    CURRENT_PAIR_NUM=$((CURRENT_PAIR_NUM + 1))
    echo "-------------------------------------------------------------------"
    echo "回测 [${CURRENT_PAIR_NUM}/${TOTAL_PAIRS}]: $PAIR"
    echo "-------------------------------------------------------------------"
    
    # 将绝对路径转换为Docker所需的相对路径
    CONFIG_FILE_DOCKER=$(echo "$CONFIG_FILE" | sed "s|^${PROJECT_ROOT}/||")

    echo "==================== START BACKTEST FOR $PAIR ====================" >> "$RAW_LOG_FILE"
    cd "$PROJECT_ROOT" && docker compose run --rm \
        -e STRATEGY_SETTINGS_FILE="${SETTINGS_FILE_CONTAINER}" \
        freqtrade backtesting \
        --strategy "$STRATEGY_FOR_BACKTEST" \
        --strategy-path user_data/strategies/SuperTrend \
        --config "$CONFIG_FILE_DOCKER" \
        --timeframe "$TIMEFRAME" \
        --timerange "$TIMERANGE" \
        -p "$PAIR" >> "$RAW_LOG_FILE" 2>&1
    echo "==================== END BACKTEST FOR $PAIR ====================" >> "$RAW_LOG_FILE"
    echo "" >> "$RAW_LOG_FILE"
done

# --- 步骤 2: 生成初步报告 ---
echo "正在从 '$RAW_LOG_FILE' 解析日志并生成初步报告..."
echo "SuperTrend 回测摘要报告" > "$BACKTEST_SUMMARY_FILE"
echo "生成时间: $(date)" >> "$BACKTEST_SUMMARY_FILE"
echo "" >> "$BACKTEST_SUMMARY_FILE"

for PAIR in $PAIRS; do
    # Escape characters in PAIR that are special to sed (like '/')
    SAFE_PAIR=$(echo "$PAIR" | sed 's/[\/&]/\\&/g')

    PAIR_OUTPUT=$(sed -n "/==================== START BACKTEST FOR $SAFE_PAIR ====================/,/==================== END BACKTEST FOR $SAFE_PAIR ====================/p" "$RAW_LOG_FILE")
    if [ -z "$PAIR_OUTPUT" ]; then
        echo "警告: 未能在日志中找到交易对 $PAIR 的回测结果。"
        continue
    fi
    echo "================================== $PAIR ==================================" >> "$BACKTEST_SUMMARY_FILE"
    echo "" >> "$BACKTEST_SUMMARY_FILE"
    echo "$PAIR_OUTPUT" | grep "^Backtested " >> "$BACKTEST_SUMMARY_FILE"
    echo "$PAIR_OUTPUT" | awk '/STRATEGY SUMMARY/,/└.*┘/' >> "$BACKTEST_SUMMARY_FILE"
    echo "" >> "$BACKTEST_SUMMARY_FILE"
done

# --- 步骤 3: 运行Python分析脚本 ---
echo "==================================================================="
echo "正在调用Python脚本进行深度分析和排名..."
if [ -f "$ANALYSIS_SCRIPT" ]; then
    python3 "$ANALYSIS_SCRIPT" -i "$BACKTEST_SUMMARY_FILE" -o "$RANKED_REPORT_FILE"
    if [ $? -eq 0 ]; then
        echo "最终排名报告已成功生成: '$RANKED_REPORT_FILE'"
    else
        echo "错误: Python分析脚本执行失败。"
    fi
else
    echo "错误: 分析脚本 '$ANALYSIS_SCRIPT' 未找到。"
fi

# 在整个工作流的最后，再次设置权限
set_permissions

echo "==================================================================="
echo "所有工作流已全部完成！"
echo "===================================================================" 