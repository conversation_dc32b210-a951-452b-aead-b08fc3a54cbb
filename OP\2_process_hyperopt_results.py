import os
import json
from pathlib import Path
import shutil
import logging
from typing import List, Dict, Any
import gzip
from datetime import datetime
import re

def get_config_from_env():
    """从环境变量中获取并验证配置"""
    project_root = os.getenv("PROJECT_ROOT")
    strategy_path = os.getenv("STRATEGY_PATH")
    opt_strategy_name = os.getenv("OPT_STRATEGY_NAME")
    bt_strategy_name = os.getenv("BT_STRATEGY_NAME")

    if not all([project_root, strategy_path, opt_strategy_name, bt_strategy_name]):
        logging.error("错误: 必要的环境变量 (e.g., PROJECT_ROOT, STRATEGY_PATH, OPT_STRATEGY_NAME) 未设置。")
        exit(1)

    hyperopt_results_dir = Path(project_root) / "user_data" / "hyperopt_results"
    
    settings_file = Path(project_root) / "user_data" / strategy_path / f"{bt_strategy_name}_Settings.json"

    logging.info(f"配置加载成功。将从 '{hyperopt_results_dir}' 读取结果，更新至 '{settings_file}'。")
    return settings_file, hyperopt_results_dir, opt_strategy_name

def find_hyperopt_files(hyperopt_dir: Path, strategy_name: str) -> List[Path]:
    """查找指定策略的所有相关hyperopt结果文件 (只找.json，忽略备份)。"""
    files_found = list(hyperopt_dir.glob(f'*{strategy_name}*.json'))
    
    if not files_found:
        logging.warning(f"在 '{hyperopt_dir}' 中未找到策略 '{strategy_name}' 的 .json 结果文件。")
    else:
        logging.info(f"为策略 '{strategy_name}' 找到 {len(files_found)} 个结果文件待处理。")
    return files_found

def process_hyperopt_file(file_path: Path, strategy_name: str) -> Dict[str, Any] or None:
    """
    读取单个优化参数文件（由步骤1生成），提取交易对和参数。
    该版本直接从文件名解析交易对，并加载整个JSON文件作为最终参数。
    """
    logging.debug(f"正在处理文件: '{file_path.name}'")
    
    # --- 核心逻辑 1: 从文件名提取交易对 ---
    file_pair = None
    # 期望的文件名格式: strategy_{strategy_name}_{pair_filename}.json
    match = re.search(rf"strategy_{strategy_name}_(.*?)\.json", file_path.name)
    if match:
        pair_filename = match.group(1)
        # 反向转换文件名: '--' -> ':', '_' -> '/'
        file_pair = pair_filename.replace('--', ':').replace('_', '/')
        logging.info(f"从文件名 '{file_path.name}' 中解析出交易对: {file_pair}")
    else:
        logging.warning(f"无法从文件名 '{file_path.name}' 的格式中解析出交易对，已跳过。")
        return None

    # --- 核心逻辑 2: 从文件内容读取最终参数 ---
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            params = json.load(f)

        # Freqtrade有时会将所有参数嵌套在 "params"键下，有时则不会。
        # 此处进行兼容处理，并展平参数结构。
        params_source = params.get('params', params)
        
        flat_params = {}
        for key, value in params_source.items():
            # 新的、更通用的展平逻辑：只要值是字典，就将其内容合并
            if isinstance(value, dict):
                flat_params.update(value)
            else:
                # 直接添加顶层参数，如 "stoploss": -0.1
                flat_params[key] = value

        # 关键修复：定义一个不应包含在最终参数文件中的参数"黑名单"
        EXCLUDED_PARAMS = {'max_open_trades'}
        final_params = {k: v for k, v in flat_params.items() if k not in EXCLUDED_PARAMS}

        if not final_params:
            logging.warning(f"文件 '{file_path.name}' 中未找到或未能解析出有效参数（过滤后）。")
            return None

        return {"pair": file_pair, "params": final_params}

    except json.JSONDecodeError:
        logging.error(f"解析JSON文件 '{file_path.name}' 失败。请检查文件内容是否为有效的JSON格式。")
        return None
    except Exception as e:
        logging.error(f"处理文件 '{file_path.name}' 时发生未知错误: {e}")
        return None

def run():
    """主函数，负责协调整个更新流程。"""
    settings_file, hyperopt_results_dir, opt_strategy_name = get_config_from_env()
    
    result_files = find_hyperopt_files(hyperopt_results_dir, opt_strategy_name)
    if not result_files:
        return

    # 初始化settings_data。如果文件存在且有效，则加载它；否则，从头开始。
    # 修改1：最终合并所有参数的地方
    final_params_by_pair: Dict[str, Dict[str, Any]] = {}
    
    if settings_file.exists():
        try:
            # 创建备份
            backup_file = settings_file.with_suffix(f".json.backup_{datetime.now().strftime('%Y%m%d%H%M%S')}")
            shutil.copy(settings_file, backup_file)
            logging.info(f"已将当前设置备份至 '{backup_file}'。")

            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    loaded_data = json.loads(content)
                    # 使用 loaded_data 初始化 final_params_by_pair
                    for key, value in loaded_data.items():
                        if '/' in key and isinstance(value, dict):
                            final_params_by_pair[key] = value
                    
                    if final_params_by_pair:
                        logging.info(f"成功从现有文件中加载了 {len(final_params_by_pair)} 个交易对的参数。")

        except (json.JSONDecodeError, FileNotFoundError):
            logging.error(f"解析 '{settings_file}' 出错或文件不存在，将创建新的配置。")

    # 修改2：遍历所有文件，直接使用找到的最佳参数覆盖旧的
    for result_file in result_files:
        # 传递 strategy_name 以便从文件名中解析交易对
        optimal_data = process_hyperopt_file(result_file, opt_strategy_name)
        
        if optimal_data:
            pair = optimal_data['pair']
            params_from_file = optimal_data['params']
            
            # 直接将从文件中找到的最佳参数作为最终参数
            final_params_by_pair[pair] = params_from_file

            logging.info(f"已为交易对 '{pair}' 记录最终优化参数。")
    
    # 修改3：基于最终结果，统计更新数量并准备写入
    valid_pairs_after_merge = {p for p, params in final_params_by_pair.items() if params}
    updated_pairs_count = len(valid_pairs_after_merge)

    # --- 关键安全检查 ---
    # 检查最终的 final_params_by_pair 是否为空。
    if not final_params_by_pair:
        logging.error("="*80)
        logging.error("致命错误: 未能从任何 hyperopt 结果文件中成功提取任何交易对的参数，且现有设置文件为空或无效。")
        logging.error(f"请检查 'user_data/hyperopt_results/' 目录中的结果文件是否有效。")
        logging.error("为了安全起见，脚本将不会修改现有的设置文件。工作流中断。")
        logging.error("="*80)
        exit(1) # 使用错误码退出，以便CI/CD等工具可以捕获

    if updated_pairs_count > 0:
        with open(settings_file, 'w', encoding='utf-8') as f:
            # 写入最终合并后的结果
            json.dump(final_params_by_pair, f, indent=4)
        logging.info(f"成功更新参数文件 '{settings_file.name}'。总共处理了 {updated_pairs_count} 个交易对。")
    else:
        logging.warning("更新完成，但没有参数被修改。")

if __name__ == "__main__":
    # 配置日志记录器以显示 DEBUG 级别消息
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    logging.info("--- 启动Hyperopt结果处理脚本 ---")
    run()
    logging.info("--- 脚本执行完毕 ---") 