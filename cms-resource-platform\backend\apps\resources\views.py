"""
资源管理视图
"""
from rest_framework import generics, permissions, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import Q, F
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from apps.core.models import Category, Tag
from apps.users.models import UserFavorite, UserHistory
from .models import Resource, ResourceRating, ResourceComment, Collection, ResourceDownload
from .serializers import (
    CategorySerializer, TagSerializer, ResourceSerializer,
    ResourceCreateSerializer, ResourceUpdateSerializer,
    ResourceRatingSerializer, ResourceCommentSerializer,
    CollectionSerializer, ResourceDownloadSerializer
)
from .filters import ResourceFilter
import secrets
import os


class CategoryListView(generics.ListAPIView):
    """分类列表"""
    queryset = Category.objects.filter(is_active=True, parent=None)
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]


class TagListView(generics.ListAPIView):
    """标签列表"""
    queryset = Tag.objects.all().order_by('-usage_count')
    serializer_class = TagSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']


class ResourceListView(generics.ListAPIView):
    """资源列表"""
    serializer_class = ResourceSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ResourceFilter
    search_fields = ['title', 'description', 'content', 'meta_keywords']
    ordering_fields = ['created_at', 'view_count', 'download_count', 'rating_score']
    ordering = ['-created_at']
    
    def get_queryset(self):
        return Resource.objects.filter(
            status='published',
            is_public=True
        ).select_related('category', 'author').prefetch_related('tags')


class ResourceDetailView(generics.RetrieveAPIView):
    """资源详情"""
    serializer_class = ResourceSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        return Resource.objects.filter(
            status='published',
            is_public=True
        ).select_related('category', 'author').prefetch_related('tags', 'images')
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # 增加浏览量
        Resource.objects.filter(id=instance.id).update(
            view_count=F('view_count') + 1
        )
        
        # 记录用户浏览历史
        if request.user.is_authenticated:
            history, created = UserHistory.objects.get_or_create(
                user=request.user,
                content_type='resource',
                object_id=str(instance.id),
                defaults={'view_count': 1}
            )
            if not created:
                history.view_count += 1
                history.last_viewed_at = timezone.now()
                history.save()
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ResourceCreateView(generics.CreateAPIView):
    """资源创建"""
    serializer_class = ResourceCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def perform_create(self, serializer):
        # 检查用户上传权限
        user = self.request.user
        if user.user_group:
            upload_limit = user.user_group.upload_limit
            if user.upload_count >= upload_limit:
                raise PermissionError("已达到上传限制")
        
        resource = serializer.save()
        
        # 更新用户上传次数
        user.upload_count += 1
        user.save(update_fields=['upload_count'])


class ResourceUpdateView(generics.UpdateAPIView):
    """资源更新"""
    serializer_class = ResourceUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Resource.objects.filter(author=self.request.user)


class ResourceDeleteView(generics.DestroyAPIView):
    """资源删除"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Resource.objects.filter(author=self.request.user)


class MyResourcesView(generics.ListAPIView):
    """我的资源"""
    serializer_class = ResourceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description']
    ordering = ['-created_at']
    
    def get_queryset(self):
        return Resource.objects.filter(
            author=self.request.user
        ).select_related('category').prefetch_related('tags')


class ResourceDownloadView(APIView):
    """资源下载"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, pk):
        resource = get_object_or_404(Resource, pk=pk, status='published')
        user = request.user
        
        # 检查下载权限
        if not resource.can_download(user):
            return Response(
                {'error': '没有下载权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 检查下载限制
        if user.user_group:
            download_limit = user.user_group.download_limit
            today = timezone.now().date()
            today_downloads = ResourceDownload.objects.filter(
                user=user,
                created_at__date=today
            ).count()
            
            if today_downloads >= download_limit:
                return Response(
                    {'error': '今日下载次数已用完'},
                    status=status.HTTP_429_TOO_MANY_REQUESTS
                )
        
        # 扣除积分
        points_cost = resource.required_points
        if points_cost > 0:
            try:
                user.spend_points(points_cost, f'下载资源: {resource.title}')
            except ValueError as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # 生成下载令牌
        download_token = secrets.token_urlsafe(32)
        expires_at = timezone.now() + timezone.timedelta(minutes=5)
        
        # 记录下载
        download_record = ResourceDownload.objects.create(
            resource=resource,
            user=user,
            points_cost=points_cost,
            download_token=download_token,
            token_expires_at=expires_at,
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        # 更新统计
        Resource.objects.filter(id=resource.id).update(
            download_count=F('download_count') + 1
        )
        user.download_count += 1
        user.save(update_fields=['download_count'])
        
        # 返回下载信息
        download_url = f"/api/resources/{pk}/download/{download_token}/"
        
        return Response({
            'download_url': download_url,
            'expires_at': expires_at,
            'points_cost': points_cost,
            'remaining_points': user.points
        })
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ResourceFavoriteView(APIView):
    """资源收藏"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, pk):
        """添加收藏"""
        resource = get_object_or_404(Resource, pk=pk, status='published')
        user = request.user
        
        favorite, created = UserFavorite.objects.get_or_create(
            user=user,
            content_type='resource',
            object_id=str(pk)
        )
        
        if created:
            # 更新收藏数
            Resource.objects.filter(id=pk).update(
                favorite_count=F('favorite_count') + 1
            )
            return Response({'message': '收藏成功'})
        else:
            return Response(
                {'message': '已经收藏过了'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def delete(self, request, pk):
        """取消收藏"""
        user = request.user
        
        try:
            favorite = UserFavorite.objects.get(
                user=user,
                content_type='resource',
                object_id=str(pk)
            )
            favorite.delete()
            
            # 更新收藏数
            Resource.objects.filter(id=pk).update(
                favorite_count=F('favorite_count') - 1
            )
            
            return Response({'message': '取消收藏成功'})
        except UserFavorite.DoesNotExist:
            return Response(
                {'message': '未收藏该资源'},
                status=status.HTTP_400_BAD_REQUEST
            )


class ResourceRatingView(generics.CreateAPIView):
    """资源评分"""
    serializer_class = ResourceRatingSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def perform_create(self, serializer):
        resource_id = self.kwargs['pk']
        resource = get_object_or_404(Resource, pk=resource_id)
        
        # 检查是否已评分
        if ResourceRating.objects.filter(
            resource=resource,
            user=self.request.user
        ).exists():
            raise ValidationError("已经评分过了")
        
        rating = serializer.save(resource=resource)
        
        # 更新资源评分
        self.update_resource_rating(resource)
    
    def update_resource_rating(self, resource):
        """更新资源评分"""
        ratings = ResourceRating.objects.filter(resource=resource)
        if ratings.exists():
            avg_score = ratings.aggregate(avg=models.Avg('score'))['avg']
            resource.rating_score = round(avg_score, 2)
            resource.rating_count = ratings.count()
            resource.save(update_fields=['rating_score', 'rating_count'])


class ResourceCommentListView(generics.ListCreateAPIView):
    """资源评论"""
    serializer_class = ResourceCommentSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        resource_id = self.kwargs['pk']
        return ResourceComment.objects.filter(
            resource_id=resource_id,
            is_approved=True,
            parent=None
        ).order_by('-created_at')
    
    def perform_create(self, serializer):
        resource_id = self.kwargs['pk']
        resource = get_object_or_404(Resource, pk=resource_id)
        serializer.save(resource=resource)


class FeaturedResourcesView(generics.ListAPIView):
    """推荐资源"""
    serializer_class = ResourceSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        return Resource.objects.filter(
            status='published',
            is_public=True,
            is_featured=True
        ).order_by('-created_at')[:10]


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def resource_stats(request):
    """资源统计"""
    stats = {
        'total_resources': Resource.objects.filter(status='published').count(),
        'total_downloads': ResourceDownload.objects.count(),
        'total_categories': Category.objects.filter(is_active=True).count(),
        'total_tags': Tag.objects.count(),
    }
    return Response(stats)
