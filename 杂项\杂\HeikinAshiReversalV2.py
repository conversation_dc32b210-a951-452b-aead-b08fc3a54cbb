import logging
import numpy as np
import pandas as pd
import talib.abstract as ta
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from pandas import DataFrame
import freqtrade.vendor.qtpylib.indicators as qtpylib

logger = logging.getLogger(__name__)


class HeikinAshiReversalV2(IStrategy):
    """
    Heikin Ashi Reversal Strategy
    Version: 2.0

    Tries to capture trend continuations after a brief pullback.
    
    Entry Logic:
    1. Identify the main trend direction (e.g., using a long-period EMA).
    2. Wait for a short-term counter-trend movement (e.g., 1-2 candles of opposite color).
    3. Confirm the trend resumption with two consecutive strong Heikin Ashi candles
       (green with no lower wick for longs, red with no upper wick for shorts).
    4. Enter the trade after the second confirmation candle.

    Exit Logic:
    - Exit when a clear reversal candle (e.g., a Doji of the opposite color) appears,
      indicating the trend might be exhausted.
    - A volatility filter is applied to avoid exiting on minor, low-volatility pullbacks.
    """
    INTERFACE_VERSION = 3
    can_short = True

    # --- Strategy parameters ---
    # ROI table and stoploss will be disabled in favor of custom exit logic
    minimal_roi = {"0": 100}
    stoploss = -0.99  # Large stoploss to give room for custom exit logic

    # Trailing stop settings
    trailing_stop = False
    
    # --- Timeframe and startup candle count ---
    timeframe = '15m'
    startup_candle_count: int = 200

    # --- Main Trend Filter ---
    ema_long_period = IntParameter(100, 250, default=200, space='buy', optimize=True)

    # --- Trend Strength Filter ---
    adx_period = IntParameter(10, 30, default=14, space='buy', optimize=True)
    adx_threshold = IntParameter(20, 40, default=25, space='buy', optimize=True)

    # --- Entry Pattern Parameters ---
    # Defines how small the wick of a "strong" candle has to be (relative to its range)
    strong_candle_wick_pct = DecimalParameter(0.0, 0.2, default=0.05, decimals=2, space='buy', optimize=True)

    # --- Exit Parameters ---
    # Defines how small the body of a candle has to be (relative to its range) to be a Doji
    exit_doji_body_pct = DecimalParameter(0.0, 0.2, default=0.05, decimals=2, space='sell', optimize=True)
    # ATR period for volatility check on exit
    exit_atr_period = IntParameter(10, 30, default=14, space='sell', optimize=True)
    # Multiplier for ATR to determine if an exit candle's range is significant
    exit_atr_multiplier = DecimalParameter(0.5, 2.0, default=1.0, decimals=1, space='sell', optimize=True)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several indicators to the dataframe.
        """
        # --- Heikin Ashi Calculation ---
        ha_df = self.heikin_ashi(dataframe)
        dataframe['ha_open'] = ha_df['open']
        dataframe['ha_high'] = ha_df['high']
        dataframe['ha_low'] = ha_df['low']
        dataframe['ha_close'] = ha_df['close']

        # --- Heikin Ashi Candle Properties ---
        dataframe['ha_is_green'] = dataframe['ha_close'] > dataframe['ha_open']
        dataframe['ha_is_red'] = dataframe['ha_close'] < dataframe['ha_open']
        
        # --- Heikin Ashi Candle Properties ---
        dataframe['ha_body_size'] = abs(dataframe['ha_close'] - dataframe['ha_open'])
        dataframe['ha_range'] = dataframe['ha_high'] - dataframe['ha_low']
        # Prevent division by zero for candles with no range & fix FutureWarning
        dataframe['ha_range'] = dataframe['ha_range'].replace(0, np.nan)
        
        # Strong Bullish Candle: Green body, very small lower wick
        lower_wick = dataframe['ha_open'] - dataframe['ha_low']
        dataframe['ha_strong_green'] = (
            dataframe['ha_is_green'] & 
            ((lower_wick / dataframe['ha_range']) < self.strong_candle_wick_pct.value)
        )
        
        # Strong Bearish Candle: Red body, very small upper wick
        upper_wick = dataframe['ha_high'] - dataframe['ha_open']
        dataframe['ha_strong_red'] = (
            dataframe['ha_is_red'] & 
            ((upper_wick / dataframe['ha_range']) < self.strong_candle_wick_pct.value)
        )

        # --- Main Trend & Strength Filters (on HA data) ---
        dataframe['ema_long'] = ta.EMA(dataframe['ha_close'], timeperiod=self.ema_long_period.value)
        dataframe['adx'] = ta.ADX(ha_df, timeperiod=self.adx_period.value)

        # --- Exit Logic Indicators ---
        dataframe['ha_is_doji'] = (dataframe['ha_body_size'] / dataframe['ha_range']) < self.exit_doji_body_pct.value

        dataframe['atr'] = ta.ATR(ha_df, timeperiod=self.exit_atr_period.value)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        V3 Entry Logic:
        Simplified to be more robust. Enters after a 1-candle pullback is followed by
        two strong confirmation candles in the direction of the main trend.
        """
        is_trending = dataframe['adx'] > self.adx_threshold.value
        
        # --- Long Entry Conditions ---
        is_uptrend = dataframe['ha_close'] > dataframe['ema_long']

        # Simplified Logic: 1 red candle (pullback), followed by 2 strong green candles.
        long_cond = (
            is_trending &
            is_uptrend &
            dataframe['ha_is_red'].shift(2) &          # Pullback candle 2 periods ago
            dataframe['ha_strong_green'].shift(1) &   # First confirmation candle
            dataframe['ha_strong_green']              # Second confirmation candle
        )
        dataframe.loc[long_cond, ['enter_long', 'enter_tag']] = (1, 'long_entry_v3')

        # --- Short Entry Conditions ---
        is_downtrend = dataframe['ha_close'] < dataframe['ema_long']

        # Simplified Logic: 1 green candle (pullback), followed by 2 strong red candles.
        short_cond = (
            is_trending &
            is_downtrend &
            dataframe['ha_is_green'].shift(2) &       # Pullback candle 2 periods ago
            dataframe['ha_strong_red'].shift(1) &     # First confirmation candle
            dataframe['ha_strong_red']                # Second confirmation candle
        )
        dataframe.loc[short_cond, ['enter_short', 'enter_tag']] = (1, 'short_entry_v3')
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Exit when a contrary Doji appears, but only if the candle has significant volatility.
        """
        # --- Exit Volatility Check ---
        # Exit only if the reversal candle's range is larger than ATR * multiplier
        is_volatile_exit = dataframe['ha_range'] > (dataframe['atr'] * self.exit_atr_multiplier.value)

        # --- Long Exit: A red Doji with enough volatility ---
        exit_long_cond = (
            dataframe['ha_is_red'] &
            dataframe['ha_is_doji'] &
            is_volatile_exit
        )
        dataframe.loc[exit_long_cond, ['exit_long', 'exit_tag']] = (1, 'exit_long_doji_v3')

        # --- Short Exit: A green Doji with enough volatility ---
        exit_short_cond = (
            dataframe['ha_is_green'] &
            dataframe['ha_is_doji'] &
            is_volatile_exit
        )
        dataframe.loc[exit_short_cond, ['exit_short', 'exit_tag']] = (1, 'exit_short_doji_v3')
        
        return dataframe
    
    def heikin_ashi(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates Heikin Ashi candles
        This implementation is self-contained and does not use any external libraries for HA.
        """
        df = dataframe.copy()
        
        df['ha_close'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4

        # Heikin Ashi Open
        # Set the first HA Open value (no previous bar exists)
        df.loc[0, 'ha_open'] = (df.loc[0, 'open'] + df.loc[0, 'close']) / 2
        # Use a vectorized operation for subsequent HA Open values
        ha_open_values = (df['ha_open'].shift(1) + df['ha_close'].shift(1)) / 2
        df['ha_open'] = ha_open_values.fillna(df['ha_open'])
        
        # Heikin Ashi High and Low
        df['ha_high'] = df[['ha_open', 'ha_close']].join(dataframe['high']).max(axis=1)
        df['ha_low'] = df[['ha_open', 'ha_close']].join(dataframe['low']).min(axis=1)
        
        # Return a new dataframe with only the HA candles
        return pd.DataFrame({
            'open': df['ha_open'],
            'high': df['ha_high'],
            'low': df['ha_low'],
            'close': df['ha_close'],
            'volume': df['volume'] # Pass volume for indicator calculations
        }) 