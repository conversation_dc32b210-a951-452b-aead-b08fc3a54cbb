import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy.interface import IStrategy
from freqtrade.strategy import merge_informative_pair, CategoricalParameter, DecimalParameter, IntParameter
from pandas import DataFrame
from functools import reduce
from typing import Dict, List, Optional
import logging
from freqtrade.persistence import Trade

# 添加logger
logger = logging.getLogger(__name__)

class ScoringStrategy(IStrategy):
    """
    完整版多指标评分系统策略+SuperTrend+BTC趋势过滤+KAMA斜率判断+买入第一信号过滤
    
    该策略通过计算多个技术指标的信号，综合评估资产的买入/卖出时机。
    每个指标具有不同的权重，特定信号组合可以获得额外加分，
    最终得分会根据交易量和ADX过滤器进行调整。

    包含以下指标组：
    - 基础指标：MACD, ADX, EMA, 布林带, RSI, MFI, Stochastic, Stochastic RSI
    - 其他指标：PSAR, APO, TRIX, CMO, AO, BOP, EMV, Fisher Transform, OBV
    - 附加MA指标：DEMA, TEMA, KAMA, HMA
    - 杂项指标：LinReg, TRIMA, VWMA, Wilder's, Momentum, AD slope
    - 新增指标: SuperTrend
    - 全局过滤: BTC趋势过滤
    """
    
    # 策略基本参数
    minimal_roi = {
        "0": 0.05,
        "15": 0.03,
        "30": 0.02,
        "60": 0.01 

    }
    
    stoploss = -0.99
    timeframe = '15m'
    
    # 可优化的分数阈值 - 更新
    entry_score = DecimalParameter(5.0, 15.0, default=11.423, space="buy", optimize=True)
    
    # 过滤器参数 - 更新
    low_volume_threshold = DecimalParameter(0.3, 1.0, default=0.797, space="buy", optimize=True)
    
    # SuperTrend参数
    supertrend_atr_period = IntParameter(5, 15, default=14, space="buy", optimize=True)
    supertrend_atr_multiplier = DecimalParameter(1.0, 3.5, default=1.14, space="buy", optimize=True)
    
    # 添加BTC趋势过滤参数
    btc_trend_filter = CategoricalParameter([True, False], default=False, space="buy", optimize=True)
    
    # 指标参数 - 可优化
    macd_fast = IntParameter(5, 15, default=12, space="buy", optimize=False)
    macd_slow = IntParameter(15, 30, default=22, space="buy", optimize=False)
    macd_signal = IntParameter(5, 20, default=6, space="buy", optimize=False)
    
    adx_period = IntParameter(10, 30, default=16, space="buy", optimize=False)
    adx_threshold = IntParameter(15, 35, default=32, space="buy", optimize=False)
    
    rsi_period = IntParameter(4, 14, default=6, space="buy", optimize=False)
    rsi_oversold = IntParameter(20, 40, default=35, space="buy", optimize=False)
    rsi_overbought = IntParameter(65, 85, default=78, space="buy", optimize=False)
    
    mfi_period = IntParameter(10, 30, default=28, space="buy", optimize=False)
    mfi_oversold = IntParameter(5, 20, default=17, space="buy", optimize=False)
    mfi_overbought = IntParameter(60, 80, default=78, space="buy", optimize=False)
    
    stoch_k = IntParameter(5, 25, default=8, space="buy", optimize=False)
    stoch_d = IntParameter(3, 15, default=6, space="buy", optimize=False)
    stoch_oversold = IntParameter(15, 35, default=29, space="buy", optimize=False)
    stoch_overbought = IntParameter(65, 85, default=81, space="buy", optimize=False)
    
    stoch_rsi_k = IntParameter(1, 10, default=8, space="buy", optimize=False)
    stoch_rsi_d = IntParameter(1, 10, default=10, space="buy", optimize=False)
    stoch_rsi_period = IntParameter(10, 30, default=29, space="buy", optimize=False)
    stoch_rsi_oversold = IntParameter(10, 40, default=19, space="buy", optimize=False)
    stoch_rsi_overbought = IntParameter(75, 95, default=75, space="buy", optimize=False)
    
    bb_period = IntParameter(20, 50, default=43, space="buy", optimize=False)
    bb_std = DecimalParameter(1.5, 3.0, default=2.214, space="buy", optimize=False)
    
    ema_13 = IntParameter(10, 20, default=18, space="buy", optimize=False)
    ema_20 = IntParameter(20, 40, default=23, space="buy", optimize=False)
    ema_34 = IntParameter(30, 50, default=38, space="buy", optimize=False)
    
    # 指标权重 - 更新
    # MACD权重
    macd_line_cross_weight = DecimalParameter(1.0, 5.0, default=1.824, space="buy", optimize=False)
    macd_hist_weight = DecimalParameter(0.5, 2.0, default=1.388, space="buy", optimize=False)
    
    # ADX & DI权重
    adx_di_cross_weight = DecimalParameter(1.0, 4.0, default=2.152, space="buy", optimize=False)
    
    # EMA权重
    ema_cross_weight = DecimalParameter(1.0, 4.0, default=3.383, space="buy", optimize=False)
    ema_price_cross_weight = DecimalParameter(0.5, 3.0, default=2.74, space="buy", optimize=False)
    
    # 布林带权重
    bb_band_weight = DecimalParameter(0.5, 3.0, default=1.795, space="buy", optimize=False)
    bb_middle_cross_weight = DecimalParameter(0.5, 2.0, default=1.915, space="buy", optimize=False)
    
    # 震荡指标权重
    rsi_weight = DecimalParameter(0.5, 3.0, default=2.193, space="buy", optimize=False)
    mfi_weight = DecimalParameter(0.5, 3.0, default=2.162, space="buy", optimize=False)
    stoch_weight = DecimalParameter(0.5, 3.0, default=0.592, space="buy", optimize=False)
    stoch_rsi_weight = DecimalParameter(0.5, 3.0, default=2.732, space="buy", optimize=False)
    
    # 协同效应加分参数 - 更新
    synergy_macd_rsi_bonus = DecimalParameter(1.0, 4.0, default=2.084, space="buy", optimize=False)
    synergy_adx_di_bonus = DecimalParameter(0.5, 2.0, default=1.613, space="buy", optimize=False)
    synergy_rsi_mfi_bonus = DecimalParameter(0.5, 3.0, default=0.905, space="buy", optimize=False)
    synergy_volume_bonus = DecimalParameter(0.5, 2.0, default=1.7, space="buy", optimize=False)
    synergy_momentum_bonus = DecimalParameter(1.0, 4.0, default=3.305, space="buy", optimize=False)
    
    # ADX过滤器参数 - 更新
    adx_weak_filter = DecimalParameter(0.2, 0.6, default=0.568, space="buy", optimize=False)
    adx_moderate_filter = DecimalParameter(1.0, 1.3, default=1.244, space="buy", optimize=False)
    adx_strong_filter = DecimalParameter(1.1, 1.6, default=1.208, space="buy", optimize=False)
    
    # 新增：用于定义"强劲得分"的因子，以应对趋势尾部入场风险
    strong_score_factor = DecimalParameter(1.1, 1.5, default=1.476, space="buy", optimize=True)
    
    # 新增指标组权重调整参数
    other_indicators_weight = DecimalParameter(0.5, 2.0, default=0.637, space="buy", optimize=False)
    ma_indicators_weight = DecimalParameter(0.5, 2.0, default=0.629, space="buy", optimize=False)
    misc_indicators_weight = DecimalParameter(0.5, 2.0, default=0.623, space="buy", optimize=False)
    
    # 较小权重指标参数
    cci_weight = DecimalParameter(0.5, 2.0, default=1.025, space="buy", optimize=False)
    roc_weight = DecimalParameter(0.5, 2.0, default=1.606, space="buy", optimize=False)
    mom_weight = DecimalParameter(0.5, 2.0, default=1.243, space="buy", optimize=False)
    williams_r_weight = DecimalParameter(0.5, 2.0, default=0.944, space="buy", optimize=False)
    ultosc_weight = DecimalParameter(0.5, 2.0, default=1.504, space="buy", optimize=False)
    aroonosc_weight = DecimalParameter(0.5, 2.0, default=0.923, space="buy", optimize=False)
    
    # 其他指标权重
    psar_weight = DecimalParameter(0.5, 2.0, default=0.828, space="buy", optimize=False)
    apo_weight = DecimalParameter(0.5, 2.0, default=1.291, space="buy", optimize=False)
    trix_weight = DecimalParameter(0.5, 2.0, default=1.286, space="buy", optimize=False)
    cmo_weight = DecimalParameter(0.5, 2.0, default=1.911, space="buy", optimize=False)
    ao_weight = DecimalParameter(0.5, 2.0, default=1.357, space="buy", optimize=False)
    bop_weight = DecimalParameter(0.5, 2.0, default=0.889, space="buy", optimize=False)
    emv_weight = DecimalParameter(0.5, 2.0, default=0.701, space="buy", optimize=False)
    fisher_weight = DecimalParameter(0.5, 2.0, default=0.663, space="buy", optimize=False)
    obv_weight = DecimalParameter(0.5, 2.0, default=1.537, space="buy", optimize=False)
    
    # 附加MA指标权重
    dema_weight = DecimalParameter(0.5, 2.0, default=0.973, space="buy", optimize=False)
    tema_weight = DecimalParameter(0.5, 2.0, default=1.892, space="buy", optimize=False)
    kama_weight = DecimalParameter(0.5, 2.0, default=1.512, space="buy", optimize=False)
    hma_weight = DecimalParameter(0.5, 2.0, default=1.511, space="buy", optimize=False)
    
    # 杂项指标权重
    linreg_weight = DecimalParameter(0.5, 2.0, default=0.982, space="buy", optimize=False)
    trima_weight = DecimalParameter(0.5, 2.0, default=0.592, space="buy", optimize=False)
    vwma_weight = DecimalParameter(0.5, 2.0, default=1.805, space="buy", optimize=False)
    wilders_weight = DecimalParameter(0.5, 2.0, default=0.783, space="buy", optimize=False)
    ad_slope_weight = DecimalParameter(0.5, 2.0, default=0.967, space="buy", optimize=False)
    
    # 额外协同效应加分参数
    ma_synergy_bonus = DecimalParameter(1.0, 4.0, default=3.544, space="buy", optimize=False)
    psar_bb_synergy_bonus = DecimalParameter(0.5, 3.0, default=1.645, space="buy", optimize=False)
    obv_ao_synergy_bonus = DecimalParameter(0.5, 3.0, default=1.978, space="buy", optimize=False)
    fisher_cmo_synergy_bonus = DecimalParameter(0.5, 3.0, default=1.068, space="buy", optimize=False)
    
    # ATR动态止损参数
    atr_period = IntParameter(5, 15, default=6, space="sell", optimize=True)
    atr_multiplier = DecimalParameter(0.4, 1.0, default=0.966, space="sell", optimize=True)

    # 关键支撑位参数
    adx_exit_threshold = IntParameter(15, 30, default=29, space="sell", optimize=False)

    # 布林带回落参数
    rsi_exit_overbought = IntParameter(50, 75, default=72, space="sell", optimize=False)
    
    trailing_stop = False
    trailing_stop_positive = 0.01  # 当利润达到1%时激活
    trailing_stop_positive_offset = 0.02  # 从最高点回落2%时卖出
    trailing_only_offset_is_reached = False  # 一旦盈利就开始跟踪

    # 存储所有交易对的分数信息
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        self.current_candle_time = None
        self.max_open_trades = config.get('max_open_trades', 4)
        # 添加BTC趋势状态存储
        self.btc_trend = None
        self.btc_trend_last_check = None  # 添加最后检查时间记录
        # 添加信号追踪字典
        self.signal_tracker = {}  # 用于追踪各交易对的信号状态

    # 添加informative_pairs方法，获取BTC数据
    def informative_pairs(self) -> List[str]:
        """
        定义需要获取的额外信息交易对
        """
        informative_pairs = []
        
        # 如果启用了BTC趋势过滤，添加BTC/USDT:USDT作为informative pair
        if self.btc_trend_filter.value:
            btc_pair = "BTC/USDT:USDT"
            informative_pairs.append((btc_pair, self.timeframe))
            
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算各种技术指标，用于后续评分系统
        """
        # MACD
        macd = ta.MACD(dataframe, 
                       fastperiod=self.macd_fast.value, 
                       slowperiod=self.macd_slow.value, 
                       signalperiod=self.macd_signal.value)
        dataframe['macd'] = macd['macd']
        dataframe['macdsignal'] = macd['macdsignal']
        dataframe['macdhist'] = macd['macdhist']
        
        # ADX & DI
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_period.value)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=self.adx_period.value)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=self.adx_period.value)
        
        # EMA
        dataframe['ema13'] = ta.EMA(dataframe, timeperiod=self.ema_13.value)
        dataframe['ema20'] = ta.EMA(dataframe, timeperiod=self.ema_20.value)
        dataframe['ema34'] = ta.EMA(dataframe, timeperiod=self.ema_34.value)
        
        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=self.bb_period.value, stds=self.bb_std.value)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']
        
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        
        # MFI
        dataframe['mfi'] = ta.MFI(dataframe, timeperiod=self.mfi_period.value)
        
        # Stochastic
        stoch = ta.STOCH(dataframe, 
                        fastk_period=self.stoch_k.value,
                        slowk_period=1,
                        slowk_matype=0,
                        slowd_period=self.stoch_d.value,
                        slowd_matype=0)
        dataframe['slowk'] = stoch['slowk']
        dataframe['slowd'] = stoch['slowd']
        
        # Stochastic RSI
        stoch_rsi = ta.STOCHRSI(dataframe, 
                               timeperiod=self.stoch_rsi_period.value,
                               fastk_period=self.stoch_rsi_k.value,
                               fastd_period=self.stoch_rsi_d.value)
        dataframe['fastk'] = stoch_rsi['fastk']
        dataframe['fastd'] = stoch_rsi['fastd']
        
        # 其他较小权重指标
        dataframe['cci'] = ta.CCI(dataframe, timeperiod=14)
        dataframe['roc'] = ta.ROC(dataframe, timeperiod=10)
        dataframe['mom'] = ta.MOM(dataframe, timeperiod=10)
        dataframe['williams_r'] = ta.WILLR(dataframe, timeperiod=14)
        dataframe['ultosc'] = ta.ULTOSC(dataframe, timeperiod1=7, timeperiod2=14, timeperiod3=28)
        dataframe['aroonosc'] = ta.AROONOSC(dataframe, timeperiod=14)
        
        # 新增指标 - 其他指标组
        dataframe['psar'] = ta.SAR(dataframe, acceleration=0.02, maximum=0.2)
        dataframe['apo'] = ta.APO(dataframe, fastperiod=12, slowperiod=26, matype=0)
        dataframe['trix'] = ta.TRIX(dataframe, timeperiod=14)
        dataframe['cmo'] = ta.CMO(dataframe, timeperiod=14)
        dataframe['bop'] = ta.BOP(dataframe)
        
        # 新增 AO (Awesome Oscillator)
        dataframe['ao'] = qtpylib.awesome_oscillator(dataframe)
        
        # 新增 EMV (Ease of Movement)
        dataframe['emv'] = ((dataframe['high'] + dataframe['low']) / 2 - (dataframe['high'].shift(1) + dataframe['low'].shift(1)) / 2) / (dataframe['volume'] / (dataframe['high'] - dataframe['low']))
        
        # 新增 Fisher Transform - 修复错误的实现方式
        period = 14
        # 计算RSI的高低值
        dataframe['rsi_high'] = dataframe['rsi'].rolling(window=period).max()
        dataframe['rsi_low'] = dataframe['rsi'].rolling(window=period).min()
        # 安全地计算归一化值，避免除以零的情况
        dataframe['rsi_high_low_range'] = dataframe['rsi_high'] - dataframe['rsi_low']
        dataframe['rsi_high_low_range'] = dataframe['rsi_high_low_range'].replace(0, 1)  # 防止除零
        # 计算归一化的RSI
        dataframe['rsi_scaled'] = 2 * ((dataframe['rsi'] - dataframe['rsi_low']) / dataframe['rsi_high_low_range']) - 1
        # 限制在[-0.99, 0.99]范围内
        dataframe['rsi_scaled'] = dataframe['rsi_scaled'].clip(-0.99, 0.99)
        # 应用Fisher Transform公式
        dataframe['fisher'] = 0.5 * np.log((1 + dataframe['rsi_scaled']) / (1 - dataframe['rsi_scaled']))
        # 删除中间变量
        dataframe = dataframe.drop(['rsi_high', 'rsi_low', 'rsi_high_low_range', 'rsi_scaled'], axis=1)
        
        # 新增 OBV (On Balance Volume)
        dataframe['obv'] = ta.OBV(dataframe)
        
        # 新增附加MA指标
        dataframe['dema'] = ta.DEMA(dataframe, timeperiod=20)
        dataframe['tema'] = ta.TEMA(dataframe, timeperiod=20)
        dataframe['kama'] = ta.KAMA(dataframe, timeperiod=20)
        
        # KAMA斜率计算
        dataframe['kama_slope'] = dataframe['kama'] - dataframe['kama'].shift(3)
        
        # 新增 HMA (Hull Moving Average)
        half_length = int(14/2)
        sqrt_length = int(14**0.5)
        dataframe['wma1'] = ta.WMA(dataframe, timeperiod=half_length) * 2
        dataframe['wma2'] = ta.WMA(dataframe, timeperiod=14)
        dataframe['diff'] = dataframe['wma1'] - dataframe['wma2']
        dataframe['hma'] = ta.WMA(dataframe['diff'], timeperiod=sqrt_length)
        
        # 新增杂项指标
        dataframe['linreg'] = ta.LINEARREG(dataframe, timeperiod=14)
        dataframe['trima'] = ta.TRIMA(dataframe, timeperiod=20)
        
        # VWMA (Volume Weighted Moving Average)
        dataframe['vwma'] = (dataframe['close'] * dataframe['volume']).rolling(window=20).sum() / dataframe['volume'].rolling(window=20).sum()
        
        # Wilder's Smoothing (近似使用EMA)
        dataframe['wilders'] = ta.EMA(dataframe, timeperiod=14)
        
        # AD Slope (Accumulation/Distribution Slope)
        dataframe['ad'] = ta.AD(dataframe)
        dataframe['ad_slope'] = dataframe['ad'] - dataframe['ad'].shift(5)
        
        # 交易量分析
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_mean']
        
        # 添加SuperTrend计算
        atr = ta.ATR(dataframe, timeperiod=int(self.supertrend_atr_period.value))
        
        # 基本上轨、下轨计算
        hl2 = (dataframe['high'] + dataframe['low']) / 2
        
        # 上轨、下轨计算
        final_upperband = hl2 + (self.supertrend_atr_multiplier.value * atr)
        final_lowerband = hl2 - (self.supertrend_atr_multiplier.value * atr)
        
        # 初始化SuperTrend
        supertrend = pd.Series(0.0, index=dataframe.index)
        direction = pd.Series(1, index=dataframe.index)  # 初始方向为多头
        
        # 计算SuperTrend值和方向
        for i in range(1, len(dataframe.index)):
            if dataframe['close'].iloc[i] > final_upperband.iloc[i-1]:
                direction.iloc[i] = 1
            elif dataframe['close'].iloc[i] < final_lowerband.iloc[i-1]:
                direction.iloc[i] = -1
            else:
                direction.iloc[i] = direction.iloc[i-1]
                
                if direction.iloc[i] == 1 and final_lowerband.iloc[i] < final_lowerband.iloc[i-1]:
                    final_lowerband.iloc[i] = final_lowerband.iloc[i-1]
                if direction.iloc[i] == -1 and final_upperband.iloc[i] > final_upperband.iloc[i-1]:
                    final_upperband.iloc[i] = final_upperband.iloc[i-1]
            
            if direction.iloc[i] == 1:
                supertrend.iloc[i] = final_lowerband.iloc[i]
            else:
                supertrend.iloc[i] = final_upperband.iloc[i]
        
        dataframe['supertrend'] = supertrend
        dataframe['supertrend_direction'] = direction  # 1为多头趋势，-1为空头趋势
        
        return dataframe

    def calculate_indicator_signals(self, dataframe: DataFrame) -> DataFrame:
        """
        计算每个指标的信号和分数
        """
        # 初始化分数列
        dataframe['score'] = 0.0
        
        # MACD 信号 (权重可优化)
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal'])),
            'score'
        ] += self.macd_line_cross_weight.value
        
        dataframe.loc[
            (qtpylib.crossed_below(dataframe['macd'], dataframe['macdsignal'])),
            'score'
        ] -= self.macd_line_cross_weight.value
        
        dataframe.loc[dataframe['macdhist'] > 0, 'score'] += self.macd_hist_weight.value
        dataframe.loc[dataframe['macdhist'] < 0, 'score'] -= self.macd_hist_weight.value
        
        # ADX & DI 信号 (权重可优化)
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['plus_di'], dataframe['minus_di'])) &
            (dataframe['adx'] > self.adx_threshold.value),
            'score'
        ] += self.adx_di_cross_weight.value
        
        dataframe.loc[
            (qtpylib.crossed_below(dataframe['plus_di'], dataframe['minus_di'])) &
            (dataframe['adx'] > self.adx_threshold.value),
            'score'
        ] -= self.adx_di_cross_weight.value
        
        # EMA 交叉信号 (权重可优化)
        dataframe.loc[
            qtpylib.crossed_above(dataframe['ema13'], dataframe['ema34']),
            'score'
        ] += self.ema_cross_weight.value
        
        dataframe.loc[
            qtpylib.crossed_below(dataframe['ema13'], dataframe['ema34']),
            'score'
        ] -= self.ema_cross_weight.value
        
        dataframe.loc[
            qtpylib.crossed_above(dataframe['close'], dataframe['ema20']),
            'score'
        ] += self.ema_price_cross_weight.value
        
        dataframe.loc[
            qtpylib.crossed_below(dataframe['close'], dataframe['ema20']),
            'score'
        ] -= self.ema_price_cross_weight.value
        
        # 布林带信号 (权重可优化)
        dataframe.loc[dataframe['close'] < dataframe['bb_lowerband'], 'score'] += self.bb_band_weight.value
        dataframe.loc[dataframe['close'] > dataframe['bb_upperband'], 'score'] -= self.bb_band_weight.value
        
        dataframe.loc[
            qtpylib.crossed_above(dataframe['close'], dataframe['bb_middleband']),
            'score'
        ] += self.bb_middle_cross_weight.value
        
        dataframe.loc[
            qtpylib.crossed_below(dataframe['close'], dataframe['bb_middleband']),
            'score'
        ] -= self.bb_middle_cross_weight.value
        
        # RSI 信号 (权重可优化)
        dataframe.loc[dataframe['rsi'] < self.rsi_oversold.value, 'score'] += self.rsi_weight.value
        dataframe.loc[dataframe['rsi'] > self.rsi_overbought.value, 'score'] -= self.rsi_weight.value
        
        # MFI 信号 (权重可优化)
        dataframe.loc[dataframe['mfi'] < self.mfi_oversold.value, 'score'] += self.mfi_weight.value
        dataframe.loc[dataframe['mfi'] > self.mfi_overbought.value, 'score'] -= self.mfi_weight.value
        
        # Stochastic 信号 (权重可优化)
        dataframe.loc[
            (dataframe['slowk'] < self.stoch_oversold.value) & 
            (dataframe['slowd'] < self.stoch_oversold.value),
            'score'
        ] += self.stoch_weight.value
        
        dataframe.loc[
            (dataframe['slowk'] > self.stoch_overbought.value) & 
            (dataframe['slowd'] > self.stoch_overbought.value),
            'score'
        ] -= self.stoch_weight.value
        
        # Stochastic RSI 信号 (权重可优化)
        dataframe.loc[
            (dataframe['fastk'] < self.stoch_rsi_oversold.value) & 
            (dataframe['fastd'] < self.stoch_rsi_oversold.value),
            'score'
        ] += self.stoch_rsi_weight.value
        
        dataframe.loc[
            (dataframe['fastk'] > self.stoch_rsi_overbought.value) & 
            (dataframe['fastd'] > self.stoch_rsi_overbought.value),
            'score'
        ] -= self.stoch_rsi_weight.value
        
        # 其他较小权重指标 (权重固定为1.0)
        dataframe.loc[dataframe['cci'] < -100, 'score'] += self.cci_weight.value
        dataframe.loc[dataframe['cci'] > 100, 'score'] -= self.cci_weight.value
        
        dataframe.loc[dataframe['roc'] > 0, 'score'] += self.roc_weight.value
        dataframe.loc[dataframe['roc'] < 0, 'score'] -= self.roc_weight.value
        
        dataframe.loc[dataframe['mom'] > 0, 'score'] += self.mom_weight.value
        dataframe.loc[dataframe['mom'] < 0, 'score'] -= self.mom_weight.value
        
        dataframe.loc[dataframe['williams_r'] < -80, 'score'] += self.williams_r_weight.value
        dataframe.loc[dataframe['williams_r'] > -20, 'score'] -= self.williams_r_weight.value
        
        dataframe.loc[dataframe['ultosc'] < 30, 'score'] += self.ultosc_weight.value
        dataframe.loc[dataframe['ultosc'] > 70, 'score'] -= self.ultosc_weight.value
        
        dataframe.loc[dataframe['aroonosc'] > 50, 'score'] += self.aroonosc_weight.value
        dataframe.loc[dataframe['aroonosc'] < -50, 'score'] -= self.aroonosc_weight.value
        
        # 新增其他指标评分逻辑 (权重固定为1.0)
        # PSAR
        dataframe.loc[dataframe['close'] > dataframe['psar'], 'score'] += self.psar_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['psar'], 'score'] -= self.psar_weight.value * self.other_indicators_weight.value
        
        # APO
        dataframe.loc[dataframe['apo'] > 0, 'score'] += self.apo_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['apo'] < 0, 'score'] -= self.apo_weight.value * self.other_indicators_weight.value
        
        # TRIX
        dataframe.loc[dataframe['trix'] > 0, 'score'] += self.trix_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['trix'] < 0, 'score'] -= self.trix_weight.value * self.other_indicators_weight.value
        
        # CMO
        dataframe.loc[dataframe['cmo'] < -30, 'score'] += self.cmo_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['cmo'] > 30, 'score'] -= self.cmo_weight.value * self.other_indicators_weight.value
        
        # AO (Awesome Oscillator)
        dataframe.loc[dataframe['ao'] > 0, 'score'] += self.ao_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['ao'] < 0, 'score'] -= self.ao_weight.value * self.other_indicators_weight.value
        
        # BOP (Balance of Power)
        dataframe.loc[dataframe['bop'] > 0, 'score'] += self.bop_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['bop'] < 0, 'score'] -= self.bop_weight.value * self.other_indicators_weight.value
        
        # EMV (Ease of Movement)
        dataframe.loc[dataframe['emv'] > 0, 'score'] += self.emv_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['emv'] < 0, 'score'] -= self.emv_weight.value * self.other_indicators_weight.value
        
        # Fisher Transform
        dataframe.loc[dataframe['fisher'] > 0, 'score'] += self.fisher_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['fisher'] < 0, 'score'] -= self.fisher_weight.value * self.other_indicators_weight.value
        
        # OBV
        dataframe.loc[dataframe['obv'] > dataframe['obv'].shift(1), 'score'] += self.obv_weight.value * self.other_indicators_weight.value
        dataframe.loc[dataframe['obv'] < dataframe['obv'].shift(1), 'score'] -= self.obv_weight.value * self.other_indicators_weight.value
        
        # 附加MA指标评分 (权重固定为1.0)
        # DEMA
        dataframe.loc[dataframe['close'] > dataframe['dema'], 'score'] += self.dema_weight.value * self.ma_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['dema'], 'score'] -= self.dema_weight.value * self.ma_indicators_weight.value
        
        # TEMA
        dataframe.loc[dataframe['close'] > dataframe['tema'], 'score'] += self.tema_weight.value * self.ma_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['tema'], 'score'] -= self.tema_weight.value * self.ma_indicators_weight.value
        
        # KAMA
        dataframe.loc[
            (dataframe['close'] > dataframe['ema20']) &
            (dataframe['close'] > dataframe['dema']) &
            (dataframe['close'] > dataframe['tema']) &
            (dataframe['close'] > dataframe['kama']),
            'score'
        ] += self.kama_weight.value * self.ma_indicators_weight.value
        
        # HMA
        dataframe.loc[dataframe['close'] > dataframe['hma'], 'score'] += self.hma_weight.value * self.ma_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['hma'], 'score'] -= self.hma_weight.value * self.ma_indicators_weight.value
        
        # 杂项指标评分 (权重固定为1.0)
        # LinReg
        dataframe.loc[dataframe['close'] > dataframe['linreg'], 'score'] += self.linreg_weight.value * self.misc_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['linreg'], 'score'] -= self.linreg_weight.value * self.misc_indicators_weight.value
        
        # TRIMA
        dataframe.loc[dataframe['close'] > dataframe['trima'], 'score'] += self.trima_weight.value * self.misc_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['trima'], 'score'] -= self.trima_weight.value * self.misc_indicators_weight.value
        
        # VWMA
        dataframe.loc[dataframe['close'] > dataframe['vwma'], 'score'] += self.vwma_weight.value * self.misc_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['vwma'], 'score'] -= self.vwma_weight.value * self.misc_indicators_weight.value
        
        # Wilder's
        dataframe.loc[dataframe['close'] > dataframe['wilders'], 'score'] += self.wilders_weight.value * self.misc_indicators_weight.value
        dataframe.loc[dataframe['close'] < dataframe['wilders'], 'score'] -= self.wilders_weight.value * self.misc_indicators_weight.value
        
        # AD Slope
        dataframe.loc[dataframe['ad_slope'] > 0, 'score'] += self.ad_slope_weight.value * self.misc_indicators_weight.value
        dataframe.loc[dataframe['ad_slope'] < 0, 'score'] -= self.ad_slope_weight.value * self.misc_indicators_weight.value
        
        # KAMA交叉信号
        dataframe.loc[qtpylib.crossed_above(dataframe['close'], dataframe['kama']), 'score'] += self.kama_weight.value * 1.5
        
        # KAMA斜率判断
        dataframe.loc[dataframe['kama_slope'] > 0, 'score'] += self.kama_weight.value * 0.5  # 上升趋势额外加分
        
        return dataframe
    
    def apply_synergy_bonuses(self, dataframe: DataFrame) -> DataFrame:
        """
        应用协同效应加分 (加分值可优化)
        """
        # 看涨MACD交叉 + RSI < 35 (加分可优化)
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal'])) &
            (dataframe['rsi'] < 35),
            'score'
        ] += self.synergy_macd_rsi_bonus.value
        
        # ADX > 21 & plusDI > minusDI (加分可优化)
        dataframe.loc[
            (dataframe['adx'] > 21) &
            (dataframe['plus_di'] > dataframe['minus_di']),
            'score'
        ] += self.synergy_adx_di_bonus.value
        
        # RSI < 30 & MFI < 30 (加分可优化)
        dataframe.loc[
            (dataframe['rsi'] < 30) &
            (dataframe['mfi'] < 30),
            'score'
        ] += self.synergy_rsi_mfi_bonus.value
        
        # 交易量上升 > 20% (加分可优化)
        dataframe.loc[dataframe['volume_ratio'] > 1.2, 'score'] += self.synergy_volume_bonus.value
        
        # 多个动量指标同时看涨 (加分可优化)
        dataframe.loc[
            (dataframe['slowk'] < self.stoch_oversold.value) &
            (dataframe['fastk'] < self.stoch_rsi_oversold.value) &
            (dataframe['cci'] < -100),
            'score'
        ] += self.synergy_momentum_bonus.value
        
        # 新增协同效应加分
        # 多个MA指标同时看涨
        dataframe.loc[
            (dataframe['close'] > dataframe['ema20']) &
            (dataframe['close'] > dataframe['dema']) &
            (dataframe['close'] > dataframe['tema']) &
            (dataframe['close'] > dataframe['kama']),
            'score'
        ] += self.ma_synergy_bonus.value
        
        # PSAR和布林带下轨共同支撑
        dataframe.loc[
            (dataframe['close'] > dataframe['psar']) &
            (dataframe['close'] < dataframe['bb_lowerband']),
            'score'
        ] += self.psar_bb_synergy_bonus.value
        
        # OBV上升 + AO上升
        dataframe.loc[
            (dataframe['obv'] > dataframe['obv'].shift(1)) &
            (dataframe['ao'] > 0),
            'score'
        ] += self.obv_ao_synergy_bonus.value
        
        # Fisher Transform + CMO组合信号
        dataframe.loc[
            (dataframe['fisher'] > 0) &
            (dataframe['cmo'] < -20),
            'score'
        ] += self.fisher_cmo_synergy_bonus.value
        
        return dataframe
    
    def apply_filters(self, dataframe: DataFrame) -> DataFrame:
        """
        应用交易量和ADX过滤器 (过滤系数可优化)
        """
        # 交易量过滤器
        dataframe.loc[dataframe['volume_ratio'] < self.low_volume_threshold.value, 'score'] *= 0.7
        
        # ADX过滤器
        dataframe.loc[dataframe['adx'] < 15, 'score'] *= self.adx_weak_filter.value
        dataframe.loc[(dataframe['adx'] > 21) & (dataframe['adx'] < 35), 'score'] *= self.adx_moderate_filter.value
        dataframe.loc[dataframe['adx'] > 35, 'score'] *= self.adx_strong_filter.value
        
        return dataframe
    
    def calculate_final_score(self, dataframe: DataFrame) -> DataFrame:
        """
        计算最终分数
        """
        # 计算基本指标信号
        dataframe = self.calculate_indicator_signals(dataframe)
        
        # 应用协同效应加分
        dataframe = self.apply_synergy_bonuses(dataframe)
        
        # 应用过滤器
        dataframe = self.apply_filters(dataframe)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于最终分数确定入场信号，同时考虑SuperTrend和BTC趋势进行过滤
        """
        pair = metadata['pair']
        dataframe = self.calculate_final_score(dataframe)
        base_entry_score = self.entry_score.value
        
        # 获取当前蜡烛图时间
        current_time = dataframe['date'].iloc[-1] if not dataframe.empty else None
        
        # 只在新的蜡烛周期或未检查过BTC趋势时进行BTC趋势检查
        if self.btc_trend_last_check != current_time:
            # 更新BTC趋势
            self.btc_trend = self.calculate_btc_trend()
            self.btc_trend_last_check = current_time
            logger.info(f"更新BTC趋势检查时间: {current_time}")
        
        # 如果BTC处于下跌趋势，不进行任何新开交易
        if self.btc_trend_filter.value and self.btc_trend == -1:
            logger.info(f"BTC处于下跌趋势，禁止所有新交易: {metadata['pair']}")
            dataframe['enter_long'] = 0
            # 清除潜在信号，避免后续first_signal逻辑误判
            dataframe['potential_signal'] = 0 
            # 如果设置了 exit_signal 列，也应在此处处理或确保其最终为0
            # dataframe['exit_signal'] = 0 # 确保没有退出信号干扰后续逻辑（如果存在）
            return dataframe
        
        # 根据BTC趋势调整入场分数阈值（当BTC上涨时可以使用正常阈值）
        adjusted_entry_score = base_entry_score # 当前版本BTC下跌直接禁止，此调整暂无作用
        strong_entry_threshold = adjusted_entry_score * self.strong_score_factor.value
        
        # 默认不入场
        dataframe['enter_long'] = 0
        
        # 添加临时列标记潜在买入信号
        dataframe['potential_signal'] = 0

        # 条件1: 评分从下方首次突破常规入场阈值
        score_crossed_normal_threshold = (dataframe['score'] > adjusted_entry_score) & \
                                         (dataframe['score'].shift(1) < adjusted_entry_score)
        
        # 条件2: 评分高于强力入场阈值，并且评分仍在上升
        score_is_strong_and_rising = (dataframe['score'] > strong_entry_threshold) & \
                                     (dataframe['score'] > dataframe['score'].shift(1))
        
        dataframe.loc[
            (score_crossed_normal_threshold | score_is_strong_and_rising) &
            (dataframe['volume'] > 0) &  # 确保有交易量
            (dataframe['supertrend_direction'] == 1) &  # SuperTrend必须看涨
            (dataframe['close'] > dataframe['kama']) &  # 价格必须高于KAMA
            (dataframe['kama_slope'].fillna(0) > 0),  # KAMA均线必须向上倾斜 (fillna(0)处理初始NaN)
            'potential_signal'
        ] = 1
        
        # 检测用于重置 first_signal 追踪器的 "趋势结束" 信号
        # 修改：当价格收盘于KAMA均线下方时，认为短期上升趋势可能结束
        if 'exit_signal' not in dataframe.columns: # 确保列存在
        dataframe['exit_signal'] = 0
        
        # 当价格收盘于KAMA均线下方时，标记为 exit_signal = 1，用于重置 first_signal
        # 这个信号不直接导致交易退出，而是影响下一轮的 first_signal 判断
        kama_crossover_exit_condition = (dataframe['close'] < dataframe['kama']) & (dataframe['close'].shift(1) >= dataframe['kama'].shift(1))
        # 或者更简单地，只要收盘在KAMA下方就认为趋势弱了
        price_below_kama_condition = dataframe['close'] < dataframe['kama']
        dataframe.loc[price_below_kama_condition, 'exit_signal'] = 1 
        
        # 计算最近趋势周期内的第一个买入信号
        # 初始化列用于跟踪从上次卖出信号后是否是第一个买入信号
        dataframe['first_signal'] = 0
        
        # 检查每个买入信号是否是趋势周期内的第一个
        # 这里需要一个回溯过程 (或者使用 self.signal_tracker)
        # 确保 self.signal_tracker 能正确处理不同交易对
        # 使用 .loc 访问 Series/DataFrame 以避免 SettingWithCopyWarning

        # 重置特定交易对的追踪器状态的条件应该是明确的 "退出事件"
        # 当前的 'exit_signal' 是基于 SuperTrend 反转的，这可以作为重置点
        # 如果有多个退出条件，它们都应该能重置 signal_tracker

        last_known_signal_state = self.signal_tracker.get(pair, False)

        for i in range(len(dataframe)):
            # 如果当前行存在导致追踪器重置的信号（例如明确的卖出信号）
            # 注意：这里的 'exit_signal' 是本函数内定义的，通常应该依赖于 populate_exit_trend 的结果
            # 或者一个更通用的"趋势结束"信号。
            # 为了演示，我们使用本函数内定义的 supertrend_exit_like_condition
            if i > 0 and dataframe['exit_signal'].iloc[i-1] == 1: # 使用 i-1 检查前一根K线的状态
                last_known_signal_state = False # 重置状态
                
            if dataframe['potential_signal'].iloc[i] == 1:
                if not last_known_signal_state:
                    dataframe.loc[dataframe.index[i], 'first_signal'] = 1
                    last_known_signal_state = True # 标记已产生第一个信号
            
        self.signal_tracker[pair] = last_known_signal_state # 保存当前交易对的最终信号状态
        
        # 只有当是趋势周期内的第一个买入信号时才真正入场
        dataframe.loc[dataframe['first_signal'] == 1, 'enter_long'] = 1
        
        # 记录入场信息
        if dataframe['enter_long'].iloc[-1] == 1:
            current_pair = metadata['pair']
            current_score = dataframe['score'].iloc[-1] if not dataframe.empty else 0
            
            btc_status = ""
            if self.btc_trend_filter.value and self.btc_trend is not None:
                btc_trend_str = "上涨" if self.btc_trend == 1 else "下跌"
                btc_status = f"BTC趋势: {btc_trend_str}, "
                
            logger.info(f"确认入场交易: {current_pair} - {btc_status}分数: {current_score:.2f}, 第一信号入场")
                
        return dataframe
    
    def calculate_btc_trend(self) -> int:
        """
        计算BTC趋势，仅在需要时调用
        返回值: 1 表示上涨趋势, -1 表示下跌趋势, None 表示无法确定
        """
        if not self.btc_trend_filter.value:
            return None
            
        # 获取BTC的dataframe
        btc_pair = 'BTC/USDT:USDT'
        btc_df = self.dp.get_pair_dataframe(btc_pair, self.timeframe)
        
        if btc_df.empty:
            logger.warning("无法获取BTC数据，跳过趋势计算")
            return None
            
        # 计算BTC的SuperTrend
        atr = ta.ATR(btc_df, timeperiod=int(self.supertrend_atr_period.value))
        hl2 = (btc_df['high'] + btc_df['low']) / 2
        
        # 上轨、下轨计算
        final_upperband = hl2 + (self.supertrend_atr_multiplier.value * atr)
        final_lowerband = hl2 - (self.supertrend_atr_multiplier.value * atr)
        
        # 初始化SuperTrend
        supertrend = pd.Series(0.0, index=btc_df.index)
        direction = pd.Series(1, index=btc_df.index)  # 初始方向为多头
        
        # 计算SuperTrend值和方向
        for i in range(1, len(btc_df.index)):
            if btc_df['close'].iloc[i] > final_upperband.iloc[i-1]:
                direction.iloc[i] = 1
            elif btc_df['close'].iloc[i] < final_lowerband.iloc[i-1]:
                direction.iloc[i] = -1
            else:
                direction.iloc[i] = direction.iloc[i-1]
                
                if direction.iloc[i] == 1 and final_lowerband.iloc[i] < final_lowerband.iloc[i-1]:
                    final_lowerband.iloc[i] = final_lowerband.iloc[i-1]
                if direction.iloc[i] == -1 and final_upperband.iloc[i] > final_upperband.iloc[i-1]:
                    final_upperband.iloc[i] = final_upperband.iloc[i-1]
            
            if direction.iloc[i] == 1:
                supertrend.iloc[i] = final_lowerband.iloc[i]
            else:
                supertrend.iloc[i] = final_upperband.iloc[i]
        
        # 返回最后一个方向值
        btc_trend = direction.iloc[-1] if not btc_df.empty else None
        
        if btc_trend is not None:
            btc_trend_str = "上涨" if btc_trend == 1 else "下跌"
            logger.info(f"BTC当前趋势: {btc_trend_str}")
            
        return btc_trend

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于最终分数确定出场信号 (阈值可优化)
        增加SuperTrend反转作为退出条件
        """
        # 初始化exit_long和exit_tag列
        dataframe['exit_long'] = 0
        dataframe['exit_tag'] = None
        
        # 1. 布林带上轨回落出场（获利保护） - 暂时移除，因其表现恶化
        # bb_exit_mask = (
        #     (dataframe['close'] > dataframe['bb_middleband']) &
        #     (dataframe['close'].shift(1) > dataframe['bb_upperband']) &
        #     (dataframe['close'] < dataframe['close'].shift(1)) &
        #     (dataframe['rsi'] > self.rsi_exit_overbought.value)
        # )
        # dataframe.loc[bb_exit_mask, 'exit_tag'] = '布林带回落'
        # dataframe.loc[bb_exit_mask, 'exit_long'] = 1
        
        # 2. SuperTrend确认反转退出条件 (已移除)
        # supertrend_flipped_to_bearish = (
        #     (dataframe['supertrend_direction'] == -1) &
        #     (dataframe['supertrend_direction'].shift(1) == 1)
        # )
        # price_closes_below_bearish_supertrend = dataframe['close'] < dataframe['supertrend']
        # 
        # supertrend_exit_mask_v2 = (
        #     supertrend_flipped_to_bearish & 
        #     price_closes_below_bearish_supertrend &
        #     (dataframe['exit_long'] == 0) # 仅当没有其他退出条件触发时应用
        # )
        # dataframe.loc[supertrend_exit_mask_v2, 'exit_tag'] = 'SuperTrend确认反转' 
        # dataframe.loc[supertrend_exit_mask_v2, 'exit_long'] = 1
        
        # 3. ATR动态止损保护 (修改 - 收紧止损)
        atr = ta.ATR(dataframe, timeperiod=int(self.atr_period.value))
        
        atr_exit_mask = (
            (dataframe['close'] < dataframe['close'].shift(1) - atr * self.atr_multiplier.value) & # 移除了 +0.3 的调整
            (dataframe['volume'] > 0) &
            (dataframe['exit_long'] == 0) # 仅当没有其他退出条件触发时应用
        )
        dataframe.loc[atr_exit_mask, 'exit_tag'] = 'ATR止损' # 更新标签，如果V2不再适用
        dataframe.loc[atr_exit_mask, 'exit_long'] = 1
        
        # 4. 关键支撑位破位出场 (已移除)
        # support_exit_mask_v2 = (
        #     (dataframe['close'] < dataframe['ema20']) &
        #     (dataframe['close'] < dataframe['ema13']) &
        #     (dataframe['close'] < dataframe['kama']) &  # 增加KAMA线条件
        #     (dataframe['plus_di'] < dataframe['minus_di']) &
        #     (dataframe['adx'] > (self.adx_exit_threshold.value + 3)) & # 提高ADX阈值
        #     (dataframe['exit_long'] == 0) # 仅当没有其他退出条件触发时应用
        # )
        # dataframe.loc[support_exit_mask_v2, 'exit_tag'] = '支撑位破位V2'
        # dataframe.loc[support_exit_mask_v2, 'exit_long'] = 1
        
        return dataframe

    @property
    def plot_config(self):
        """
        在FreqUI中自定义显示的指标
        """
        plot_config = {
            'main_plot': {
                # SuperTrend指标（主图显示）
                'supertrend': {'color': '#00FFFF'},  # 青色线条
                
                # 重要均线
                'ema13': {'color': '#FFEB3B'},   # 黄色
                'ema20': {'color': '#FF9800'},   # 橙色
                'ema34': {'color': '#F44336'},   # 红色
                
                # 布林带
                'bb_upperband': {'color': '#4CAF50', 'alpha': 0.4},  # 绿色半透明
                'bb_middleband': {'color': '#2196F3', 'alpha': 0.4}, # 蓝色半透明
                'bb_lowerband': {'color': '#F44336', 'alpha': 0.4},  # 红色半透明
                
                # PSAR
                'psar': {'color': '#E91E63', 'markersize': 2}, # 粉色点
            },
            'subplots': {
                # MACD指标子图
                "MACD": {
                    'macd': {'color': '#1976D2'},        # 蓝色
                    'macdsignal': {'color': '#FF9800'},  # 橙色
                    'macdhist': {'color': '#4CAF50', 'type': 'bar'}, # 绿色柱状图
                },
                # RSI和MFI指标子图
                "RSI/MFI": {
                    'rsi': {'color': '#2196F3'},  # 蓝色
                    'mfi': {'color': '#673AB7'},  # 紫色
                    'rsi_ob': {'color': '#f44336', 'value': self.rsi_overbought.value}, # 超买线
                    'rsi_os': {'color': '#4caf50', 'value': self.rsi_oversold.value},   # 超卖线
                },
                # ADX和DI指标子图
                "ADX/DI": {
                    'adx': {'color': '#FF5722'},       # 橙红色
                    'plus_di': {'color': '#4CAF50'},   # 绿色
                    'minus_di': {'color': '#F44336'},  # 红色
                    'adx_threshold': {'color': '#607D8B', 'value': self.adx_threshold.value} # 阈值线
                },
                # SuperTrend方向指标子图
                "SuperTrend": {
                    'supertrend_direction': {'color': '#4CAF50', 'type': 'bar'}, # 绿色柱状图
                },
                # 分数指标子图
                "Score": {
                    'score': {'color': '#2196F3'},                          # 蓝色
                    'entry_level': {'color': '#4CAF50', 'value': self.entry_score.value},   # 绿色入场阈值线
                }
            }
        }
        return plot_config