import json
import sys
from pathlib import Path

def process_hyperopt_results(file_path):
    """
    Processes a Freqtrade hyperopt result file and prints the best buy parameters as a JSON object.
    """
    path = Path(file_path)
    if not path.is_file():
        print(f"Error: File not found at {file_path}", file=sys.stderr)
        sys.exit(1)

    with open(path, 'r') as f:
        try:
            data = json.load(f)
        except json.JSONDecodeError:
            print(f"Error: Could not decode JSON from {file_path}", file=sys.stderr)
            sys.exit(1)

    results = data.get('results_per_epoch', [])
    if not results:
        print("No results per epoch found in the file.", file=sys.stderr)
        sys.exit(1)

    # Sort results by total profit to find the best one
    try:
        best_result = max(results, key=lambda x: x['total_profit'])
    except (KeyError, TypeError):
        print("Could not find 'total_profit' in results.", file=sys.stderr)
        sys.exit(1)

    best_buy_params = best_result.get('params', {}).get('buy', {})

    if not best_buy_params:
        print("No buy parameters found in the best result.", file=sys.stderr)
        # Output an empty JSON object so the flow doesn't break
        print(json.dumps({}))
        sys.exit(0)
    
    # Print the buy parameters as a JSON object to stdout
    print(json.dumps(best_buy_params, indent=4))

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python 2_process_hyperopt_results.py <path_to_hyperopt_file>", file=sys.stderr)
        sys.exit(1)
    
    file_path = sys.argv[1]
    process_hyperopt_results(file_path) 