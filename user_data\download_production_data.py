#!/usr/bin/env python3
"""
生产级数据下载脚本
- 获取昨天交易量前200名的交易对
- 剔除稳定币
- 剔除上市不足120天的交易对
- 下载2023年1月1日至今的数据
"""

import ccxt
import pandas as pd
import json
import os
from datetime import datetime, timedelta
import time

def get_predefined_futures_pairs():
    """获取预定义的期货交易对列表"""
    return [
        'BTC/USDT:USDT',
        'ETH/USDT:USDT',
        'SOL/USDT:USDT',
        'XRP/USDT:USDT',
        'DOGE/USDT:USDT',
        'ENA/USDT:USDT',
        'TRX/USDT:USDT',
        'BNB/USDT:USDT',
        'PEPE/USDT:USDT',
        'SUI/USDT:USDT',
        'BONK/USDT:USDT',
        'PENGU/USDT:USDT',
        'ADA/USDT:USDT',
        'HBAR/USDT:USDT',
        'UNI/USDT:USDT',
        'BCH/USDT:USDT',
        'LTC/USDT:USDT',
        'WIF/USDT:USDT',
        'LINK/USDT:USDT',
        'AVAX/USDT:USDT',
        'AAVE/USDT:USDT',
        'XLM/USDT:USDT',
        'FLOKI/USDT:USDT',
        'WLD/USDT:USDT',
        'ARB/USDT:USDT',
        'NEAR/USDT:USDT',
        'APT/USDT:USDT',
        'SEI/USDT:USDT',
        'TAO/USDT:USDT',
        'ONDO/USDT:USDT',
        'DOT/USDT:USDT',
        'PNUT/USDT:USDT',
        'SHIB/USDT:USDT',
        'OP/USDT:USDT',
        'CAKE/USDT:USDT',
        'NEIRO/USDT:USDT',
        'ETHFI/USDT:USDT',
        'FET/USDT:USDT',
        'ENS/USDT:USDT',
        'LDO/USDT:USDT',
        'FIL/USDT:USDT',
        'TIA/USDT:USDT',
        'MKR/USDT:USDT',
        'PENDLE/USDT:USDT',
        'RUNE/USDT:USDT',
        'ETC/USDT:USDT',
        'MOVE/USDT:USDT',
        'RAY/USDT:USDT',
        'TON/USDT:USDT',
        'JUP/USDT:USDT',
        'INJ/USDT:USDT',
        'OM/USDT:USDT',
        'EIGEN/USDT:USDT',
        'RENDER/USDT:USDT',
        'ALGO/USDT:USDT',
        'GALA/USDT:USDT',
        'ORDI/USDT:USDT',
        'POL/USDT:USDT',
        'XTZ/USDT:USDT',
        'ICP/USDT:USDT',
        'ATOM/USDT:USDT',
        'ARKM/USDT:USDT',
        'USUAL/USDT:USDT',
        'BOME/USDT:USDT',
        'PEOPLE/USDT:USDT',
        'VET/USDT:USDT',
        'TURBO/USDT:USDT',
        'IO/USDT:USDT',
        'SAGA/USDT:USDT',
        'AR/USDT:USDT',
        'SUSHI/USDT:USDT',
        'JTO/USDT:USDT',
        'ALT/USDT:USDT',
        'ZRO/USDT:USDT',
        'COMP/USDT:USDT',
        'STRK/USDT:USDT',
        'DYDX/USDT:USDT',
        'LISTA/USDT:USDT',
        'SAND/USDT:USDT',
        'GRT/USDT:USDT',
        'JASMY/USDT:USDT',
        'W/USDT:USDT',
        'OMNI/USDT:USDT',
        '1MBABYDOGE/USDT:USDT',
        'TRB/USDT:USDT',
        'ZK/USDT:USDT',
        'LPT/USDT:USDT',
        '1000SATS/USDT:USDT',
        'STX/USDT:USDT',
        'IOTA/USDT:USDT',
        'NEO/USDT:USDT',
        'BLUR/USDT:USDT',
        'THETA/USDT:USDT',
        'BEAMX/USDT:USDT',
        'RSR/USDT:USDT',
        'KAIA/USDT:USDT',
        'ACT/USDT:USDT',
        'XAI/USDT:USDT',
        'SUPER/USDT:USDT',
        'THE/USDT:USDT',
        'KAVA/USDT:USDT',
        'PERP/USDT:USDT',
        'NOT/USDT:USDT',
        'PYTH/USDT:USDT',
        'SSV/USDT:USDT',
        'REZ/USDT:USDT',
        'PORTAL/USDT:USDT',
        'MANTA/USDT:USDT',
        'ROSE/USDT:USDT',
        'MANA/USDT:USDT',
        'HFT/USDT:USDT',
        'CELO/USDT:USDT',
        'CKB/USDT:USDT',
        'AXS/USDT:USDT',
        'ZEN/USDT:USDT',
        'QNT/USDT:USDT',
        'IMX/USDT:USDT',
        'YFI/USDT:USDT',
        'GMT/USDT:USDT'
    ]

def setup_exchange_with_proxy():
    """设置带代理的交易所连接"""
    
    exchange = ccxt.binance({
        'proxies': {
            'http': 'http://127.0.0.1:1080',
            'https': 'http://127.0.0.1:1080'
        },
        'timeout': 30000,
        'enableRateLimit': True,
        'rateLimit': 1200,
        'options': {
            'defaultType': 'future'  # 改为future
        },
        'sandbox': False
    })
    
    return exchange

def get_top_volume_pairs(exchange, top_n=200):
    """获取昨天交易量前N名的期货交易对"""
    
    print(f"📊 获取交易量前{top_n}名的期货交易对...")
    
    try:
        # 先加载市场信息
        markets = exchange.load_markets()
        
        # 获取24小时ticker数据
        tickers = exchange.fetch_tickers()
        
        # 筛选期货USDT交易对并按交易量排序
        futures_volume_data = []
        
        for symbol, market in markets.items():
            if (market['type'] == 'future' and 
                market['quote'] == 'USDT' and 
                market['active'] and
                market['info'].get('contractType') == 'PERPETUAL' and
                symbol in tickers):
                
                ticker = tickers[symbol]
                if ticker['quoteVolume']:  # 确保有交易量数据
                    futures_volume_data.append({
                        'symbol': symbol,
                        'volume': float(ticker['quoteVolume'])
                    })
        
        # 按交易量排序
        futures_volume_data.sort(key=lambda x: x['volume'], reverse=True)
        
        # 取前N个
        top_futures_pairs = [item['symbol'] for item in futures_volume_data[:top_n]]
        
        print(f"✅ 找到{len(futures_volume_data)}个期货USDT交易对，选择前{len(top_futures_pairs)}个")
        
        if top_futures_pairs:
            print(f"📊 交易量前5名: {', '.join(top_futures_pairs[:5])}")
            return top_futures_pairs
        else:
            raise Exception("未找到有效的期货交易对")
        
    except Exception as e:
        print(f"❌ 获取交易对失败: {e}")
        # 如果自动获取失败，使用预定义的期货交易对列表
        print("🔄 使用预定义的期货交易对列表...")
        return get_predefined_futures_pairs()[:top_n]

def filter_stable_coins(pairs):
    """剔除稳定币"""
    
    print("🚫 剔除稳定币...")
    
    # 稳定币列表
    stable_coins = [
        'USDC', 'USDT', 'BUSD', 'DAI', 'TUSD', 'USDP', 'USDN', 'UST', 'FRAX',
        'LUSD', 'SUSD', 'GUSD', 'HUSD', 'USDK', 'USDD', 'USTC', 'USDJ',
        'FDUSD', 'PYUSD', 'USDE'
    ]
    
    filtered_pairs = []
    removed_pairs = []
    
    for pair in pairs:
        base_asset = pair.split('/')[0]
        if base_asset in stable_coins:
            removed_pairs.append(pair)
        else:
            filtered_pairs.append(pair)
    
    print(f"✅ 剔除{len(removed_pairs)}个稳定币交易对")
    if removed_pairs:
        print(f"   剔除的稳定币: {', '.join(removed_pairs[:10])}{'...' if len(removed_pairs) > 10 else ''}")
    
    return filtered_pairs

def filter_new_listings(exchange, pairs, min_days=120):
    """剔除上市不足指定天数的交易对"""
    
    print(f"📅 剔除上市不足{min_days}天的交易对...")
    
    try:
        # 获取交易所信息
        markets = exchange.load_markets()
        
        filtered_pairs = []
        removed_pairs = []
        cutoff_date = datetime.now() - timedelta(days=min_days)
        
        for pair in pairs:
            try:
                if pair in markets:
                    market_info = markets[pair]
                    
                    # 检查是否有上市时间信息
                    if 'info' in market_info and 'onboardDate' in market_info['info']:
                        listing_timestamp = int(market_info['info']['onboardDate'])
                        listing_date = datetime.fromtimestamp(listing_timestamp / 1000)
                        
                        if listing_date < cutoff_date:
                            filtered_pairs.append(pair)
                        else:
                            removed_pairs.append(pair)
                            print(f"   ⏰ {pair} 上市时间: {listing_date.strftime('%Y-%m-%d')} (太新)")
                    else:
                        # 如果没有上市时间信息，保守起见保留
                        filtered_pairs.append(pair)
                else:
                    removed_pairs.append(pair)
                    
            except Exception as e:
                print(f"   ⚠️ {pair} 检查失败: {e}")
                # 出错时保留交易对
                filtered_pairs.append(pair)
        
        print(f"✅ 剔除{len(removed_pairs)}个新上市交易对")
        print(f"📊 最终保留{len(filtered_pairs)}个交易对")
        
        return filtered_pairs
        
    except Exception as e:
        print(f"❌ 过滤新上市交易对失败: {e}")
        return pairs

def download_historical_data(exchange, symbol, timeframe='1h', start_date='2023-01-01'):
    """下载历史数据"""
    
    print(f"📈 下载 {symbol} {timeframe} 数据 (从{start_date})...")
    
    try:
        # 转换开始时间
        start_timestamp = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
        
        all_data = []
        current_timestamp = start_timestamp
        
        while current_timestamp < int(datetime.now().timestamp() * 1000):
            try:
                # 获取数据
                ohlcv = exchange.fetch_ohlcv(
                    symbol, 
                    timeframe, 
                    since=current_timestamp,
                    limit=1000
                )
                
                if not ohlcv:
                    break
                
                all_data.extend(ohlcv)
                
                # 更新时间戳
                current_timestamp = ohlcv[-1][0] + 1
                
                # 避免请求过快
                time.sleep(0.1)
                
                # 显示进度
                current_date = datetime.fromtimestamp(current_timestamp / 1000)
                print(f"   📅 已下载到: {current_date.strftime('%Y-%m-%d')}")
                
            except Exception as e:
                print(f"   ⚠️ 下载中断: {e}")
                break
        
        if all_data:
            # 去重并排序
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
            
            print(f"✅ {symbol} 下载成功: {len(df)} 条记录")
            print(f"   时间范围: {datetime.fromtimestamp(df['timestamp'].min()/1000).strftime('%Y-%m-%d')} 到 {datetime.fromtimestamp(df['timestamp'].max()/1000).strftime('%Y-%m-%d')}")
            
            return df.values.tolist()
        else:
            print(f"❌ {symbol} 没有数据")
            return None
            
    except Exception as e:
        print(f"❌ {symbol} 下载失败: {e}")
        return None

def save_data(data, symbol, timeframe):
    """保存数据为freqtrade格式"""
    
    if data is None:
        return False
    
    # 创建目录
    data_dir = "user_data/data/binance"
    os.makedirs(data_dir, exist_ok=True)
    
    # 保存文件
    filename = f"{symbol.replace('/', '_')}-{timeframe}.json"
    filepath = os.path.join(data_dir, filename)
    
    with open(filepath, 'w') as f:
        json.dump(data, f)
    
    print(f"💾 保存到: {filepath}")
    return True

def update_config_pairs(pairs):
    """更新config.json中的交易对列表"""
    
    print("📝 更新config.json配置文件...")
    
    try:
        config_path = "user_data/config.json"
        
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新交易对列表
        config['exchange']['pair_whitelist'] = pairs
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print(f"✅ 配置文件已更新，包含{len(pairs)}个期货交易对")
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")

def main():
    """主函数"""
    
    print("🚀 期货交易对数据下载工具")
    print("=" * 80)
    print("📋 任务:")
    print("   1. 获取交易量前200名的期货交易对")
    print("   2. 剔除稳定币")
    print("   3. 剔除上市不足120天的交易对")
    print("   4. 下载2023年1月1日至今的数据")
    print("   5. 更新config.json配置文件")
    print("=" * 80)
    
    try:
        # 设置交易所
        print("🔗 连接交易所...")
        exchange = setup_exchange_with_proxy()
        
        # 获取交易量前200名期货交易对
        top_pairs = get_top_volume_pairs(exchange, 200)
        if not top_pairs:
            print("❌ 无法获取交易对列表")
            return
        
        # 剔除稳定币
        filtered_pairs = filter_stable_coins(top_pairs)
        
        # 剔除新上市交易对
        final_pairs = filter_new_listings(exchange, filtered_pairs, 120)
        
        print(f"\n📊 最终选择的期货交易对数量: {len(final_pairs)}")
        print(f"📋 前10个交易对: {', '.join(final_pairs[:10])}")
        
        # 保存交易对列表
        with open('selected_pairs.json', 'w') as f:
            json.dump(final_pairs, f, indent=2)
        print(f"💾 交易对列表保存到: selected_pairs.json")
        
        # 更新配置文件
        update_config_pairs(final_pairs)
        
        # 下载数据
        print(f"\n📈 开始下载历史数据...")
        success_count = 0
        
        for i, symbol in enumerate(final_pairs, 1):
            print(f"\n[{i}/{len(final_pairs)}] 处理 {symbol}")
            
            data = download_historical_data(exchange, symbol, '1h', '2023-01-01')
            if save_data(data, symbol, '1h'):
                success_count += 1
            
            # 每10个交易对休息一下
            if i % 10 == 0:
                print("😴 休息5秒...")
                time.sleep(5)
        
        print(f"\n🎉 期货数据下载完成!")
        print(f"📊 成功下载: {success_count}/{len(final_pairs)} 个期货交易对")
        print(f"📝 配置文件已更新为期货交易对")
        
    except Exception as e:
        print(f"❌ 下载过程失败: {e}")

if __name__ == "__main__":
    main()
