# --- Do not remove these libs ---
import logging
import numpy as np
import pandas as pd
import talib.abstract as ta
import pandas_ta as pta
from pandas import DataFrame
from typing import Optional, Dict
from datetime import datetime

from freqtrade.strategy import (
    IStrategy,
    IntParameter,
    DecimalParameter,
    CategoricalParameter,
    informative
)
from freqtrade.persistence import Trade
from freqtrade.vendor.qtpylib.indicators import crossed_above

# --- Strategy Author ---
# @author: <PERSON> (Quant Expert)
# @version: 1.0

# --------------------------------
# --- Strategy Description ---
#
# HybridTrendMomentumV1
# A hybrid strategy combining multi-timeframe analysis, multi-indicator confirmation,
# and comprehensive dynamic risk management.
#
# Core Concepts:
# 1. Multi-Timeframe (MTF) Analysis:
#    - 4h Informative Timeframe: Establishes the macro trend using EMA(200) and KAMA slope.
#    - 1h Trading Timeframe: Identifies precise entry/exit points.
#
# 2. Multi-Indicator Confirmation:
#    - Trend: SuperTrend (1h) for primary signal generation.
#    - Momentum: RSI (1h) to confirm market strength.
#    - Volume/Money Flow: CMF (1h) to validate price action with volume.
#
# 3. Dynamic Risk Management:
#    - Dynamic Position Sizing (`custom_stake_amount`): Based on ATR and risk tolerance.
#    - Dynamic Initial Stoploss (`custom_stoploss`): ATR-based initial stop.
#    - Dynamic Trailing Stop (`custom_exit`): High-water mark trailing stop to protect profits.
#
# --------------------------------

logger = logging.getLogger(__name__)

class HybridTrendMomentumV1(IStrategy):
    """
    HybridTrendMomentumV1 Strategy
    """
    # --- Strategy Core Configuration ---
    INTERFACE_VERSION = 3
    timeframe = '1h'
    informative_timeframe = '4h'

    # --- Position Configuration ---
    can_short: bool = True
    max_open_trades: int = 5

    # --- Risk Management Configuration ---
    # Fallback stoploss (should not be hit if custom_stoploss works)
    stoploss = -0.99
    # Enable custom stoploss, exit, and stake amount methods
    use_custom_stoploss = True
    use_custom_exit = True
    use_exit_signal = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- Hyper-optimizable Parameters ---

    # General Risk Management
    risk_per_trade = DecimalParameter(0.01, 0.03, default=0.015, decimals=3, space='buy', optimize=True)

    # Informative TF (4h)
    kama_long_period = IntParameter(20, 40, default=30, space='buy', optimize=True)
    kama_long_slope_period = IntParameter(5, 15, default=8, space='buy', optimize=True)

    # Trading TF (1h) - Entry
    st_atr_period = IntParameter(10, 20, default=14, space='buy', optimize=True)
    st_multiplier = DecimalParameter(2.0, 4.0, default=3.0, decimals=1, space='buy', optimize=True)
    rsi_period = IntParameter(10, 20, default=14, space='buy', optimize=True)
    rsi_threshold = IntParameter(45, 55, default=50, space='buy', optimize=True)
    cmf_period = IntParameter(15, 30, default=20, space='buy', optimize=True)
    cmf_threshold = DecimalParameter(0.0, 0.1, default=0.05, decimals=2, space='buy', optimize=True)

    # Trading TF (1h) - Risk
    sl_atr_multiplier = DecimalParameter(1.5, 3.0, default=2.0, decimals=1, space='stoploss', optimize=True)
    tsl_profit_threshold = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space='trailing', optimize=True) # Profit in terms of initial risk (e.g., 1.5R)
    tsl_atr_multiplier = DecimalParameter(1.0, 2.5, default=1.5, decimals=1, space='trailing', optimize=True)

    # --- Plotting Configuration ---
    plot_config = {
        'main_plot': {
            f'ema_200_4h': {'color': 'white', 'type': 'line', 'linestyle': ':'},
            'st_trend': {'color': 'cyan', 'type': 'line'},
            'st_up': {'color': 'green', 'type': 'line'},
            'st_down': {'color': 'red', 'type': 'line'},
        },
        'subplots': {
            "Macro Trend": {
                f'kama_slope_4h': {'color': 'orange'},
            },
            "Momentum": {
                'rsi': {'color': 'purple'},
            },
            "Money Flow": {
                'cmf': {'color': 'blue'},
            }
        }
    }

    # --- Informative Timeframe (4h) ---
    @informative('4h')
    def populate_indicators_4h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populates indicators for the 4-hour informative timeframe.
        """
        dataframe[f'ema_200'] = ta.EMA(dataframe, timeperiod=200)
        kama = ta.KAMA(dataframe, timeperiod=self.kama_long_period.value)
        dataframe[f'kama_slope'] = ta.LINEARREG_SLOPE(kama, timeperiod=self.kama_long_slope_period.value)
        return dataframe

    # --- Trading Timeframe Indicators (1h) ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populates indicators for the 1-hour trading timeframe.
        """
        # SuperTrend
        st = pta.supertrend(
            high=dataframe['high'],
            low=dataframe['low'],
            close=dataframe['close'],
            length=self.st_atr_period.value,
            multiplier=self.st_multiplier.value
        )
        if st is not None and not st.empty:
            dataframe['st_trend'] = st[f'SUPERT_{self.st_atr_period.value}_{self.st_multiplier.value}']
            dataframe['st_direction'] = st[f'SUPERTd_{self.st_atr_period.value}_{self.st_multiplier.value}']
            dataframe['st_up'] = np.where(dataframe['st_direction'] == 1, dataframe['st_trend'], np.nan)
            dataframe['st_down'] = np.where(dataframe['st_direction'] == -1, dataframe['st_trend'], np.nan)

        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)

        # CMF
        dataframe['cmf'] = pta.cmf(
            high=dataframe['high'],
            low=dataframe['low'],
            close=dataframe['close'],
            volume=dataframe['volume'],
            length=self.cmf_period.value
        )

        # ATR for risk management
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        return dataframe

    # --- Entry Logic ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Defines the entry logic.
        """
        # --- Long Entry Conditions ---
        long_conditions = [
            # Macro Filter (4h)
            (dataframe[f'close_{self.informative_timeframe}'] > dataframe[f'ema_200_{self.informative_timeframe}']),
            (dataframe[f'kama_slope_{self.informative_timeframe}'] > 0),

            # Entry Trigger & Confirmation (1h)
            (dataframe['st_direction'] == 1), # SuperTrend is up
            (dataframe['rsi'] > self.rsi_threshold.value),
            (dataframe['cmf'] > self.cmf_threshold.value),
            (dataframe['volume'] > 0)
        ]
        # Combine all long conditions
        dataframe.loc[
            (pd.concat(long_conditions, axis=1)).all(axis=1),
            'enter_long'] = 1

        # --- Short Entry Conditions ---
        short_conditions = [
            # Macro Filter (4h)
            (dataframe[f'close_{self.informative_timeframe}'] < dataframe[f'ema_200_{self.informative_timeframe}']),
            (dataframe[f'kama_slope_{self.informative_timeframe}'] < 0),

            # Entry Trigger & Confirmation (1h)
            (dataframe['st_direction'] == -1), # SuperTrend is down
            (dataframe['rsi'] < (100 - self.rsi_threshold.value)),
            (dataframe['cmf'] < -self.cmf_threshold.value),
            (dataframe['volume'] > 0)
        ]
        # Combine all short conditions
        if self.can_short:
            dataframe.loc[
                (pd.concat(short_conditions, axis=1)).all(axis=1),
                'enter_short'] = 1

        return dataframe

    # --- Exit Logic ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Defines the exit logic based on SuperTrend reversal.
        """
        # Exit long if SuperTrend flips to down
        dataframe.loc[
            (dataframe['st_direction'] == -1) & (dataframe['st_direction'].shift(1) == 1),
            'exit_long'] = 1

        # Exit short if SuperTrend flips to up
        if self.can_short:
            dataframe.loc[
                (dataframe['st_direction'] == 1) & (dataframe['st_direction'].shift(1) == -1),
                'exit_short'] = 1

        return dataframe

    # --- Dynamic Position Sizing ---
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            entry_tag: Optional[str], **kwargs) -> float:
        """
        Calculates stake amount based on ATR and risk percentage.
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return proposed_stake
        
        last_candle = dataframe.iloc[-1]
        atr_value = last_candle.get('atr')

        if not atr_value:
            return proposed_stake
        
        # Calculate stoploss distance from current rate
        stoploss_distance = atr_value * self.sl_atr_multiplier.value
        if stoploss_distance == 0:
            return proposed_stake

        # Get total balance
        try:
            total_balance = self.wallets.get_total_stake_amount()
        except Exception:
            # Fallback for backtesting/plotting
            total_balance = self.config['dry_run_wallet']

        # Capital allocated per trade.
        # self.config['max_open_trades'] is used to ensure the correct value from config is used.
        capital_per_trade = total_balance / self.config['max_open_trades']

        # Calculate stake size
        risk_amount_per_trade = capital_per_trade * self.risk_per_trade.value
        position_size_in_asset = risk_amount_per_trade / stoploss_distance
        calculated_stake = position_size_in_asset * current_rate

        # Ensure stake is within min/max limits
        final_stake = max(min_stake, calculated_stake)
        final_stake = min(max_stake, final_stake)

        return final_stake

    # --- Dynamic Initial Stoploss ---
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Sets initial stoploss based on ATR at the time of entry.
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return -self.stoploss

        # Get the candle corresponding to the trade's open date
        trade_entry_candle = dataframe.loc[dataframe['date'] == trade.open_date_utc]
        if trade_entry_candle.empty:
            # If we can't find the exact candle, use the last available ATR
            atr_value = dataframe.iloc[-1].get('atr')
        else:
            atr_value = trade_entry_candle.iloc[0].get('atr')

        if not atr_value:
            return -self.stoploss

        # Calculate stop price
        sl_price = 0
        sl_atr = atr_value * self.sl_atr_multiplier.value
        if trade.is_short:
            sl_price = trade.open_rate + sl_atr
        else:
            sl_price = trade.open_rate - sl_atr
        
        # Save initial risk (distance to stoploss) for trailing stop calculation
        initial_risk = abs(trade.open_rate - sl_price)
        trade.set_custom_data('initial_risk', initial_risk)
        trade.set_custom_data('trailing_stop_activated', False)
        
        # Return stoploss as a percentage of current rate
        return (sl_price / current_rate) - 1.0

    # --- Dynamic Trailing Stoploss ---
    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[str]:
        """
        Implements a high-water mark trailing stop.
        This method should not return an exit signal, only update the stoploss.
        """
        initial_risk = trade.get_custom_data('initial_risk')
        if not initial_risk:
            return None # Not ready yet

        # --- Trailing Stop Activation ---
        # Activate trailing stop when profit is > X times initial risk
        trailing_stop_activated = trade.get_custom_data('trailing_stop_activated')
        if not trailing_stop_activated and current_profit > (initial_risk / trade.open_rate) * self.tsl_profit_threshold.value:
            logger.info(f"Activating trailing stop for {pair} as profit > {self.tsl_profit_threshold.value}R")
            trade.set_custom_data('trailing_stop_activated', True)
            trailing_stop_activated = True
        
        if not trailing_stop_activated:
            return None # Do not trail until activated

        # --- Update Stoploss using High-Water Mark principle ---
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        atr_value = dataframe.iloc[-1].get('atr')
        if not atr_value:
            return None

        new_stop_price = 0
        trail_dist = atr_value * self.tsl_atr_multiplier.value
        
        if trade.is_short:
            new_stop_price = current_rate + trail_dist
            # For short trades, the stoploss can only move down (become smaller)
            trade.stop_loss = min(trade.stop_loss, new_stop_price)
        else:
            new_stop_price = current_rate - trail_dist
            # For long trades, the stoploss can only move up (become larger)
            trade.stop_loss = max(trade.stop_loss, new_stop_price)
        
        # This method is for updating stoploss, not for exiting.
        # The bot will exit when the market hits the updated `trade.stop_loss`.
        return None 