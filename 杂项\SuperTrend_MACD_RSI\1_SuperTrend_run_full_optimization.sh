#!/bin/bash
#
# 该脚本提供了一个全自动的 Freqtrade 优化工作流：
# 1. 逐个对交易对列表运行参数优化，并将所有输出保存到一个原始日志文件。
# 2. 优化完成后，从原始日志中解析出每个交易对的最佳参数和轮数详情。
# 3. 将最佳参数实时更新回策略的 settings.json 文件中。
# 4. 将轮数详情保存到独立的报告文件中供分析。
# 该脚本不依赖任何外部Python脚本进行解析。

# --- VPS 绝对路径配置 ---
# Freqtrade 项目根目录 (必须是 docker-compose.yml 文件所在的目录)
PROJECT_ROOT="/root/ft"
# Freqtrade 策略名称
STRATEGY_NAME="SuperTrend_MACD_RSI"
# Freqtrade 主配置文件路径
CONFIG_FILE="${PROJECT_ROOT}/user_data/SuperTrend_config.json"
# 策略的参数设置文件路径 (此文件将被本脚本自动更新)
SETTINGS_FILE="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_MACD_RSI_PairOptimized_Settings.json"
# 优化结果目录
HYPEROPT_RESULTS_DIR="${PROJECT_ROOT}/user_data/hyperopt_results"
# 原始日志文件
RAW_LOG_FILE="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_hyperopt_raw_log.txt"
# 优化轮数报告文件
HYPEROPT_EPOCHS_FILE="${PROJECT_ROOT}/user_data/strategies/SuperTrend/SuperTrend_hyperopt_epochs.txt"

# --- 其他优化参数 ---
TIMERANGE="20240401-20250430"
TIMEFRAME="1h"
EPOCHS=200
JOBS=-1

# --- 函数定义 ---
# 定义一个函数，用于递归设置 user_data 目录的读取权限
set_permissions() {
    echo "正在更新项目文件权限以兼容Docker容器..."
    if [ -d "${PROJECT_ROOT}/user_data" ]; then
        # 此命令确保容器内的非root用户可以读取主机上的策略和配置文件
        # a+rX 确保文件可读，目录可访问
        # 脚本执行者需要有sudo免密权限，或者以root身份运行
        sudo chmod -R a+rX "${PROJECT_ROOT}/user_data"
        echo "权限更新完成。"
    else
        echo "警告: '${PROJECT_ROOT}/user_data' 目录不存在，跳过权限设置。"
    fi
}

# --- 脚本开始 ---

echo "启动全自动优化与更新工作流 (使用绝对路径)..."

# --- 步骤 0: 权限及环境准备 ---
set_permissions # 脚本开始时，设置初始权限

echo "正在准备优化环境..."
mkdir -p "$HYPEROPT_RESULTS_DIR"
rm -f ${HYPEROPT_RESULTS_DIR}/*
mkdir -p "$(dirname "$SETTINGS_FILE")"
touch "$SETTINGS_FILE"
touch "$HYPEROPT_EPOCHS_FILE"

# 初始化原始日志文件
echo "SuperTrend 全自动优化原始日志 - $(date)" > "$RAW_LOG_FILE"
echo "SuperTrend Hyperopt Epochs Summary - $(date)" > "$HYPEROPT_EPOCHS_FILE"
echo "" >> "$HYPEROPT_EPOCHS_FILE"

if ! command -v jq &> /dev/null; then
    echo "错误: jq 未安装。请先安装。" >&2
    exit 1
fi

# 从主配置文件中提取 pair_whitelist 列表
PAIRS=$(jq -r '.exchange.pair_whitelist[]' ${CONFIG_FILE})
if [ -z "$PAIRS" ]; then
    echo "错误：无法从 ${CONFIG_FILE} 中读取交易对列表。" >&2
    exit 1
fi

# --- 步骤 1: 运行优化并记录日志 ---
TOTAL_PAIRS=$(echo "$PAIRS" | wc -w)
CURRENT_PAIR_NUM=0

for PAIR in $PAIRS; do
    CURRENT_PAIR_NUM=$((CURRENT_PAIR_NUM + 1))
    echo "-------------------------------------------------------------------"
    echo "[${CURRENT_PAIR_NUM}/${TOTAL_PAIRS}] 开始优化交易对: $PAIR"
    echo "-------------------------------------------------------------------"
    
    rm -f ${PROJECT_ROOT}/user_data/hyperopt.lock
    
    # 将绝对路径转换为Docker所需的相对路径
    CONFIG_FILE_DOCKER=$(echo "$CONFIG_FILE" | sed "s|^${PROJECT_ROOT}/||")

    # 记录每个交易对的优化日志块
    echo "==================== START OPTIMIZATION FOR $PAIR ====================" >> "$RAW_LOG_FILE"
    cd "$PROJECT_ROOT" && docker compose run --rm freqtrade hyperopt \
        --strategy "$STRATEGY_NAME" \
        --strategy-path user_data/strategies/SuperTrend \
        --config "$CONFIG_FILE_DOCKER" \
        --timeframe "$TIMEFRAME" \
        --timerange "$TIMERANGE" \
        --hyperopt-loss MultiMetricHyperOptLoss \
        --spaces buy \
        -e "$EPOCHS" \
        -j "$JOBS" \
        --random-state 9319 \
        --min-trades 20 \
        --max-open-trades 1 \
        -p "$PAIR" >> "$RAW_LOG_FILE" 2>&1
    echo "==================== END OPTIMIZATION FOR $PAIR ====================" >> "$RAW_LOG_FILE"
    echo "" >> "$RAW_LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        echo "警告: 为 $PAIR 的优化任务返回了非零退出代码，跳过。"
        continue
    fi
done

echo "==================================================================="
echo "所有交易对的优化任务均已执行完毕。"
echo "现在开始从日志 '$RAW_LOG_FILE' 解析结果并更新设置文件..."
echo "==================================================================="

# --- 步骤 2: 从日志解析并更新JSON配置 ---

# 加载现有的JSON文件或创建一个空的JSON对象
JSON_CONTENT=$(cat "$SETTINGS_FILE")
if [ -z "$JSON_CONTENT" ]; then
    JSON_CONTENT="{}"
fi

for PAIR in $PAIRS; do
    echo "正在处理交易对: $PAIR"

    # Escape characters in PAIR that are special to sed (like '/')
    SAFE_PAIR=$(echo "$PAIR" | sed 's/[\/&]/\\&/g')
    
    # 从原始日志中提取对应交易对的完整日志块
    PAIR_LOG_BLOCK=$(sed -n "/==================== START OPTIMIZATION FOR $SAFE_PAIR ====================/,/==================== END OPTIMIZATION FOR $SAFE_PAIR ====================/p" "$RAW_LOG_FILE")

    if [ -z "$PAIR_LOG_BLOCK" ]; then
        echo "警告: 未找到 $PAIR 的日志块，跳过。"
        continue
    fi

    # --- 任务1: 解析最佳参数并更新JSON ---
    BEST_PARAMS_BLOCK=$(echo "$PAIR_LOG_BLOCK" | sed -n '/Best result:/,/^$/p')
    
    if [ -z "$BEST_PARAMS_BLOCK" ]; then
        echo "警告: 未找到 $PAIR 的最佳参数块，跳过参数更新。"
    else
        BUY_PARAMS=$(echo "$BEST_PARAMS_BLOCK" | awk '/buy_params = {/,/}/' | sed 's/buy_params = {//' | sed 's/}//' | tr -d '[:space:]' | tr -d '"')
        PAIR_JSON="{}"
        IFS=',' read -ra ADDR <<< "$BUY_PARAMS"
        for i in "${ADDR[@]}"; do
            KEY=$(echo "$i" | cut -d: -f1)
            VALUE=$(echo "$i" | cut -d: -f2)
            PAIR_JSON=$(echo "$PAIR_JSON" | jq --arg k "$KEY" --argjson v "$VALUE" '. + {($k): $v}')
        done
        JSON_CONTENT=$(echo "$JSON_CONTENT" | jq --arg pair_key "$PAIR" --argjson pair_value "$PAIR_JSON" '. + {($pair_key): $pair_value}')
    fi

    # --- 任务2: 提取并保存轮数摘要 ---
    EPOCHS_TABLE=$(echo "$PAIR_LOG_BLOCK" | awk '/Hyperopt results/,/└.*┘/')
    
    echo "================================== $PAIR ==================================" >> "$HYPEROPT_EPOCHS_FILE"
    echo "" >> "$HYPEROPT_EPOCHS_FILE"
    
    if [ -n "$EPOCHS_TABLE" ]; then
        echo "$EPOCHS_TABLE" >> "$HYPEROPT_EPOCHS_FILE"
    else
        echo "未找到 $PAIR 的轮数摘要表。" >> "$HYPEROPT_EPOCHS_FILE"
    fi
    echo "" >> "$HYPEROPT_EPOCHS_FILE"
done

# 将最终的JSON内容写回文件
echo "$JSON_CONTENT" | jq '.' > "$SETTINGS_FILE"

echo "==================================================================="
echo "所有交易对的优化与参数更新工作流已全部完成！"
echo "您的配置文件 '$SETTINGS_FILE' 已是最新状态。"
echo "优化轮数详情保存在 '$HYPEROPT_EPOCHS_FILE'。"
echo "完整的原始日志保存在 '$RAW_LOG_FILE'。"
echo "==================================================================="

# 在调用下一个脚本前，再次设置权限，以覆盖本次运行中新创建的文件
set_permissions

echo ""
echo "即将自动启动 SuperTrend 回测及分析流程..."
echo "==================================================================="
echo ""

# 定义回测脚本的绝对路径
BACKTEST_SCRIPT_PATH="${PROJECT_ROOT}/user_data/strategies/SuperTrend/2_SuperTrend_run_backtest_and_analysis.sh"

# 调用回测脚本
if [ -f "$BACKTEST_SCRIPT_PATH" ]; then
    bash "$BACKTEST_SCRIPT_PATH"
else
    echo "错误：回测脚本 '$BACKTEST_SCRIPT_PATH' 未找到。"
fi 