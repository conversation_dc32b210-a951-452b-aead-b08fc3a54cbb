{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 10, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 100, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "password": "", "ccxt_config": {"httpsProxy": "http://127.0.0.1:1080", "wsProxy": "http://127.0.01:1080"}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "XRP/USDT:USDT", "DOGE/USDT:USDT", "BNB/USDT:USDT", "ADA/USDT:USDT", "UNI/USDT:USDT", "LINK/USDT:USDT", "AVAX/USDT:USDT", "AAVE/USDT:USDT", "XLM/USDT:USDT", "NEAR/USDT:USDT", "DOT/USDT:USDT", "VET/USDT:USDT", "ATOM/USDT:USDT", "ALGO/USDT:USDT"], "pair_blacklist": [], "skip_pair_validation": true}, "dataformat_ohlcv": "json", "dataformat_trades": "jsongz", "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": false, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "CryptoLongShortStrategy_v1_2_Backtest", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}