import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from pandas import DataFrame
from datetime import datetime, timedelta
from typing import Dict, List
import logging
from functools import reduce

logger = logging.getLogger(__name__)


def hma(series, window):
    """
    赫尔移动平均线(Hull Moving Average)的自定义实现
    
    HMA = WMA(2*WMA(n/2) - WMA(n)), sqrt(n))
    其中WMA是加权移动平均线，n是周期
    """
    half_window = int(window / 2)
    sqrt_window = int(np.sqrt(window))
    
    # 计算WMA(n)
    wma1 = ta.WMA(series, timeperiod=window)
    
    # 计算WMA(n/2)
    wma2 = ta.WMA(series, timeperiod=half_window)
    
    # 计算2*WMA(n/2) - WMA(n)
    wma_diff = 2 * wma2 - wma1
    
    # 计算最终的HMA
    return ta.WMA(wma_diff, timeperiod=sqrt_window)


class GaussianTrendFilter(IStrategy):
    """
    平滑高斯趋势滤波器策略 (Smoothed Gaussian Trend Filter Strategy)
    
    该策略基于高斯滤波器和线性回归的组合，用于识别市场趋势并生成交易信号。
    核心指标通过高斯滤波器和线性回归双重平滑处理价格数据，以过滤市场噪音并捕捉主要趋势。
    
    特点:
    - 多空双向交易
    - 基于趋势方向和强度的动态仓位管理
    - ATR动态止损和追踪止盈
    - 成交量过滤，增强信号可靠性
    - 多时间周期确认，提高胜率
    
    参数可通过超参数优化进行调整。
    """

    # 策略版本，用于跟踪更新
    INTERFACE_VERSION = 3

    # 最小ROI表，定义不同持有时间的最小利润目标
    minimal_roi = {
        "0": 0.10,  # 0分钟后，如果利润达到10%，则卖出
        "30": 0.05,  # 30分钟后，如果利润达到5%，则卖出
        "60": 0.03,  # 60分钟后，如果利润达到3%，则卖出
        "120": 0.01  # 120分钟后，如果利润达到1%，则卖出
    }

    # 止损设置，初始值，将被ATR动态止损覆盖
    stoploss = -0.15

    # 时间框架设置
    timeframe = '5m'
    
    # 建议的时间框架列表，用于信息收集
    informative_timeframes = ['15m', '1h', '4h', '1d']

    # 回测设置
    startup_candle_count: int = 100  # 启动所需的K线数量
    process_only_new_candles = True  # 只处理新K线
    use_exit_signal = True  # 使用退出信号
    exit_profit_only = False  # 不仅在盈利时退出
    ignore_roi_if_entry_signal = False  # 不忽略ROI
    position_adjustment_enable = True  # 启用仓位调整
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # 杠杆设置
    leverage_optimization = True  # 是否优化杠杆
    max_leverage = 5.0  # 最大杠杆倍数

    # 可优化的参数
    # 高斯滤波器参数
    gaussian_length = IntParameter(10, 30, default=15, space="buy", optimize=True)
    poles = IntParameter(1, 4, default=3, space="buy", optimize=True)
    
    # 线性回归平滑参数
    linreg_length = IntParameter(15, 30, default=22, space="buy", optimize=True)
    flatten_multiplier = IntParameter(3, 12, default=7, space="buy", optimize=True)
    
    # 成交量过滤参数
    volume_filter_length = IntParameter(20, 50, default=33, space="buy", optimize=True)
    volume_threshold = IntParameter(50, 90, default=70, space="buy", optimize=True)
    
    # ATR参数
    atr_period = IntParameter(10, 30, default=14, space="sell", optimize=True)
    atr_multiplier_sl = DecimalParameter(1.0, 3.0, default=2.0, space="sell", optimize=True)
    atr_multiplier_tp = DecimalParameter(2.0, 5.0, default=3.0, space="sell", optimize=True)
    
    # 仓位管理参数
    risk_per_trade = DecimalParameter(0.01, 0.05, default=0.02, space="buy", optimize=True)
    leverage_factor = IntParameter(1, 5, default=3, space="buy", optimize=True)
    
    # 趋势强度过滤
    trend_strength_threshold = IntParameter(50, 90, default=70, space="buy", optimize=True)
    
    # 多时间周期确认
    use_multi_timeframe = CategoricalParameter([True, False], default=True, space="buy", optimize=True)
    min_timeframes_aligned = IntParameter(2, 4, default=3, space="buy", optimize=True)
    
    # 中期趋势信号
    mid_trnd_sig = CategoricalParameter([True, False], default=False, space="buy", optimize=True)

    def calc_gaussian_alpha(self, length, order):
        """
        计算高斯滤波器的alpha参数
        """
        freq = (2.0 * np.pi) / length
        factor_b = (1.0 - np.cos(freq)) / (np.power(1.414, (2.0 / order)) - 1.0)
        alpha_val = -factor_b + np.sqrt(factor_b * factor_b + 2.0 * factor_b)
        return alpha_val

    def gaussian_smooth(self, data, filter_level, alpha_coeff):
        """
        应用高斯平滑滤波器
        """
        result = np.zeros_like(data)
        one_minus_alpha = 1.0 - alpha_coeff
        alpha_squared = alpha_coeff * alpha_coeff
        alpha_cubed = alpha_coeff * alpha_coeff * alpha_coeff
        alpha4 = alpha_coeff * alpha_coeff * alpha_coeff * alpha_coeff
        oma_squared = one_minus_alpha * one_minus_alpha
        oma_cubed = oma_squared * one_minus_alpha
        oma4 = oma_cubed * one_minus_alpha
        
        # 初始化第一个值
        result[0] = data[0]
        
        # 根据滤波器级别应用不同的平滑算法
        if filter_level == 1:
            for i in range(1, len(data)):
                result[i] = alpha_coeff * data[i] + one_minus_alpha * result[i-1]
        elif filter_level == 2:
            result[1] = data[1]  # 初始化第二个值
            for i in range(2, len(data)):
                result[i] = alpha_squared * data[i] + 2.0 * one_minus_alpha * result[i-1] - oma_squared * result[i-2]
        elif filter_level == 3:
            result[1] = data[1]  # 初始化第二个值
            result[2] = data[2]  # 初始化第三个值
            for i in range(3, len(data)):
                result[i] = alpha_cubed * data[i] + 3.0 * one_minus_alpha * result[i-1] - 3.0 * oma_squared * result[i-2] + oma_cubed * result[i-3]
        elif filter_level == 4:
            result[1] = data[1]  # 初始化第二个值
            result[2] = data[2]  # 初始化第三个值
            result[3] = data[3]  # 初始化第四个值
            for i in range(4, len(data)):
                result[i] = alpha4 * data[i] + 4.0 * one_minus_alpha * result[i-1] - 6.0 * oma_squared * result[i-2] + 4.0 * oma_cubed * result[i-3] - oma4 * result[i-4]
        
        return result

    def pine_supertrend(self, df, src, factor, atr_period):
        """
        计算SuperTrend指标
        """
        high = df['high'].values
        low = df['low'].values
        
        # 计算ATR
        atr = ta.ATR(df, timeperiod=atr_period)
        
        # 计算上下轨
        upper_band = src + factor * atr
        lower_band = src - factor * atr
        
        # 初始化超级趋势和方向
        supertrend = np.zeros_like(src)
        direction = np.zeros_like(src)
        
        # 第一个值初始化
        supertrend[0] = src[0]
        direction[0] = 1
        
        # 计算超级趋势
        for i in range(1, len(src)):
            # 更新上下轨
            if lower_band[i] > lower_band[i-1] or src[i-1] < lower_band[i-1]:
                lower_band[i] = lower_band[i]
            else:
                lower_band[i] = lower_band[i-1]
                
            if upper_band[i] < upper_band[i-1] or src[i-1] > upper_band[i-1]:
                upper_band[i] = upper_band[i]
            else:
                upper_band[i] = upper_band[i-1]
            
            # 更新方向和超级趋势值
            if supertrend[i-1] == upper_band[i-1]:
                if src[i] > upper_band[i]:
                    direction[i] = -1
                else:
                    direction[i] = 1
            else:
                if src[i] < lower_band[i]:
                    direction[i] = 1
                else:
                    direction[i] = -1
            
            # 根据方向设置超级趋势值
            if direction[i] == -1:
                supertrend[i] = lower_band[i]
            else:
                supertrend[i] = upper_band[i]
        
        return supertrend, direction

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算策略所需的技术指标
        """
        # 获取参数值
        gaussian_len = self.gaussian_length.value
        poles_val = self.poles.value
        linreg_len = self.linreg_length.value
        flatten_mul = self.flatten_multiplier.value
        vol_len = self.volume_filter_length.value
        atr_len = self.atr_period.value
        
        # 计算高斯滤波器的alpha参数
        alpha_value = self.calc_gaussian_alpha(gaussian_len, poles_val)
        
        # 应用高斯平滑滤波器
        close_array = dataframe['close'].values
        gma_output = self.gaussian_smooth(close_array, poles_val, alpha_value)
        dataframe['gma_output'] = gma_output
        
        # 应用线性回归平滑
        dataframe['final'] = ta.LINEARREG(dataframe['gma_output'], timeperiod=linreg_len)
        
        # 计算SuperTrend
        st, direction = self.pine_supertrend(dataframe, dataframe['final'].values, 0.15, 21)
        dataframe['supertrend'] = st
        dataframe['st_direction'] = direction
        
        # 计算趋势方向
        dataframe['trend'] = np.where(dataframe['final'] > dataframe['final'].shift(1), 1, -1)
        dataframe['trend1'] = np.where(dataframe['final'] > dataframe['supertrend'], 1, -1)
        
        # 判断是否处于盘整状态
        dataframe['ranging'] = np.where((dataframe['trend'] * dataframe['trend1']) < 0, 1, 0)
        
        # 计算成交量强度
        dataframe['volume_ma'] = ta.SMA(dataframe['volume'], timeperiod=vol_len)
        dataframe['volume_min'] = dataframe['volume'].rolling(window=vol_len).min()
        dataframe['volume_max'] = dataframe['volume'].rolling(window=vol_len).max()
        dataframe['s_vol'] = hma(
            (dataframe['volume'] - dataframe['volume_min']) / 
            (dataframe['volume_max'] - dataframe['volume_min']),
            window=4
        )
        
        # 计算K线实体大小的平均值（用于趋势带宽度）
        dataframe['body_size'] = abs(dataframe['close'] - dataframe['open'])
        dataframe['avg_body_size'] = ta.SMA(dataframe['body_size'], timeperiod=100)
        
        # 计算ATR，用于止损和止盈
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=atr_len)
        
        # 计算趋势强度指标
        dataframe['trend_strength'] = abs(dataframe['final'] - dataframe['final'].shift(10)) / (dataframe['atr'] * 10) * 100
        dataframe['trend_strength'] = dataframe['trend_strength'].clip(0, 100)
        
        # 计算买入和卖出信号
        dataframe['buy_signal'] = np.where(
            (dataframe['trend1'] > 0) & 
            ((~self.mid_trnd_sig.value & (dataframe['trend1'].shift(1) < 0)) | 
             (self.mid_trnd_sig.value & (dataframe['ranging'] == 0) & (dataframe['ranging'].shift(1) == 1))),
            1, 0
        )
        
        dataframe['sell_signal'] = np.where(
            (dataframe['trend1'] < 0) & 
            ((~self.mid_trnd_sig.value & (dataframe['trend1'].shift(1) > 0)) | 
             (self.mid_trnd_sig.value & (dataframe['ranging'] == 0) & (dataframe['ranging'].shift(1) == 1))),
            1, 0
        )
        
        # 添加多时间周期分析
        if self.use_multi_timeframe.value:
            for tf in self.informative_timeframes:
                try:
                    informative = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=tf)
                    
                    # 确保数据足够
                    if len(informative) < 50:  # 至少需要50根K线
                        logger.warning(f"时间周期 {tf} 的数据不足，跳过该时间周期的分析")
                        dataframe[f'trend_state_{tf}'] = 0  # 默认为中性
                        continue
                    
                    # 计算该时间周期的高斯趋势
                    alpha_value_inf = self.calc_gaussian_alpha(gaussian_len, poles_val)
                    gma_output_inf = self.gaussian_smooth(informative['close'].values, poles_val, alpha_value_inf)
                    informative['gma_output'] = gma_output_inf
                    informative['final'] = ta.LINEARREG(informative['gma_output'], timeperiod=linreg_len)
                    
                    # 计算该时间周期的SuperTrend
                    st_inf, dir_inf = self.pine_supertrend(informative, informative['final'].values, 0.15, 21)
                    informative['supertrend'] = st_inf
                    
                    # 计算该时间周期的趋势方向
                    informative['trend1'] = np.where(informative['final'] > informative['supertrend'], 1, -1)
                    informative['trend'] = np.where(informative['final'] > informative['final'].shift(1), 1, -1)
                    informative['ranging'] = np.where((informative['trend'] * informative['trend1']) < 0, 1, 0)
                    
                    # 计算趋势状态
                    informative['trend_state'] = np.where(
                        informative['ranging'] == 1, 0,  # 盘整
                        np.where(informative['trend1'] > 0, 1, -1)  # 看涨或看跌
                    )
                    
                    # 合并到主数据框
                    dataframe[f'trend_state_{tf}'] = informative['trend_state']
                except Exception as e:
                    logger.error(f"处理时间周期 {tf} 时出错: {e}")
                    dataframe[f'trend_state_{tf}'] = 0  # 出错时默认为中性
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入和卖出信号
        """
        conditions = []
        
        # 多头入场条件
        long_conditions = []
        
        # 基本条件：高斯趋势滤波器发出买入信号
        long_conditions.append(dataframe['buy_signal'] > 0)
        
        # 成交量过滤：确保成交量足够
        long_conditions.append(dataframe['s_vol'] > self.volume_threshold.value / 100)
        
        # 趋势强度过滤：确保趋势足够强
        long_conditions.append(dataframe['trend_strength'] > self.trend_strength_threshold.value)
        
        # 多时间周期确认
        if self.use_multi_timeframe.value:
            # 为每个时间周期创建看涨标志
            for tf in self.informative_timeframes:
                dataframe[f'is_bullish_{tf}'] = dataframe[f'trend_state_{tf}'] > 0
            
            # 计算有多少个时间周期是看涨的
            dataframe['aligned_bullish_timeframes'] = 0
            for tf in self.informative_timeframes:
                dataframe['aligned_bullish_timeframes'] += dataframe[f'is_bullish_{tf}'].astype(int)
            
            # 要求至少指定数量的时间周期是看涨的
            long_conditions.append(dataframe['aligned_bullish_timeframes'] >= self.min_timeframes_aligned.value)
        
        # 组合所有多头条件
        if long_conditions:
            conditions.append(reduce(lambda x, y: x & y, long_conditions))
            
        # 空头入场条件
        short_conditions = []
        
        # 基本条件：高斯趋势滤波器发出卖出信号
        short_conditions.append(dataframe['sell_signal'] > 0)
        
        # 成交量过滤：确保成交量足够
        short_conditions.append(dataframe['s_vol'] > self.volume_threshold.value / 100)
        
        # 趋势强度过滤：确保趋势足够强
        short_conditions.append(dataframe['trend_strength'] > self.trend_strength_threshold.value)
        
        # 多时间周期确认
        if self.use_multi_timeframe.value:
            # 为每个时间周期创建看跌标志
            for tf in self.informative_timeframes:
                dataframe[f'is_bearish_{tf}'] = dataframe[f'trend_state_{tf}'] < 0
            
            # 计算有多少个时间周期是看跌的
            dataframe['aligned_bearish_timeframes'] = 0
            for tf in self.informative_timeframes:
                dataframe['aligned_bearish_timeframes'] += dataframe[f'is_bearish_{tf}'].astype(int)
            
            # 要求至少指定数量的时间周期是看跌的
            short_conditions.append(dataframe['aligned_bearish_timeframes'] >= self.min_timeframes_aligned.value)
        
        # 组合所有空头条件
        if short_conditions:
            conditions.append(reduce(lambda x, y: x & y, short_conditions))
            
        # 设置信号标签
        if conditions:
            dataframe.loc[
                reduce(lambda x, y: x | y, conditions),
                'enter_tag'
            ] = 'gaussian_trend_entry'
            
            # 设置多头信号
            dataframe.loc[
                conditions[0],
                'enter_long'
            ] = 1
            
            # 设置空头信号
            if len(conditions) > 1:
                dataframe.loc[
                    conditions[1],
                    'enter_short'
                ] = 1
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成退出信号
        """
        # 多头退出条件
        dataframe.loc[
            (dataframe['trend1'] < 0) &  # 趋势转为看跌
            (dataframe['trend1'].shift(1) > 0),  # 之前是看涨
            'exit_long'
        ] = 1
        
        # 空头退出条件
        dataframe.loc[
            (dataframe['trend1'] > 0) &  # 趋势转为看涨
            (dataframe['trend1'].shift(1) < 0),  # 之前是看跌
            'exit_short'
        ] = 1
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        基于ATR的动态止损
        """
        # 获取数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 如果数据不足，使用默认止损
        if len(dataframe) == 0:
            return self.stoploss
            
        # 获取最新的ATR值
        last_candle = dataframe.iloc[-1].squeeze()
        atr = last_candle['atr']
        
        # 计算动态止损
        if trade.is_short:
            # 空头止损
            dynamic_stoploss = self.atr_multiplier_sl.value * atr / current_rate
        else:
            # 多头止损
            dynamic_stoploss = -self.atr_multiplier_sl.value * atr / current_rate
            
        return dynamic_stoploss

    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        自定义退出逻辑，实现追踪止盈
        """
        # 获取数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 如果数据不足，不执行任何操作
        if len(dataframe) == 0:
            return None
            
        # 获取最新的ATR值
        try:
            last_candle = dataframe.iloc[-1].squeeze()
            atr = last_candle['atr']
            
            # 计算利润目标（以ATR的倍数表示）
            atr_profit_target = self.atr_multiplier_tp.value * atr / current_rate
            
            # 追踪止盈逻辑
            if trade.is_short:
                # 空头追踪止盈
                # 如果利润超过ATR的一定倍数，则设置追踪止盈
                if current_profit > atr_profit_target:
                    return 'hit_profit_short'
            else:
                # 多头追踪止盈
                # 如果利润超过ATR的一定倍数，则设置追踪止盈
                if current_profit > atr_profit_target:
                    return 'hit_profit_long'
        except Exception as e:
            logger.error(f"计算追踪止盈时出错: {e}")
                
        return None

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            leverage: float, entry_tag: str, side: str, **kwargs) -> float:
        """
        基于风险的仓位管理
        """
        # 获取数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 如果数据不足，使用默认仓位
        if len(dataframe) == 0:
            return proposed_stake
            
        # 获取最新的ATR值
        last_candle = dataframe.iloc[-1].squeeze()
        atr = last_candle['atr']
        
        # 计算止损点
        if side == 'long':
            stop_price = current_rate - self.atr_multiplier_sl.value * atr
            risk_per_unit = current_rate - stop_price
        else:
            stop_price = current_rate + self.atr_multiplier_sl.value * atr
            risk_per_unit = stop_price - current_rate
            
        # 计算账户总价值
        total_portfolio_value = self.wallets.get_total_stake_amount()
        
        # 计算风险金额
        risk_amount = total_portfolio_value * self.risk_per_trade.value
        
        # 计算仓位大小
        position_size = risk_amount / risk_per_unit
        stake_amount = position_size * current_rate / leverage
        
        # 确保仓位在允许范围内
        stake_amount = min(max(stake_amount, min_stake), max_stake)
        
        return stake_amount

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """
        动态杠杆管理
        """
        # 如果不优化杠杆，使用默认值
        if not self.leverage_optimization:
            return self.leverage_factor.value
            
        # 获取数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 如果数据不足，使用默认杠杆
        if len(dataframe) == 0:
            return self.leverage_factor.value
            
        # 获取最新的趋势强度
        last_candle = dataframe.iloc[-1].squeeze()
        trend_strength = last_candle['trend_strength']
        
        # 根据趋势强度调整杠杆
        # 趋势越强，使用越高的杠杆
        dynamic_leverage = 1 + (self.leverage_factor.value - 1) * (trend_strength / 100)
        
        # 确保杠杆在允许范围内
        dynamic_leverage = min(max(dynamic_leverage, 1), self.max_leverage)
        
        return dynamic_leverage

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: str,
                           side: str, **kwargs) -> bool:
        """
        交易前的最终确认，可以添加额外的检查
        """
        # 获取数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 如果数据不足，不允许交易
        if len(dataframe) == 0:
            return False
            
        # 获取最新K线
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 检查成交量是否异常
        if last_candle['volume'] < last_candle['volume_ma'] * 0.5:
            return False
            
        # 检查是否有足够的K线数据
        if len(dataframe) < self.startup_candle_count:
            return False
            
        return True

    def informative_pairs(self):
        """
        定义需要获取的信息时间框架
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, timeframe) for pair in pairs for timeframe in self.informative_timeframes]
        return informative_pairs 