"""
HeikinAshiAdvancedLongShort - 高级Heikin Ashi多空策略

作者: AI Assistant
版本: 1.0
创建时间: 2025-07-17

策略核心理念:
1. 基于Heikin Ashi K线的趋势识别和反转信号
2. 中枢理论：识别价格中枢区间，进行震荡交易和突破跟随
3. 支撑/压力位：动态计算关键价位，作为入场和止损参考
4. 转折点识别：小阴线（十字星）+ 两根无下影线大阳/阴线确认
5. 斐波那契回撤：用于精确入场点位和止盈目标
6. 多时间框架分析：结合不同周期确认趋势
7. 动态仓位管理：根据市场波动性调整仓位大小
8. 智能止损止盈：ATR动态止损 + 斐波那契目标止盈

技术特点:
- 无未来函数，所有信号基于历史数据
- 支持多空双向交易
- 动态风险管理
- 中枢内震荡 + 突破趋势双重策略
- 智能加仓机制
"""

import os
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

import logging
import numpy as np
import talib.abstract as ta
from datetime import datetime, timezone
from freqtrade.strategy import IntParameter, IStrategy, DecimalParameter, CategoricalParameter
from freqtrade.exchange import timeframe_to_seconds
from pandas import DataFrame, Series
import pandas as pd
from typing import Optional, Any

logger = logging.getLogger(__name__)

class HeikinAshiAdvancedLongShort(IStrategy):
    """
    高级Heikin Ashi多空策略

    核心逻辑:
    1. 使用Heikin Ashi K线识别趋势和反转
    2. 中枢理论识别震荡区间和突破点
    3. 转折点确认：小阴线 + 两根无下影线大阳/阴线
    4. 斐波那契回撤位精确入场
    5. 动态止损止盈和仓位管理
    """

    # === 策略基础配置 ===
    INTERFACE_VERSION: int = 4
    timeframe = "15m"
    can_short: bool = True
    startup_candle_count = 200
    process_only_new_candles = True

    # === 多时间框架配置 ===
    higher_tf = '1h'
    highest_tf = '4h'

    # === 基础止损配置 ===
    stoploss = -0.08
    use_custom_stoploss = True
    trailing_stop = False

    # === 订单类型配置 ===
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }

    # === 超参数优化参数 ===

    # Heikin Ashi 相关参数
    ha_trend_length = IntParameter(3, 8, default=5, space='buy', optimize=True)
    ha_reversal_strength = DecimalParameter(0.3, 0.8, default=0.5, decimals=2, space='buy', optimize=True)

    # 中枢识别参数
    pivot_lookback = IntParameter(10, 30, default=20, space='buy', optimize=True)
    pivot_deviation = DecimalParameter(0.01, 0.05, default=0.02, decimals=3, space='buy', optimize=True)
    consolidation_min_bars = IntParameter(8, 20, default=12, space='buy', optimize=True)

    # 转折点确认参数
    reversal_confirmation_bars = IntParameter(2, 4, default=2, space='buy', optimize=True)
    doji_body_ratio = DecimalParameter(0.1, 0.4, default=0.2, decimals=2, space='buy', optimize=True)
    strong_candle_ratio = DecimalParameter(0.6, 0.9, default=0.75, decimals=2, space='buy', optimize=True)

    # 斐波那契参数
    fib_lookback = IntParameter(20, 50, default=34, space='buy', optimize=True)
    fib_entry_level = CategoricalParameter([0.382, 0.5, 0.618], default=0.618, space='buy', optimize=True)
    fib_tp_level = CategoricalParameter([1.272, 1.414, 1.618], default=1.414, space='buy', optimize=True)

    # 风险管理参数
    atr_period = IntParameter(10, 25, default=14, space='buy', optimize=True)
    atr_multiplier = DecimalParameter(1.5, 3.5, default=2.5, decimals=1, space='buy', optimize=True)
    max_position_size = DecimalParameter(0.5, 1.5, default=1.0, decimals=1, space='buy', optimize=True)
    volatility_adjustment = DecimalParameter(0.5, 2.0, default=1.0, decimals=1, space='buy', optimize=True)

    # 趋势过滤参数
    trend_ema_fast = IntParameter(8, 21, default=13, space='buy', optimize=True)
    trend_ema_slow = IntParameter(34, 89, default=55, space='buy', optimize=True)
    trend_strength_threshold = DecimalParameter(0.5, 2.0, default=1.0, decimals=1, space='buy', optimize=True)

    # RSI 参数
    rsi_period = IntParameter(10, 25, default=14, space='buy', optimize=True)
    rsi_overbought = IntParameter(70, 85, default=75, space='buy', optimize=True)
    rsi_oversold = IntParameter(15, 30, default=25, space='buy', optimize=True)

    # MACD 参数
    macd_fast = IntParameter(8, 16, default=12, space='buy', optimize=True)
    macd_slow = IntParameter(20, 30, default=26, space='buy', optimize=True)
    macd_signal = IntParameter(7, 12, default=9, space='buy', optimize=True)

    # === 绘图配置 ===
    @property
    def plot_config(self):
        return {
            "main_plot": {
                "ha_open": {"color": "blue", "type": "line"},
                "ha_close": {"color": "red", "type": "line"},
                "pivot_high": {"color": "red", "type": "scatter"},
                "pivot_low": {"color": "green", "type": "scatter"},
                "support_level": {"color": "green", "type": "line", "linestyle": "--"},
                "resistance_level": {"color": "red", "type": "line", "linestyle": "--"},
                "fib_382": {"color": "orange", "type": "line", "linestyle": ":"},
                "fib_618": {"color": "purple", "type": "line", "linestyle": ":"},
                "ema_fast": {"color": "blue", "type": "line"},
                "ema_slow": {"color": "orange", "type": "line"},
            },
            "subplots": {
                "RSI": {
                    "rsi": {"color": "purple"},
                },
                "MACD": {
                    "macd": {"color": "blue"},
                    "macd_signal": {"color": "red"},
                    "macd_histogram": {"color": "gray", "type": "bar"},
                },
                "Signals": {
                    "doji_signal": {"color": "yellow", "type": "scatter"},
                    "strong_bull_signal": {"color": "green", "type": "scatter"},
                    "strong_bear_signal": {"color": "red", "type": "scatter"},
                    "consolidation_zone": {"color": "gray", "type": "bar"},
                }
            }
        }

    # === 信息对配置 ===
    def informative_pairs(self):
        # 只返回当前正在处理的交易对的高时间框架数据
        # 这样可以大大减少数据加载时间
        pairs = self.dp.current_whitelist()
        informative_pairs = []

        # 限制informative pairs数量以提高性能
        # 只加载1小时数据，4小时数据改为可选
        for pair in pairs:
            informative_pairs.append((pair, self.higher_tf))
            # 注释掉4小时数据以提高性能
            # informative_pairs.append((pair, self.highest_tf))

        return informative_pairs

    # === 自定义合并函数 ===
    def merge_informative_pair(self, dataframe: pd.DataFrame, informative: pd.DataFrame,
                              timeframe: str, timeframe_inf: str,
                              ffill: bool = True) -> pd.DataFrame:
        """合并informative dataframe到主要timeframe"""
        if not isinstance(dataframe.index, pd.DatetimeIndex):
            dataframe = dataframe.set_index('date', drop=False)
        if not isinstance(informative.index, pd.DatetimeIndex):
            informative = informative.set_index('date', drop=False)

        # 重命名列
        informative_cols = informative.columns
        informative_cols_names = [f"{col}_{timeframe_inf}" for col in informative_cols]
        informative.columns = informative_cols_names

        # 重采样并合并
        resampled = informative.reindex(dataframe.index, method='ffill')
        dataframe = pd.concat([dataframe, resampled], axis=1)

        if ffill:
            dataframe = dataframe.ffill()

        return dataframe

    # === 核心指标计算 ===
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算所有技术指标"""

        if not self.dp:
            return dataframe

        # === 多时间框架数据获取 ===
        try:
            # 只获取1小时数据以提高性能
            informative_1h = self.dp.get_pair_dataframe(metadata['pair'], self.higher_tf)
            if not informative_1h.empty:
                informative_1h = self.calculate_higher_tf_indicators(informative_1h)
                dataframe = self.merge_informative_pair(dataframe, informative_1h,
                                                       self.timeframe, self.higher_tf, ffill=True)

            # 暂时注释掉4小时数据以提高性能
            # informative_4h = self.dp.get_pair_dataframe(metadata['pair'], self.highest_tf)
            # if not informative_4h.empty:
            #     informative_4h = self.calculate_higher_tf_indicators(informative_4h)
            #     dataframe = self.merge_informative_pair(dataframe, informative_4h,
            #                                            self.timeframe, self.highest_tf, ffill=True)
        except Exception as e:
            logger.warning(f"无法获取高时间框架数据: {e}")
            # 如果无法获取高时间框架数据，创建默认列
            dataframe[f'trend_direction_{self.higher_tf}'] = 0

        # === Heikin Ashi K线计算 ===
        dataframe = self.calculate_heikin_ashi(dataframe)

        # === 基础技术指标 ===
        dataframe = self.calculate_basic_indicators(dataframe)

        # === 中枢和支撑压力位计算 ===
        dataframe = self.calculate_pivot_levels(dataframe)

        # === 斐波那契回撤位计算 ===
        dataframe = self.calculate_fibonacci_levels(dataframe)

        # === 转折点信号计算 ===
        dataframe = self.calculate_reversal_signals(dataframe)

        # === 趋势和中枢状态识别 ===
        dataframe = self.calculate_trend_and_consolidation(dataframe)

        return dataframe

    def calculate_higher_tf_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算高时间框架指标"""
        # EMA趋势
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=self.trend_ema_fast.value)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=self.trend_ema_slow.value)
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)

        # 趋势方向
        dataframe['trend_direction'] = 0
        dataframe.loc[
            (dataframe['close'] > dataframe['ema_fast']) &
            (dataframe['ema_fast'] > dataframe['ema_slow']) &
            (dataframe['ema_slow'] > dataframe['ema_200']), 'trend_direction'
        ] = 1  # 强上升趋势

        dataframe.loc[
            (dataframe['close'] < dataframe['ema_fast']) &
            (dataframe['ema_fast'] < dataframe['ema_slow']) &
            (dataframe['ema_slow'] < dataframe['ema_200']), 'trend_direction'
        ] = -1  # 强下降趋势

        return dataframe

    def calculate_heikin_ashi(self, dataframe: DataFrame) -> DataFrame:
        """计算Heikin Ashi K线"""
        # Heikin Ashi 收盘价
        dataframe['ha_close'] = (dataframe['open'] + dataframe['high'] +
                                dataframe['low'] + dataframe['close']) / 4

        # Heikin Ashi 开盘价
        dataframe['ha_open'] = np.nan
        dataframe.iloc[0, dataframe.columns.get_loc('ha_open')] = (
            dataframe.iloc[0]['open'] + dataframe.iloc[0]['close']) / 2

        for i in range(1, len(dataframe)):
            dataframe.iloc[i, dataframe.columns.get_loc('ha_open')] = (
                dataframe.iloc[i-1]['ha_open'] + dataframe.iloc[i-1]['ha_close']) / 2

        # Heikin Ashi 最高价和最低价
        dataframe['ha_high'] = dataframe[['ha_open', 'ha_close', 'high']].max(axis=1)
        dataframe['ha_low'] = dataframe[['ha_open', 'ha_close', 'low']].min(axis=1)

        # Heikin Ashi K线特征
        dataframe['ha_body'] = abs(dataframe['ha_close'] - dataframe['ha_open'])
        dataframe['ha_upper_shadow'] = dataframe['ha_high'] - dataframe[['ha_open', 'ha_close']].max(axis=1)
        dataframe['ha_lower_shadow'] = dataframe[['ha_open', 'ha_close']].min(axis=1) - dataframe['ha_low']
        dataframe['ha_total_range'] = dataframe['ha_high'] - dataframe['ha_low']

        # 避免除零错误
        dataframe['ha_total_range'] = dataframe['ha_total_range'].replace(0, np.nan)

        # Heikin Ashi K线类型识别
        dataframe['ha_is_bullish'] = dataframe['ha_close'] > dataframe['ha_open']
        dataframe['ha_is_bearish'] = dataframe['ha_close'] < dataframe['ha_open']

        # 强势K线（无下影线的大阳线/大阴线）
        dataframe['ha_strong_bull'] = (
            dataframe['ha_is_bullish'] &
            (dataframe['ha_lower_shadow'] <= dataframe['ha_total_range'] * 0.1) &
            (dataframe['ha_body'] >= dataframe['ha_total_range'] * self.strong_candle_ratio.value)
        )

        dataframe['ha_strong_bear'] = (
            dataframe['ha_is_bearish'] &
            (dataframe['ha_upper_shadow'] <= dataframe['ha_total_range'] * 0.1) &
            (dataframe['ha_body'] >= dataframe['ha_total_range'] * self.strong_candle_ratio.value)
        )

        # 十字星/小阴线识别
        dataframe['ha_doji'] = (
            dataframe['ha_body'] <= dataframe['ha_total_range'] * self.doji_body_ratio.value
        )

        # 连续强势K线计数
        dataframe['ha_bull_streak'] = 0
        dataframe['ha_bear_streak'] = 0

        for i in range(1, len(dataframe)):
            if dataframe.iloc[i]['ha_strong_bull']:
                dataframe.iloc[i, dataframe.columns.get_loc('ha_bull_streak')] = (
                    dataframe.iloc[i-1]['ha_bull_streak'] + 1 if dataframe.iloc[i-1]['ha_strong_bull'] else 1
                )

            if dataframe.iloc[i]['ha_strong_bear']:
                dataframe.iloc[i, dataframe.columns.get_loc('ha_bear_streak')] = (
                    dataframe.iloc[i-1]['ha_bear_streak'] + 1 if dataframe.iloc[i-1]['ha_strong_bear'] else 1
                )

        return dataframe

    def calculate_basic_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算基础技术指标"""

        # === EMA趋势指标 ===
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=self.trend_ema_fast.value)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=self.trend_ema_slow.value)
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)

        # === RSI ===
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)

        # === MACD ===
        macd = ta.MACD(dataframe,
                      fastperiod=self.macd_fast.value,
                      slowperiod=self.macd_slow.value,
                      signalperiod=self.macd_signal.value)
        dataframe['macd'] = macd['macd']
        dataframe['macd_signal'] = macd['macdsignal']
        dataframe['macd_histogram'] = macd['macdhist']

        # === ATR波动性指标 ===
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['atr_pct'] = dataframe['atr'] / dataframe['close'] * 100

        # === 成交量指标 ===
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']

        # === 布林带 ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0)
        dataframe['bb_upper'] = bollinger['upperband']
        dataframe['bb_middle'] = bollinger['middleband']
        dataframe['bb_lower'] = bollinger['lowerband']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']

        # === 趋势强度 ===
        dataframe['trend_strength'] = abs(dataframe['ema_fast'] - dataframe['ema_slow']) / dataframe['ema_slow'] * 100

        # === 价格位置 ===
        dataframe['price_position'] = (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower'])

        return dataframe

    def calculate_pivot_levels(self, dataframe: DataFrame) -> DataFrame:
        """计算中枢和支撑压力位"""

        # === 动态支撑压力位 ===
        # 使用滚动窗口计算高低点
        dataframe['pivot_high'] = dataframe['high'].rolling(
            window=self.pivot_lookback.value, center=True).max()
        dataframe['pivot_low'] = dataframe['low'].rolling(
            window=self.pivot_lookback.value, center=True).min()

        # 当前支撑压力位（向前填充）
        dataframe['resistance_level'] = dataframe['pivot_high'].shift(self.pivot_lookback.value // 2).ffill()
        dataframe['support_level'] = dataframe['pivot_low'].shift(self.pivot_lookback.value // 2).ffill()

        # === 中枢识别 ===
        # 计算价格波动范围
        dataframe['price_range'] = dataframe['high'] - dataframe['low']
        dataframe['range_pct'] = dataframe['price_range'] / dataframe['close'] * 100

        # 中枢区间识别（优化版本 - 使用向量化操作）
        window = self.consolidation_min_bars.value
        dataframe['rolling_high'] = dataframe['high'].rolling(window=window).max()
        dataframe['rolling_low'] = dataframe['low'].rolling(window=window).min()
        dataframe['rolling_range_pct'] = (dataframe['rolling_high'] - dataframe['rolling_low']) / dataframe['close'] * 100

        # 中枢识别：价格波动幅度小于阈值
        dataframe['consolidation_zone'] = (
            dataframe['rolling_range_pct'] <= self.pivot_deviation.value * 100
        ).astype(int)

        # === 中枢边界计算（优化版本） ===
        # 使用向量化操作计算中枢边界
        dataframe['consolidation_high'] = dataframe['rolling_high']
        dataframe['consolidation_low'] = dataframe['rolling_low']
        dataframe['consolidation_mid'] = (dataframe['consolidation_high'] + dataframe['consolidation_low']) / 2

        # 只在中枢区域保留边界值，其他地方设为NaN然后前向填充
        dataframe.loc[dataframe['consolidation_zone'] == 0, ['consolidation_high', 'consolidation_low', 'consolidation_mid']] = np.nan

        # 前向填充中枢边界
        dataframe['consolidation_high'] = dataframe['consolidation_high'].ffill()
        dataframe['consolidation_low'] = dataframe['consolidation_low'].ffill()
        dataframe['consolidation_mid'] = dataframe['consolidation_mid'].ffill()

        # === 突破信号 ===
        dataframe['breakout_up'] = (
            (dataframe['close'] > dataframe['consolidation_high']) &
            (dataframe['close'].shift(1) <= dataframe['consolidation_high'].shift(1))
        )

        dataframe['breakout_down'] = (
            (dataframe['close'] < dataframe['consolidation_low']) &
            (dataframe['close'].shift(1) >= dataframe['consolidation_low'].shift(1))
        )

        return dataframe

    def calculate_fibonacci_levels(self, dataframe: DataFrame) -> DataFrame:
        """计算斐波那契回撤位（简化版本以提高性能）"""

        # 使用向量化操作计算斐波那契位
        window = self.fib_lookback.value

        # 计算滚动高低点
        dataframe['fib_high'] = dataframe['high'].rolling(window=window).max()
        dataframe['fib_low'] = dataframe['low'].rolling(window=window).min()
        dataframe['fib_range'] = dataframe['fib_high'] - dataframe['fib_low']

        # 简化的斐波那契回撤位计算（假设上升趋势）
        dataframe['fib_382'] = dataframe['fib_high'] - dataframe['fib_range'] * 0.382
        dataframe['fib_500'] = dataframe['fib_high'] - dataframe['fib_range'] * 0.5
        dataframe['fib_618'] = dataframe['fib_high'] - dataframe['fib_range'] * 0.618
        dataframe['fib_786'] = dataframe['fib_high'] - dataframe['fib_range'] * 0.786

        # 扩展目标位
        dataframe['fib_ext_1272'] = dataframe['fib_high'] + dataframe['fib_range'] * 0.272
        dataframe['fib_ext_1414'] = dataframe['fib_high'] + dataframe['fib_range'] * 0.414
        dataframe['fib_ext_1618'] = dataframe['fib_high'] + dataframe['fib_range'] * 0.618

        return dataframe

    def calculate_reversal_signals(self, dataframe: DataFrame) -> DataFrame:
        """计算转折点信号（简化版本以提高性能）"""

        # 使用向量化操作计算信号
        dataframe['doji_signal'] = dataframe['ha_doji'].astype(int)
        dataframe['strong_bull_signal'] = dataframe['ha_strong_bull'].astype(int)
        dataframe['strong_bear_signal'] = dataframe['ha_strong_bear'].astype(int)

        # 简化的转折点信号
        # 做多信号：前面有十字星，当前有强势阳线，RSI不超买
        dataframe['reversal_long_signal'] = (
            (dataframe['ha_doji'].shift(1) | dataframe['ha_doji'].shift(2)) &
            dataframe['ha_strong_bull'] &
            (dataframe['rsi'] < 70)
        ).astype(int)

        # 做空信号：前面有十字星，当前有强势阴线，RSI不超卖
        dataframe['reversal_short_signal'] = (
            (dataframe['ha_doji'].shift(1) | dataframe['ha_doji'].shift(2)) &
            dataframe['ha_strong_bear'] &
            (dataframe['rsi'] > 30)
        ).astype(int)

        return dataframe

    def calculate_trend_and_consolidation(self, dataframe: DataFrame) -> DataFrame:
        """计算趋势和中枢状态"""

        # === 趋势状态识别 ===
        dataframe['trend_state'] = 0  # 0: 震荡, 1: 上升, -1: 下降

        # 上升趋势条件
        uptrend_condition = (
            (dataframe['ema_fast'] > dataframe['ema_slow']) &
            (dataframe['close'] > dataframe['ema_fast']) &
            (dataframe['trend_strength'] > self.trend_strength_threshold.value)
        )

        # 下降趋势条件
        downtrend_condition = (
            (dataframe['ema_fast'] < dataframe['ema_slow']) &
            (dataframe['close'] < dataframe['ema_fast']) &
            (dataframe['trend_strength'] > self.trend_strength_threshold.value)
        )

        dataframe.loc[uptrend_condition, 'trend_state'] = 1
        dataframe.loc[downtrend_condition, 'trend_state'] = -1

        # === 市场状态综合判断 ===
        dataframe['market_state'] = 'consolidation'  # 默认震荡

        # 趋势突破状态
        dataframe.loc[
            (dataframe['trend_state'] == 1) & dataframe['breakout_up'],
            'market_state'
        ] = 'uptrend_breakout'

        dataframe.loc[
            (dataframe['trend_state'] == -1) & dataframe['breakout_down'],
            'market_state'
        ] = 'downtrend_breakout'

        # 中枢震荡状态
        dataframe.loc[
            (dataframe['consolidation_zone'] == 1) & (dataframe['trend_state'] == 0),
            'market_state'
        ] = 'consolidation_range'

        # === 入场时机评分 ===
        dataframe['long_score'] = 0
        dataframe['short_score'] = 0

        # 做多评分
        long_conditions = [
            dataframe['reversal_long_signal'] == 1,  # 转折点信号
            dataframe['rsi'] < self.rsi_oversold.value + 10,  # RSI不过度超买
            dataframe['macd'] > dataframe['macd_signal'],  # MACD金叉
            dataframe['close'] > dataframe['ema_fast'],  # 价格在快线上方
            dataframe['volume_ratio'] > 1.2,  # 成交量放大
        ]

        # 做空评分
        short_conditions = [
            dataframe['reversal_short_signal'] == 1,  # 转折点信号
            dataframe['rsi'] > self.rsi_overbought.value - 10,  # RSI不过度超卖
            dataframe['macd'] < dataframe['macd_signal'],  # MACD死叉
            dataframe['close'] < dataframe['ema_fast'],  # 价格在快线下方
            dataframe['volume_ratio'] > 1.2,  # 成交量放大
        ]

        # 计算评分
        for i, condition in enumerate(long_conditions):
            dataframe.loc[condition, 'long_score'] += 1

        for i, condition in enumerate(short_conditions):
            dataframe.loc[condition, 'short_score'] += 1

        return dataframe

    # === 入场逻辑 ===
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义入场条件"""

        # === 做多入场条件 ===

        # 1. 转折点做多信号
        reversal_long_conditions = (
            (dataframe['reversal_long_signal'] == 1) &
            (dataframe['long_score'] >= 3) &  # 评分足够高
            (dataframe['rsi'] < self.rsi_overbought.value) &  # 避免超买
            (dataframe['close'] > dataframe['support_level']) &  # 价格在支撑位上方
            (dataframe['atr_pct'] < 5.0)  # 避免过度波动
        )

        # 2. 中枢震荡做多（低吸）
        consolidation_long_conditions = (
            (dataframe['market_state'] == 'consolidation_range') &
            (dataframe['close'] <= dataframe['consolidation_low'] * 1.01) &  # 接近中枢下沿
            (dataframe['rsi'] < 40) &  # RSI偏低
            (dataframe['ha_strong_bull']) &  # 出现强势阳线
            (dataframe['volume_ratio'] > 1.1)  # 成交量放大
        )

        # 3. 突破做多（趋势跟随）
        breakout_long_conditions = (
            (dataframe['market_state'] == 'uptrend_breakout') &
            (dataframe['close'] > dataframe['consolidation_high']) &  # 突破中枢上沿
            (dataframe['volume_ratio'] > 1.5) &  # 成交量显著放大
            (dataframe['macd'] > dataframe['macd_signal']) &  # MACD金叉
            (dataframe['rsi'] > 50) &  # RSI中性偏强
            (dataframe['rsi'] < 80)  # 避免极度超买
        )

        # 4. 斐波那契回撤做多
        fib_level_name = f'fib_{int(self.fib_entry_level.value * 1000)}'
        if self.fib_entry_level.value == 0.382:
            fib_level_name = 'fib_382'
        elif self.fib_entry_level.value == 0.5:
            fib_level_name = 'fib_500'
        elif self.fib_entry_level.value == 0.618:
            fib_level_name = 'fib_618'

        fib_long_conditions = (
            (dataframe['close'] <= dataframe[fib_level_name]) &
            (dataframe['close'] > dataframe['fib_786']) &  # 不要在深度回撤处入场
            (dataframe['ha_strong_bull']) &  # 强势反弹信号
            (dataframe['trend_state'] == 1) &  # 主趋势向上
            (dataframe['rsi'] < 60)
        )

        # 合并做多条件
        dataframe.loc[reversal_long_conditions, ['enter_long', 'enter_tag']] = (1, 'reversal_long')
        dataframe.loc[consolidation_long_conditions, ['enter_long', 'enter_tag']] = (1, 'consolidation_long')
        dataframe.loc[breakout_long_conditions, ['enter_long', 'enter_tag']] = (1, 'breakout_long')
        dataframe.loc[fib_long_conditions, ['enter_long', 'enter_tag']] = (1, 'fib_long')

        # === 做空入场条件 ===

        # 1. 转折点做空信号
        reversal_short_conditions = (
            (dataframe['reversal_short_signal'] == 1) &
            (dataframe['short_score'] >= 3) &  # 评分足够高
            (dataframe['rsi'] > self.rsi_oversold.value) &  # 避免超卖
            (dataframe['close'] < dataframe['resistance_level']) &  # 价格在压力位下方
            (dataframe['atr_pct'] < 5.0)  # 避免过度波动
        )

        # 2. 中枢震荡做空（高抛）
        consolidation_short_conditions = (
            (dataframe['market_state'] == 'consolidation_range') &
            (dataframe['close'] >= dataframe['consolidation_high'] * 0.99) &  # 接近中枢上沿
            (dataframe['rsi'] > 60) &  # RSI偏高
            (dataframe['ha_strong_bear']) &  # 出现强势阴线
            (dataframe['volume_ratio'] > 1.1)  # 成交量放大
        )

        # 3. 突破做空（趋势跟随）
        breakout_short_conditions = (
            (dataframe['market_state'] == 'downtrend_breakout') &
            (dataframe['close'] < dataframe['consolidation_low']) &  # 突破中枢下沿
            (dataframe['volume_ratio'] > 1.5) &  # 成交量显著放大
            (dataframe['macd'] < dataframe['macd_signal']) &  # MACD死叉
            (dataframe['rsi'] < 50) &  # RSI中性偏弱
            (dataframe['rsi'] > 20)  # 避免极度超卖
        )

        # 4. 斐波那契回撤做空
        fib_short_conditions = (
            (dataframe['close'] >= dataframe[fib_level_name]) &
            (dataframe['close'] < dataframe['fib_786']) &  # 不要在深度回撤处入场
            (dataframe['ha_strong_bear']) &  # 强势下跌信号
            (dataframe['trend_state'] == -1) &  # 主趋势向下
            (dataframe['rsi'] > 40)
        )

        # 合并做空条件
        dataframe.loc[reversal_short_conditions, ['enter_short', 'enter_tag']] = (1, 'reversal_short')
        dataframe.loc[consolidation_short_conditions, ['enter_short', 'enter_tag']] = (1, 'consolidation_short')
        dataframe.loc[breakout_short_conditions, ['enter_short', 'enter_tag']] = (1, 'breakout_short')
        dataframe.loc[fib_short_conditions, ['enter_short', 'enter_tag']] = (1, 'fib_short')

        return dataframe

    # === 出场逻辑 ===
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义出场条件"""

        # === 做多出场条件 ===

        # 1. 趋势反转出场
        long_trend_reversal = (
            (dataframe['trend_state'] == -1) &  # 趋势转为下降
            (dataframe['ha_strong_bear']) &  # 出现强势阴线
            (dataframe['rsi'] < 40)  # RSI确认弱势
        )

        # 2. 技术指标背离出场
        long_divergence_exit = (
            (dataframe['rsi'] > self.rsi_overbought.value) &  # RSI超买
            (dataframe['macd'] < dataframe['macd_signal']) &  # MACD死叉
            (dataframe['close'] < dataframe['ema_fast'])  # 价格跌破快线
        )

        # 3. 中枢上沿阻力出场
        long_resistance_exit = (
            (dataframe['close'] >= dataframe['resistance_level'] * 0.99) &  # 接近阻力位
            (dataframe['volume_ratio'] < 0.8) &  # 成交量萎缩
            (dataframe['ha_doji'] | dataframe['ha_strong_bear'])  # 出现反转信号
        )

        # 合并做多出场条件
        dataframe.loc[long_trend_reversal, ['exit_long', 'exit_tag']] = (1, 'trend_reversal')
        dataframe.loc[long_divergence_exit, ['exit_long', 'exit_tag']] = (1, 'divergence_exit')
        dataframe.loc[long_resistance_exit, ['exit_long', 'exit_tag']] = (1, 'resistance_exit')

        # === 做空出场条件 ===

        # 1. 趋势反转出场
        short_trend_reversal = (
            (dataframe['trend_state'] == 1) &  # 趋势转为上升
            (dataframe['ha_strong_bull']) &  # 出现强势阳线
            (dataframe['rsi'] > 60)  # RSI确认强势
        )

        # 2. 技术指标背离出场
        short_divergence_exit = (
            (dataframe['rsi'] < self.rsi_oversold.value) &  # RSI超卖
            (dataframe['macd'] > dataframe['macd_signal']) &  # MACD金叉
            (dataframe['close'] > dataframe['ema_fast'])  # 价格突破快线
        )

        # 3. 中枢下沿支撑出场
        short_support_exit = (
            (dataframe['close'] <= dataframe['support_level'] * 1.01) &  # 接近支撑位
            (dataframe['volume_ratio'] < 0.8) &  # 成交量萎缩
            (dataframe['ha_doji'] | dataframe['ha_strong_bull'])  # 出现反转信号
        )

        # 合并做空出场条件
        dataframe.loc[short_trend_reversal, ['exit_short', 'exit_tag']] = (1, 'trend_reversal')
        dataframe.loc[short_divergence_exit, ['exit_short', 'exit_tag']] = (1, 'divergence_exit')
        dataframe.loc[short_support_exit, ['exit_short', 'exit_tag']] = (1, 'support_exit')

        return dataframe

    # === 仓位管理 ===
    def custom_position_size(self, pair: str, current_time: datetime, **kwargs) -> float:
        """动态仓位管理"""
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) < 20:
                return self.max_position_size.value * 0.5

            last_candle = dataframe.iloc[-1]

            # 基础仓位
            base_position = self.max_position_size.value

            # 根据波动性调整仓位
            volatility_factor = min(2.0, max(0.3, 1.0 / (last_candle['atr_pct'] / 2.0)))

            # 根据信号强度调整仓位
            signal_strength = max(last_candle['long_score'], last_candle['short_score']) / 5.0

            # 根据市场状态调整仓位
            market_state_multiplier = 1.0
            if last_candle['market_state'] == 'uptrend_breakout' or last_candle['market_state'] == 'downtrend_breakout':
                market_state_multiplier = 1.2  # 突破时增加仓位
            elif last_candle['market_state'] == 'consolidation_range':
                market_state_multiplier = 0.8  # 震荡时减少仓位

            # 计算最终仓位
            final_position = (base_position * volatility_factor * signal_strength *
                            market_state_multiplier * self.volatility_adjustment.value)

            return max(0.1, min(1.5, final_position))

        except Exception as e:
            logger.warning(f"仓位计算错误: {e}")
            return self.max_position_size.value * 0.5

    # === 自定义止损 ===
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """动态止损管理"""
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if dataframe.empty:
                return self.stoploss

            # 获取开仓时的数据
            open_candle_df = dataframe.loc[dataframe['date'] < trade.open_date_utc]
            if open_candle_df.empty:
                return self.stoploss

            open_candle = open_candle_df.iloc[-1]
            current_candle = dataframe.iloc[-1]

            # ATR动态止损
            atr_value = open_candle['atr']
            atr_stop_distance = atr_value * self.atr_multiplier.value

            if trade.is_short:
                # 做空止损
                if current_profit > 0.02:  # 盈利2%后启用追踪止损
                    trailing_stop = trade.open_rate * (1 - current_profit * 0.5)
                    return (trailing_stop / current_rate) - 1
                else:
                    # 基于阻力位的止损
                    resistance_stop = current_candle['resistance_level']
                    atr_stop = trade.open_rate + atr_stop_distance
                    stop_price = min(resistance_stop, atr_stop)
                    return (stop_price / current_rate) - 1
            else:
                # 做多止损
                if current_profit > 0.02:  # 盈利2%后启用追踪止损
                    trailing_stop = trade.open_rate * (1 + current_profit * 0.5)
                    return (trailing_stop / current_rate) - 1
                else:
                    # 基于支撑位的止损
                    support_stop = current_candle['support_level']
                    atr_stop = trade.open_rate - atr_stop_distance
                    stop_price = max(support_stop, atr_stop)
                    return (stop_price / current_rate) - 1

        except Exception as e:
            logger.warning(f"止损计算错误: {e}")
            return self.stoploss

    # === 自定义出场 ===
    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime,
                   current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        """自定义出场逻辑"""
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if dataframe.empty:
                return None

            current_candle = dataframe.iloc[-1]

            # 斐波那契目标止盈
            fib_tp_level_name = 'fib_ext_1414'  # 默认值
            if self.fib_tp_level.value == 1.272:
                fib_tp_level_name = 'fib_ext_1272'
            elif self.fib_tp_level.value == 1.414:
                fib_tp_level_name = 'fib_ext_1414'
            elif self.fib_tp_level.value == 1.618:
                fib_tp_level_name = 'fib_ext_1618'

            if trade.is_short:
                if current_profit > 0.01:  # 至少1%盈利
                    # 检查是否达到斐波那契扩展目标
                    if (current_rate <= current_candle[fib_tp_level_name] and
                        current_candle['rsi'] < 30):
                        return 'fib_target_short'

                    # 强势反弹信号
                    if (current_candle['ha_strong_bull'] and
                        current_candle['volume_ratio'] > 1.5):
                        return 'reversal_exit_short'
            else:
                if current_profit > 0.01:  # 至少1%盈利
                    # 检查是否达到斐波那契扩展目标
                    if (current_rate >= current_candle[fib_tp_level_name] and
                        current_candle['rsi'] > 70):
                        return 'fib_target_long'

                    # 强势下跌信号
                    if (current_candle['ha_strong_bear'] and
                        current_candle['volume_ratio'] > 1.5):
                        return 'reversal_exit_long'

            # 时间止损（持仓超过一定时间且亏损）
            trade_duration = (current_time.replace(tzinfo=timezone.utc) - trade.open_date_utc).total_seconds() / 3600
            if trade_duration > 24 and current_profit < -0.01:  # 持仓超过24小时且亏损超过1%
                return 'time_exit'

            return None

        except Exception as e:
            logger.warning(f"自定义出场错误: {e}")
            return None