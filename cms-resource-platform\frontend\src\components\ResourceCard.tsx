'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Card, Tag, Avatar, Space, Button, Tooltip, message } from 'antd'
import { 
  HeartOutlined, 
  HeartFilled,
  DownloadOutlined, 
  EyeOutlined,
  StarOutlined,
  UserOutlined,
  CrownOutlined,
  LockOutlined
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import { useAuthStore } from '@/store/authStore'
import { resourceAPI } from '@/lib/api'
import { useMutation, useQueryClient } from 'react-query'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

interface ResourceCardProps {
  resource: any
  showAuthor?: boolean
  showStats?: boolean
}

const ResourceCard: React.FC<ResourceCardProps> = ({ 
  resource, 
  showAuthor = true, 
  showStats = true 
}) => {
  const { user, isAuthenticated } = useAuthStore()
  const queryClient = useQueryClient()
  const [isFavorited, setIsFavorited] = useState(false)

  // 收藏/取消收藏
  const favoriteMutation = useMutation(
    (action: 'add' | 'remove') => {
      if (action === 'add') {
        return resourceAPI.favoriteResource(resource.id)
      } else {
        return resourceAPI.unfavoriteResource(resource.id)
      }
    },
    {
      onSuccess: (_, action) => {
        setIsFavorited(action === 'add')
        message.success(action === 'add' ? '收藏成功' : '取消收藏成功')
        queryClient.invalidateQueries(['resources'])
      },
      onError: () => {
        message.error('操作失败，请重试')
      }
    }
  )

  // 下载资源
  const downloadMutation = useMutation(
    () => resourceAPI.downloadResource(resource.id),
    {
      onSuccess: (response) => {
        const { download_url } = response.data
        window.open(download_url, '_blank')
        message.success('下载开始')
        queryClient.invalidateQueries(['resources'])
      },
      onError: (error: any) => {
        const errorMessage = error.response?.data?.error || '下载失败'
        message.error(errorMessage)
      }
    }
  )

  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!isAuthenticated) {
      message.warning('请先登录')
      return
    }
    
    favoriteMutation.mutate(isFavorited ? 'remove' : 'add')
  }

  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!isAuthenticated) {
      message.warning('请先登录')
      return
    }
    
    downloadMutation.mutate()
  }

  const canDownload = resource.can_download
  const isVIPRequired = resource.required_vip
  const pointsRequired = resource.required_points

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <Card
        hoverable
        className="h-full overflow-hidden group"
        cover={
          <div className="relative aspect-video overflow-hidden">
            <Image
              src={resource.cover_image || '/placeholder-image.jpg'}
              alt={resource.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-110"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
            
            {/* 覆盖层 */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Space>
                  <Button
                    type="primary"
                    icon={<EyeOutlined />}
                    size="large"
                    className="shadow-lg"
                  >
                    查看详情
                  </Button>
                </Space>
              </div>
            </div>

            {/* 标签 */}
            <div className="absolute top-2 left-2 space-y-1">
              {resource.is_featured && (
                <Tag color="gold" icon={<StarOutlined />}>
                  推荐
                </Tag>
              )}
              {isVIPRequired && (
                <Tag color="purple" icon={<CrownOutlined />}>
                  VIP
                </Tag>
              )}
              {pointsRequired > 0 && (
                <Tag color="blue">
                  {pointsRequired} 积分
                </Tag>
              )}
              {resource.is_premium && (
                <Tag color="orange" icon={<LockOutlined />}>
                  付费
                </Tag>
              )}
            </div>

            {/* 收藏按钮 */}
            <div className="absolute top-2 right-2">
              <Button
                type="text"
                icon={isFavorited ? <HeartFilled /> : <HeartOutlined />}
                onClick={handleFavorite}
                loading={favoriteMutation.isLoading}
                className={`text-white hover:text-red-500 ${
                  isFavorited ? 'text-red-500' : ''
                }`}
              />
            </div>
          </div>
        }
        actions={[
          <Tooltip key="download" title={canDownload ? '下载资源' : '无下载权限'}>
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
              loading={downloadMutation.isLoading}
              disabled={!canDownload}
            />
          </Tooltip>,
          <Space key="stats" size="small">
            <EyeOutlined />
            <span>{resource.view_count}</span>
          </Space>,
          <Space key="downloads" size="small">
            <DownloadOutlined />
            <span>{resource.download_count}</span>
          </Space>,
        ]}
      >
        <Link href={`/resources/${resource.id}`}>
          <Card.Meta
            title={
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 hover:text-primary-600 transition-colors">
                  {resource.title}
                </h3>
                
                {/* 分类和标签 */}
                <div className="flex flex-wrap gap-1">
                  {resource.category && (
                    <Tag color="blue" className="text-xs">
                      {resource.category.name}
                    </Tag>
                  )}
                  {resource.tags?.slice(0, 2).map((tag: any) => (
                    <Tag key={tag.id} className="text-xs">
                      {tag.name}
                    </Tag>
                  ))}
                </div>
              </div>
            }
            description={
              <div className="space-y-3">
                {/* 描述 */}
                <p className="text-gray-600 text-sm line-clamp-2">
                  {resource.description}
                </p>

                {/* 作者信息 */}
                {showAuthor && resource.author && (
                  <div className="flex items-center justify-between">
                    <Space size="small">
                      <Avatar
                        src={resource.author.avatar}
                        icon={<UserOutlined />}
                        size="small"
                      />
                      <span className="text-gray-500 text-sm">
                        {resource.author.display_name}
                      </span>
                    </Space>
                    
                    <span className="text-gray-400 text-xs">
                      {dayjs(resource.created_at).fromNow()}
                    </span>
                  </div>
                )}

                {/* 评分 */}
                {showStats && resource.rating_count > 0 && (
                  <div className="flex items-center space-x-1">
                    <StarOutlined className="text-yellow-500" />
                    <span className="text-sm font-medium">
                      {resource.rating_score}
                    </span>
                    <span className="text-gray-400 text-xs">
                      ({resource.rating_count}人评价)
                    </span>
                  </div>
                )}

                {/* 文件信息 */}
                {resource.file_size_mb && (
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{resource.file_type?.toUpperCase()}</span>
                    <span>{resource.file_size_mb}MB</span>
                  </div>
                )}
              </div>
            }
          />
        </Link>
      </Card>
    </motion.div>
  )
}

export default ResourceCard
