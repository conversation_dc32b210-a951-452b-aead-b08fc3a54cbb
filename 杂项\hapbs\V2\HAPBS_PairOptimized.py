# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy (HAPBS) - Pair Optimized
#
#   作者: Gemini & User
#   版本: PairOptimized
#
#   策略理念:
#   - 本策略是用于实盘或最终回测的"生产"版本。
#   - 它从一个 JSON 配置文件 (HAPBS_PairOptimized_Settings.json) 中读取每个交易对【专属】的
#     全套参数，包括入场、止损和追踪止损。
#   - 它通过自定义函数 (custom_stoploss) 来精确模拟 Freqtrade 的内置风控功能，
#     从而实现了按币对配置出场参数这一强大功能。
# --------------------------------

class HAPBS_PairOptimized(IStrategy):

    # 将 custom_info 定义为类属性，以确保所有实例共享同一份配置
    custom_info: dict = {}

    def __init__(self, config: dict):
        super().__init__(config)
        # 仅在类属性为空时加载一次配置，避免重复加载
        if not HAPBS_PairOptimized.custom_info:
            self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                # 加载到类属性中
                HAPBS_PairOptimized.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it to function.")
            HAPBS_PairOptimized.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}. Strategy may not function correctly.")
            HAPBS_PairOptimized.custom_info = {}

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 风险控制: 基于币对配置的动态系统 ---
    # use_custom_stoploss 必须为 True，以激活下方的 custom_stoploss 函数。
    # custom_stoploss 函数将从 JSON 文件中读取每个币对的专属止损和追踪止损参数。
    stoploss = -0.99
    use_custom_stoploss = True
    
    # minimal_roi 表在此处定义为全局设置。
    # 它将与 custom_stoploss 中的追踪止损并行工作。
    minimal_roi = {
        "0": 100
    }

    # --- 重要提示: 关于 JSON 配置文件 ---
    # HAPBS_PairOptimized_Settings.json 文件现在应包含由 HAPBS_Final.py 优化的
    # 【全套】参数，包括: "stoploss", "trailing_stop_positive", "trailing_stop_positive_offset"
    
    # --- 图表配置 ---
    plot_config = {
        'main_plot': {
            'ema_short_long': {'color': 'blue', 'linestyle': '-'},
            'ema_long_long': {'color': 'cyan', 'linestyle': '-'},
            'ema_short_short': {'color': 'red', 'linestyle': '--'},
            'ema_long_short': {'color': 'magenta', 'linestyle': '--'},
            'ema_200': {'color': 'black', 'linestyle': ':'},
        },
        'subplots': {
            "ADX": {
                'adx': {'color': 'green'},
            },
        },
    }

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            return dataframe

        pair_settings = HAPBS_PairOptimized.custom_info[pair]

        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        
        dataframe['ha_strong_bear'] = (
            (dataframe['ha_close'] < dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_high'])
        )
        
        dataframe['ha_strong_bull'] = (
            (dataframe['ha_close'] > dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_low'])
        )

        # 添加十字星K线定义
        body_size = abs(dataframe['ha_close'] - dataframe['ha_open'])
        candle_range = dataframe['ha_high'] - dataframe['ha_low']
        dataframe['ha_doji'] = (body_size < candle_range * 0.03) # 收紧定义

        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_long'])
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_long'])
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_short'])
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_short'])
        
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            return dataframe

        pair_settings = HAPBS_PairOptimized.custom_info[pair]

        # --- 信号1：直接突破入场 ---
        long_breakout_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long'])) &
            (dataframe['ha_strong_bull']) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_breakout_conditions, ['enter_long', 'enter_tag']] = (1, 'long_breakout')

        # --- 信号1：直接破位入场 ---
        short_breakdown_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short'])) &
            (dataframe['ha_strong_bear']) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_breakdown_conditions, ['enter_short', 'enter_tag']] = (1, 'short_breakdown')

        # --- 信号2：突破后跟进入场 ---
        recent_cross_up = qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long']).shift(1).rolling(3).sum() > 0
        
        long_followup_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (dataframe['ha_strong_bull']) &
            (dataframe['ha_close'] > dataframe['ema_short_long']) &
            (recent_cross_up) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_followup_conditions, ['enter_long', 'enter_tag']] = (1, 'long_followup')

        # --- 信号2：破位后跟进入场 ---
        recent_cross_down = qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short']).shift(1).rolling(3).sum() > 0

        short_followup_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (dataframe['ha_strong_bear']) &
            (dataframe['ha_close'] < dataframe['ema_short_short']) &
            (recent_cross_down) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_followup_conditions, ['enter_short', 'enter_tag']] = (1, 'short_followup')

        # --- 信号3：十字星后跟进入场 ---
        recent_doji = dataframe['ha_doji'].shift(1).rolling(5).sum() > 0

        long_doji_followup_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (dataframe['ha_strong_bull']) &
            (dataframe['ha_close'] > dataframe['ema_short_long']) &
            (recent_doji) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_doji_followup_conditions, ['enter_long', 'enter_tag']] = (1, 'long_doji_followup')

        short_doji_followup_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (dataframe['ha_strong_bear']) &
            (dataframe['ha_close'] < dataframe['ema_short_short']) &
            (recent_doji) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_doji_followup_conditions, ['enter_short', 'enter_tag']] = (1, 'short_doji_followup')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        必须实现此方法才能使用 Freqtrade 的 ROI 功能。
        由于我们完全依赖 custom_stoploss 函数来处理出场逻辑，
        此函数仅返回一个带有空 exit 信号的数据框以满足框架要求。
        """
        dataframe.loc[:, 'exit_long'] = 0
        dataframe.loc[:, 'exit_short'] = 0
        return dataframe

    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        建议 2: 实现基于风险回报率(RR)的止盈
        利用配置文件中的 'exit_rr_ratio' 实现止盈.
        """
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            return None 

        pair_settings = HAPBS_PairOptimized.custom_info[pair]
        rr_ratio = pair_settings.get('exit_rr_ratio')

        if rr_ratio is None or current_profit <= 0:
            return None 

        try:
            # 1. 重新计算初始止损价格，以获取初始风险
            initial_stop_price = self.custom_stoploss(
                pair=pair, trade=trade, current_time=current_time,
                current_rate=current_rate, current_profit=current_profit, **kwargs
            )
            
            if initial_stop_price is None:
                return None

            # 2. 计算风险距离和盈利目标距离
            stop_loss_dist = abs(trade.open_rate - initial_stop_price)
            take_profit_dist = stop_loss_dist * rr_ratio
            
            # 3. 检查当前价格移动距离是否达到目标
            current_dist = abs(current_rate - trade.open_rate)

            if current_dist >= take_profit_dist:
                return f'rr_take_profit ({rr_ratio}:1)'

        except Exception as e:
            logger.warning(f"Error in custom_exit for pair {pair}: {e}")

        return None

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        为每个交易对实现定制化的初始止损和追踪止损。
        该函数会从 JSON 配置文件中读取每个交易对的 "stoploss", "trailing_stop_positive",
        和 "trailing_stop_positive_offset" 参数，并手动模拟 Freqtrade 的行为。
        """
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            # 如果未找到配置，返回一个安全的、不会被触发的值
            return -self.stoploss 

        pair_settings = HAPBS_PairOptimized.custom_info[pair]

        # 1. 获取特定币对的初始止损百分比 (例如 -0.1)
        initial_stop_pct = pair_settings.get('stoploss')
        if initial_stop_pct is None:
            return -self.stoploss
        
        # 计算初始止损的绝对价格
        initial_stop_price = trade.open_rate * (1 + initial_stop_pct)

        # 2. 获取追踪止损参数
        tsp = pair_settings.get('trailing_stop_positive')
        tspo = pair_settings.get('trailing_stop_positive_offset')

        # 如果没有追踪止损参数，或者利润未达到激活点，则只使用初始止损
        if tsp is None or tspo is None or current_profit < tsp:
            return initial_stop_price

        # 3. 计算追踪止损的绝对价格
        if trade.is_short:
            # 对于空头交易，追踪止损位在当前价格之上
            new_trailing_stop_price = current_rate * (1 + tspo)
            # 止损只应向下移动（取更小的值）
            # 我们比较的值是：初始止损，当前已设置的止损，以及新计算的追踪止损
            return min(initial_stop_price, trade.stop_loss, new_trailing_stop_price)
        else:
            # 对于多头交易，追踪止损位在当前价格之下
            new_trailing_stop_price = current_rate * (1 - tspo)
            # 止损只应向上移动（取更大的值）
            return max(initial_stop_price, trade.stop_loss, new_trailing_stop_price) 