#!/bin/bash

# Define the strategy name
STRATEGY_NAME="NFI5MOHO_WIP"

# Define the number of epochs for hyperoptimization
HYPEROPT_EPOCHS=100

# Define the timerange for optimization
OPTIMIZATION_TIMERANGE="20210401-20210630"

# Define the configuration file
CONFIG_FILE="config.json"

# --- Automatically find user_data_dir and strategy_path ---
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Config file $CONFIG_FILE not found. Please ensure it is in the same directory."
    exit 1
fi

# The directory where hyperopt results are stored
# Assumes 'jq' is installed.
USER_DATA_DIR=$(jq -r '.user_data_dir // "user_data"' "$CONFIG_FILE")
HYPEROPT_RESULTS_DIR="$USER_DATA_DIR/hyperopt_results"
STRATEGY_PATH_IN_CONFIG=$(jq -r '.strategy_path // "user_data/strategies"' "$CONFIG_FILE")

# The directory for storing intermediate parameter files
PARAMS_DIR="$HYPEROPT_RESULTS_DIR/intermediate_params"
mkdir -p "$PARAMS_DIR"

# Path to your freqtrade executable
FREQTRADE_EXECUTABLE="freqtrade"

# Total number of optimization groups
TOTAL_GROUPS=21

# --- Cleanup old intermediate parameter files ---
echo "Cleaning up old intermediate parameter files..."
rm -f "$PARAMS_DIR"/group_*.json

# --- Main Optimization Loop ---
for i in $(seq 1 $TOTAL_GROUPS)
do
    echo "-------------------------------------------------------------------"
    echo "--- Starting hyperoptimization for GROUP $i / $TOTAL_GROUPS ---"
    echo "-------------------------------------------------------------------"

    # Set the environment variable for the strategy
    export OPTIMIZE_GROUP=$i

    # Run Freqtrade hyperoptimization
    $FREQTRADE_EXECUTABLE hyperopt \
        --config $CONFIG_FILE \
        --strategy $STRATEGY_NAME \
        --hyperopt-loss SharpeHyperOptLoss \
        --epochs $HYPEROPT_EPOCHS \
        --spaces buy \
        --timerange $OPTIMIZATION_TIMERANGE

    # Check if hyperoptimization was successful
    if [ $? -ne 0 ]; then
        echo "Hyperoptimization for GROUP $i failed. Exiting."
        # Unset the variable before exiting
        unset OPTIMIZE_GROUP
        exit 1
    fi

    echo "Hyperoptimization for GROUP $i finished."
    
    # --- Process Results for the Current Group ---
    echo "Processing results for GROUP $i..."

    # Find the latest hyperopt results file
    LATEST_HYPEROPT_FILE=$(find "$HYPEROPT_RESULTS_DIR" -type f -name "*.json" -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2-)

    if [ -z "$LATEST_HYPEROPT_FILE" ]; then
        echo "No hyperopt result file found in $HYPEROPT_RESULTS_DIR"
        unset OPTIMIZE_GROUP
        exit 1
    fi

    echo "Found latest hyperopt file: $LATEST_HYPEROPT_FILE"
    
    # Define the output file for this group's parameters
    GROUP_PARAMS_FILE="$PARAMS_DIR/group_${i}.json"

    # Run the Python script to extract best params and save to the group's file
    python 2_process_hyperopt_results.py "$LATEST_HYPEROPT_FILE" > "$GROUP_PARAMS_FILE"

    if [ $? -ne 0 ]; then
        echo "Processing hyperopt results for GROUP $i failed. Exiting."
        unset OPTIMIZE_GROUP
        exit 1
    fi

    echo "Best parameters for GROUP $i saved to $GROUP_PARAMS_FILE"
    
done

# Unset the environment variable after the loop finishes
unset OPTIMIZE_GROUP

echo "-------------------------------------------------------------------"
echo "--- All optimization groups have been processed. ---"
echo "-------------------------------------------------------------------"

# --- Final Step: Combine all parameters and update the strategy ---
echo "Combining all optimized parameters..."
FULL_STRATEGY_FILE_PATH="$STRATEGY_PATH_IN_CONFIG/杂/$STRATEGY_NAME.py" # Assumes the 杂 subdir

python 5_update_config_from_report.py "$FULL_STRATEGY_FILE_PATH" "$PARAMS_DIR"

if [ $? -ne 0 ]; then
    echo "Failed to combine parameters and update the strategy. Please check the logs."
    exit 1
fi

echo "Strategy file $FULL_STRATEGY_FILE_PATH has been updated with all optimized parameters."
echo "Optimization process complete." 