"""
ScoringStrategy7 自适应市场环境策略
根据市场状态自动选择最优策略
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Tuple
from enum import Enum

import numpy as np
import pandas as pd
import talib.abstract as ta
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter
from pandas import DataFrame

logger = logging.getLogger(__name__)


class MarketState(Enum):
    """市场状态枚举"""
    STRONG_TREND = "strong_trend"
    SIDEWAYS = "sideways" 
    BREAKOUT = "breakout"
    UNCERTAIN = "uncertain"


class ScoringStrategy7_adaptive_market(IStrategy):
    """
    自适应市场环境策略
    根据市场状态动态选择最优策略逻辑
    """
    
    INTERFACE_VERSION = 3
    timeframe = '1h'
    can_short = True
    
    # ROI设置 - 使用最佳的分层止盈
    minimal_roi = {
        "0": 0.025,
        "8": 0.02,
        "15": 0.015,
        "30": 0.01,
        "60": 0.005
    }
    
    # 止损设置
    stoploss = -0.15
    
    # 尾随止损
    trailing_stop = False
    
    # 市场状态识别参数
    volatility_threshold = DecimalParameter(0.02, 0.08, default=0.04, space="buy")
    trend_strength_threshold = IntParameter(20, 40, default=25, space="buy")
    efficiency_threshold = DecimalParameter(0.3, 0.8, default=0.5, space="buy")
    
    # 策略选择参数
    strong_trend_score_threshold = DecimalParameter(1.8, 2.5, default=2.0, space="buy")
    sideways_score_threshold = DecimalParameter(1.5, 2.2, default=1.7, space="buy")
    breakout_score_threshold = DecimalParameter(1.6, 2.3, default=1.9, space="buy")
    uncertain_score_threshold = DecimalParameter(2.0, 2.8, default=2.2, space="buy")
    
    # 冷静期参数
    profit_cooldown_candles = IntParameter(1, 10, default=3, space="buy")
    loss_cooldown_candles = IntParameter(3, 15, default=8, space="buy")
    
    # 交易对表现跟踪参数
    pair_performance_window = IntParameter(20, 100, default=50, space="buy")
    poor_performance_threshold = DecimalParameter(0.3, 0.6, default=0.45, space="buy")
    performance_penalty = DecimalParameter(0.2, 0.8, default=0.5, space="buy")
    
    # 出场参数
    exit_score = DecimalParameter(-2.0, -0.5, default=-1.0, space="sell")
    adx_exit_threshold = IntParameter(15, 25, default=18, space="sell")
    atr_multiplier = DecimalParameter(1.2, 2.0, default=1.6, space="sell")
    atr_period = IntParameter(10, 20, default=14, space="sell")
    emergency_stop_loss = DecimalParameter(-0.12, -0.05, default=-0.08, space="sell")
    max_holding_hours = IntParameter(24, 72, default=48, space="sell")
    rsi_exit_overbought = IntParameter(65, 80, default=70, space="sell")

    # EMA趋势过滤参数
    ema_trend_period = IntParameter(8, 21, default=13, space="buy")
    enable_ema_filter = IntParameter(0, 1, default=1, space="buy")  # 0=False, 1=True
    
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        # 交易历史跟踪
        self.trade_history: Dict[str, list] = {}
        self.last_trade_time: Dict[str, datetime] = {}
        self.pair_performance: Dict[str, float] = {}

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标和市场状态识别指标
        """
        
        # 基础技术指标
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=14)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=14)
        
        # MACD
        macd = ta.MACD(dataframe)
        dataframe['macd'] = macd['macd']
        dataframe['macd_signal'] = macd['macdsignal']
        dataframe['macd_hist'] = macd['macdhist']
        
        # 布林带
        bollinger = ta.BBANDS(dataframe)
        dataframe['bb_lower'] = bollinger['lowerband']
        dataframe['bb_middle'] = bollinger['middleband']
        dataframe['bb_upper'] = bollinger['upperband']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        
        # EMA
        dataframe['ema13'] = ta.EMA(dataframe, timeperiod=13)
        dataframe['ema21'] = ta.EMA(dataframe, timeperiod=21)
        dataframe['ema55'] = ta.EMA(dataframe, timeperiod=55)

        # EMA趋势过滤线
        dataframe['ema_trend'] = ta.EMA(dataframe, timeperiod=self.ema_trend_period.value)
        
        # ATR和波动率
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']
        
        # 成交量指标
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['mfi'] = ta.MFI(dataframe, timeperiod=14)
        
        # VWAP
        dataframe['vwap'] = (dataframe['volume'] * (dataframe['high'] + dataframe['low'] + dataframe['close']) / 3).cumsum() / dataframe['volume'].cumsum()
        
        # ADX斜率 (用于强趋势识别)
        dataframe['adx_slope'] = dataframe['adx'].diff(5)
        
        # KAMA和效率比率 (用于震荡市场识别)
        dataframe['kama'] = self.calculate_kama(dataframe['close'])
        dataframe['efficiency_ratio'] = self.calculate_efficiency_ratio(dataframe['close'])
        
        # 价格突破指标 (用于突破市场识别)
        dataframe['breakout_high'] = dataframe['high'].rolling(20).max()
        dataframe['breakout_low'] = dataframe['low'].rolling(20).min()
        dataframe['price_position'] = (dataframe['close'] - dataframe['breakout_low']) / (dataframe['breakout_high'] - dataframe['breakout_low'])
        
        # 市场状态识别
        dataframe['market_state'] = self.identify_market_state(dataframe)
        
        return dataframe

    def calculate_kama(self, close_prices, period=20):
        """计算KAMA"""
        change = abs(close_prices.diff(period))
        volatility = abs(close_prices.diff()).rolling(period).sum()
        er = change / volatility
        er = er.fillna(0)
        
        fast_alpha = 2 / (2 + 1)
        slow_alpha = 2 / (30 + 1)
        sc = (er * (fast_alpha - slow_alpha) + slow_alpha) ** 2
        
        kama = close_prices.copy()
        for i in range(period, len(close_prices)):
            kama.iloc[i] = kama.iloc[i-1] + sc.iloc[i] * (close_prices.iloc[i] - kama.iloc[i-1])
            
        return kama

    def calculate_efficiency_ratio(self, close_prices, period=20):
        """计算效率比率"""
        change = abs(close_prices.diff(period))
        volatility = abs(close_prices.diff()).rolling(period).sum()
        return (change / volatility).fillna(0)

    def identify_market_state(self, dataframe: DataFrame) -> pd.Series:
        """
        识别市场状态
        """
        market_state = pd.Series(index=dataframe.index, dtype=object)
        
        for i in range(len(dataframe)):
            if i < 20:  # 需要足够的历史数据
                market_state.iloc[i] = MarketState.UNCERTAIN.value
                continue
                
            volatility = dataframe['volatility'].iloc[i]
            adx = dataframe['adx'].iloc[i]
            adx_slope = dataframe['adx_slope'].iloc[i]
            efficiency_ratio = dataframe['efficiency_ratio'].iloc[i]
            price_position = dataframe['price_position'].iloc[i]
            
            # 强趋势市场：高ADX + 正斜率 + 方向明确
            if (adx > self.trend_strength_threshold.value and 
                adx_slope > 1.0 and
                abs(dataframe['plus_di'].iloc[i] - dataframe['minus_di'].iloc[i]) > 5):
                market_state.iloc[i] = MarketState.STRONG_TREND.value
                
            # 突破市场：价格接近极值 + 成交量放大
            elif (price_position > 0.9 or price_position < 0.1) and \
                 dataframe['volume'].iloc[i] > dataframe['volume_sma'].iloc[i] * 1.5:
                market_state.iloc[i] = MarketState.BREAKOUT.value
                
            # 震荡市场：低效率比率 + 低波动率 + 低ADX
            elif (efficiency_ratio < self.efficiency_threshold.value and
                  volatility < self.volatility_threshold.value and
                  adx < self.trend_strength_threshold.value):
                market_state.iloc[i] = MarketState.SIDEWAYS.value
                
            # 不确定市场：其他情况
            else:
                market_state.iloc[i] = MarketState.UNCERTAIN.value
                
        return market_state

    def _normalize_datetime(self, dt: datetime) -> datetime:
        """
        标准化datetime对象，确保具有UTC时区信息
        """
        if dt.tzinfo is None:
            return dt.replace(tzinfo=timezone.utc)
        return dt

    def check_cooldown_period(self, pair: str, current_time: datetime) -> bool:
        """
        检查冷静期
        """
        if pair not in self.last_trade_time:
            return True

        last_trade = self.last_trade_time[pair]

        # 标准化时间对象，确保时区一致性
        current_time = self._normalize_datetime(current_time)
        last_trade = self._normalize_datetime(last_trade)

        time_diff = current_time - last_trade
        
        # 获取最后一笔交易的结果
        if pair in self.trade_history and self.trade_history[pair]:
            last_result = self.trade_history[pair][-1]
            
            if last_result > 0:  # 盈利交易
                required_cooldown = timedelta(hours=self.profit_cooldown_candles.value)
            else:  # 亏损交易
                required_cooldown = timedelta(hours=self.loss_cooldown_candles.value)
                
            return time_diff >= required_cooldown
            
        return True

    def get_pair_performance_adjustment(self, pair: str) -> float:
        """
        获取交易对表现调整系数
        """
        if pair not in self.trade_history or len(self.trade_history[pair]) < 10:
            return 0.0  # 交易历史不足，不调整
            
        recent_trades = self.trade_history[pair][-self.pair_performance_window.value:]
        win_rate = sum(1 for trade in recent_trades if trade > 0) / len(recent_trades)
        
        # 更新交易对表现
        self.pair_performance[pair] = win_rate
        
        # 如果表现差，增加入场门槛
        if win_rate < self.poor_performance_threshold.value:
            return self.performance_penalty.value
            
        return 0.0

    def calculate_adaptive_score(self, dataframe: DataFrame, pair: str, market_state: str) -> DataFrame:
        """
        根据市场状态计算自适应评分
        """
        dataframe = dataframe.copy()
        dataframe['score'] = 0.0

        # 基础技术指标评分
        # RSI超卖
        rsi_condition = dataframe['rsi'] < 35
        dataframe.loc[rsi_condition, 'score'] += 0.8

        # MACD金叉
        macd_condition = (
            (dataframe['macd'] > dataframe['macd_signal']) &
            (dataframe['macd'].shift(1) <= dataframe['macd_signal'].shift(1))
        )
        dataframe.loc[macd_condition, 'score'] += 0.9

        # 成交量确认
        volume_condition = dataframe['volume'] > dataframe['volume_sma'] * 1.2
        dataframe.loc[volume_condition, 'score'] += 0.7

        # 根据市场状态应用不同的策略逻辑
        if market_state == MarketState.STRONG_TREND.value:
            # ADX斜率策略逻辑
            adx_trend_condition = (
                (dataframe['adx'] > 25) &
                (dataframe['adx_slope'] > 1.0) &
                (dataframe['plus_di'] > dataframe['minus_di'])
            )
            dataframe.loc[adx_trend_condition, 'score'] += 2.0

            # 趋势跟随奖励
            trend_follow_condition = (
                (dataframe['close'] > dataframe['ema21']) &
                (dataframe['ema13'] > dataframe['ema21']) &
                (dataframe['macd_hist'] > 0)
            )
            dataframe.loc[trend_follow_condition, 'score'] += 1.5

        elif market_state == MarketState.SIDEWAYS.value:
            # KAMA自适应策略逻辑
            kama_condition = (
                (dataframe['close'] > dataframe['kama']) &
                (dataframe['efficiency_ratio'] < 0.5) &
                (dataframe['volatility'] < 0.04)
            )
            dataframe.loc[kama_condition, 'score'] += 1.8

            # 震荡市场均值回归
            mean_reversion_condition = (
                (dataframe['close'] < dataframe['bb_lower'] * 1.02) &
                (dataframe['rsi'] < 30)
            )
            dataframe.loc[mean_reversion_condition, 'score'] += 1.2

        elif market_state == MarketState.BREAKOUT.value:
            # K线形态突破策略逻辑
            breakout_condition = (
                (dataframe['close'] > dataframe['breakout_high'].shift(1)) &
                (dataframe['volume'] > dataframe['volume_sma'] * 1.5) &
                (dataframe['price_position'] > 0.9)
            )
            dataframe.loc[breakout_condition, 'score'] += 2.2

            # 突破确认
            breakout_confirm_condition = (
                (dataframe['close'] > dataframe['bb_upper']) &
                (dataframe['rsi'] > 50) &
                (dataframe['adx'] > 20)
            )
            dataframe.loc[breakout_confirm_condition, 'score'] += 1.3

        else:  # UNCERTAIN市场
            # 保守策略，提高门槛
            conservative_condition = (
                (dataframe['rsi'] < 30) &
                (dataframe['macd'] > dataframe['macd_signal']) &
                (dataframe['close'] > dataframe['vwap']) &
                (dataframe['adx'] > 20)
            )
            dataframe.loc[conservative_condition, 'score'] += 1.0

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        自适应入场信号
        """
        pair = metadata['pair']
        current_time = datetime.now(timezone.utc)

        # 检查冷静期
        if not self.check_cooldown_period(pair, current_time):
            dataframe['enter_long'] = 0
            return dataframe

        # 获取当前市场状态
        if 'market_state' not in dataframe.columns or dataframe['market_state'].isna().all():
            dataframe['enter_long'] = 0
            return dataframe

        current_market_state = dataframe['market_state'].iloc[-1]

        # 计算自适应评分
        dataframe = self.calculate_adaptive_score(dataframe, pair, current_market_state)

        # 根据市场状态选择入场阈值
        if current_market_state == MarketState.STRONG_TREND.value:
            threshold = self.strong_trend_score_threshold.value
        elif current_market_state == MarketState.SIDEWAYS.value:
            threshold = self.sideways_score_threshold.value
        elif current_market_state == MarketState.BREAKOUT.value:
            threshold = self.breakout_score_threshold.value
        else:
            threshold = self.uncertain_score_threshold.value

        # 应用交易对表现调整
        performance_adjustment = self.get_pair_performance_adjustment(pair)
        adjusted_threshold = threshold + performance_adjustment

        # EMA趋势过滤（简单有效的二次确认）
        ema_trend_filter = True  # 默认通过
        if self.enable_ema_filter.value:
            ema_trend_filter = (dataframe['close'] > dataframe['ema_trend'])

        # 入场条件（添加EMA趋势过滤）
        conditions = (
            (dataframe['score'] >= adjusted_threshold) &
            (dataframe['volume'] > 0) &
            (dataframe['rsi'] < 70) &
            ema_trend_filter  # 简单的EMA趋势过滤
        )

        dataframe.loc[conditions, 'enter_long'] = 1
        dataframe.loc[conditions, 'enter_tag'] = f'{current_market_state}_{dataframe.loc[conditions, "score"].round(2).astype(str)}'

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        自适应出场信号 - 补全完整的出场逻辑
        """
        # 确保有评分
        if 'score' not in dataframe.columns:
            pair = metadata['pair']
            current_market_state = dataframe['market_state'].iloc[-1] if 'market_state' in dataframe.columns else MarketState.UNCERTAIN.value
            dataframe = self.calculate_adaptive_score(dataframe, pair, current_market_state)

        # 1. 基于评分的出场
        score_exit = (
            (dataframe['score'] < self.exit_score.value) &
            (dataframe['volume'] > 0)
        )
        dataframe.loc[score_exit, 'exit_long'] = 1

        # 2. ATR动态止损保护
        if 'atr' in dataframe.columns:
            atr_exit = (
                (dataframe['close'] < dataframe['close'].shift(1) - dataframe['atr'] * self.atr_multiplier.value) &
                (dataframe['volume'] > 0)
            )
            dataframe.loc[atr_exit, 'exit_long'] = 1

        # 3. 关键支撑位破位出场
        if all(c in dataframe.columns for c in ['close', 'ema21', 'ema13', 'plus_di', 'minus_di', 'adx']):
            support_break_exit = (
                (dataframe['close'] < dataframe['ema21']) &
                (dataframe['close'] < dataframe['ema13']) &
                (dataframe['plus_di'] < dataframe['minus_di']) &
                (dataframe['adx'] > self.adx_exit_threshold.value)
            )
            dataframe.loc[support_break_exit, 'exit_long'] = 1

        # 4. 布林带上轨回落出场
        if all(c in dataframe.columns for c in ['close', 'bb_middle', 'bb_upper', 'rsi']):
            bb_exit = (
                (dataframe['close'] > dataframe['bb_middle']) &
                (dataframe['close'].shift(1) > dataframe['bb_upper']) &
                (dataframe['close'] < dataframe['close'].shift(1)) &
                (dataframe['rsi'] > self.rsi_exit_overbought.value)
            )
            dataframe.loc[bb_exit, 'exit_long'] = 1

        # 5. RSI超买出场
        if 'rsi' in dataframe.columns:
            rsi_exit = (
                (dataframe['rsi'] > self.rsi_exit_overbought.value) &
                (dataframe['close'] < dataframe['close'].shift(1))
            )
            dataframe.loc[rsi_exit, 'exit_long'] = 1

        # 6. 市场状态变化出场
        if 'market_state' in dataframe.columns:
            market_change_exit = (
                (dataframe['market_state'] == MarketState.UNCERTAIN.value) &
                (dataframe['score'] < 0)
            )
            dataframe.loc[market_change_exit, 'exit_long'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自适应止损
        """
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return self.stoploss

            trade_duration = current_time - trade.open_date_utc

            # 紧急止损
            if current_profit < self.emergency_stop_loss.value:
                return self.emergency_stop_loss.value

            # 根据市场状态调整止损
            current_market_state = dataframe['market_state'].iloc[-1] if 'market_state' in dataframe.columns else MarketState.UNCERTAIN.value

            if current_market_state == MarketState.STRONG_TREND.value:
                # 强趋势市场，给更多空间
                if trade_duration > timedelta(hours=self.max_holding_hours.value * 1.5) and current_profit < 0:
                    return -0.02
            elif current_market_state == MarketState.SIDEWAYS.value:
                # 震荡市场，更严格的止损
                if trade_duration > timedelta(hours=self.max_holding_hours.value * 0.5) and current_profit < 0:
                    return -0.03

            return self.stoploss

        except Exception as e:
            logger.warning(f"自适应止损计算错误 {pair}: {e}")
            return self.stoploss

    def confirm_trade_exit(self, pair: str, trade, order_type: str, amount: float,
                          rate: float, time_in_force: str, exit_reason: str,
                          current_time: datetime, **kwargs) -> bool:
        """
        交易退出确认，记录交易历史
        """
        try:
            # 安全计算交易结果，避免除零错误
            if trade.open_rate == 0 or trade.open_rate is None:
                logger.warning(f"交易开仓价格异常 {pair}: open_rate={trade.open_rate}")
                return True  # 跳过异常交易，但允许退出

            profit_ratio = (rate - trade.open_rate) / trade.open_rate

            # 记录交易历史
            if pair not in self.trade_history:
                self.trade_history[pair] = []

            self.trade_history[pair].append(profit_ratio)
            self.last_trade_time[pair] = self._normalize_datetime(current_time)

            # 保持历史记录在合理范围内
            if len(self.trade_history[pair]) > self.pair_performance_window.value * 2:
                self.trade_history[pair] = self.trade_history[pair][-self.pair_performance_window.value:]

        except Exception as e:
            logger.error(f"交易退出确认错误 {pair}: {e}")
            # 即使出错也允许交易退出

        return True
