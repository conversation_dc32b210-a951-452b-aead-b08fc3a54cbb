'use client'

import { useEffect, useState } from 'react'
import { Button, Row, Col, Card, Typography, Space, Statistic, Input, Select } from 'antd'
import { 
  SearchOutlined, 
  DownloadOutlined, 
  StarOutlined,
  UserOutlined,
  ArrowRightOutlined,
  FireOutlined,
  TrophyOutlined
} from '@ant-design/icons'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { useQuery } from 'react-query'
import { resourceAPI, categoryAPI } from '@/lib/api'
import ResourceCard from '@/components/ResourceCard'
import CategoryList from '@/components/CategoryList'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const { Title, Paragraph } = Typography
const { Option } = Select

export default function HomePage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  // 获取推荐资源
  const { data: featuredResources, isLoading: featuredLoading } = useQuery(
    'featured-resources',
    () => resourceAPI.getFeaturedResources(),
    {
      select: (response) => response.data,
    }
  )

  // 获取最新资源
  const { data: latestResources, isLoading: latestLoading } = useQuery(
    'latest-resources',
    () => resourceAPI.getResources({ ordering: '-created_at', limit: 8 }),
    {
      select: (response) => response.data.results,
    }
  )

  // 获取分类
  const { data: categories } = useQuery(
    'categories',
    () => categoryAPI.getCategories(),
    {
      select: (response) => response.data,
    }
  )

  const handleSearch = () => {
    const params = new URLSearchParams()
    if (searchTerm) params.set('search', searchTerm)
    if (selectedCategory) params.set('category', selectedCategory)
    
    window.location.href = `/resources?${params.toString()}`
  }

  const stats = [
    { title: '资源总数', value: 50000, suffix: '+', icon: <DownloadOutlined /> },
    { title: '用户数量', value: 10000, suffix: '+', icon: <UserOutlined /> },
    { title: '下载次数', value: 1000000, suffix: '+', icon: <DownloadOutlined /> },
    { title: '满意度', value: 98, suffix: '%', icon: <StarOutlined /> },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-20 lg:py-32">
        <div className="container-custom">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <Title level={1} className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  专业的
                  <span className="text-gradient">
                    资源管理
                  </span>
                  平台
                </Title>
                
                <Paragraph className="text-xl text-gray-600 leading-relaxed">
                  汇聚海量优质设计资源，提供便捷的内容管理和下载服务。
                  让创作变得更简单，让灵感触手可及。
                </Paragraph>

                {/* 搜索框 */}
                <div className="bg-white rounded-xl shadow-lg p-6 space-y-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Input
                      size="large"
                      placeholder="搜索资源、关键词..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onPressEnter={handleSearch}
                      className="flex-1"
                    />
                    <Select
                      size="large"
                      placeholder="选择分类"
                      value={selectedCategory}
                      onChange={setSelectedCategory}
                      className="w-full sm:w-48"
                      allowClear
                    >
                      {categories?.map((category: any) => (
                        <Option key={category.id} value={category.id}>
                          {category.name}
                        </Option>
                      ))}
                    </Select>
                    <Button
                      type="primary"
                      size="large"
                      icon={<SearchOutlined />}
                      onClick={handleSearch}
                      className="w-full sm:w-auto"
                    >
                      搜索
                    </Button>
                  </div>
                </div>

                <Space size="large" className="flex flex-wrap">
                  <Link href="/resources">
                    <Button
                      type="primary"
                      size="large"
                      icon={<ArrowRightOutlined />}
                      className="h-12 px-8 text-lg"
                    >
                      浏览资源
                    </Button>
                  </Link>
                  <Link href="/upload">
                    <Button
                      size="large"
                      icon={<DownloadOutlined />}
                      className="h-12 px-8 text-lg"
                    >
                      上传资源
                    </Button>
                  </Link>
                </Space>
              </motion.div>
            </Col>

            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="relative z-10 bg-white rounded-2xl shadow-2xl p-8">
                  <div className="aspect-square bg-gradient-to-br from-primary-100 to-secondary-100 rounded-xl flex items-center justify-center">
                    <DownloadOutlined className="text-6xl text-primary-500" />
                  </div>
                </div>
                
                {/* 装饰元素 */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-secondary-200 rounded-full opacity-60 animate-float"></div>
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-200 rounded-full opacity-60 animate-float" style={{ animationDelay: '2s' }}></div>
              </motion.div>
            </Col>
          </Row>
        </div>
      </section>

      {/* 统计数据 */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <Row gutter={[32, 32]}>
            {stats.map((stat, index) => (
              <Col xs={12} md={6} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <Card className="hover:shadow-lg transition-shadow">
                    <Space direction="vertical" size="small" className="w-full">
                      <div className="text-3xl text-primary-500">
                        {stat.icon}
                      </div>
                      <Statistic
                        title={stat.title}
                        value={stat.value}
                        suffix={stat.suffix}
                        valueStyle={{ 
                          color: '#0ea5e9',
                          fontSize: '2rem',
                          fontWeight: 'bold'
                        }}
                      />
                    </Space>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* 推荐资源 */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              <FireOutlined className="text-orange-500 mr-3" />
              推荐资源
            </Title>
            <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
              精选优质资源，为您的创作提供灵感和素材
            </Paragraph>
          </motion.div>

          <Row gutter={[24, 24]}>
            {featuredResources?.slice(0, 8).map((resource: any, index: number) => (
              <Col xs={24} sm={12} lg={8} xl={6} key={resource.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <ResourceCard resource={resource} />
                </motion.div>
              </Col>
            ))}
          </Row>

          <div className="text-center mt-12">
            <Link href="/resources?featured=true">
              <Button type="primary" size="large" icon={<ArrowRightOutlined />}>
                查看更多推荐
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* 最新资源 */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              <TrophyOutlined className="text-yellow-500 mr-3" />
              最新资源
            </Title>
            <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
              第一时间获取最新上传的优质资源
            </Paragraph>
          </motion.div>

          <Row gutter={[24, 24]}>
            {latestResources?.map((resource: any, index: number) => (
              <Col xs={24} sm={12} lg={8} xl={6} key={resource.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <ResourceCard resource={resource} />
                </motion.div>
              </Col>
            ))}
          </Row>

          <div className="text-center mt-12">
            <Link href="/resources">
              <Button type="primary" size="large" icon={<ArrowRightOutlined />}>
                浏览所有资源
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* 分类导航 */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              资源分类
            </Title>
            <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
              按分类浏览，快速找到您需要的资源类型
            </Paragraph>
          </motion.div>

          <CategoryList categories={categories} />
        </div>
      </section>

      <Footer />
    </div>
  )
}
