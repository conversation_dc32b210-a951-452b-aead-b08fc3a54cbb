{"max_open_trades": 1, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 1000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "order_types": {"entry": "limit", "exit": "limit", "emergency_exit": "market", "force_exit": "market", "force_entry": "market", "stoploss": "market", "stoploss_on_exchange": true, "stoploss_price_type": "last", "stoploss_on_exchange_interval": 60, "stoploss_on_exchange_limit_ratio": 0.99}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "SOL/USDT:USDT", "XRP/USDT:USDT", "DOGE/USDT:USDT", "TRX/USDT:USDT", "ADA/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT", "BCH/USDT:USDT", "DOT/USDT:USDT", "UNI/USDT:USDT", "NEAR/USDT:USDT", "LTC/USDT:USDT", "XMR/USDT:USDT", "ETC/USDT:USDT", "XLM/USDT:USDT", "AAVE/USDT:USDT", "FIL/USDT:USDT", "FTM/USDT:USDT", "VET/USDT:USDT", "ATOM/USDT:USDT", "RUNE/USDT:USDT", "GRT/USDT:USDT", "MKR/USDT:USDT", "ALGO/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "ea6e62fa52289932648c49d353476a017aad46062f04e7d85adce94bb11ced9d", "ws_token": "7QxYS4ZNJQLz11pyZwYrvys626IDChEIYw", "CORS_origins": [], "username": "freqtrade", "password": "!QAZ2wsx"}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}