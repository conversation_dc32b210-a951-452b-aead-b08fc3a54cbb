# 平滑高斯趋势滤波器策略 (Smoothed Gaussian Trend Filter Strategy)

这是一个基于平滑高斯趋势滤波器的多空策略，专为加密货币交易而设计。该策略利用高斯滤波器和线性回归的组合来识别市场趋势，并在趋势转变时生成交易信号。

## 策略特点

- **双重平滑处理**：通过高斯滤波器和线性回归双重平滑价格数据，有效过滤市场噪音
- **多空双向交易**：支持多头和空头交易，充分利用市场波动
- **动态仓位管理**：基于风险比例和ATR动态调整仓位大小
- **ATR动态止损**：根据市场波动性自动调整止损位置
- **追踪止盈**：在趋势强劲时锁定利润
- **成交量过滤**：通过成交量确认增强信号可靠性
- **多时间周期确认**：要求多个时间周期趋势一致，提高交易成功率
- **动态杠杆管理**：根据趋势强度自动调整杠杆倍数

## 工作原理

1. **核心指标计算**：
   - 应用高斯滤波器对价格数据进行初步平滑
   - 对高斯滤波结果应用线性回归进行二次平滑
   - 计算SuperTrend指标作为趋势确认工具

2. **趋势判断**：
   - 当平滑线位于SuperTrend线上方时，识别为上涨趋势
   - 当平滑线位于SuperTrend线下方时，识别为下跌趋势
   - 当平滑线的短期方向与它相对于SuperTrend的位置相矛盾时，识别为盘整状态

3. **入场信号**：
   - **多头入场**：当趋势从下跌转为上涨，或从盘整转为上涨
   - **空头入场**：当趋势从上涨转为下跌，或从盘整转为下跌

4. **退出信号**：
   - **多头退出**：当趋势从上涨转为下跌
   - **空头退出**：当趋势从下跌转为上涨
   - **追踪止盈**：当利润达到ATR的一定倍数时

## 参数设置

### 高斯滤波器参数

- **gaussian_length** (10-30, 默认15)：高斯滤波器的周期长度，影响平滑度和灵敏度
- **poles** (1-4, 默认3)：高斯滤波器的阶数，数值越高，线条越平滑

### 线性回归平滑参数

- **linreg_length** (15-30, 默认22)：线性回归的周期长度
- **flatten_multiplier** (3-12, 默认7)：平滑度调整因子

### 成交量过滤参数

- **volume_filter_length** (20-50, 默认33)：成交量计算周期
- **volume_threshold** (50-90, 默认70)：成交量强度阈值，百分比

### ATR参数

- **atr_period** (10-30, 默认14)：ATR计算周期
- **atr_multiplier_sl** (1.0-3.0, 默认2.0)：止损ATR乘数
- **atr_multiplier_tp** (2.0-5.0, 默认3.0)：止盈ATR乘数

### 仓位管理参数

- **risk_per_trade** (1%-5%, 默认2%)：每笔交易风险比例
- **leverage_factor** (1-5, 默认3)：杠杆倍数

### 趋势过滤参数

- **trend_strength_threshold** (50-90, 默认70)：趋势强度阈值
- **use_multi_timeframe** (True/False, 默认True)：是否使用多时间周期确认
- **min_timeframes_aligned** (2-4, 默认3)：要求一致的最小时间周期数量
- **mid_trnd_sig** (True/False, 默认False)：是否启用中期趋势信号

## 使用建议

1. **参数优化**：
   - 使用Freqtrade的超参数优化功能找到最佳参数组合
   - 不同交易对和市场条件可能需要不同的参数设置

2. **风险管理**：
   - 建议将`risk_per_trade`设置在1%-3%之间
   - 在高波动性市场中降低杠杆倍数

3. **多时间周期分析**：
   - 启用`use_multi_timeframe`可以显著提高胜率
   - 建议将`min_timeframes_aligned`设置为至少3，确保趋势足够强劲

4. **中期趋势信号**：
   - 启用`mid_trnd_sig`会增加交易频率，但可能降低单笔交易的平均利润
   - 在震荡市场中建议关闭此选项

## 回测结果

该策略在不同市场条件下表现各异，但通常在以下情况下表现最佳：

- 有明确趋势的市场
- 中等波动性的市场
- 成交量稳定的市场

在横盘震荡或极端波动的市场中，建议调整参数或暂时停止交易。

## 优化方向

1. 添加更多的市场状态过滤条件
2. 实现动态的参数调整机制
3. 加入基本面数据分析
4. 开发更精细的退出策略

## 免责声明

交易加密货币存在高风险，可能导致本金损失。本策略不构成投资建议，使用者应自行承担风险。建议在实盘交易前，先在模拟环境中充分测试策略表现。 