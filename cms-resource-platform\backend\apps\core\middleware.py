"""
核心中间件
"""
import time
import logging
from django.http import JsonResponse
from django.core.cache import cache
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class SecurityMiddleware(MiddlewareMixin):
    """安全中间件"""
    
    def process_request(self, request):
        # 检查User-Agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if not user_agent or len(user_agent) < 10:
            return JsonResponse({'error': 'Invalid request'}, status=400)
        
        # 检查可疑的爬虫
        suspicious_agents = ['bot', 'crawler', 'spider', 'scraper']
        if any(agent in user_agent.lower() for agent in suspicious_agents):
            logger.warning(f"Suspicious user agent: {user_agent}")
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        return None


class RateLimitMiddleware(MiddlewareMixin):
    """频率限制中间件"""
    
    def process_request(self, request):
        if not getattr(settings, 'ENABLE_RATE_LIMITING', True):
            return None
        
        # 获取客户端IP
        ip = self.get_client_ip(request)
        
        # API请求限制
        if request.path.startswith('/api/'):
            cache_key = f"rate_limit_api_{ip}"
            requests = cache.get(cache_key, 0)
            
            if requests >= 100:  # 每分钟100次请求
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'detail': 'Too many requests'
                }, status=429)
            
            cache.set(cache_key, requests + 1, 60)
        
        # 下载请求限制
        if '/download/' in request.path:
            cache_key = f"rate_limit_download_{ip}"
            requests = cache.get(cache_key, 0)
            
            if requests >= 10:  # 每分钟10次下载
                return JsonResponse({
                    'error': 'Download limit exceeded',
                    'detail': 'Too many download requests'
                }, status=429)
            
            cache.set(cache_key, requests + 1, 60)
        
        return None
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PerformanceMiddleware(MiddlewareMixin):
    """性能监控中间件"""
    
    def process_request(self, request):
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 记录慢请求
            if duration > 2.0:  # 超过2秒的请求
                logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {duration:.2f}s"
                )
            
            # 添加响应头
            response['X-Response-Time'] = f"{duration:.3f}s"
        
        return response


class CORSMiddleware(MiddlewareMixin):
    """CORS中间件"""
    
    def process_response(self, request, response):
        # 只在开发环境或特定域名下允许跨域
        if settings.DEBUG:
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
            response['Access-Control-Allow-Credentials'] = 'true'
        
        return response
