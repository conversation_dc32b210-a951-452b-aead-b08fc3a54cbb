'use client'

import { Row, Col, Card } from 'antd'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  AppstoreOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  FileTextOutlined,
  FolderOutlined
} from '@ant-design/icons'

interface CategoryListProps {
  categories?: any[]
}

const CategoryList: React.FC<CategoryListProps> = ({ categories = [] }) => {
  // 默认图标映射
  const getIconByName = (name: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      '图片': <PictureOutlined className="text-2xl" />,
      '视频': <VideoCameraOutlined className="text-2xl" />,
      '音频': <AudioOutlined className="text-2xl" />,
      '文档': <FileTextOutlined className="text-2xl" />,
      '设计': <AppstoreOutlined className="text-2xl" />,
      '素材': <FolderOutlined className="text-2xl" />,
    }
    
    // 根据名称匹配图标
    for (const [key, icon] of Object.entries(iconMap)) {
      if (name.includes(key)) {
        return icon
      }
    }
    
    return <AppstoreOutlined className="text-2xl" />
  }

  // 默认分类（如果没有从API获取到）
  const defaultCategories = [
    {
      id: 1,
      name: '设计素材',
      description: '各类设计相关的素材资源',
      color: '#1890ff',
      count: 1200
    },
    {
      id: 2,
      name: '图片资源',
      description: '高质量图片和插图',
      color: '#52c41a',
      count: 800
    },
    {
      id: 3,
      name: '视频素材',
      description: '视频模板和素材',
      color: '#722ed1',
      count: 450
    },
    {
      id: 4,
      name: '音频资源',
      description: '音效和背景音乐',
      color: '#fa8c16',
      count: 320
    },
    {
      id: 5,
      name: '文档模板',
      description: '各类文档模板',
      color: '#eb2f96',
      count: 600
    },
    {
      id: 6,
      name: '字体资源',
      description: '中英文字体文件',
      color: '#13c2c2',
      count: 280
    },
  ]

  const displayCategories = categories.length > 0 ? categories : defaultCategories

  return (
    <Row gutter={[24, 24]}>
      {displayCategories.map((category, index) => (
        <Col xs={24} sm={12} md={8} lg={6} key={category.id}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
            whileHover={{ y: -4 }}
          >
            <Link href={`/resources?category=${category.id}`}>
              <Card
                hoverable
                className="h-full text-center group overflow-hidden"
                bodyStyle={{ padding: '24px 16px' }}
              >
                <div className="space-y-4">
                  {/* 图标 */}
                  <div 
                    className="w-16 h-16 mx-auto rounded-full flex items-center justify-center text-white transition-transform duration-300 group-hover:scale-110"
                    style={{ backgroundColor: category.color || '#1890ff' }}
                  >
                    {category.icon ? (
                      <span className="text-2xl">{category.icon}</span>
                    ) : (
                      getIconByName(category.name)
                    )}
                  </div>

                  {/* 分类名称 */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                      {category.name}
                    </h3>
                    
                    {/* 描述 */}
                    {category.description && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {category.description}
                      </p>
                    )}
                  </div>

                  {/* 资源数量 */}
                  <div className="text-gray-500 text-sm">
                    {category.count || 0} 个资源
                  </div>

                  {/* 子分类 */}
                  {category.children && category.children.length > 0 && (
                    <div className="flex flex-wrap gap-1 justify-center">
                      {category.children.slice(0, 3).map((child: any) => (
                        <span
                          key={child.id}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                        >
                          {child.name}
                        </span>
                      ))}
                      {category.children.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          +{category.children.length - 3}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </Card>
            </Link>
          </motion.div>
        </Col>
      ))}
    </Row>
  )
}

export default CategoryList
