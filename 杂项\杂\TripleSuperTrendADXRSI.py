import logging
import numpy as np
import talib.abstract as ta
from freqtrade.strategy import IntParameter, IStrategy, DecimalParameter
from numpy.lib import math
from pandas import DataFrame
import pandas_ta as pta


class TripleSuperTrendADXRSI(IStrategy):
    INTERFACE_VERSION: int = 3

    # Enable short trades
    can_short: bool = True

    # ROI table: defines the minimal return on investment for different time periods
    minimal_roi = {"0": 0.1, "30": 0.75, "60": 0.05, "120": 0.025}
    # Stoploss threshold
    stoploss = -0.20

    # Trailing stop configuration
    trailing_stop = True
    trailing_stop_positive = 0.05
    trailing_stop_positive_offset = 0.1
    trailing_only_offset_is_reached = False

    # Strategy timeframe and startup candle count
    timeframe = "15m"
    startup_candle_count = 120

    # ----- COMMON SUPER TREND PARAMETERS (used for both Buy and Sell sides) -----
    # First Supertrend
    st1_period = IntParameter(7, 20, default=10, load=True, space='buy', optimize=True)
    st1_multiplier = DecimalParameter(1.0, 3.0, default=1.0, decimals=1, load=True, space='buy', optimize=True)

    # Second Supertrend
    st2_period = IntParameter(15, 30, default=11, load=True, space='buy', optimize=True)
    st2_multiplier = DecimalParameter(2.0, 4.0, default=2.0, decimals=1, load=True, space='buy', optimize=True)

    # Third Supertrend
    st3_period = IntParameter(25, 50, default=12, load=True, space='buy', optimize=True)
    st3_multiplier = DecimalParameter(3.0, 5.0, default=3.0, decimals=1, load=True, space='buy', optimize=True)

    # ----- KAMA PARAMETERS -----
    kama_period = IntParameter(10, 50, default=20, load=True, space='buy', optimize=True)
    kama_roc_filter_threshold = DecimalParameter(0.01, 0.5, default=0.05, decimals=3, load=True, space='buy', optimize=True)

    @property
    def plot_config(self):
        return {
            "main_plot": {
                "st1_value": {
                    "color": "green",
                    "type": "line",
                    "fill_to": "close"
                },
                "st2_value": {
                    "color": "blue",
                    "type": "line",
                    "fill_to": "close"
                },
                "st3_value": {
                    "color": "purple",
                    "type": "line",
                    "fill_to": "close"
                },
                "kama": {  # Add KAMA to main plot
                    "color": "orange",
                    "type": "line"
                }
            },
            "subplots": {
            }
        }

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Calculate 1st Supertrend
        st1 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=self.st1_period.value,
            multiplier=self.st1_multiplier.value
        )
        if st1 is not None and not st1.empty:
            dataframe['st1_value'] = st1[f'SUPERT_{self.st1_period.value}_{self.st1_multiplier.value}']
            dataframe['st1_dir'] = np.where(st1[f'SUPERTd_{self.st1_period.value}_{self.st1_multiplier.value}'] == 1, 'up', 'down')
        else:
            dataframe['st1_value'] = np.nan
            dataframe['st1_dir'] = 'neutral'

        # Calculate 2nd Supertrend
        st2 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=self.st2_period.value,
            multiplier=self.st2_multiplier.value
        )
        if st2 is not None and not st2.empty:
            dataframe['st2_value'] = st2[f'SUPERT_{self.st2_period.value}_{self.st2_multiplier.value}']
            dataframe['st2_dir'] = np.where(st2[f'SUPERTd_{self.st2_period.value}_{self.st2_multiplier.value}'] == 1, 'up', 'down')
        else:
            dataframe['st2_value'] = np.nan
            dataframe['st2_dir'] = 'neutral'

        # Calculate 3rd Supertrend
        st3 = pta.supertrend(
            dataframe['high'], dataframe['low'], dataframe['close'],
            length=self.st3_period.value,
            multiplier=self.st3_multiplier.value
        )
        if st3 is not None and not st3.empty:
            dataframe['st3_value'] = st3[f'SUPERT_{self.st3_period.value}_{self.st3_multiplier.value}']
            dataframe['st3_dir'] = np.where(st3[f'SUPERTd_{self.st3_period.value}_{self.st3_multiplier.value}'] == 1, 'up', 'down')
        else:
            dataframe['st3_value'] = np.nan
            dataframe['st3_dir'] = 'neutral'
        
        # Calculate KAMA
        dataframe['kama'] = ta.KAMA(dataframe['close'], timeperiod=self.kama_period.value)
        # Calculate KAMA Rate of Change (ROC) - Absolute percentage change
        dataframe['kama_roc'] = dataframe['kama'].pct_change().abs() * 100
        # Calculate KAMA Slope (difference from previous period)
        dataframe['kama_slope'] = dataframe['kama'].diff()

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Creates entry signals based on a combination of Supertrend, ADX, and RSI indicators.
        A long entry is signaled if all three common Supertrend indicators are 'up',
        ADX/RSI confirm, and this state was not true on the previous candle.
        A short entry is signaled if all three common Supertrend indicators are 'down',
        ADX/RSI confirm, and this state was not true on the previous candle.
        """

        # Current long conditions
        current_long_cond = (
            (dataframe['st1_dir'] == "up") &
            (dataframe['st2_dir'] == "up") &
            (dataframe['st3_dir'] == "up") &
            (dataframe["volume"] > 0) &
            (dataframe['kama_slope'] > 0) &  # KAMA slope up
            (dataframe['close'] > dataframe['kama']) &  # Close above KAMA (support)
            (dataframe["kama_roc"] > self.kama_roc_filter_threshold.value) # Filter chop
        )

        # Previous long Supertrend alignment
        previous_st_long_aligned = (
            (dataframe['st1_dir'].shift(1) == "up") &
            (dataframe['st2_dir'].shift(1) == "up") &
            (dataframe['st3_dir'].shift(1) == "up")
        )

        dataframe.loc[
            current_long_cond & ~previous_st_long_aligned,
            "enter_long",
        ] = 1

        # Current short conditions
        current_short_cond = (
            (dataframe['st1_dir'] == "down") &
            (dataframe['st2_dir'] == "down") &
            (dataframe['st3_dir'] == "down") &
            (dataframe["volume"] > 0) &
            (dataframe['kama_slope'] < 0) &  # KAMA slope down
            (dataframe['close'] < dataframe['kama']) &  # Close below KAMA (resistance)
            (dataframe["kama_roc"] > self.kama_roc_filter_threshold.value) # Filter chop
        )

        # Previous short Supertrend alignment
        previous_st_short_aligned = (
            (dataframe['st1_dir'].shift(1) == "down") &
            (dataframe['st2_dir'].shift(1) == "down") &
            (dataframe['st3_dir'].shift(1) == "down")
        )

        dataframe.loc[
            current_short_cond & ~previous_st_short_aligned,
            "enter_short",
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Sets exit conditions for trades.
        For longs: exit if ANY TWO of the three Supertrend indicators turn 'down' OR if RSI is above the long exit threshold.
        For shorts: exit if ANY TWO of the three Supertrend indicators turn 'up' OR if RSI is below the short exit threshold.
        """
        # Conditions for two Supertrends turning down for long exit
        long_exit_st_cond = (
            ((dataframe['st1_dir'] == "down") & (dataframe['st2_dir'] == "down")) |
            ((dataframe['st1_dir'] == "down") & (dataframe['st3_dir'] == "down")) |
            ((dataframe['st2_dir'] == "down") & (dataframe['st3_dir'] == "down"))
        )

        # LONG EXIT CONDITION
        dataframe.loc[
            (
                long_exit_st_cond |
                (dataframe['kama_slope'] < 0) |  # KAMA slope turns down
                (dataframe['close'] < dataframe['kama'])  # Close breaks below KAMA
            ),
            "exit_long",
        ] = 1

        # Conditions for two Supertrends turning up for short exit
        short_exit_st_cond = (
            ((dataframe['st1_dir'] == "up") & (dataframe['st2_dir'] == "up")) |
            ((dataframe['st1_dir'] == "up") & (dataframe['st3_dir'] == "up")) |
            ((dataframe['st2_dir'] == "up") & (dataframe['st3_dir'] == "up"))
        )

        # SHORT EXIT CONDITION
        dataframe.loc[
            (
                short_exit_st_cond |
                (dataframe['kama_slope'] > 0) |  # KAMA slope turns up
                (dataframe['close'] > dataframe['kama'])  # Close breaks above KAMA
            ),
            "exit_short",
        ] = 1

        return dataframe
