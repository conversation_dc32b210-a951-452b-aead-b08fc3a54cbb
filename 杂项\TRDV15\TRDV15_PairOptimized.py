import logging
import numpy as np
import pandas as pd
import talib.abstract as ta
from pandas import DataFrame
from typing import Optional, Dict, List
from datetime import datetime, timedelta
import json
from pathlib import Path

from freqtrade.strategy import (IStrategy, IntParameter, DecimalParameter, CategoricalParameter, RealParameter, informative)
from freqtrade.persistence import Trade
from freqtrade.exchange import timeframe_to_minutes

# 忽略性能警告
from warnings import simplefilter
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)

logger = logging.getLogger(__name__)

class TRDV15_PairOptimized(IStrategy):
    """
    Trend Recognition Dual Version 1.5
    - Multi-Timeframe (1h for trend, 15m for entry)
    - Core Trend: SuperTrend
    - Auxiliary/Confirmation: KAMA
    - Market Regime Detection (Initial)
    - ATR based position sizing and trailing stop loss
    """
    INTERFACE_VERSION: int = 3

    def __init__(self, config: dict):
        super().__init__(config)
        self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                self.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it to function. No trades will be opened.")
            self.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}. Strategy may not function correctly.")
            self.custom_info = {}

    # --- 策略核心配置 ---
    timeframe = '15m'  # 交易执行时间周期
    # info_timeframe = '1h' # 主趋势判断时间周期 - now handled by @informative

    # 启用做空 (根据您的需求，可以设为True或False)
    can_short: bool = True

    # 同时开启的最大交易数量 (根据优化结果设置)
    max_open_trades: int = 4

    # ROI表 (初期可以简单设置，后续根据回测优化)
    minimal_roi = {
        "0": 0.10, # 10%
        "30": 0.05, # 30分钟后5%
        "60": 0.025, # 60分钟后2.5%
        "120": 0.01 # 120分钟后1%
    }

    # 固定止损 (作为ATR止损的最后防线，可以设得宽一些)
    stoploss = -0.10  # 从 -0.20 修改为 -0.10

    # 跟踪止损设置
    trailing_stop = True
    trailing_stop_positive = 0.02  # 当盈利达到2%时激活跟踪止损
    trailing_stop_positive_offset = 0.05  # 从盈利高点回撤5%时触发止损
    trailing_only_offset_is_reached = True # 只有当盈利达到 trailing_stop_positive 时才开始跟踪

    # 运行配置
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False # 出场信号触发时，即使亏损也退出
    ignore_roi_if_entry_signal = True # 如果有新的入场信号，旧的ROI仍然有效

    # --- 可优化的核心参数 (现在从 JSON 文件加载) ---

    # SuperTrend (15m - 执行周期)
    # st_short_atr_period = IntParameter(7, 21, default=7, space='buy', optimize=True, load=True)
    # st_short_multiplier = DecimalParameter(1.5, 6.0, default=5.1, decimals=1, space='buy', optimize=True, load=True)

    # KAMA (15m - 执行周期，用于过滤)
    # kama_short_period = IntParameter(10, 40, default=25, space='buy', optimize=True, load=True)
    # KAMA 斜率判断 (用于过滤震荡)
    # kama_short_slope_period = IntParameter(3, 10, default=8, space='buy', optimize=True, load=True)
    # kama_short_slope_threshold_entry = DecimalParameter(0.00001, 0.001, default=0.00011, decimals=5, space='buy', optimize=True, load=True)


    # ATR (用于仓位管理和跟踪止损)
    # atr_period_pos_size = IntParameter(10, 30, default=23, space='buy', optimize=True, load=True) # ATR周期用于计算仓位
    # atr_multiplier_pos_size = DecimalParameter(1.5, 7.0, default=7.0, decimals=1, space='buy', optimize=True, load=True) # ATR倍数用于计算仓位大小的分母

    # 风险管理
    # risk_per_trade = DecimalParameter(0.01, 0.03, default=0.01, decimals=3, space='buy', optimize=True, load=True) # 每笔交易承担的风险比例 (账户的百分比)


    # --- 高时间周期 (1h) 指标参数 (通常不直接优化，或者与执行周期参数联动) ---
    # SuperTrend (1h - 主趋势周期)
    st_long_atr_period = 14 # 1h Supertrend ATR 周期
    st_long_multiplier = 3.5 # 1h Supertrend 乘数

    # KAMA (1h - 主趋势周期)
    kama_long_period = 30 # 1h KAMA 周期
    kama_long_slope_period = 8 # 1h KAMA 斜率周期
    kama_long_slope_threshold = 0.00005 # 1h KAMA 斜率阈值，用于判断趋势有效性


    # --- 辅助方法 ---
    def calculate_supertrend(self, dataframe: DataFrame, period: int, multiplier: float) -> DataFrame:
        """
        Calculates SuperTrend indicator
        """
        df = dataframe.copy()
        df['TR'] = ta.TRANGE(df)
        df['ATR'] = ta.SMA(df['TR'], period)

        df['basic_ub'] = (df['high'] + df['low']) / 2 + multiplier * df['ATR']
        df['basic_lb'] = (df['high'] + df['low']) / 2 - multiplier * df['ATR']

        df['final_ub'] = 0.00
        df['final_lb'] = 0.00

        for i in range(period, len(df)):
            df.loc[i, 'final_ub'] = (df.loc[i, 'basic_ub'] if df.loc[i, 'basic_ub'] < df.loc[i-1, 'final_ub'] or df.loc[i-1, 'close'] > df.loc[i-1, 'final_ub'] else df.loc[i-1, 'final_ub'])
            df.loc[i, 'final_lb'] = (df.loc[i, 'basic_lb'] if df.loc[i, 'basic_lb'] > df.loc[i-1, 'final_lb'] or df.loc[i-1, 'close'] < df.loc[i-1, 'final_lb'] else df.loc[i-1, 'final_lb'])

        df['ST'] = np.nan # Initialize with NaN
        df['STX'] = ''

        # Set initial SuperTrend value at the 'period' index
        if period < len(df): # Ensure dataframe is long enough
            if df.loc[period, 'close'] > df.loc[period, 'final_lb']: # Initial guess: if close > lower band, trend is up
                df.loc[period, 'ST'] = df.loc[period, 'final_lb']
            elif df.loc[period, 'close'] < df.loc[period, 'final_ub']: # Initial guess: if close < upper band, trend is down
                df.loc[period, 'ST'] = df.loc[period, 'final_ub']
            # else: ST remains NaN if close is exactly on a band or between basic_ub/lb without clear direction yet.
            # This NaN will be forward-filled by subsequent logic or bfill at the end if it's the only point.

        for i in range(period + 1, len(df)): # Start loop from period + 1
            prev_st = df.loc[i-1, 'ST']
            # If previous ST is NaN (can happen if initial ST was not set), try to set it based on current price vs final bands
            if pd.isna(prev_st):
                if df.loc[i, 'close'] > df.loc[i, 'final_lb']:
                    df.loc[i, 'ST'] = df.loc[i, 'final_lb']
                elif df.loc[i, 'close'] < df.loc[i, 'final_ub']:
                    df.loc[i, 'ST'] = df.loc[i, 'final_ub']
                # If still can't determine, ST remains NaN for this point too
                continue # Skip to next iteration if ST can't be determined

            if prev_st == df.loc[i-1, 'final_ub']:
                df.loc[i, 'ST'] = df.loc[i, 'final_ub'] if df.loc[i, 'close'] <= df.loc[i, 'final_ub'] else df.loc[i, 'final_lb']
            elif prev_st == df.loc[i-1, 'final_lb']:
                df.loc[i, 'ST'] = df.loc[i, 'final_lb'] if df.loc[i, 'close'] >= df.loc[i, 'final_lb'] else df.loc[i, 'final_ub']
            # else: This case should ideally not be reached if prev_st was a valid final_ub or final_lb

        df.loc[df['close'] > df['ST'], 'STX'] = 'up'
        df.loc[df['close'] < df['ST'], 'STX'] = 'down'
        df.loc[df['STX'] == '', 'STX'] = pd.NA # Set to NA if no trend determined

        df['ST'] = df['ST'].bfill().ffill() # Fill any remaining NaNs robustly
        df['STX'] = df['STX'].bfill().ffill()

        return df[['ST', 'STX']]

    # --- Informative Timeframe (1h) ---
    @informative('1h')
    def populate_info_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populates indicators for the 1-hour informative timeframe
        """
        # 1h SuperTrend
        supertrend_long = self.calculate_supertrend(dataframe.copy(), period=self.st_long_atr_period, multiplier=self.st_long_multiplier)
        dataframe[f'st_long'] = supertrend_long['ST'] 
        dataframe[f'stx_long'] = supertrend_long['STX'] 

        # 1h KAMA
        dataframe[f'kama_long'] = ta.KAMA(dataframe, timeperiod=self.kama_long_period) 
        dataframe[f'kama_long_slope'] = ta.LINEARREG_SLOPE(dataframe[f'kama_long'], timeperiod=self.kama_long_slope_period)
        
        # 1h EMA Filter (for L1 proposal)
        dataframe[f'ema_long_filter'] = ta.EMA(dataframe, timeperiod=50) 
        # For M2 proposal: Slope of the 1h EMA filter
        dataframe[f'ema_long_filter_slope'] = ta.LINEARREG_SLOPE(dataframe[f'ema_long_filter'], timeperiod=5) # 5-period slope of EMA50

        return dataframe

    # --- 主指标计算 (15m) ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several indicators to the main timeframe (15m) dataframe.
        Informative indicators from 1h are automatically merged by Freqtrade.
        """
        pair = metadata['pair']
        if pair not in self.custom_info:
            return dataframe
        pair_settings = self.custom_info[pair]

        # SuperTrend (15m - 执行周期)
        supertrend_short = self.calculate_supertrend(dataframe.copy(), period=pair_settings['st_short_atr_period'], multiplier=pair_settings['st_short_multiplier'])
        dataframe['st_short'] = supertrend_short['ST']
        dataframe['stx_short'] = supertrend_short['STX']

        # KAMA (15m - 执行周期)
        dataframe['kama_short'] = ta.KAMA(dataframe, timeperiod=pair_settings['kama_short_period'])
        dataframe['kama_short_slope'] = ta.LINEARREG_SLOPE(dataframe['kama_short'], timeperiod=pair_settings['kama_short_slope_period'])

        # ATR (15m - 用于仓位和止损)
        dataframe['atr_pos_size'] = ta.ATR(dataframe, timeperiod=pair_settings['atr_period_pos_size'])

        # --- 新增15m MACD ---
        # macd = ta.MACD(dataframe, fastperiod=self.macd_fast_period.value, slowperiod=self.macd_slow_period.value, signalperiod=self.macd_signal_period.value)
        # dataframe['macd_15m'] = macd['macd']
        # dataframe['macdsignal_15m'] = macd['macdsignal']
        # dataframe['macdhist_15m'] = macd['macdhist']

        # --- 新增15m RSI ---
        # dataframe['rsi_15m'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)

        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        return dataframe

    # --- 入场信号逻辑 ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        if pair not in self.custom_info:
            return dataframe
        pair_settings = self.custom_info[pair]

        # --- 1. 检查必需的1h informative列 ---
        required_cols_1h = [
            f'stx_long_1h', f'kama_long_slope_1h',
            f'ema_long_filter_1h', f'close_1h', f'ema_long_filter_slope_1h'
        ]
        # --- 2. 检查必需的15m执行周期列 ---
        required_cols_15m = [
            'stx_short', 'kama_short_slope', 
            # 'macd_15m', 'macdsignal_15m', 'rsi_15m', # REMOVED
            'volume', 'close' # volume 和 close 也是常用的
        ]

        if not all(col in dataframe.columns for col in required_cols_1h):
            # logger.warning(f"Missing 1h informative columns for pair {metadata['pair']} in entry. Skipping.")
            return dataframe
        if not all(col in dataframe.columns for col in required_cols_15m):
            # logger.warning(f"Missing 15m indicator columns for pair {metadata['pair']} in entry. Skipping.")
            return dataframe

        # --- 3. 定义1小时主趋势条件 ---
        long_condition_1h_strong_trend_up = (
            (dataframe[f'stx_long_1h'] == 'up') &
            (dataframe[f'kama_long_slope_1h'] > self.kama_long_slope_threshold) &
            (dataframe[f'close_1h'] > dataframe[f'ema_long_filter_1h']) &
            (dataframe[f'ema_long_filter_slope_1h'] > 0)
        )
        short_condition_1h_strong_trend_down = (
            (dataframe[f'stx_long_1h'] == 'down') &
            (dataframe[f'kama_long_slope_1h'] < -self.kama_long_slope_threshold) &
            (dataframe[f'close_1h'] < dataframe[f'ema_long_filter_1h']) &
            (dataframe[f'ema_long_filter_slope_1h'] < 0)
        )

        # --- 4. 定义15分钟原始信号条件 ---
        # 做多原始信号 (15m)
        raw_15m_long_signal_conditions = (
            (dataframe['stx_short'] == 'up') &
            (dataframe['stx_short'].shift(1) == 'down') &                                  # 主触发：15m ST 刚刚向上反转
            (dataframe['kama_short_slope'] > pair_settings['kama_short_slope_threshold_entry']) & # 确认：15m KAMA 斜率向上
            # (dataframe['macd_15m'] > dataframe['macdsignal_15m']) &                         # 确认：15m MACD 在信号线上方 (不再要求刚刚金叉) # REMOVED
            # (dataframe['rsi_15m'] > self.rsi_buy_enter_threshold.value) &                                                   # 确认：15m RSI 强势区域 # REMOVED
            (dataframe['volume'] > 0) # 基础成交量过滤
        )
        # 做空原始信号 (15m)
        raw_15m_short_signal_conditions = (
            (dataframe['stx_short'] == 'down') &
            (dataframe['stx_short'].shift(1) == 'up') &                                    # 主触发：15m ST 刚刚向下反转
            (dataframe['kama_short_slope'] < -pair_settings['kama_short_slope_threshold_entry']) & # 确认：15m KAMA 斜率向下
            # (dataframe['macd_15m'] < dataframe['macdsignal_15m']) &                         # 确认：15m MACD 在信号线下方 (不再要求刚刚死叉) # REMOVED
            # (dataframe['rsi_15m'] < self.rsi_sell_enter_threshold.value) &                                                   # 确认：15m RSI 弱势区域 # REMOVED
            (dataframe['volume'] > 0) # 基础成交量过滤
        )

        # --- 5. 标记1小时主趋势活跃状态 ---
        dataframe['long_1h_trend_active'] = long_condition_1h_strong_trend_up
        dataframe['short_1h_trend_active'] = short_condition_1h_strong_trend_down

        # --- 6. 为1小时主趋势的每个连续阶段创建唯一ID ---
        # 当1h趋势状态改变时，累加值增加1，从而为每个趋势阶段创建ID
        dataframe['long_1h_trend_phase_id'] = (dataframe['long_1h_trend_active'] != dataframe['long_1h_trend_active'].shift(1, fill_value=False)).cumsum()
        dataframe['short_1h_trend_phase_id'] = (dataframe['short_1h_trend_active'] != dataframe['short_1h_trend_active'].shift(1, fill_value=False)).cumsum()

        # --- 7. 识别潜在的首次信号 (15m信号在1h趋势激活时) ---
        potential_first_long_signals = dataframe['long_1h_trend_active'] & raw_15m_long_signal_conditions
        potential_first_short_signals = dataframe['short_1h_trend_active'] & raw_15m_short_signal_conditions
        
        # --- 8. 确定实际的首次信号 (在每个1h趋势阶段中，只取第一个15m原始信号) ---
        # 对每个long_1h_trend_phase_id分组，计算potential_first_long_signals的累加和，
        # 只有当累加和为1时（即该阶段的第一个信号），is_actual_first_long_in_1h_phase才为True。
        dataframe['is_actual_first_long_in_1h_phase'] = potential_first_long_signals & \
                                                     (potential_first_long_signals.groupby(dataframe['long_1h_trend_phase_id']).cumsum() == 1)
        
        dataframe['is_actual_first_short_in_1h_phase'] = potential_first_short_signals & \
                                                      (potential_first_short_signals.groupby(dataframe['short_1h_trend_phase_id']).cumsum() == 1)

        # --- 9. 设置入场信号 ---
        dataframe.loc[dataframe['is_actual_first_long_in_1h_phase'], 'enter_long'] = 1
        
        if self.can_short:
            dataframe.loc[dataframe['is_actual_first_short_in_1h_phase'], 'enter_short'] = 1
            
        # 可选：移除辅助列以减少内存占用，如果后续绘图或调试不需要它们
        # dataframe.drop(columns=['long_1h_trend_active', 'short_1h_trend_active', 
        #                         'long_1h_trend_phase_id', 'short_1h_trend_phase_id',
        #                         'is_actual_first_long_in_1h_phase', 'is_actual_first_short_in_1h_phase'], inplace=True, errors='ignore')

        return dataframe

    # --- 出场信号逻辑 ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        required_cols_1h = [f'stx_long_1h']
        # 确保kama_short和close在15m所需列中
        required_cols_15m = [
            'stx_short', 'kama_short', 'close'
            # 'macd_15m', 'macdsignal_15m', 'rsi_15m' # REMOVED
        ]

        if not all(col in dataframe.columns for col in required_cols_1h):
            dataframe['exit_long'] = 0
            dataframe['exit_short'] = 0
            return dataframe
        
        if not all(col in dataframe.columns for col in required_cols_15m):
            dataframe['exit_long'] = 0
            dataframe['exit_short'] = 0
            # logger.warning(f"Missing 15m indicator columns for pair {metadata['pair']} in exit. Columns: {dataframe.columns}. Required: {required_cols_15m}")
            return dataframe

        if 'exit_long' not in dataframe.columns:
            dataframe['exit_long'] = 0
        if 'exit_short' not in dataframe.columns:
            dataframe['exit_short'] = 0

        # --- 做多出场条件 ---
        exit_long_cond_1h_st_flip = (
            (dataframe[f'stx_long_1h'] == 'down') &
            (dataframe[f'stx_long_1h'].shift(1) == 'up')
        )
        exit_long_cond_15m_st_flip = (dataframe['stx_short'] == 'down') & (dataframe['stx_short'].shift(1) == 'up')
        
        # 更严格的15m MACD 和 RSI 联合退出条件
        # exit_long_cond_15m_macd_rsi_confirm = ( # REMOVED
        #     (dataframe['macd_15m'] < dataframe['macdsignal_15m']) & # MACD 死叉 # REMOVED
        #     (dataframe['macd_15m'].shift(1) >= dataframe['macdsignal_15m'].shift(1)) & # 确认为刚刚发生的死叉 # REMOVED
        #     (dataframe['rsi_15m'] < self.rsi_exit_long_threshold.value) # RSI 进入弱势区 # REMOVED
        # ) # REMOVED

        # 新增：价格下穿15m KAMA线作为出场条件
        exit_long_cond_kama_cross_down = (
            (dataframe['close'] < dataframe['kama_short']) &
            (dataframe['close'].shift(1) >= dataframe['kama_short'].shift(1)) # 刚刚下穿
        )

        dataframe.loc[
            exit_long_cond_1h_st_flip | 
            exit_long_cond_15m_st_flip |
            # exit_long_cond_15m_macd_rsi_confirm | # REMOVED
            exit_long_cond_kama_cross_down, # 添加KAMA出场条件
            'exit_long'] = 1
        
        # --- 做空出场条件 ---
        if self.can_short:
            exit_short_cond_1h_st_flip = (
                (dataframe[f'stx_long_1h'] == 'up') &
                (dataframe[f'stx_long_1h'].shift(1) == 'down')
            )
            exit_short_cond_15m_st_flip = (dataframe['stx_short'] == 'up') & (dataframe['stx_short'].shift(1) == 'down')

            # 更严格的15m MACD 和 RSI 联合退出条件
            # exit_short_cond_15m_macd_rsi_confirm = ( # REMOVED
            #     (dataframe['macd_15m'] > dataframe['macdsignal_15m']) & # MACD 金叉 # REMOVED
            #     (dataframe['macd_15m'].shift(1) <= dataframe['macdsignal_15m'].shift(1)) & # 确认为刚刚发生的金叉 # REMOVED
            #     (dataframe['rsi_15m'] > self.rsi_exit_short_threshold.value) # RSI 进入强势区 # REMOVED
            # ) # REMOVED

            # 新增：价格上穿15m KAMA线作为出场条件
            exit_short_cond_kama_cross_up = (
                (dataframe['close'] > dataframe['kama_short']) &
                (dataframe['close'].shift(1) <= dataframe['kama_short'].shift(1)) # 刚刚上穿
            )
            
            dataframe.loc[
                exit_short_cond_1h_st_flip |
                exit_short_cond_15m_st_flip |
                # exit_short_cond_15m_macd_rsi_confirm | # REMOVED
                exit_short_cond_kama_cross_up, # 添加KAMA出场条件
                'exit_short'] = 1
            
        return dataframe

    # --- 动态仓位管理 ---
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            entry_tag: Optional[str], **kwargs) -> float:
        
        if pair not in self.custom_info:
            return proposed_stake
        pair_settings = self.custom_info[pair]
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return proposed_stake
        
        last_candle = dataframe.iloc[-1]
        
        atr_value = last_candle.get('atr_pos_size') # Use .get for safety

        if atr_value is None or atr_value == 0 or pair_settings['atr_multiplier_pos_size'] == 0:
            logger.warning(f"ATR for position size is 0 or None for {pair} at {current_time}. Using proposed_stake: {proposed_stake}")
            return proposed_stake
        
        try:
            current_balance = self.wallets.get_total_stake_amount()
        except Exception as e:
            logger.warning(f"Could not get total stake amount: {e}. Defaulting to a calculated balance if possible or proposed_stake.")
            # Fallback if wallet info is not available (e.g. during plotting or some backtest modes)
            current_balance = proposed_stake / (pair_settings['risk_per_trade'] if pair_settings['risk_per_trade'] > 0 else 0.01) # Estimate total equity

        if current_balance <= 0: # Ensure current_balance is positive
             logger.warning(f"Current balance is {current_balance}. Using proposed_stake.")
             return proposed_stake

        risk_amount_per_trade = current_balance * pair_settings['risk_per_trade']
        
        stop_loss_distance = atr_value * pair_settings['atr_multiplier_pos_size'] # This is in quote currency if ATR is from quote asset
        
        if stop_loss_distance == 0:
            logger.warning(f"Stop loss distance is 0 for {pair} (ATR or multiplier is zero). Using proposed_stake.")
            return proposed_stake
            
        position_size_in_asset = risk_amount_per_trade / stop_loss_distance
        
        calculated_stake = position_size_in_asset * current_rate
        
        final_stake = max(min_stake, calculated_stake)
        final_stake = min(max_stake, final_stake)
        
        # logger.info(f"Pair: {pair}, Rate: {current_rate}, ATR: {atr_value:.4f}, SL Dist: {stop_loss_distance:.4f}, Risk Amt: {risk_amount_per_trade:.2f}, Calc Stake: {calculated_stake:.2f}, Final Stake: {final_stake:.2f}")

        return final_stake

    # --- Freqtrade 回调函数 ---
    def version(self) -> str:
        """
        Returns strategy version.
        """
        return "TRDV15" # Incremented version

    def bot_loop_start(self, **kwargs) -> None:
        """
        Called at the start of the bot iteration (one loop).
        """
        # For informative decorator, the suffix is automatically determined by Freqtrade
        # self.info_timeframe_suffix = timeframe_to_minutes(self.info_timeframe) # Not needed like this
        logger.info(f"Starting Loop for {self.version()} on {self.timeframe} timeframe. ")
        return super().bot_loop_start(**kwargs)

    # --- 绘图配置 (可选) ---
    plot_config = {
        'main_plot': {
            'st_short': {'color': 'blue', 'type': 'line'},
            'st_long_1h': {'color': 'purple', 'type': 'line'}, 
            'kama_short': {'color': 'green'},
            'kama_long_1h': {'color': 'orange'},
            'ema_long_filter_1h': {'color': 'cyan', 'type': 'line'},
        },
        'subplots': {
            "ATR": {
                'atr_pos_size': {'color': 'brown'},
            },
            "KAMA_SLOPE":{
                'kama_short_slope': {'color': 'red'},
                'kama_long_slope_1h': {'color': 'magenta'}
            },
            "STX_1H": { # Example for visualizing 1h trend direction
                 'stx_long_1h': {'color': 'black', 'type': 'bar'} # May need conversion to 1 for up, -1 for down
            },
            "EMA_SLOPE_1H": { 'ema_long_filter_slope_1h': {'color': 'orange'} }
            # , # REMOVED
            # "MACD_15M": { # REMOVED
            #     'macd_15m': {'color': 'blue', 'type': 'line'}, # REMOVED
            #     'macdsignal_15m': {'color': 'orange', 'type': 'line'}, # REMOVED
            #     'macdhist_15m': {'color': 'green', 'type': 'bar'} # REMOVED
            # }, # REMOVED
            # "RSI_15M": { # REMOVED
            #     'rsi_15m': {'color': 'purple', 'type': 'line'} # REMOVED
            # } # REMOVED
        }
    }
