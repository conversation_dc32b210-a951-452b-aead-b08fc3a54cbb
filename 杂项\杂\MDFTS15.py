# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, CategoricalParameter, DecimalParameter, IntParameter, RealParameter, merge_informative_pair
from pandas import DataFrame
import talib.abstract as ta
import pandas_ta as pta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np # For NaN handling if necessary
from datetime import datetime, timedelta
from freqtrade.persistence import Trade
import re
from typing import Optional


# --- Strategy Namespace ---
class MDFTS15(IStrategy):
    """
    MDFTS Re-engineered: No SuperTrend, KAMA-driven with Kelly Sizing.
    """

    # Strategy interface version - allow new iterations of the strategy interface.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy
    timeframe = '15m'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI is not used, exits are defined by custom_exit
    minimal_roi = {
    }

    # Stoploss is defined by custom_stoploss
    stoploss = -0.99 # Set high as a fallback

    # Trailing stoploss is handled by custom_stoploss
    trailing_stop = False

    # --- Custom Exit Configuration ---
    use_custom_exit = True
    
    # --- Custom Stoploss Configuration ---
    use_custom_stoploss = True

    # --- Hyperoptable Parameters ---

    # Long-term trend filter
    ema_long_term_period = IntParameter(150, 250, default=200, space="buy", optimize=True)

    # KAMA Parameters
    buy_kama_period = IntParameter(5, 20, default=10, space="buy", optimize=True)
    sell_kama_period = IntParameter(5, 20, default=10, space="sell", optimize=True)

    # KAMA Slope Parameters
    buy_kama_slope_n_periods = IntParameter(2, 8, default=4, space="buy", optimize=True)
    buy_kama_slope_threshold_bullish = RealParameter(0.00001, 0.0005, default=0.0001, space="buy", optimize=True)
    
    sell_kama_slope_n_periods = IntParameter(2, 8, default=4, space="sell", optimize=True)
    sell_kama_slope_threshold_bearish = RealParameter(-0.0005, -0.00001, default=-0.0001, space="sell", optimize=True)

    # ADX/DMI Parameters
    buy_adx_period = IntParameter(10, 30, default=20, space="buy", optimize=True)
    buy_adx_trend_threshold = IntParameter(20, 35, default=25, space="buy", optimize=True)
    
    sell_adx_period = IntParameter(10, 30, default=20, space="sell", optimize=True)
    sell_adx_trend_threshold = IntParameter(20, 35, default=25, space="sell", optimize=True)
    
    # OBV Parameters
    buy_obv_sma_period = IntParameter(10, 30, default=20, space="buy", optimize=True)
    sell_obv_sma_period = IntParameter(10, 30, default=20, space="sell", optimize=True)

    # --- EXIT / SL PARAMETERS (SEPARATED FOR LONG/SHORT) ---

    # ATR Parameters for Custom Stoploss
    long_atr_period = IntParameter(5, 20, default=14, space="buy", optimize=True)
    long_atr_multiplier = RealParameter(1.0, 5.0, default=3.0, space="buy", optimize=True)
    short_atr_period = IntParameter(5, 20, default=14, space="sell", optimize=True)
    short_atr_multiplier = RealParameter(1.0, 5.0, default=3.0, space="sell", optimize=True)

    # Risk-Reward Ratio for Take Profit
    long_rr_ratio = RealParameter(1.0, 5.0, default=2.0, space="buy", optimize=True)
    short_rr_ratio = RealParameter(1.0, 5.0, default=2.0, space="sell", optimize=True)

    # Time-based Exit
    # Stop loss if the trade is open for more than this number of candles and is in profit
    max_trade_duration_candles = IntParameter(200, 800, default=480, space="sell", optimize=True) # 480 candles = 5 days on 15m TF

    # --- Kelly Criterion Parameters ---
    # These should be determined from backtesting analysis
    # A conservative starting point.
    kelly_win_rate = RealParameter(0.5, 0.85, default=0.70, space="buy", optimize=False)
    kelly_win_loss_ratio = RealParameter(1.0, 4.0, default=1.8, space="buy", optimize=False)
    # Maximum fraction of portfolio to risk on a single trade.
    kelly_max_risk = RealParameter(0.05, 0.25, default=0.1, space="buy", optimize=False)


    # --- Indicator Populate Functions ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # -- Long-term Trend Filter (EMA) --
        dataframe['ema_long_term'] = pta.ema(dataframe['close'], length=self.ema_long_term_period.value)

        # -- KAMA (Kaufman Adaptive Moving Average) --
        dataframe['kama_buy'] = pta.kama(dataframe['close'], length=self.buy_kama_period.value)
        dataframe['kama_sell'] = pta.kama(dataframe['close'], length=self.sell_kama_period.value)

        # KAMA Slope for Buy signals
        kama_buy_abs_change_n = dataframe['kama_buy'] - dataframe['kama_buy'].shift(self.buy_kama_slope_n_periods.value)
        dataframe['kama_buy_slope'] = kama_buy_abs_change_n / self.buy_kama_slope_n_periods.value
        
        # KAMA Slope for Sell signals (shorting)
        kama_sell_abs_change_n = dataframe['kama_sell'] - dataframe['kama_sell'].shift(self.sell_kama_slope_n_periods.value)
        dataframe['kama_sell_slope'] = kama_sell_abs_change_n / self.sell_kama_slope_n_periods.value

        # -- ADX (Average Directional Index) & DMI (Directional Movement Index) --
        adx_buy_data = pta.adx(dataframe['high'], dataframe['low'], dataframe['close'], length=self.buy_adx_period.value)
        dataframe['adx_buy'] = adx_buy_data[f'ADX_{self.buy_adx_period.value}']
        dataframe['plus_di_buy'] = adx_buy_data[f'DMP_{self.buy_adx_period.value}']
        dataframe['minus_di_buy'] = adx_buy_data[f'DMN_{self.buy_adx_period.value}']
        
        adx_sell_data = pta.adx(dataframe['high'], dataframe['low'], dataframe['close'], length=self.sell_adx_period.value)
        dataframe['adx_sell'] = adx_sell_data[f'ADX_{self.sell_adx_period.value}']
        dataframe['plus_di_sell'] = adx_sell_data[f'DMP_{self.sell_adx_period.value}']
        dataframe['minus_di_sell'] = adx_sell_data[f'DMN_{self.sell_adx_period.value}']

        # -- OBV (On-Balance Volume) --
        dataframe['obv'] = pta.obv(dataframe['close'], dataframe['volume'])
        dataframe['obv_sma_buy'] = pta.sma(dataframe['obv'], length=self.buy_obv_sma_period.value)
        dataframe['obv_sma_sell'] = pta.sma(dataframe['obv'], length=self.sell_obv_sma_period.value)
        
        # -- ATR for custom stoploss (separated for long/short) --
        dataframe['atr_long'] = pta.atr(dataframe['high'], dataframe['low'], dataframe['close'], 
                                   length=self.long_atr_period.value)
        dataframe['atr_short'] = pta.atr(dataframe['high'], dataframe['low'], dataframe['close'], 
                                   length=self.short_atr_period.value)
        return dataframe

    # --- Buy (Long) Logic ---
    def populate_buy_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = []
        # Main Trend Filter: Price should be above the long-term EMA
        conditions.append(dataframe['close'] > dataframe['ema_long_term'])
        # Trend Confirmation: KAMA slope is positive and strong
        conditions.append(dataframe['kama_buy_slope'] > self.buy_kama_slope_threshold_bullish.value)
        # Trend Strength: ADX indicates a strong trend
        conditions.append(dataframe['adx_buy'] > self.buy_adx_trend_threshold.value)
        # Bullish Momentum: +DI is above -DI
        conditions.append(dataframe['plus_di_buy'] > dataframe['minus_di_buy'])
        # Volume Confirmation: OBV is above its moving average
        conditions.append(dataframe['obv'] > dataframe['obv_sma_buy'])
        # Entry Trigger: Price crosses above the KAMA line
        conditions.append(qtpylib.crossed_above(dataframe['close'], dataframe['kama_buy']))

        dataframe['buy'] = 0
        if conditions:
            combined_conditions = conditions[0]
            for i in range(1, len(conditions)):
                combined_conditions &= conditions[i]
            dataframe.loc[combined_conditions, 'buy'] = 1
            
        return dataframe

    # --- Sell (Exit Long) Logic ---
    def populate_sell_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # This method is intentionally left blank.
        # Exits are handled by custom_exit and custom_stoploss.
        dataframe['sell'] = 0
        return dataframe

    # --- Short Entry Logic ---
    def populate_short_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = []
        # Main Trend Filter: Price should be below the long-term EMA
        conditions.append(dataframe['close'] < dataframe['ema_long_term'])
        # Trend Confirmation: KAMA slope is negative and strong
        conditions.append(dataframe['kama_sell_slope'] < self.sell_kama_slope_threshold_bearish.value)
        # Trend Strength: ADX indicates a strong trend
        conditions.append(dataframe['adx_sell'] > self.sell_adx_trend_threshold.value)
        # Bearish Momentum: -DI is above +DI
        conditions.append(dataframe['minus_di_sell'] > dataframe['plus_di_sell'])
        # Volume Confirmation: OBV is below its moving average
        conditions.append(dataframe['obv'] < dataframe['obv_sma_sell'])
        # Entry Trigger: Price crosses below the KAMA line
        conditions.append(qtpylib.crossed_below(dataframe['close'], dataframe['kama_sell']))
        
        dataframe['short'] = 0
        if conditions:
            combined_conditions = conditions[0]
            for i in range(1, len(conditions)):
                combined_conditions &= conditions[i]
            dataframe.loc[combined_conditions, 'short'] = 1
            
        return dataframe

    # --- Exit Short Logic ---
    def populate_exit_short_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # This method is intentionally left blank.
        # Exits are handled by custom_exit and custom_stoploss.
        dataframe['exit_short'] = 0
        return dataframe

    # --- Compatibility shims for older Freqtrade versions ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        兼容性方法: 统一处理入场信号。
        """
        dataframe = self.populate_buy_trend(dataframe, metadata)
        if self.can_short:
            dataframe = self.populate_short_trend(dataframe, metadata)

        # Ensure 'enter_long' and 'enter_short' columns exist for compatibility
        if 'buy' in dataframe.columns:
            dataframe['enter_long'] = dataframe['buy']
        else:
            dataframe['enter_long'] = 0
            
        if self.can_short and 'short' in dataframe.columns:
            dataframe['enter_short'] = dataframe['short']
        else:
            dataframe['enter_short'] = 0
            
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        兼容性方法: 统一处理出场信号。
        出场由 custom_exit 和 custom_stoploss 处理，这里仅确保列存在。
        """
        dataframe = self.populate_sell_trend(dataframe, metadata)
        if self.can_short:
            dataframe = self.populate_exit_short_trend(dataframe, metadata)

        # Ensure 'exit_long' and 'exit_short' columns exist for compatibility
        if 'sell' in dataframe.columns:
            dataframe['exit_long'] = dataframe['sell']
        else:
            dataframe['exit_long'] = 0

        if 'exit_short' not in dataframe.columns:
            dataframe['exit_short'] = 0
            
        return dataframe

    # --- Custom Stoploss ---
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                        current_rate: float, current_profit: float, **kwargs) -> float:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return -1

        last_candle = dataframe.iloc[-1].squeeze()
        if not isinstance(last_candle['atr_long'], np.number) or np.isnan(last_candle['atr_long']):
             return -1 # Fallback in case of invalid ATR
        if not isinstance(last_candle['atr_short'], np.number) or np.isnan(last_candle['atr_short']):
             return -1 # Fallback in case of invalid ATR

        if trade.trade_direction == 'long':
            atr_value = last_candle['atr_long'] * self.long_atr_multiplier.value
            # ATR Trailing Stop from high-water mark
            stop_price = trade.max_rate - atr_value
            
            # Get the current stop loss price from the trade object
            current_stop_loss = trade.stop_loss if trade.stop_loss else 0
            
            # The new stop-loss should not be lower than the current one.
            return max(stop_price, current_stop_loss)

        elif trade.trade_direction == 'short':
            atr_value = last_candle['atr_short'] * self.short_atr_multiplier.value
            # ATR Trailing Stop from low-water mark
            stop_price = trade.min_rate + atr_value
            current_stop_loss = trade.stop_loss if trade.stop_loss else float('inf')
            
            # The new stop-loss should not be higher than the current one.
            return min(stop_price, current_stop_loss)

        return -1
        
    # --- Custom Exit Signal ---
    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime',
                    current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        # --- Exit on Trend Weakness (ADX) ---
        # Exit if the trend strength indicator (ADX) falls below the threshold.
        # This suggests the trend is fading and it's time to exit to protect profits or cut losses.
        if trade.trade_direction == 'long':
            if qtpylib.crossed_below(dataframe['adx_buy'], self.buy_adx_trend_threshold.value).iloc[-1]:
                return 'exit_adx_weaken'
        elif trade.trade_direction == 'short':
            if qtpylib.crossed_below(dataframe['adx_sell'], self.sell_adx_trend_threshold.value).iloc[-1]:
                return 'exit_adx_weaken'

        # --- Time-based Exit (REMOVED) ---
        # The original time-based exit for losing trades was found to be highly unprofitable in backtests.
        # It has been removed in favor of the more dynamic ADX-based exit signal.
        # A potential future improvement could be a time-based exit for trades in profit.

        # --- Take Profit based on Risk/Reward Ratio ---
        entry_price = trade.open_rate
        
        try:
            # Find the candle corresponding to the trade's open date
            entry_candle = dataframe.loc[dataframe['date'] < trade.open_date].iloc[-1]
            
            if not entry_candle.empty:
                if trade.trade_direction == 'long':
                    entry_atr = entry_candle['atr_long']
                    rr_ratio = self.long_rr_ratio.value
                    atr_multiplier = self.long_atr_multiplier.value
                else: # short
                    entry_atr = entry_candle['atr_short']
                    rr_ratio = self.short_rr_ratio.value
                    atr_multiplier = self.short_atr_multiplier.value

                if not np.isnan(entry_atr):
                    initial_risk_amount = entry_atr * atr_multiplier
                    
                    if trade.trade_direction == 'long':
                        take_profit_price = entry_price + (initial_risk_amount * rr_ratio)
                        if current_rate >= take_profit_price:
                            return f"take_profit_rr_{rr_ratio}"
                    elif trade.trade_direction == 'short':
                        take_profit_price = entry_price - (initial_risk_amount * rr_ratio)
                        if current_rate <= take_profit_price:
                            return f"take_profit_rr_{rr_ratio}"
        except Exception:
            # Could happen if entry candle is not in the analyzed dataframe anymore
            # Or other pandas errors. Silently pass.
            pass

        # --- Exit based on indicator reversal (REMOVED due to poor performance) ---
        # The following exit signals have been REMOVED based on backtest analysis
        # as they were causing significant losses.
        # last_candle = dataframe.iloc[-1].squeeze()
        # if trade.trade_direction == 'long':
        #     # Bearish crossover on DMI or KAMA turns bearish
        #     if qtpylib.crossed_below(dataframe['plus_di_sell'], dataframe['minus_di_sell']).iloc[-1]:
        #         return "exit_dmi_cross"
        #     if last_candle['kama_sell_slope'] < 0:
        #          return "exit_kama_slope"

        # elif trade.trade_direction == 'short':
        #     # Bullish crossover on DMI or KAMA turns bullish
        #     if qtpylib.crossed_above(dataframe['plus_di_buy'], dataframe['minus_di_buy']).iloc[-1]:
        #          return "exit_dmi_cross"
        #     if last_candle['kama_buy_slope'] > 0:
        #          return "exit_kama_slope"

        return None
        
    # --- Kelly Criterion Position Sizing ---
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            entry_tag: Optional[str], **kwargs) -> float:
        
        win_rate = self.kelly_win_rate.value
        win_loss_ratio = self.kelly_win_loss_ratio.value
        
        if win_loss_ratio == 0:
            return 0.0

        # Kelly Criterion Formula
        # f* = p - (q / R) where p=win_rate, q=1-p, R=win_loss_ratio
        kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)
        
        if kelly_fraction <= 0:
            # According to Kelly, we shouldn't bet if the edge is not in our favor.
            return 0.0

        # Apply a cap to the risk
        # This is often called "Fractional Kelly" and is a prudent risk management approach.
        capped_kelly_fraction = min(kelly_fraction, self.kelly_max_risk.value)

        # Calculate the stake amount based on the wallet balance.
        # `self.wallets` is available in live and dry-run modes.
        if self.wallets:
            wallet_balance = self.wallets.get_free(self.config['stake_currency'])
            if wallet_balance is None: # In case of wallet error
                return proposed_stake

            stake_amount = wallet_balance * capped_kelly_fraction
            
            # Ensure the calculated stake is within the exchange's limits and our config's max_stake.
            # We must not exceed max_stake, and we must be above min_stake if we are to trade.
            stake_amount = min(stake_amount, max_stake)

            if stake_amount < min_stake:
                # If the calculated stake is too small, we don't trade.
                return 0.0
            
            return stake_amount
        else:
            # In backtesting, `self.wallets` is not available. 
            # We fall back to the proposed_stake from the configuration.
            # A more advanced backtest could simulate a wallet balance.
            return proposed_stake


    # --- Plotting Configuration ---
    plot_config = {
        'main_plot': {
            'kama_buy': {'color': 'blue', 'type': 'line'},
            'kama_sell': {'color': 'purple', 'type': 'line'},
            'ema_long_term': {'color': 'goldenrod', 'type': 'line'},
        },
        'subplots': {
            "ATR": {
                'atr_long': {'color': 'violet', 'type': 'line'},
                'atr_short': {'color': 'goldenrod', 'type': 'line'}
            },
            "KAMA_Slope": {
                'kama_buy_slope': {'color': 'blue', 'type': 'line'},
                'kama_sell_slope': {'color': 'purple', 'type': 'line'}
            },
            "ADX/DMI_Buy": {
                'adx_buy': {'color': 'gold', 'type': 'line'},
                'plus_di_buy': {'color': 'green', 'type': 'line'},
                'minus_di_buy': {'color': 'red', 'type': 'line'}
            },
            "ADX/DMI_Sell": {
                'adx_sell': {'color': 'orange', 'type': 'line'},
                'plus_di_sell': {'color': 'lightgreen', 'type': 'line'},
                'minus_di_sell': {'color': 'lightcoral', 'type': 'line'}
            },
            "OBV": {
                'obv': {'color': 'blue', 'type': 'line'},
                'obv_sma_buy': {'color': 'orange', 'type': 'line'},
                'obv_sma_sell': {'color': 'purple', 'type': 'line'}
            }
        }
    }
