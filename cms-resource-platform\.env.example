# ===========================================
# CMS资源管理平台 - 环境变量配置
# ===========================================

# 基础配置
DEBUG=True
SECRET_KEY=your-super-secret-key-change-in-production-environment
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 数据库配置
DATABASE_URL=postgresql://cms_user:cms_password123@localhost:5432/cms_db
REDIS_URL=redis://localhost:6379/0

# 跨域配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com
CORS_ALLOW_CREDENTIALS=True

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 文件存储配置
MEDIA_ROOT=media
STATIC_ROOT=static
MAX_UPLOAD_SIZE=104857600  # 100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,zip,rar,7z,doc,docx,ppt,pptx,xls,xlsx

# CDN配置 (可选)
USE_CDN=False
CDN_URL=https://your-cdn.com
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_STORAGE_BUCKET_NAME=your-bucket

# 邮件配置
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=CMS平台 <<EMAIL>>

# 缓存配置
CACHE_TTL=3600  # 1小时
SESSION_CACHE_ALIAS=default

# 安全配置
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
X_FRAME_OPTIONS=DENY
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# API限流配置
THROTTLE_ANON_RATE=100/hour
THROTTLE_USER_RATE=1000/hour
THROTTLE_LOGIN_RATE=5/minute

# 支付配置
PAYMENT_GATEWAY=alipay  # alipay, wechat, stripe
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key

# 微信支付配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_MCH_ID=your-wechat-mch-id
WECHAT_API_KEY=your-wechat-api-key

# 积分系统配置
DEFAULT_SIGNUP_POINTS=100
DAILY_SIGNIN_POINTS=10
INVITE_USER_POINTS=50
DOWNLOAD_COST_POINTS=10

# 会员系统配置
VIP_MONTHLY_PRICE=29.9
VIP_YEARLY_PRICE=299.9
VIP_DOWNLOAD_LIMIT=1000
FREE_DOWNLOAD_LIMIT=10

# 文件安全配置
ENABLE_DOWNLOAD_PROTECTION=True
DOWNLOAD_TOKEN_EXPIRE_MINUTES=5
MAX_DOWNLOAD_ATTEMPTS=3
ENABLE_WATERMARK=True

# 防爬虫配置
ENABLE_RATE_LIMITING=True
ENABLE_CAPTCHA=True
CAPTCHA_DIFFICULTY=medium
BLOCK_SUSPICIOUS_IPS=True

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/cms.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=Asia/Shanghai

# 监控配置
ENABLE_MONITORING=True
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_MEDIA_URL=http://localhost:8000/media
NEXT_PUBLIC_SITE_NAME=CMS资源平台
NEXT_PUBLIC_SITE_DESCRIPTION=专业的图文资源管理平台
NEXT_PUBLIC_ENABLE_PWA=True

# 开发环境配置
DJANGO_SETTINGS_MODULE=config.settings.development
NODE_ENV=development
