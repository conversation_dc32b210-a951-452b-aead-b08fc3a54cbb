'use client'

import { Layout, Row, Col, Space, Divider, Typography } from 'antd'
import { 
  GithubOutlined, 
  TwitterOutlined, 
  WechatOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined
} from '@ant-design/icons'
import Link from 'next/link'

const { Footer: AntFooter } = Layout
const { Title, Text } = Typography

const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <AntFooter className="bg-gray-900 text-gray-300 mt-auto">
      <div className="max-w-7xl mx-auto px-4 lg:px-8 py-12">
        <Row gutter={[32, 32]}>
          {/* 品牌信息 */}
          <Col xs={24} sm={12} md={6}>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">C</span>
                </div>
                <span className="text-xl font-bold text-white">
                  CMS资源平台
                </span>
              </div>
              <Text className="text-gray-400 text-sm block">
                专业的图文资源管理平台，为创作者提供优质的设计素材和便捷的管理工具。
              </Text>
              <Space size="large">
                <GithubOutlined className="text-gray-400 hover:text-white cursor-pointer text-lg transition-colors" />
                <TwitterOutlined className="text-gray-400 hover:text-white cursor-pointer text-lg transition-colors" />
                <WechatOutlined className="text-gray-400 hover:text-white cursor-pointer text-lg transition-colors" />
                <MailOutlined className="text-gray-400 hover:text-white cursor-pointer text-lg transition-colors" />
              </Space>
            </div>
          </Col>

          {/* 产品服务 */}
          <Col xs={12} sm={6} md={4}>
            <div className="space-y-3">
              <Title level={5} className="text-white font-semibold">产品服务</Title>
              <div className="space-y-2 text-sm">
                <Link href="/resources" className="text-gray-400 hover:text-white block transition-colors">
                  资源库
                </Link>
                <Link href="/categories" className="text-gray-400 hover:text-white block transition-colors">
                  分类浏览
                </Link>
                <Link href="/upload" className="text-gray-400 hover:text-white block transition-colors">
                  上传资源
                </Link>
                <Link href="/vip" className="text-gray-400 hover:text-white block transition-colors">
                  VIP会员
                </Link>
              </div>
            </div>
          </Col>

          {/* 用户中心 */}
          <Col xs={12} sm={6} md={4}>
            <div className="space-y-3">
              <Title level={5} className="text-white font-semibold">用户中心</Title>
              <div className="space-y-2 text-sm">
                <Link href="/profile" className="text-gray-400 hover:text-white block transition-colors">
                  个人中心
                </Link>
                <Link href="/my-resources" className="text-gray-400 hover:text-white block transition-colors">
                  我的资源
                </Link>
                <Link href="/favorites" className="text-gray-400 hover:text-white block transition-colors">
                  我的收藏
                </Link>
                <Link href="/points" className="text-gray-400 hover:text-white block transition-colors">
                  积分中心
                </Link>
              </div>
            </div>
          </Col>

          {/* 帮助支持 */}
          <Col xs={12} sm={6} md={4}>
            <div className="space-y-3">
              <Title level={5} className="text-white font-semibold">帮助支持</Title>
              <div className="space-y-2 text-sm">
                <Link href="/help" className="text-gray-400 hover:text-white block transition-colors">
                  使用帮助
                </Link>
                <Link href="/faq" className="text-gray-400 hover:text-white block transition-colors">
                  常见问题
                </Link>
                <Link href="/contact" className="text-gray-400 hover:text-white block transition-colors">
                  联系我们
                </Link>
                <Link href="/feedback" className="text-gray-400 hover:text-white block transition-colors">
                  意见反馈
                </Link>
              </div>
            </div>
          </Col>

          {/* 联系信息 */}
          <Col xs={24} sm={12} md={6}>
            <div className="space-y-3">
              <Title level={5} className="text-white font-semibold">联系我们</Title>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2 text-gray-400">
                  <MailOutlined />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2 text-gray-400">
                  <PhoneOutlined />
                  <span>************</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-400">
                  <EnvironmentOutlined />
                  <span>北京市朝阳区xxx大厦</span>
                </div>
              </div>
              
              {/* 工作时间 */}
              <div className="mt-4 p-3 bg-gray-800 rounded-lg">
                <Text className="text-gray-300 text-xs block">
                  <strong>客服时间：</strong><br />
                  周一至周五 9:00-18:00<br />
                  周六至周日 10:00-17:00
                </Text>
              </div>
            </div>
          </Col>
        </Row>

        <Divider className="border-gray-700 my-8" />

        {/* 底部信息 */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-400">
            <span>© {currentYear} CMS资源平台. All rights reserved.</span>
            <div className="flex space-x-4">
              <Link href="/privacy" className="hover:text-white transition-colors">
                隐私政策
              </Link>
              <Link href="/terms" className="hover:text-white transition-colors">
                服务条款
              </Link>
              <Link href="/copyright" className="hover:text-white transition-colors">
                版权声明
              </Link>
            </div>
          </div>
          
          <div className="text-sm text-gray-400">
            <span>ICP备案号：京ICP备12345678号</span>
          </div>
        </div>
      </div>
    </AntFooter>
  )
}

export default Footer
