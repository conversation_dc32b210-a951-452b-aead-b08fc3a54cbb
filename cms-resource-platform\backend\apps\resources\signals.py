"""
资源系统信号处理器
"""
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.utils import timezone
from django.utils.text import slugify
from .models import Resource, ResourceRating, ResourceComment
import os


@receiver(pre_save, sender=Resource)
def generate_slug(sender, instance, **kwargs):
    """自动生成URL别名"""
    if not instance.slug:
        instance.slug = slugify(instance.title)
        
        # 确保slug唯一
        original_slug = instance.slug
        counter = 1
        while Resource.objects.filter(slug=instance.slug).exclude(pk=instance.pk).exists():
            instance.slug = f"{original_slug}-{counter}"
            counter += 1


@receiver(pre_save, sender=Resource)
def set_published_at(sender, instance, **kwargs):
    """设置发布时间"""
    if instance.pk:
        old_instance = Resource.objects.get(pk=instance.pk)
        if old_instance.status != 'published' and instance.status == 'published':
            instance.published_at = timezone.now()


@receiver(post_save, sender=Resource)
def update_file_info(sender, instance, created, **kwargs):
    """更新文件信息"""
    if created and instance.resource_file:
        # 获取文件大小
        if hasattr(instance.resource_file, 'size'):
            instance.file_size = instance.resource_file.size
        
        # 获取文件类型和格式
        if hasattr(instance.resource_file, 'name'):
            filename = instance.resource_file.name
            instance.file_format = os.path.splitext(filename)[1][1:].lower()
            
            # 根据扩展名判断文件类型
            image_formats = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            video_formats = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
            audio_formats = ['mp3', 'wav', 'flac', 'aac', 'ogg']
            document_formats = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
            archive_formats = ['zip', 'rar', '7z', 'tar', 'gz']
            
            if instance.file_format in image_formats:
                instance.file_type = 'image'
            elif instance.file_format in video_formats:
                instance.file_type = 'video'
            elif instance.file_format in audio_formats:
                instance.file_type = 'audio'
            elif instance.file_format in document_formats:
                instance.file_type = 'document'
            elif instance.file_format in archive_formats:
                instance.file_type = 'archive'
            else:
                instance.file_type = 'other'
        
        # 保存更新
        Resource.objects.filter(pk=instance.pk).update(
            file_size=instance.file_size,
            file_type=instance.file_type,
            file_format=instance.file_format
        )


@receiver(post_save, sender=ResourceRating)
def update_resource_rating(sender, instance, created, **kwargs):
    """更新资源评分"""
    if created:
        resource = instance.resource
        ratings = ResourceRating.objects.filter(resource=resource)
        
        # 计算平均评分
        total_score = sum(rating.score for rating in ratings)
        avg_score = total_score / ratings.count()
        
        # 更新资源评分信息
        resource.rating_score = round(avg_score, 2)
        resource.rating_count = ratings.count()
        resource.save(update_fields=['rating_score', 'rating_count'])


@receiver(post_delete, sender=ResourceRating)
def update_resource_rating_on_delete(sender, instance, **kwargs):
    """删除评分时更新资源评分"""
    resource = instance.resource
    ratings = ResourceRating.objects.filter(resource=resource)
    
    if ratings.exists():
        total_score = sum(rating.score for rating in ratings)
        avg_score = total_score / ratings.count()
        resource.rating_score = round(avg_score, 2)
        resource.rating_count = ratings.count()
    else:
        resource.rating_score = 0
        resource.rating_count = 0
    
    resource.save(update_fields=['rating_score', 'rating_count'])


@receiver(post_delete, sender=Resource)
def delete_resource_files(sender, instance, **kwargs):
    """删除资源时清理文件"""
    # 删除资源文件
    if instance.resource_file:
        if os.path.isfile(instance.resource_file.path):
            os.remove(instance.resource_file.path)
    
    # 删除封面图片
    if instance.cover_image:
        if os.path.isfile(instance.cover_image.path):
            os.remove(instance.cover_image.path)
