version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: cms-postgres
    environment:
      POSTGRES_DB: cms_db
      POSTGRES_USER: cms_user
      POSTGRES_PASSWORD: cms_password123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cms-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: cms-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cms-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Django 后端
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: cms-backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=***************************************************/cms_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - media_files:/app/media
      - static_files:/app/static
    networks:
      - cms-network
    restart: unless-stopped
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  # Celery 异步任务
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cms-celery
    environment:
      - DEBUG=True
      - DATABASE_URL=***************************************************/cms_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - media_files:/app/media
    networks:
      - cms-network
    restart: unless-stopped
    command: celery -A config worker --loglevel=info

  # Next.js 前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cms-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_MEDIA_URL=http://localhost:8000/media
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - cms-network
    restart: unless-stopped
    command: npm run dev

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: cms-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - media_files:/var/www/media
      - static_files:/var/www/static
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - cms-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  media_files:
  static_files:

networks:
  cms-network:
    driver: bridge
