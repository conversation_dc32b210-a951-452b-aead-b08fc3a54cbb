import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import Cookies from 'js-cookie'
import { authAPI } from '@/lib/api'

export interface User {
  id: number
  username: string
  email: string
  first_name?: string
  last_name?: string
  nickname?: string
  avatar?: string
  bio?: string
  phone?: string
  birthday?: string
  gender?: string
  user_group?: any
  is_verified: boolean
  is_vip: boolean
  vip_expire_date?: string
  points: number
  total_points_earned: number
  total_points_spent: number
  download_count: number
  upload_count: number
  login_count: number
  date_joined: string
  last_login?: string
  display_name: string
  is_vip_active: boolean
  profile?: any
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (userData: any) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
  refreshUserData: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        try {
          const response = await authAPI.login({ email, password })
          const { user, tokens } = response.data
          
          // 保存token到cookie
          Cookies.set('token', tokens.access, { expires: 7 })
          Cookies.set('refresh_token', tokens.refresh, { expires: 30 })
          
          set({
            user,
            token: tokens.access,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      register: async (userData: any) => {
        set({ isLoading: true })
        try {
          const response = await authAPI.register(userData)
          const { user, tokens } = response.data
          
          // 保存token到cookie
          Cookies.set('token', tokens.access, { expires: 7 })
          Cookies.set('refresh_token', tokens.refresh, { expires: 30 })
          
          set({
            user,
            token: tokens.access,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        // 清除token
        Cookies.remove('token')
        Cookies.remove('refresh_token')
        
        // 调用登出API
        authAPI.logout().catch(() => {
          // 忽略错误，因为可能token已经失效
        })
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
      },

      checkAuth: async () => {
        const token = Cookies.get('token')
        if (!token) {
          set({ isAuthenticated: false })
          return
        }

        try {
          const response = await authAPI.getMe()
          const user = response.data
          
          set({
            user,
            token,
            isAuthenticated: true,
          })
        } catch (error) {
          // Token无效，尝试刷新
          const refreshToken = Cookies.get('refresh_token')
          if (refreshToken) {
            try {
              const refreshResponse = await authAPI.refreshToken(refreshToken)
              const newToken = refreshResponse.data.access
              
              Cookies.set('token', newToken, { expires: 7 })
              
              // 重新获取用户信息
              const userResponse = await authAPI.getMe()
              const user = userResponse.data
              
              set({
                user,
                token: newToken,
                isAuthenticated: true,
              })
            } catch (refreshError) {
              // 刷新失败，清除认证状态
              Cookies.remove('token')
              Cookies.remove('refresh_token')
              set({
                user: null,
                token: null,
                isAuthenticated: false,
              })
            }
          } else {
            // 没有刷新token，清除认证状态
            Cookies.remove('token')
            set({
              user: null,
              token: null,
              isAuthenticated: false,
            })
          }
        }
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData },
          })
        }
      },

      refreshUserData: async () => {
        try {
          const response = await authAPI.getMe()
          const user = response.data
          set({ user })
        } catch (error) {
          console.error('Failed to refresh user data:', error)
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
