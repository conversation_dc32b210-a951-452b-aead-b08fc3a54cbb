# 导入必要的库
import freqtrade.vendor.qtpylib.indicators as qtpylib  # 导入qtpylib指标库
import numpy as np  # 导入numpy用于数值计算
from functools import reduce  # 导入reduce函数用于条件组合
import talib.abstract as ta  # 导入talib技术分析库
from freqtrade.strategy.interface import IStrategy  # 导入策略接口基类
from freqtrade.strategy import merge_informative_pair, DecimalParameter, stoploss_from_open, RealParameter,IntParameter,informative  # 导入freqtrade策略相关工具
from pandas import DataFrame, Series  # 导入pandas数据结构
from datetime import datetime  # 导入datetime处理时间
import math  # 导入数学函数
import logging  # 导入日志模块
from freqtrade.persistence import Trade  # 导入Trade类用于交易管理
import pandas_ta as pta  # 导入pandas_ta技术分析库
from technical.indicators import RMI  # 导入RMI指标

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义艾略特波浪振荡器函数
def ewo(dataframe, sma1_length=5, sma2_length=35):
    """
    计算艾略特波浪振荡器
    参数:
        sma1_length: 短期EMA周期
        sma2_length: 长期EMA周期
    """
    sma1 = ta.EMA(dataframe, timeperiod=sma1_length)  # 计算短期EMA
    sma2 = ta.EMA(dataframe, timeperiod=sma2_length)  # 计算长期EMA
    smadif = (sma1 - sma2) / dataframe['close'] * 100  # 计算差值并标准化
    return smadif

# 定义计算DCA(动态成本平均)的百分比变化函数
def top_percent_change_dca(dataframe: DataFrame, length: int) -> float:
    """
    计算当前收盘价与区间最高开盘价的百分比变化
    用于DCA(动态成本平均)策略
    """
    if length == 0:
        return (dataframe['open'] - dataframe['close']) / dataframe['close']
    else:
        return (dataframe['open'].rolling(length).max() - dataframe['close']) / dataframe['close']

# 定义另一个艾略特波浪振荡器实现
def EWO(dataframe, ema_length=5, ema2_length=3):
    """
    另一个艾略特波浪振荡器实现
    使用不同的默认参数
    """
    df = dataframe.copy()  # 复制数据框
    ema1 = ta.EMA(df, timeperiod=ema_length)  # 计算第一个EMA
    ema2 = ta.EMA(df, timeperiod=ema2_length)  # 计算第二个EMA
    emadif = (ema1 - ema2) / df['close'] * 100  # 计算差值并标准化
    return emadif

# 定义威廉斯%R函数
def williams_r(dataframe: DataFrame, period: int = 14) -> Series:
    """
    威廉斯%R指标，用于显示当前收盘价相对于最近N天的高低之间的位置
    用于判断股票或商品市场是否处于高低之间或接近高低
    指标在负范围内，从-100（最低）到0（最高）
    """
    highest_high = dataframe["high"].rolling(center=False, window=period).max()  # 计算最高价
    lowest_low = dataframe["low"].rolling(center=False, window=period).min()  # 计算最低价

    WR = Series(
        (highest_high - dataframe["close"]) / (highest_high - lowest_low),  # 计算威廉指标值
        name="{0} Williams %R".format(period),  # 设置指标名称
    )

    return WR * -100  # 返回负值结果

# 定义VWAP带函数
def VWAPB(dataframe, window_size=20, num_of_std=1):
    """
    计算VWAP(成交量加权平均价格)带
    参数:
        window_size: 窗口大小
        num_of_std: 标准差倍数
    """
    df = dataframe.copy()  # 复制数据框
    df['vwap'] = qtpylib.rolling_vwap(df,window=window_size)  # 计算VWAP
    rolling_std = df['vwap'].rolling(window=window_size).std()  # 计算标准差
    df['vwap_low'] = df['vwap'] - (rolling_std * num_of_std)  # 计算下带
    df['vwap_high'] = df['vwap'] + (rolling_std * num_of_std)  # 计算上带
    return df['vwap_low'], df['vwap'], df['vwap_high']

# 定义布林带函数
def bollinger_bands(stock_price, window_size, num_of_std):
    """
    计算布林带
    参数:
        stock_price: 股价数据
        window_size: 窗口大小
        num_of_std: 标准差倍数
    """
    rolling_mean = stock_price.rolling(window=window_size).mean()  # 计算移动平均线
    rolling_std = stock_price.rolling(window=window_size).std()  # 计算标准差
    lower_band = rolling_mean - (rolling_std * num_of_std)  # 计算下带
    return np.nan_to_num(rolling_mean), np.nan_to_num(lower_band)  # 返回处理后的均线和下带

# 定义钱德动量潮函数
def chaikin_money_flow(dataframe, n=20, fillna=False) -> Series:
    """
    钱德动量潮指标（CMF）
    用于衡量一定时期内的资金流量
    http://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:chaikin_money_flow_cmf
    参数:
        dataframe: 包含OHLCV数据的数据框
        n: 周期
        fillna: 是否填充NaN值
    """
    mfv = ((dataframe['close'] - dataframe['low']) - (dataframe['high'] - dataframe['close'])) / (dataframe['high'] - dataframe['low'])  # 计算资金流量比率
    mfv = mfv.fillna(0.0)  # 处理除以零的情况
    mfv *= dataframe['volume']  # 乘以成交量
    cmf = (mfv.rolling(n, min_periods=0).sum()
           / dataframe['volume'].rolling(n, min_periods=0).sum())  # 计算CMF
    if fillna:
        cmf = cmf.replace([np.inf, -np.inf], np.nan).fillna(0)  # 处理无穷大和NaN值
    return Series(cmf, name='cmf')

# 定义希尔伯特变换平均价格函数
def ha_typical_price(bars):
    """
    计算希尔伯特变换平均价格
    使用高开低收四个价格计算平均值
    """
    res = (bars['ha_high'] + bars['ha_low'] + bars['ha_close']) / 3.  # 计算平均价格
    return Series(index=bars.index, data=res)  # 返回结果序列

# 定义策略类
class GeneTrader_gen10vps(IStrategy):
    """
    从超优化中粘贴输出
    可以在特定子策略（标的货币）底部覆盖
    """
    
    # ROI表: 定义不同时间段的最小利润目标
    minimal_roi = {
        "0": 100  # 从开始就需要100%的利润才会触发ROI退出(实际上禁用了ROI)
    }
    
    # DCA(动态成本平均)设置
    position_adjustment_enable = True  # 启用仓位调整

    # 止损设置:
    stoploss = -0.99  # 99%止损(实际上是一个非常宽松的止损)

    # 追踪止损设置:
    trailing_stop = False  # 关闭追踪止损
    trailing_stop_positive = 0.02  # 当利润达到2%时激活追踪止损
    trailing_stop_positive_offset = 0.10  # 追踪止损会在10%的位置开始跟踪
    trailing_only_offset_is_reached = True  # 只有在达到offset后才启用追踪止损

    #dca
    position_adjustment_enable = True

    # 基本设置
    timeframe = '5m'  # 使用5分钟K线

    # 策略行为设置
    use_exit_signal = True  # 使用退出信号
    exit_profit_only = False  # 不仅在盈利时退出
    ignore_roi_if_entry_signal = False  # 不忽略ROI当有入场信号时
    
    # 止损设置
    use_custom_stoploss = False  # 不使用自定义止损

    # 处理设置
    process_only_new_candles = True  # 只在新K线时处理
    startup_candle_count = 168  # 启动需要的K线数量(约14小时)

    # 订单类型设置
    order_types = {
        'entry': 'market',  # 入场使用市价单
        'exit': 'market',  # 退出使用市价单
        'emergency_exit': 'market',  # 紧急退出使用市价单
        'force_entry': "market",  # 强制入场使用市价单
        'force_exit': 'market',  # 强制退出使用市价单
        'stoploss': 'market',  # 止损使用市价单
        'stoploss_on_exchange': False,  # 不在交易所设置止损
        'stoploss_on_exchange_interval': 60,  # 交易所止损检查间隔
        'stoploss_on_exchange_limit_ratio': 0.99  # 交易所止损限价比率
    }
    
    def is_support(self, row_data) -> bool:
        """
        判断是否为支撑位
        通过分析价格走势判断是否形成支撑
        """
        conditions = []
        for row in range(len(row_data)-1):
            if row < len(row_data)/2:
                conditions.append(row_data[row] > row_data[row+1])  # 前半段价格下降
            else:
                conditions.append(row_data[row] < row_data[row+1])  # 后半段价格上升
        return reduce(lambda x, y: x & y, conditions)  # 所有条件都满足才是支撑位
    
    # 保护（NFIX29）参数
    fast_ewo = 50  # 快速EWO周期
    slow_ewo = 200  # 慢速EWO周期
    
    # NFINext44策略参数
    buy_44_ma_offset = 0.982  # MA偏移量
    buy_44_ewo = -18.143  # EWO阈值
    buy_44_cti = -0.8  # CTI阈值
    buy_44_r_1h = -75.0  # 1小时威廉指标阈值

    # NFINext37策略参数
    buy_37_ma_offset = 0.98  # MA偏移量
    buy_37_ewo = 9.8  # EWO阈值
    buy_37_rsi = 56.0  # RSI阈值
    buy_37_cti = -0.7  # CTI阈值

    # NFINext7策略参数
    buy_ema_open_mult_7 = 0.030  # EMA开盘价乘数
    buy_cti_7 = -0.89  # CTI阈值
    
    # 买入指标参数(可优化)
    buy_rmi = IntParameter(30, 50, default=45, space='buy', optimize=True)  # RMI指标参数
    buy_cci = IntParameter(-135, -90, default=-126, space='buy', optimize=True)  # CCI指标参数
    buy_srsi_fk = IntParameter(30, 50, default=42, space='buy', optimize=True)  # 随机RSI快线参数
    buy_cci_length = IntParameter(25, 45, default=42, space='buy', optimize=True)  # CCI长度
    buy_rmi_length = IntParameter(8, 20, default=11, space='buy', optimize=True)  # RMI长度

    buy_bb_width = DecimalParameter(0.065, 0.135, default=0.097, space='buy', optimize=True)  # 布林带宽度
    buy_bb_delta = DecimalParameter(0.018, 0.035, default=0.028, space='buy', optimize=True)  # 布林带差值
    
    buy_roc_1h = IntParameter(-25, 200, default=13, space='buy', optimize=True)  # 1小时ROC参数
    buy_bb_width_1h = DecimalParameter(0.3, 2.0, default=1.3, space='buy', optimize=True)  # 1小时布林带宽度

    # ClucHA策略参数(可优化)
    is_optimize_clucha = False  # 是否优化ClucHA参数
    buy_clucha_bbdelta_close = DecimalParameter(0.0005, 0.02, default=0.001, space='buy', optimize=True)  # 布林带差值/收盘价
    buy_clucha_bbdelta_tail = DecimalParameter(0.7, 1.0, default=1.0, space='buy', optimize=True)  # 布林带差值/尾部
    buy_clucha_close_bblower = DecimalParameter(0.0005, 0.02, default=0.008, space='buy', optimize=True)  # 收盘价/布林下轨
    buy_clucha_closedelta_close = DecimalParameter(0.0005, 0.02, default=0.014, space='buy', optimize=True)  # 收盘价变化率
    buy_clucha_rocr_1h = DecimalParameter(0.5, 1.0, default=0.51, space='buy', optimize=True)  # 1小时ROCR
    
    # Local_Uptrend参数    
    buy_ema_diff = DecimalParameter(0.022, 0.027, default=0.026, space='buy', optimize=True)  # EMA差值参数
    buy_bb_factor = DecimalParameter(0.99, 0.999, default=0.995, space='buy', optimize=True)  # 布林带因子
    buy_closedelta = DecimalParameter(12.0, 18.0, default=13.1, space='buy', optimize=True)  # 收盘价变化阈值
    
    # 买入参数
    rocr_1h = DecimalParameter(0.5, 1.0, default=0.51, space='buy', optimize=True)  # 1小时ROCR参数
    rocr1_1h = DecimalParameter(0.5, 1.0, default=0.59, space='buy', optimize=True)  # 1小时ROCR1参数
    bbdelta_close = DecimalParameter(0.0005, 0.02, default=0.001, space='buy', optimize=True)  # 布林带差值/收盘价
    closedelta_close = DecimalParameter(0.0005, 0.02, default=0.014, space='buy', optimize=True)  # 收盘价变化率
    bbdelta_tail = DecimalParameter(0.7, 1.0, default=1.0, space='buy', optimize=True)  # 布林带差值/尾部
    close_bblower = DecimalParameter(0.0005, 0.02, default=0.008, space='buy', optimize=True)  # 收盘价/布林下轨

    # 卖出参数
    sell_fisher = DecimalParameter(0.1, 0.5, default=0.5, space='sell', optimize=True)  # Fisher指标卖出阈值
    sell_bbmiddle_close = DecimalParameter(0.97, 1.1, default=1.067, space='sell', optimize=True)  # 布林带中轨/收盘价
    
    # Deadfish卖出参数
    sell_deadfish_bb_width = DecimalParameter(0.03, 0.75, default=0.06, space='sell', optimize=True)  # 布林带宽度
    sell_deadfish_profit = DecimalParameter(-0.15, -0.05, default=-0.1, space='sell', optimize=True)  # 止损利润
    sell_deadfish_bb_factor = DecimalParameter(0.9, 1.2, default=1.2, space='sell', optimize=True)  # 布林带因子
    sell_deadfish_volume_factor = DecimalParameter(1.0, 2.5, default=1.9, space='sell', optimize=True)  # 成交量因子
    
    # SMAOffset参数
    base_nb_candles_buy = IntParameter(8, 20, default=13, space='buy', optimize=True)  # 买入K线数
    base_nb_candles_sell = IntParameter(8, 50, default=44, space='sell', optimize=True)  # 卖出K线数
    low_offset = DecimalParameter(0.985, 0.995, default=0.991, space='buy', optimize=True)  # 低点偏移
    high_offset = DecimalParameter(1.005, 1.015, default=1.007, space='sell', optimize=True)  # 高点偏移
    high_offset_2 = DecimalParameter(1.01, 1.02, default=1.01, space='sell', optimize=True)  # 高点偏移2
    
    # 追踪止盈参数1
    sell_trail_profit_min_1 = DecimalParameter(0.1, 0.25, default=0.25, space='sell', optimize=True)  # 最小利润
    sell_trail_profit_max_1 = DecimalParameter(0.3, 0.5, default=0.5, space='sell', optimize=True)  # 最大利润
    sell_trail_down_1 = DecimalParameter(0.04, 0.1, default=0.08, space='sell', optimize=True)  # 回撤比例

    # 追踪止盈参数2
    sell_trail_profit_min_2 = DecimalParameter(0.04, 0.1, default=0.04, space='sell', optimize=True)  # 最小利润
    sell_trail_profit_max_2 = DecimalParameter(0.08, 0.25, default=0.08, space='sell', optimize=True)  # 最大利润
    sell_trail_down_2 = DecimalParameter(0.04, 0.2, default=0.07, space='sell', optimize=True)  # 回撤比例

    # 硬止损利润参数
    pHSL = DecimalParameter(-0.5, -0.04, default=-0.163, space='sell', optimize=True)  # 硬止损点
    # 利润阈值1，触发点，使用SL_1
    pPF_1 = DecimalParameter(0.008, 0.02, default=0.01, space='sell', optimize=True)  # 利润阈值1
    pSL_1 = DecimalParameter(0.008, 0.02, default=0.008, space='sell', optimize=True)  # 止损点1

    # 利润阈值2，使用SL_2
    pPF_2 = DecimalParameter(0.04, 0.1, default=0.072, space='sell', optimize=True)  # 利润阈值2
    pSL_2 = DecimalParameter(0.02, 0.07, default=0.054, space='sell', optimize=True)  # 止损点2
    
    def informative_pairs(self):
        """
        获取信息性配对
        """
        pairs = self.dp.current_whitelist()  # 获取当前白名单中的交易对
        informative_pairs = [(pair, '1h') for pair in pairs]  # 为每个交易对添加1小时时间框架
        
        informative_pairs += [("BTC/USDT:USDT", "5m")]  # 添加BTC/USDT 5分钟数据作为参考
        return informative_pairs
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算策略所需的所有技术指标
        """
        
        # 获取1小时时间框架的数据
        informative = self.dp.get_pair_dataframe(metadata['pair'], '1h')
        
        # 计算1小时布林带
        bollinger2_1h = qtpylib.bollinger_bands(qtpylib.typical_price(informative), window=20, stds=2)
        informative['bb_upperband_1h'] = bollinger2_1h['upper']
        informative['bb_midband_1h'] = bollinger2_1h['mid']
        informative['bb_lowerband_1h'] = bollinger2_1h['lower']
        informative['bb_width_1h'] = ((informative['bb_upperband_1h'] - informative['bb_lowerband_1h']) / informative['bb_midband_1h'])
        
        # 将1小时数据合并到主数据框
        dataframe = merge_informative_pair(dataframe, informative, self.timeframe, '1h', ffill=True)
        
        return dataframe
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float, current_profit: float, **kwargs):
        """
        自定义退出逻辑
        参数:
            pair: 交易对
            trade: 交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前利润
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)  # 获取分析后的数据
        last_candle = dataframe.iloc[-1].squeeze()  # 获取最新K线数据
        filled_buys = trade.select_filled_orders('buy')  # 获取已完成的买入订单
        count_of_buys = len(filled_buys)  # 计算买入订单数量

        if (last_candle is not None):
            # 追踪止盈条件1
            if (current_profit > self.sell_trail_profit_min_1.value) & (current_profit < self.sell_trail_profit_max_1.value) & (((trade.max_rate - trade.open_rate) / 100) > (current_profit + self.sell_trail_down_1.value)):
                return 'trail_target_1'
            # 追踪止盈条件2
            elif (current_profit > self.sell_trail_profit_min_2.value) & (current_profit < self.sell_trail_profit_max_2.value) & (((trade.max_rate - trade.open_rate) / 100) > (current_profit + self.sell_trail_down_2.value)):
                return 'trail_target_2'
            # RSI超买区退出
            elif (current_profit > 3) & (last_candle['rsi'] > 85):
                 return 'RSI-85 target'
            
            # 卖出信号1 - 针对少量买入订单
            if (current_profit > 0) & (count_of_buys < 4) & (last_candle['close'] > last_candle['hma_50']) & (last_candle['close'] > (last_candle[f'ma_sell_{self.base_nb_candles_sell.value}'] * self.high_offset_2.value)) & (last_candle['rsi']>50) & (last_candle['volume'] > 0) & (last_candle['rsi_fast'] > last_candle['rsi_slow']):
                return 'sell signal1'
            # 卖出信号1变体 - 针对多量买入订单
            if (current_profit > 0) & (count_of_buys >= 4) & (last_candle['close'] > last_candle['hma_50'] * 1.01) & (last_candle['close'] > (last_candle[f'ma_sell_{self.base_nb_candles_sell.value}'] * self.high_offset_2.value)) & (last_candle['rsi']>50) & (last_candle['volume'] > 0) & (last_candle['rsi_fast'] > last_candle['rsi_slow']):
                return 'sell signal1 * 1.01'
            # 卖出信号2
            if (current_profit > 0) & (last_candle['close'] > last_candle['hma_50']) & (last_candle['close'] > (last_candle[f'ma_sell_{self.base_nb_candles_sell.value}'] * self.high_offset.value)) &  (last_candle['volume'] > 0) & (last_candle['rsi_fast'] > last_candle['rsi_slow']):
                return 'sell signal2'

            # Deadfish卖出信号
            if (    (current_profit < self.sell_deadfish_profit.value)  # 当前亏损超过阈值
                and (last_candle['close'] < last_candle['ema_200'])  # 价格低于200均线
                and (last_candle['bb_width'] < self.sell_deadfish_bb_width.value)  # 布林带收窄
                and (last_candle['close'] > last_candle['bb_middleband2'] * self.sell_deadfish_bb_factor.value)  # 价格高于布林带中轨
                and (last_candle['volume_mean_12'] < last_candle['volume_mean_24'] * self.sell_deadfish_volume_factor.value)  # 成交量萎缩
                and (last_candle['cmf'] < 0.0)  # 资金流为负
            ):
                return f"sell_stoploss_deadfish"

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算策略所需的所有技术指标
        """
        
        info_tf = '5m'  # 设置信息时间框架为5分钟

        # 获取BTC/USDT的参考数据
        informative = self.dp.get_pair_dataframe('BTC/USDT:USDT', timeframe=info_tf)
        informative_btc = informative.copy().shift(1)  # 复制并向后移动一个周期

        # 计算BTC相关指标
        dataframe['btc_close'] = informative_btc['close']  # BTC收盘价
        dataframe['btc_ema_fast'] = ta.EMA(informative_btc, timeperiod=20)  # BTC快速EMA
        dataframe['btc_ema_slow'] = ta.EMA(informative_btc, timeperiod=25)  # BTC慢速EMA
        dataframe['down'] = (dataframe['btc_ema_fast'] < dataframe['btc_ema_slow']).astype('int')  # BTC下跌趋势标记
        
        # 计算所有ma_sell值
        for val in range(int(self.base_nb_candles_sell.low), int(self.base_nb_candles_sell.high) + 1):
             dataframe[f'ma_sell_{val}'] = ta.EMA(dataframe, timeperiod=val)  # 计算不同周期的EMA
        
        # 计算成交量均值
        dataframe['volume_mean_12'] = dataframe['volume'].rolling(12).mean().shift(1)  # 12周期成交量均值
        dataframe['volume_mean_24'] = dataframe['volume'].rolling(24).mean().shift(1)  # 24周期成交量均值
        
        # 计算CMF(钱德动量流)
        dataframe['cmf'] = chaikin_money_flow(dataframe, 20)  
        
        # 计算布林带
        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)  # 标准布林带
        dataframe['bb_lowerband2'] = bollinger2['lower']  # 下轨
        dataframe['bb_middleband2'] = bollinger2['mid']  # 中轨
        dataframe['bb_upperband2'] = bollinger2['upper']  # 上轨
        dataframe['bb_width'] = ((dataframe['bb_upperband2'] - dataframe['bb_lowerband2']) / dataframe['bb_middleband2'])  # 带宽
        
        # 计算40周期布林带
        bollinger2_40 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=40, stds=2)
        dataframe['bb_lowerband2_40'] = bollinger2_40['lower']
        dataframe['bb_middleband2_40'] = bollinger2_40['mid']
        dataframe['bb_upperband2_40'] = bollinger2_40['upper']
        
        # 计算EMA指标
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)  # 200周期EMA
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)    # 50周期EMA
        
        # 计算RSI指标族
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)       # 标准RSI
        dataframe['rsi_fast'] = ta.RSI(dataframe, timeperiod=4)   # 快速RSI
        dataframe['rsi_slow'] = ta.RSI(dataframe, timeperiod=20)  # 慢速RSI
        dataframe['rsi_84'] = ta.RSI(dataframe, timeperiod=84)    # 84周期RSI
        dataframe['rsi_112'] = ta.RSI(dataframe, timeperiod=112)  # 112周期RSI
        
        # 希尔伯特-阿希坦尔蜡烛
        heikinashi = qtpylib.heikinashi(dataframe)  # 计算平滑K线
        dataframe['ha_open'] = heikinashi['open']   # 平滑开盘价
        dataframe['ha_close'] = heikinashi['close'] # 平滑收盘价
        dataframe['ha_high'] = heikinashi['high']   # 平滑最高价
        dataframe['ha_low'] = heikinashi['low']     # 平滑最低价
        
        # ClucHA指标计算
        dataframe['bb_delta_cluc'] = (dataframe['bb_middleband2_40'] - dataframe['bb_lowerband2_40']).abs()  # 布林带差值
        dataframe['ha_closedelta'] = (dataframe['ha_close'] - dataframe['ha_close'].shift()).abs()  # 平滑收盘价变化

        # SRSI超优化（is DIP）
        stoch = ta.STOCHRSI(dataframe, 15, 20, 2, 2)  # 计算随机RSI
        dataframe['srsi_fk'] = stoch['fastk']  # 快速K线
        dataframe['srsi_fd'] = stoch['fastd']  # 快速D线

        # 设置布林带
        mid, lower = bollinger_bands(ha_typical_price(dataframe), window_size=40, num_of_std=2)  # 基于平滑价格的布林带
        dataframe['lower'] = lower  # 下轨
        dataframe['mid'] = mid      # 中轨

        dataframe['bbdelta'] = (mid - dataframe['lower']).abs()  # 中轨与下轨差值
        dataframe['closedelta'] = (dataframe['ha_close'] - dataframe['ha_close'].shift()).abs()  # 收盘价变化
        dataframe['tail'] = (dataframe['ha_close'] - dataframe['ha_low']).abs()  # 下影线长度

        dataframe['bb_lowerband'] = dataframe['lower']  # 布林下轨
        dataframe['bb_middleband'] = dataframe['mid']   # 布林中轨
        
        # is DIP指标
        bollinger3 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=3)  # 3倍标准差布林带
        dataframe['bb_lowerband3'] = bollinger3['lower']  # 3sigma下轨
        dataframe['bb_middleband3'] = bollinger3['mid']   # 3sigma中轨
        dataframe['bb_upperband3'] = bollinger3['upper']  # 3sigma上轨
        dataframe['bb_delta'] = ((dataframe['bb_lowerband2'] - dataframe['bb_lowerband3']) / dataframe['bb_lowerband2'])  # 布林带差值比率

        dataframe['ema_fast'] = ta.EMA(dataframe['ha_close'], timeperiod=3)   # 3周期EMA
        dataframe['ema_slow'] = ta.EMA(dataframe['ha_close'], timeperiod=50)  # 50周期EMA
        dataframe['volume_mean_slow'] = dataframe['volume'].rolling(window=30).mean()  # 30周期成交量均值
        dataframe['rocr'] = ta.ROCR(dataframe['ha_close'], timeperiod=28)  # 28周期变化率
        
        # VWAP指标计算
        vwap_low, vwap, vwap_high = VWAPB(dataframe, 20, 1)  # 计算VWAP带
        
        vwap_low, vwap, vwap_high = VWAPB(dataframe, 20, 1)  # 再次计算VWAP带(可能是代码重复)
        dataframe['vwap_low'] = vwap_low  # VWAP下轨
        
        dataframe['vwap_upperband'] = vwap_high     # VWAP上轨
        dataframe['vwap_middleband'] = vwap         # VWAP中轨
        dataframe['vwap_lowerband'] = vwap_low      # VWAP下轨
        dataframe['vwap_width'] = ( (dataframe['vwap_upperband'] - dataframe['vwap_lowerband']) / dataframe['vwap_middleband'] ) * 100  # VWAP带宽
        # Diff
        dataframe['ema_vwap_diff_50'] = ( ( dataframe['ema_50'] - dataframe['vwap_lowerband'] ) / dataframe['ema_50'] )  # EMA与VWAP的差值比率
        
        # Dip protection
        dataframe['tpct_change_0']   = top_percent_change_dca(dataframe,0)    # 当前K线的百分比变化
        dataframe['tpct_change_1']   = top_percent_change_dca(dataframe,1)    # 1根K线的百分比变化
        dataframe['tcp_percent_4'] =   top_percent_change_dca(dataframe , 4)  # 4根K线的百分比变化

        #NFINEXT44
        dataframe['ewo'] = ewo(dataframe, 50, 200)  # 计算艾略特波浪振荡器

        # SMA
        dataframe['sma_15'] = ta.SMA(dataframe, timeperiod=15)
        dataframe['sma_30'] = ta.SMA(dataframe, timeperiod=30)
        
        
        # RMI超优化
        for val in range(int(self.buy_rmi_length.low), int(self.buy_rmi_length.high) + 1):
            dataframe[f'rmi_length_{val}'] = RMI(dataframe, length=val, mom=4)
            
        # CCI超优化
        for val in range(int(self.buy_cci_length.low), int(self.buy_cci_length.high) + 1):
            dataframe[f'cci_length_{val}'] = ta.CCI(dataframe, val)
        
        #CTI
        dataframe['cti'] = pta.cti(dataframe["close"], length=20)
        
        
        #NFIX39
        dataframe['bb_delta_cluc'] = (dataframe['bb_middleband2_40'] - dataframe['bb_lowerband2_40']).abs()
        
        #NFIX29
        dataframe['ema_16'] = ta.EMA(dataframe, timeperiod=16)

            
        
        dataframe['EWO'] = EWO(dataframe, self.fast_ewo, self.slow_ewo)
        
        #local_uptrend
        dataframe['ema_26'] = ta.EMA(dataframe, timeperiod=26)
        dataframe['ema_12'] = ta.EMA(dataframe, timeperiod=12)
        
        #insta_signal
        dataframe['r_14'] = williams_r(dataframe, period=14)
        
        #rebuy check if EMA is rising
        dataframe['ema_5'] = ta.EMA(dataframe, timeperiod=5)
        dataframe['ema_10'] = ta.EMA(dataframe, timeperiod=10)

        # Profit Maximizer - PMAX (NFINext37)
        dataframe['pm'], dataframe['pmx'] = pmax(heikinashi, MAtype=1, length=9, multiplier=27, period=10, src=3)
        dataframe['source'] = (dataframe['high'] + dataframe['low'] + dataframe['open'] + dataframe['close'])/4
        dataframe['pmax_thresh'] = ta.EMA(dataframe['source'], timeperiod=9)
        dataframe['sma_75'] = ta.SMA(dataframe, timeperiod=75)

        rsi = ta.RSI(dataframe)
        dataframe["rsi"] = rsi
        rsi = 0.1 * (rsi - 50)
        dataframe["fisher"] = (np.exp(2 * rsi) - 1) / (np.exp(2 * rsi) + 1)

        inf_tf = '1h'

        informative = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=inf_tf)

        inf_heikinashi = qtpylib.heikinashi(informative)

        informative['ha_close'] = inf_heikinashi['close']
        informative['rocr'] = ta.ROCR(informative['ha_close'], timeperiod=168)
        informative['rsi_14'] = ta.RSI(dataframe, timeperiod=14)
        informative['cmf'] = chaikin_money_flow(dataframe, 20)
        sup_series = informative['low'].rolling(window = 5, center=True).apply(lambda row: self.is_support(row), raw=True).shift(2)
        informative['sup_level'] = Series(np.where(sup_series, np.where(informative['close'] < informative['open'], informative['close'], informative['open']), float('NaN'))).ffill()
        informative['roc'] = ta.ROC(informative, timeperiod=9)

        informative['r_480'] = williams_r(informative, period=480)
        
        # 布林带（is DIP）
        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(informative), window=20, stds=2)
        informative['bb_lowerband2'] = bollinger2['lower']
        informative['bb_middleband2'] = bollinger2['mid']
        informative['bb_upperband2'] = bollinger2['upper']
        informative['bb_width'] = ((informative['bb_upperband2'] - informative['bb_lowerband2']) / informative['bb_middleband2'])
        
        informative['r_84'] = williams_r(informative, period=84)
        informative['cti_40'] = pta.cti(informative["close"], length=40)
        
        
        dataframe['hma_50'] = qtpylib.hull_moving_average(dataframe['close'], window=50)
        

        dataframe = merge_informative_pair(dataframe, informative, self.timeframe, inf_tf, ffill=True)

        # NFI Quick/Slow
        dataframe['adx'] = ta.ADX(dataframe)  # 计算ADX趋势强度指标
        dataframe['cci'] = ta.CCI(dataframe, timeperiod=20)  # 计算CCI顺势指标
        dataframe['mfi'] = ta.MFI(dataframe)  # 计算MFI资金流量指标
        dataframe['rmi'] = RMI(dataframe, length=8, mom=4)  # 计算RMI相对动量指标

        # NFI static
        dataframe['cci_sma'] = ta.SMA(dataframe['cci'], timeperiod=5)  # CCI的5周期简单移动平均
        dataframe['rmi_sma'] = ta.SMA(dataframe['rmi'], timeperiod=5)  # RMI的5周期简单移动平均
        
        # 计算HMA指标
        dataframe['hma_50'] = qtpylib.hull_moving_average(dataframe['close'], window=50)  # 50周期赫尔移动平均线
        
        # 计算CTI指标
        dataframe['cti'] = pta.cti(dataframe["close"], length=20)  # 20周期CTI指标
        
        # 计算Williams %R指标
        dataframe['r_14'] = williams_r(dataframe, period=14)  # 14周期威廉指标
        dataframe['r_480'] = williams_r(dataframe, period=480)  # 480周期威廉指标
        
        # 计算Fisher指标
        rsi = 0.1 * (dataframe['rsi'] - 50)  # 将RSI值标准化
        dataframe['fisher_rsi'] = (np.exp(2 * rsi) - 1) / (np.exp(2 * rsi) + 1)  # Fisher变换
        
        # 计算1小时时间框架的指标
        inf_tf = '1h'  # 设置1小时时间框架
        informative = self.dp.get_pair_dataframe(metadata['pair'], inf_tf)  # 获取1小时数据
        
        # 计算1小时数据的指标
        informative['rsi'] = ta.RSI(informative, timeperiod=14)  # RSI
        informative['ema_200'] = ta.EMA(informative, timeperiod=200)  # 200周期EMA
        informative['ema_50'] = ta.EMA(informative, timeperiod=50)  # 50周期EMA
        informative['sma_9'] = ta.SMA(informative, timeperiod=9)  # 9周期SMA
        
        # 计算1小时布林带
        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(informative), window=20, stds=2)
        informative['bb_lowerband2'] = bollinger2['lower']  # 下轨
        informative['bb_middleband2'] = bollinger2['mid']  # 中轨
        informative['bb_upperband2'] = bollinger2['upper']  # 上轨
        informative['bb_width'] = ((informative['bb_upperband2'] - informative['bb_lowerband2']) / informative['bb_middleband2'])  # 带宽
        
        # 计算1小时Williams %R
        informative['r_480'] = williams_r(informative, period=480)  # 480周期威廉指标
        
        # 将1小时数据合并到主数据框
        dataframe = merge_informative_pair(dataframe, informative, self.timeframe, inf_tf, ffill=True)
        
        # 计算最终指标
        dataframe['roc'] = ta.ROC(dataframe, timeperiod=9)  # 9周期变化率
        dataframe['roc_1h'] = ta.ROC(dataframe, timeperiod=9)  # 1小时9周期变化率
        dataframe['rocr'] = ta.ROCR(dataframe, timeperiod=28)  # 28周期变化率比率
        dataframe['rocr_1h'] = ta.ROCR(dataframe, timeperiod=28)  # 1小时28周期变化率比率

        # 添加各种技术指标到数据框中
        if self.dp:
            inf_tf = '1h'
            informative = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=inf_tf)
            
            # 计算1小时布林带
            bollinger = qtpylib.bollinger_bands(informative['close'], window=20, stds=2)
            informative['bb_upperband_1h'] = bollinger['upper']
            informative['bb_midband_1h'] = bollinger['mid']
            informative['bb_lowerband_1h'] = bollinger['lower']
            
            # 计算布林带宽度
            informative['bb_width_1h'] = (
                (informative['bb_upperband_1h'] - informative['bb_lowerband_1h']) / informative['bb_midband_1h']
            )
            
            # 合并回主数据框
            dataframe = merge_informative_pair(dataframe, informative, self.timeframe, inf_tf, ffill=True)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入信号
        """
        conditions = []  # 存储所有买入条件
        dataframe.loc[:, 'enter_tag'] = ''  # 初始化进场标签列
        
        # 保护和过滤器
        is_dip = (  # DIP(下跌)条件判断
            (dataframe['tpct_change_1'] < 0.08)  # 1根K线跌幅小于8%
            &
            (dataframe['tpct_change_0'] < 0.08)  # 当前K线跌幅小于8%
        )

        is_pump_1 = (  # 泵动条件1判断
            (dataframe['close'].rolling(48).max() >= (dataframe['close'] * 1.1 ))  # 48根K线内最高价超过当前价格10%
        )

        is_pump_2 = (  # 泵动条件2判断
            (dataframe['close'].rolling(24).max() >= (dataframe['close'] * 1.1 ))  # 24根K线内最高价超过当前价格10%
        )

        is_pump_3 = (  # 泵动条件3判断
            (dataframe['close'].rolling(12).max() >= (dataframe['close'] * 1.1 ))  # 12根K线内最高价超过当前价格10%
        )

        is_pump_4 = (  # 泵动条件4判断
            (dataframe['close'].rolling(4).max() >= (dataframe['close'] * 1.1 ))  # 4根K线内最高价超过当前价格10%
        )

        # 泵动保护
        is_pump = (  # 综合泵动条件
            is_pump_1 
            | 
            is_pump_2 
            | 
            is_pump_3 
            | 
            is_pump_4
        )

        # 基本过滤条件
        is_bull = (  # 多头市场条件
            (dataframe['close'] > dataframe['ema_200'])  # 价格在200均线上方
            &
            (dataframe['close'] > dataframe['ema_50'])   # 价格在50均线上方
            &
            (dataframe['ema_50'] > dataframe['ema_200'])  # 50均线在200均线上方
        )

        is_bull_2 = (  # 多头市场条件2
            (dataframe['close'] > dataframe['ema_200'])  # 价格在200均线上方
            &
            (dataframe['close'] > dataframe['ema_50'])   # 价格在50均线上方
            &
            (dataframe['ema_50'] > dataframe['ema_200'])  # 50均线在200均线上方
            &
            (dataframe['rsi'] > 50)  # RSI大于50
        )

        is_bull_3 = (  # 多头市场条件3
            (dataframe['close'] > dataframe['ema_200'])  # 价格在200均线上方
            &
            (dataframe['close'] > dataframe['ema_50'])   # 价格在50均线上方
            &
            (dataframe['ema_50'] > dataframe['ema_200'])  # 50均线在200均线上方
            &
            (dataframe['rsi'] > 50)  # RSI大于50
            &
            (dataframe['r_14'] < -25)  # 威廉指标小于-25
        )

        # 组合买入条件
        conditions.append(
            (dataframe['btc_close'].rolling(24).max() >= (dataframe['btc_close'] * 1.03 ))  # BTC价格超过3%
        )
        conditions.append(
            (dataframe['rsi_84'] < 60) & (dataframe['rsi_112'] < 60)  # RSI在60以下
        )
        conditions.append(
            (dataframe[f'rmi_length_{self.buy_rmi_length.value}'] < self.buy_rmi.value) &  # RMI小于买入阈值
            (dataframe[f'cci_length_{self.buy_cci_length.value}'] <= self.buy_cci.value) &  # CCI小于等于买入阈值
            (dataframe['srsi_fk'] < self.buy_srsi_fk.value) &  # 随机RSI快线小于买入阈值
            (dataframe['bb_delta'] > self.buy_bb_delta.value) &  # 布林带宽度大于买入阈值
            (dataframe['bb_width'] > self.buy_bb_width.value) &  # 布林带宽度大于买入阈值
            (dataframe['closedelta'] > dataframe['close'] * self.buy_closedelta.value / 1000 ) &    # 收盘价变化大于买入阈值
            (dataframe['close'] < dataframe['bb_lowerband3'] * self.buy_bb_factor.value) &  # 价格低于3sigma下轨
            (dataframe['roc_1h'] < self.buy_roc_1h.value) &  # 1小时ROC小于买入阈值
            (dataframe['bb_width_1h'] < self.buy_bb_width_1h.value)  # 1小时布林带宽度小于买入阈值
        )
        conditions.append(
            (dataframe['bb_delta'] > self.buy_bb_delta.value) &  # 布林带宽度大于买入阈值
            (dataframe['bb_width'] > self.buy_bb_width.value) &  # 布林带宽度大于买入阈值
            (dataframe['closedelta'] > dataframe['close'] * self.buy_closedelta.value / 1000 ) &    # 收盘价变化大于买入阈值
            (dataframe['close'] < dataframe['bb_lowerband3'] * self.buy_bb_factor.value) &  # 价格低于3sigma下轨
            (dataframe['roc_1h'] < self.buy_roc_1h.value) &  # 1小时ROC小于买入阈值
            (dataframe['bb_width_1h'] < self.buy_bb_width_1h.value)  # 1小时布林带宽度小于买入阈值
        )
        conditions.append(
            (dataframe['rocr_1h'] > self.buy_clucha_rocr_1h.value ) &  # 1小时ROCR大于买入阈值
            (dataframe['bb_lowerband2_40'].shift() > 0) &  # 40周期布林带下轨大于0
            (dataframe['bb_delta_cluc'] > dataframe['ha_close'] * self.buy_clucha_bbdelta_close.value) &  # 布林带差值大于收盘价乘以买入阈值
            (dataframe['ha_closedelta'] > dataframe['ha_close'] * self.buy_clucha_closedelta_close.value) &  # 平滑收盘价变化大于收盘价乘以买入阈值
            (dataframe['tail'] < dataframe['bb_delta_cluc'] * self.buy_clucha_bbdelta_tail.value) &  # 下影线长度小于布林带差值乘以买入阈值
            (dataframe['ha_close'] < dataframe['bb_lowerband2_40'].shift()) &  # 平滑收盘价小于40周期布林带下轨
            (dataframe['close'] > (dataframe['sup_level_1h'] * 0.88)) &  # 价格高于1小时支撑位0.88倍
            (dataframe['ha_close'] < dataframe['ha_close'].shift())  # 平滑收盘价小于前一根K线的收盘价
        )
        conditions.append(
            (dataframe['ema_200'] > (dataframe['ema_200'].shift(12) * 1.01)) &  # 200周期EMA大于前一根K线的1.01倍
            (dataframe['ema_200'] > (dataframe['ema_200'].shift(48) * 1.07)) &  # 200周期EMA大于前一根K线的1.07倍
            (dataframe['bb_lowerband2_40'].shift().gt(0)) &  # 40周期布林带下轨大于0
            (dataframe['bb_delta_cluc'].gt(dataframe['close'] * 0.056)) &  # 布林带差值大于收盘价乘以0.056
            (dataframe['closedelta'].gt(dataframe['close'] * 0.01)) &  # 收盘价变化大于买入阈值
            (dataframe['tail'].lt(dataframe['bb_delta_cluc'] * 0.5)) &  # 下影线长度小于布林带差值的一半
            (dataframe['close'].lt(dataframe['bb_lowerband2_40'].shift())) &  # 价格低于40周期布林带下轨
            (dataframe['close'].le(dataframe['close'].shift())) &  # 价格小于等于前一根K线的收盘价
            (dataframe['close'] > dataframe['ema_50'] * 0.912)  # 价格大于50周期EMA的91.2%
        )
        conditions.append(
            (dataframe['close'] > (dataframe['sup_level_1h'] * 0.72)) &  # 价格高于1小时支撑位0.72倍
            (dataframe['close'] < (dataframe['ema_16'] * 0.982)) &  # 价格低于16周期EMA的98.2%
            (dataframe['EWO'] < -10.0) &  # EWO小于-10.0
            (dataframe['cti'] < -0.9)  # CTI小于-0.9
        )
        conditions.append(
            (dataframe['ema_26'] > dataframe['ema_12']) &  # 26周期EMA大于12周期EMA
            (dataframe['ema_26'] - dataframe['ema_12'] > dataframe['open'] * self.buy_ema_diff.value) &  # 26周期EMA减去12周期EMA大于开盘价乘以买入阈值
            (dataframe['ema_26'].shift() - dataframe['ema_12'].shift() > dataframe['open'] / 100) &  # 26周期EMA减去12周期EMA大于开盘价除以100
            (dataframe['close'] < dataframe['bb_lowerband2'] * self.buy_bb_factor.value) &  # 价格低于2周期布林带下轨
            (dataframe['closedelta'] > dataframe['close'] * self.buy_closedelta.value / 1000 )  # 收盘价变化大于买入阈值
        )
        conditions.append(
            (dataframe['close'] < dataframe['vwap_low']) &  # 价格低于VWAP下轨
            (dataframe['tcp_percent_4'] > 0.053) &  # 4根K线内价格变化大于5.3%
            (dataframe['cti'] < -0.8) &  # CTI小于-0.8
            (dataframe['rsi'] < 35) &  # RSI小于35
            (dataframe['rsi_84'] < 60) &  # 84周期RSI小于60
            (dataframe['rsi_112'] < 60) &  # 112周期RSI小于60
            (dataframe['volume'] > 0)  # 成交量大于0
        )
        conditions.append(
            (dataframe['bb_width_1h'] > 0.131) &  # 1小时布林带宽度大于0.131
            (dataframe['r_14'] < -51) &  # 14周期威廉指标小于-51
            (dataframe['r_84_1h'] < -70) &  # 1小时84周期RSI小于-70
            (dataframe['cti'] < -0.845) &  # CTI小于-0.845
            (dataframe['cti_40_1h'] < -0.735)  # 1小时40周期CTI小于-0.735
        )
        conditions.append(
            (dataframe['close'].rolling(48).max() >= (dataframe['close'] * 1.1 ))  # 48根K线内最高价超过当前价格10%
        )
        conditions.append(
            (dataframe['close'] < (dataframe['ema_16'] * self.buy_44_ma_offset)) &  # 价格低于16周期EMA的0.982倍
            (dataframe['ewo'] < self.buy_44_ewo) &  # EWO小于买入阈值
            (dataframe['cti'] < self.buy_44_cti) &  # CCI小于买入阈值
            (dataframe['r_480_1h'] < self.buy_44_r_1h)  # 1小时480周期RSI小于买入阈值
        )
        conditions.append(
            (dataframe['pm'] > dataframe['pmax_thresh']) &  # PMAX大于利润阈值
            (dataframe['close'] < dataframe['sma_75'] * self.buy_37_ma_offset) &  # 价格低于75周期SMA的0.98倍
            (dataframe['ewo'] > self.buy_37_ewo) &  # EWO大于买入阈值
            (dataframe['rsi'] < self.buy_37_rsi) &  # RSI小于买入阈值
            (dataframe['cti'] < self.buy_37_cti)  # CTI小于买入阈值
        )
        conditions.append(
            (dataframe['ema_26'] > dataframe['ema_12']) &  # 26周期EMA大于12周期EMA
            ((dataframe['ema_26'] - dataframe['ema_12']) > (dataframe['open'] * self.buy_ema_open_mult_7)) &  # 26周期EMA减去12周期EMA大于开盘价乘以买入阈值
            ((dataframe['ema_26'].shift() - dataframe['ema_12'].shift()) > (dataframe['open'] / 100)) &  # 26周期EMA减去12周期EMA大于开盘价除以100
            (dataframe['cti'] < self.buy_cti_7)  # CTI小于买入阈值
        )
        conditions.append(
            (dataframe['rsi_slow'] < dataframe['rsi_slow'].shift(1)) &  # 慢速RSI小于前一根K线的慢速RSI
            (dataframe['rsi_fast'] < 46) &  # 快速RSI小于46
            (dataframe['rsi'] > 19) &  # RSI大于19
            (dataframe['close'] < dataframe['sma_15'] * 0.942) &  # 价格低于15周期SMA的94.2%
            (dataframe['cti'] < -0.86)  # CTI小于-0.86
        )
        conditions.append(
            (dataframe['bb_lowerband2_40'].shift() > 0) &  # 40周期布林带下轨大于0
            (dataframe['bb_delta_cluc'] > dataframe['close'] * 0.059) &  # 布林带差值大于收盘价乘以0.059
            (dataframe['ha_closedelta'] > dataframe['close'] * 0.023) &  # 平滑收盘价变化大于收盘价乘以0.023
            (dataframe['tail'] < dataframe['bb_delta_cluc'] * 0.24) &  # 下影线长度小于布林带差值乘以0.24
            (dataframe['close'] < dataframe['bb_lowerband2_40'].shift()) &  # 价格低于40周期布林带下轨
            (dataframe['close'] < dataframe['close'].shift()) &  # 价格小于等于前一根K线的收盘价
            (btc_dump == 0)  # BTC价格未超过3%
        )
        conditions.append(
            (dataframe['close'] < dataframe['vwap_lowerband']) &  # 价格低于VWAP下轨
            (dataframe['tpct_change_1'] > 0.04) &  # 1根K线内价格变化大于4%
            (dataframe['cti'] < -0.8) &  # CTI小于-0.8
            (dataframe['rsi'] < 35) &  # RSI小于35
            (rsi_check) &  # RSI在60以下
            (btc_dump == 0)  # BTC价格未超过3%
        )

        # 买入条件1 - NFI 7
        is_nfi_7 = (  # NFI 7策略条件
            (dataframe['ema_50'] > dataframe['ema_200'])  # 50均线在200均线上方
            &
            (dataframe['close'] > dataframe['ema_50'])    # 收盘价在50均线上方
            &
            (dataframe['close'] > dataframe['bb_middleband3'] * self.buy_ema_open_mult_7.value)  # 价格高于布林中轨
            &
            (dataframe['cti'] < self.buy_cti_7.value)  # CTI指标低于阈值
        )

        # 买入条件2 - NFI 37
        is_nfi_37 = (  # NFI 37策略条件
            (dataframe['close'] < dataframe['ema_50'] * self.buy_37_ma_offset.value)  # 价格低于50均线偏移值
            &
            (dataframe['ewo'] > self.buy_37_ewo.value)  # EWO大于阈值
            &
            (dataframe['rsi'] < self.buy_37_rsi.value)  # RSI低于阈值
            &
            (dataframe['cti'] < self.buy_37_cti.value)  # CTI低于阈值
        )

        # 买入条件3 - NFI 44
        is_nfi_44 = (  # NFI 44策略条件
            (dataframe['close'] < dataframe['ema_50'] * self.buy_44_ma_offset.value)  # 价格低于50均线偏移值
            &
            (dataframe['ewo'] < self.buy_44_ewo.value)  # EWO小于阈值
            &
            (dataframe['cti'] < self.buy_44_cti.value)  # CTI低于阈值
            &
            (dataframe['r_14'] < self.buy_44_r_1h.value)  # 14周期威廉指标低于阈值
        )

        # 买入条件4 - NFI static
        is_nfi_static = (  # NFI static策略条件
            (dataframe['roc_1h'] > self.buy_roc_1h.value)  # 1小时ROC大于阈值
            &
            (dataframe['bb_width_1h'] > self.buy_bb_width_1h.value)  # 1小时布林带宽度大于阈值
            &
            (dataframe['rmi'] < self.buy_rmi.value)  # RMI小于阈值
            &
            (dataframe['cci'] < self.buy_cci.value)  # CCI小于阈值
            &
            (dataframe['srsi_fk'] < self.buy_srsi_fk.value)  # 随机RSI快线小于阈值
            &
            (dataframe['bb_width'] > self.buy_bb_width.value)  # 布林带宽度大于阈值
            &
            (dataframe['bb_delta'] > self.buy_bb_delta.value)  # 布林带差值大于阈值
        )

        # 买入条件5 - ClucHA
        is_clucHA = (  # ClucHA策略条件
            (dataframe['rocr_1h'].gt(self.buy_clucha_rocr_1h.value))  # 1小时ROCR大于阈值
            &
            (
                (
                    (dataframe['bb_lowerband2_40'].shift() > 0)  # 40周期布林下轨大于0
                    &
                    (dataframe['bb_delta_cluc'] > dataframe['ha_close'] * self.buy_clucha_bbdelta_close.value)  # 布林带差值条件
                    &
                    (dataframe['ha_closedelta'] > dataframe['ha_close'] * self.buy_clucha_closedelta_close.value)  # 收盘价变化条件
                    &
                    (dataframe['tail'] < dataframe['bb_delta_cluc'] * self.buy_clucha_bbdelta_tail.value)  # 下影线长度条件
                    &
                    (dataframe['ha_close'] < dataframe['bb_lowerband2_40'].shift())  # 收盘价低于布林下轨
                    &
                    (dataframe['ha_close'] < dataframe['ha_close'].shift())  # 收盘价下跌
                )
            )
            &
            (dataframe['ha_close'] < dataframe['ema_slow'])  # 收盘价低于慢速EMA
            &
            (dataframe['ha_close'] < dataframe['bb_lowerband2'] * self.buy_clucha_close_bblower.value)  # 收盘价相对布林下轨位置
        )

        # 买入条件6 - Local Uptrend
        is_local_uptrend = (  # 局部上涨趋势条件
            (dataframe['ema_fast'] > dataframe['ema_slow'])  # 快速EMA大于慢速EMA
            &
            (dataframe['ema_fast'].shift() > dataframe['ema_slow'].shift())  # 前一周期快速EMA大于慢速EMA
            &
            (dataframe['ema_fast'].shift(2) > dataframe['ema_slow'].shift(2))  # 前两周期快速EMA大于慢速EMA
            &
            (dataframe['close'] < dataframe['bb_lowerband2'] * self.buy_bb_factor.value)  # 收盘价相对布林下轨位置
            &
            (dataframe['closedelta'] > dataframe['close'] * self.buy_closedelta.value)  # 收盘价变化条件
            &
            (dataframe['tail'] < dataframe['bb_delta'] * 0.75)  # 下影线长度条件
            &
            (dataframe['close'] < dataframe['bb_middleband2'] * 0.99)  # 收盘价相对布林中轨位置
            &
            (dataframe['close'] < dataframe['ema_slow'] * self.buy_ema_diff.value)  # 收盘价相对慢速EMA位置
            &
            (dataframe['cti'] < -0.8)  # CTI指标条件
            &
            (dataframe['r_14'] < -50.0)  # 威廉指标条件
        )

        # 买入条件7 - NFI 33
        is_nfi_33 = (  # NFI 33策略条件
            (dataframe['close'] < (dataframe['ema_50'] * 0.944))  # 价格低于50均线的94.4%
            &
            (dataframe['ewo'] > 8.2)  # EWO大于8.2
            &
            (dataframe['rsi'] < 32)  # RSI低于32
            &
            (dataframe['cti'] < -0.9)  # CTI低于-0.9
            &
            (dataframe['r_14'] < -98.0)  # 威廉指标极低
        )

        # 买入条件8 - NFI 38
        is_nfi_38 = (  # NFI 38策略条件
            (dataframe['close'] < dataframe['ema_50'] * 0.942)  # 价格低于50均线的94.2%
            &
            (dataframe['ewo'] > 7.8)  # EWO大于7.8
            &
            (dataframe['rsi'] < 32)  # RSI低于32
            &
            (dataframe['cti'] < -0.9)  # CTI低于-0.9
            &
            (dataframe['r_14'] < -98.0)  # 威廉指标极低
        )

        # 组合所有买入条件
        conditions.append(is_dip & is_nfi_7 & (is_bull_3) & (not is_pump))  # NFI 7策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'nfi7_'  # 添加标签

        conditions.append(is_dip & is_nfi_33 & (is_bull_3) & (not is_pump))  # NFI 33策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'nfi33_'  # 添加标签

        conditions.append(is_dip & is_nfi_37 & (is_bull_3) & (not is_pump))  # NFI 37策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'nfi37_'  # 添加标签

        conditions.append(is_dip & is_nfi_38 & (is_bull_3) & (not is_pump))  # NFI 38策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'nfi38_'  # 添加标签

        conditions.append(is_dip & is_nfi_44 & (is_bull_3) & (not is_pump))  # NFI 44策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'nfi44_'  # 添加标签

        conditions.append(is_dip & is_nfi_static & (is_bull_3) & (not is_pump))  # NFI static策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'nfi_static_'  # 添加标签

        conditions.append(is_dip & is_clucHA & (is_bull_3) & (not is_pump))  # ClucHA策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'clucHA_'  # 添加标签

        conditions.append(is_dip & is_local_uptrend & (is_bull_3) & (not is_pump))  # Local Uptrend策略条件
        dataframe.loc[conditions[-1], 'enter_tag'] += 'local_uptrend_'  # 添加标签

        if conditions:  # 如果有任何买入条件满足
            dataframe.loc[
                reduce(lambda x, y: x | y, conditions),  # 合并所有条件
                'enter_long'  # 设置买入信号
            ] = 1

        return dataframe  # 返回处理后的数据框

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        # 使用dataframe.loc来设置退出条件
        dataframe.loc[
            # Fisher指标大于设定的卖出阈值
            (dataframe['fisher'] > self.sell_fisher.value) &
            # 当前最高价小于等于前一根K线的最高价(价格下降)
            (dataframe['ha_high'].le(dataframe['ha_high'].shift(1))) &
            # 前一根K线的最高价小于等于前两根K线的最高价(持续下降)
            (dataframe['ha_high'].shift(1).le(dataframe['ha_high'].shift(2))) &
            # 当前收盘价小于等于前一根K线的收盘价(价格下跌)
            (dataframe['ha_close'].le(dataframe['ha_close'].shift(1))) &
            # 快速EMA大于平滑收盘价(下跌趋势确认)
            (dataframe['ema_fast'] > dataframe['ha_close']) &
            # 平滑收盘价乘以系数大于布林带中轨(价格偏高)
            ((dataframe['ha_close'] * self.sell_bbmiddle_close.value) > dataframe['bb_middleband']) &
            # 成交量大于0(确保有交易发生)
            (dataframe['volume'] > 0),
            # 满足以上所有条件时,设置sell信号为0
            'sell'
        ] = 0

        # 返回处理后的数据框
        return dataframe

    # DCA(动态成本平均)策略的初始参数设置
    initial_safety_order_trigger = -0.018  # 首次DCA触发的亏损比例为-1.8%
    max_safety_orders = 8  # 最大DCA次数为8次
    safety_order_step_scale = 1.2  # DCA步长的缩放比例为1.2
    safety_order_volume_scale = 1.4  # DCA数量的缩放比例为1.4

    # 计算DCA策略中的百分比变化
    def top_percent_change_dca(self, dataframe: DataFrame, length: int) -> float:
        """
        计算当前收盘价与区间最高开盘价的百分比变化
        用于DCA(动态成本平均)策略
        """
        # 如果length为0,直接计算当前K线的开盘价与收盘价的变化率
        if length == 0:
            return (dataframe['open'] - dataframe['close']) / dataframe['close']
        # 如果length不为0,计算过去length根K线的最高开盘价与当前收盘价的变化率
        else:
            return (dataframe['open'].rolling(length).max() - dataframe['close']) / dataframe['close']

    def adjust_trade_position(self, trade: Trade, current_time: datetime,
                              current_rate: float, current_profit: float, min_stake: float,
                              max_stake: float, **kwargs):
        # 如果当前利润大于初始DCA触发阈值,则不执行DCA
        if current_profit > self.initial_safety_order_trigger:
            return None

        # 获取交易对的数据框
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        # 获取最新K线数据
        last_candle = dataframe.iloc[-1].squeeze()

        # 获取已完成的买入订单
        filled_buys = trade.select_filled_orders('buy')
        count_of_buys = len(filled_buys)

        # DCA条件1: 第一次追加时的条件判断
        if count_of_buys == 1 and (last_candle['tpct_change_0'] > 0.018) and (last_candle['close'] < last_candle['open']) :
            return None
        
        # DCA条件2: 第二次追加时的条件判断
        elif count_of_buys == 2 and (last_candle['tpct_change_0'] > 0.018) and (last_candle['close'] < last_candle['open']) and (last_candle['ema_vwap_diff_50'] < 0.215):
            return None
        
        # DCA条件3: 第三次追加时的条件判断
        elif count_of_buys == 3 and (last_candle['tpct_change_0'] > 0.018) and (last_candle['close'] < last_candle['open'])and (last_candle['ema_vwap_diff_50'] < 0.215) :
            return None
        
        # DCA条件4: 第四次追加时的条件判断,增加了EMA指标条件
        elif count_of_buys == 4 and (last_candle['tpct_change_0'] > 0.018) and (last_candle['close'] < last_candle['open'])and (last_candle['ema_vwap_diff_50'] < 0.215) and (last_candle['ema_5']) >= (last_candle['ema_10']):
            return None
        
        # DCA条件5-8: 第五到第八次追加时的条件判断,增加了CMF和RSI指标条件
        elif count_of_buys == 5 and (last_candle['cmf_1h'] < 0.00) and (last_candle['close'] < last_candle['open']) and (last_candle['rsi_14_1h'] < 30) and (last_candle['tpct_change_0'] > 0.018) and (last_candle['close'] < last_candle['open']) and (last_candle['ema_vwap_diff_50'] < 0.215) and (last_candle['ema_5']) >= (last_candle['ema_10']):
            # 记录日志,等待CMF和RSI指标改善
            logger.info(f"DCA for {trade.pair} waiting for cmf_1h ({last_candle['cmf_1h']}) to rise above 0. Waiting for rsi_1h ({last_candle['rsi_14_1h']})to rise above 30")
            return None
        # 后续条件6-8与条件5类似,只是追加次数不同

        # 如果当前买入次数在允许范围内(1-8次)
        if 1 <= count_of_buys <= self.max_safety_orders:
            # 计算安全订单触发器
            safety_order_trigger = (abs(self.initial_safety_order_trigger) * count_of_buys)
            
            # 如果步长比例大于1,使用指数增长计算触发器
            if (self.safety_order_step_scale > 1):
                safety_order_trigger = abs(self.initial_safety_order_trigger) + (abs(self.initial_safety_order_trigger) * self.safety_order_step_scale * (math.pow(self.safety_order_step_scale,(count_of_buys - 1)) - 1) / (self.safety_order_step_scale - 1))
            # 如果步长比例小于1,使用对数增长计算触发器
            elif (self.safety_order_step_scale < 1):
                safety_order_trigger = abs(self.initial_safety_order_trigger) + (abs(self.initial_safety_order_trigger) * self.safety_order_step_scale * (1 - math.pow(self.safety_order_step_scale,(count_of_buys - 1))) / (1 - self.safety_order_step_scale))

            # 如果当前亏损达到触发条件
            if current_profit <= (-1 * abs(safety_order_trigger)):
                try:
                    # 获取第一笔订单的投资额
                    stake_amount = filled_buys[0].cost
                    # 根据追加次数计算当前安全订单的大小
                    stake_amount = stake_amount * math.pow(self.safety_order_volume_scale,(count_of_buys - 1))
                    amount = stake_amount / current_rate
                    # 记录日志
                    logger.info(f"Initiating safety order buy #{count_of_buys} for {trade.pair} with stake amount of {stake_amount} which equals {amount}")
                    return stake_amount
                except Exception as exception:
                    # 如果出现错误,记录日志并返回None
                    logger.info(f'Error occured while trying to get stake amount for {trade.pair}: {str(exception)}') 
                    return None

        return None

# PMAX指标计算函数
def pmax(df, period, multiplier, length, MAtype, src):
    # 将输入参数转换为整数
    period = int(period)      # PMAX周期
    multiplier = int(multiplier)  # ATR乘数
    length = int(length)      # 移动平均长度
    MAtype = int(MAtype)      # 移动平均类型
    src = int(src)           # 价格源

    # 构建指标名称
    mavalue = 'MA_' + str(MAtype) + '_' + str(length)  # 移动平均值的列名
    atr = 'ATR_' + str(period)  # ATR的列名
    pm = 'pm_' + str(period) + '_' + str(multiplier) + '_' + str(length) + '_' + str(MAtype)  # PMAX值的列名
    pmx = 'pmX_' + str(period) + '_' + str(multiplier) + '_' + str(length) + '_' + str(MAtype)  # PMAX方向的列名

    # 选择价格源
    if src == 1:
        masrc = df["close"]  # 使用收盘价
    elif src == 2:
        masrc = (df["high"] + df["low"]) / 2  # 使用高低价平均值
    elif src == 3:
        masrc = (df["high"] + df["low"] + df["close"] + df["open"]) / 4  # 使用OHLC平均值

    # 根据MAtype选择不同的移动平均计算方法
    if MAtype == 1:
        mavalue = ta.EMA(masrc, timeperiod=length)  # 指数移动平均
    elif MAtype == 2:
        mavalue = ta.DEMA(masrc, timeperiod=length)  # 双指数移动平均
    elif MAtype == 3:
        mavalue = ta.T3(masrc, timeperiod=length)  # T3移动平均
    elif MAtype == 4:
        mavalue = ta.SMA(masrc, timeperiod=length)  # 简单移动平均
    elif MAtype == 5:
        mavalue = VIDYA(df, length=length)  # VIDYA移动平均
    elif MAtype == 6:
        mavalue = ta.TEMA(masrc, timeperiod=length)  # 三重指数移动平均
    elif MAtype == 7:
        mavalue = ta.WMA(df, timeperiod=length)  # 加权移动平均
    elif MAtype == 8:
        mavalue = vwma(df, length)  # 成交量加权移动平均
    elif MAtype == 9:
        mavalue = zema(df, period=length)  # Zero lag EMA

    # 计算ATR和基础上下轨
    df[atr] = ta.ATR(df, timeperiod=period)  # 计算ATR
    df['basic_ub'] = mavalue + ((multiplier/10) * df[atr])  # 基础上轨
    df['basic_lb'] = mavalue - ((multiplier/10) * df[atr])  # 基础下轨

    # 初始化最终上下轨数组
    basic_ub = df['basic_ub'].values
    final_ub = np.full(len(df), 0.00)
    basic_lb = df['basic_lb'].values
    final_lb = np.full(len(df), 0.00)

    # 计算最终上下轨
    for i in range(period, len(df)):
        # 计算最终上轨
        final_ub[i] = basic_ub[i] if (
            basic_ub[i] < final_ub[i - 1]
            or mavalue[i - 1] > final_ub[i - 1]) else final_ub[i - 1]
        # 计算最终下轨
        final_lb[i] = basic_lb[i] if (
            basic_lb[i] > final_lb[i - 1]
            or mavalue[i - 1] < final_lb[i - 1]) else final_lb[i - 1]

    # 将计算结果存入数据框
    df['final_ub'] = final_ub
    df['final_lb'] = final_lb

    # 计算PMAX值
    pm_arr = np.full(len(df), 0.00)
    for i in range(period, len(df)):
        pm_arr[i] = (
            final_ub[i] if (pm_arr[i - 1] == final_ub[i - 1]
                                    and mavalue[i] <= final_ub[i])
        else final_lb[i] if (
            pm_arr[i - 1] == final_ub[i - 1]
            and mavalue[i] > final_ub[i]) else final_lb[i]
        if (pm_arr[i - 1] == final_lb[i - 1]
            and mavalue[i] >= final_lb[i]) else final_ub[i]
        if (pm_arr[i - 1] == final_lb[i - 1]
            and mavalue[i] < final_lb[i]) else 0.00)

    pm = Series(pm_arr)  # 转换为Pandas Series

    # 标记趋势方向（上涨/下跌）
    pmx = np.where((pm_arr > 0.00), np.where((mavalue < pm_arr), 'down',  'up'), np.NaN)

    # 返回PMAX值和趋势方向
    return pm, pmx
    
