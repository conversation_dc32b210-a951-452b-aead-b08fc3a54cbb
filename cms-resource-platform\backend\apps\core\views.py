"""
核心功能视图
"""
import os
from django.http import JsonResponse, HttpResponse, Http404
from django.shortcuts import get_object_or_404
from django.conf import settings
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser
from apps.resources.models import Resource
from apps.users.models import User
from .utils import (
    generate_unique_filename, get_file_type, is_valid_file_type,
    validate_download_token, decrypt_file_path
)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def health_check(request):
    """健康检查"""
    return Response({
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'version': '1.0.0'
    })


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def system_stats(request):
    """系统统计"""
    from apps.resources.models import Resource, ResourceDownload
    from apps.users.models import User
    
    stats = {
        'total_users': User.objects.count(),
        'total_resources': Resource.objects.filter(status='published').count(),
        'total_downloads': ResourceDownload.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
    }
    
    return Response(stats)


class FileUploadView(APIView):
    """文件上传"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request):
        file_obj = request.FILES.get('file')
        if not file_obj:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查文件大小
        max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 100 * 1024 * 1024)  # 100MB
        if file_obj.size > max_size:
            return Response(
                {'error': f'File too large. Max size: {max_size // 1024 // 1024}MB'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查文件类型
        if not is_valid_file_type(file_obj.name):
            return Response(
                {'error': 'File type not allowed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 生成唯一文件名
        filename = generate_unique_filename(file_obj.name)
        file_path = os.path.join(settings.MEDIA_ROOT, 'uploads', filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存文件
        with open(file_path, 'wb+') as destination:
            for chunk in file_obj.chunks():
                destination.write(chunk)
        
        return Response({
            'filename': filename,
            'original_name': file_obj.name,
            'size': file_obj.size,
            'file_type': get_file_type(file_obj.name),
            'url': f'/media/uploads/{filename}'
        })


class ImageUploadView(APIView):
    """图片上传"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request):
        image_obj = request.FILES.get('image')
        if not image_obj:
            return Response(
                {'error': 'No image provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查是否为图片
        if not image_obj.content_type.startswith('image/'):
            return Response(
                {'error': 'File is not an image'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查文件大小
        max_size = 10 * 1024 * 1024  # 10MB
        if image_obj.size > max_size:
            return Response(
                {'error': 'Image too large. Max size: 10MB'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 生成唯一文件名
        filename = generate_unique_filename(image_obj.name)
        file_path = os.path.join(settings.MEDIA_ROOT, 'images', filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存文件
        with open(file_path, 'wb+') as destination:
            for chunk in image_obj.chunks():
                destination.write(chunk)
        
        return Response({
            'filename': filename,
            'original_name': image_obj.name,
            'size': image_obj.size,
            'url': f'/media/images/{filename}'
        })


@require_http_methods(["GET"])
def protected_download(request, token):
    """受保护的文件下载"""
    # 从token中解析信息
    try:
        # 这里应该从token中解析出resource_id和user_id
        # 简化实现，实际应该使用JWT或其他安全方式
        resource_id = request.GET.get('resource_id')
        user_id = request.GET.get('user_id')
        
        if not resource_id or not user_id:
            raise Http404("Invalid download link")
        
        # 验证token
        if not validate_download_token(token, int(resource_id), int(user_id)):
            raise Http404("Download link expired or invalid")
        
        # 获取资源
        resource = get_object_or_404(Resource, id=resource_id)
        
        # 检查文件是否存在
        if not resource.resource_file or not os.path.exists(resource.resource_file.path):
            raise Http404("File not found")
        
        # 返回文件
        with open(resource.resource_file.path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename="{resource.title}"'
            response['Content-Length'] = os.path.getsize(resource.resource_file.path)
            return response
            
    except Exception as e:
        raise Http404("Download failed")
