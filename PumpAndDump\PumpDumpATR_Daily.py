import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from pandas import DataFrame
from datetime import datetime, timedelta
from typing import Dict, List, Optional


class PumpDumpATRDaily(IStrategy):
    """
    PumpDumpATR_Daily - 优化版泵出策略
    
    这个策略专注于识别加密货币市场中的"泵出"模式，并在价格回落时做空。
    主要特点:
    - 使用日线周期，减少市场噪音
    - 基于ATR的动态止损和止盈，适应不同市场波动性
    - 严格的入场条件，包括成交量、RSI和K线形态确认
    - 仓位管理和风险控制
    - 多重过滤条件，避免虚假信号
    
    作者: Claude AI & User
    版本: 2.2 (Final Crash Fix)
    日期: 2025-07-11
    """
    
    # --- DEBUG ---
    debug_mode = True
    debug_pair = 'PNUT/USDT'  # !! 请将这里替换为您回测时使用的一个交易对 !!
    # --- END DEBUG ---
    
    # 策略参数
    timeframe = '1d'
    minimal_roi = {"0": 100}
    stoploss = -0.99
    
    # 交易控制参数
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False
    
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }
    
    # 可调参数
    atr_period = IntParameter(7, 21, default=14, space="buy", optimize=True)
    stoploss_atr_multiplier = DecimalParameter(2.0, 4.0, default=3.0, decimals=1, space="buy", optimize=True)
    take_profit_atr_multiplier = DecimalParameter(4.0, 8.0, default=6.0, decimals=1, space="sell", optimize=True)
    
    # --- 全面升级：采用高级信号识别参数 ---
    short_consecutive_green_candles = IntParameter(2, 4, default=2, space="buy", optimize=True)
    short_pump_rsi_threshold = IntParameter(60, 80, default=65, space="buy", optimize=True)
    short_upper_wick_body_ratio = DecimalParameter(1.2, 1.8, default=1.3, decimals=1, space="buy", optimize=True)
    short_climax_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    short_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    short_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    short_min_climax_conditions = IntParameter(2, 4, default=2, space="buy", optimize=True)
    
    leverage_optimization = CategoricalParameter([1, 2, 3], default=2, space="buy", optimize=True)
    max_drawdown_allowed = DecimalParameter(5.0, 15.0, default=10.0, decimals=1, space="protection", optimize=True)
    position_size_percentage = DecimalParameter(20.0, 50.0, default=30.0, decimals=1, space="buy", optimize=True)
    
    trailing_stop = True
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True
    
    def informative_pairs(self):
        return []
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 基本指标
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['atr_ratio'] = dataframe['atr'] / dataframe['close']
        dataframe['ema_20'] = ta.EMA(dataframe, timeperiod=20)
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)

        # MACD
        macd = ta.MACD(dataframe, fastperiod=12, slowperiod=26, signalperiod=9)
        dataframe['macd'] = macd['macd']
        dataframe['macdsignal'] = macd['macdsignal']
        
        # DMI / ADX
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=14)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=14)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)

        # --- 核心信号指标：全面升级为高级评分系统 ---
        dataframe['is_green'] = dataframe['close'] > dataframe['open']

        # 1. 趋势条件: 连续N根上涨且成交量放大
        volume_is_rising = dataframe['volume'] > dataframe['volume'].shift(1)
        green_with_rising_vol = (dataframe['is_green']) & (volume_is_rising)
        dataframe['short_trend_cond'] = (green_with_rising_vol.rolling(
            window=self.short_consecutive_green_candles.value).sum() >= self.short_consecutive_green_candles.value
        ).astype(int)

        # 2. RSI条件
        dataframe['short_rsi_cond'] = (dataframe['rsi'] >= self.short_pump_rsi_threshold.value).astype(int)

        # 3. K线形态条件: 射击之星 (长上影线)
        body = abs(dataframe['close'] - dataframe['open'])
        upper_wick = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        dataframe['shooting_star'] = ((upper_wick > body * self.short_upper_wick_body_ratio.value) & (body > 0.000001)).astype(int)

        # 4. & 5. 振幅与成交量激增条件
        dataframe['range_1d'] = dataframe['high'] - dataframe['low']
        range_sma = ta.SMA(dataframe['range_1d'], timeperiod=self.short_climax_sma_period.value)
        volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.short_climax_sma_period.value)
        dataframe['short_range_spike'] = (dataframe['range_1d'] > range_sma * self.short_range_spike_multiplier.value).astype(int)
        dataframe['short_volume_spike'] = (dataframe['volume'] > volume_sma * self.short_volume_spike_multiplier.value).astype(int)

        # 综合评分并生成 "泵出K线" (climax_candle)
        conditions_met = (
            dataframe['short_trend_cond'] + 
            dataframe['short_rsi_cond'] + 
            dataframe['shooting_star'] + 
            dataframe['short_volume_spike'] + 
            dataframe['short_range_spike']
        )
        dataframe['is_pump_candle'] = (conditions_met >= self.short_min_climax_conditions.value).astype(int)
        
        # 停顿K线识别 - 采用参考策略的严格定义
        is_shrinking_volume = dataframe['volume'] < dataframe['volume'].shift(1)
        is_lower_high = dataframe['high'] <= dataframe['high'].shift(1)
        dataframe['pause_candle'] = (
            (dataframe['is_green']) &
            (is_shrinking_volume) &
            (is_lower_high)
        ).astype(int)

        # --- 智能调试日志 ---
        if self.debug_mode and metadata['pair'] == self.debug_pair:
            print(f"--- Indicator Analysis for {metadata['pair']} ---")
            columns_to_show = ['date', 'close', 'is_green', 'is_pump_candle', 'pause_candle', 
                               'short_trend_cond', 'short_rsi_cond', 'shooting_star', 
                               'short_range_spike', 'short_volume_spike']
            debug_df = dataframe[columns_to_show].copy()
            for col in debug_df.columns:
                if debug_df[col].dtype == 'bool':
                    debug_df[col] = debug_df[col].astype(int)
            print(debug_df.tail(15))
            print(f"(Info: 'is_pump_candle' becomes 1 if sum of conditions >= {self.short_min_climax_conditions.value})")
            print("--------------------------------------------------")
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['enter_short'] = 0
        dataframe['enter_long'] = 0
        
        is_red_candle = dataframe['close'] < dataframe['open']

        # 触发条件1: 泵出后立即反转
        trigger1 = (dataframe['is_pump_candle'].shift(1) == 1) & (is_red_candle)
        # 触发条件2: 泵出后，经过一个停顿日，再反转
        trigger2 = (dataframe['is_pump_candle'].shift(2) == 1) & (dataframe['pause_candle'].shift(1) == 1) & (is_red_candle)

        dataframe.loc[trigger1 | trigger2, 'enter_short'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['exit_short'] = 0
        dataframe['exit_long'] = 0
        
        dataframe.loc[
            (
                (dataframe['rsi'] < 30) |
                ((dataframe['plus_di'] > dataframe['minus_di']) & (dataframe['adx'] > 20))
            ),
            'exit_short'] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float, current_profit: float, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return self.stoploss
        
        last_candle = dataframe.iloc[-1].squeeze()
        atr_value = last_candle['atr']
        
        if trade.is_short:
            entry_price = trade.open_rate
            stop_price = entry_price * (1 + self.stoploss_atr_multiplier.value * atr_value / entry_price)
            stoploss_percent = (stop_price / current_rate - 1)
            return min(stoploss_percent, 0.5)
        
        return self.stoploss
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return None
        
        last_candle = dataframe.iloc[-1].squeeze()
        atr_value = last_candle['atr']
        
        # 斐波那契回调水平的计算
        window = 60
        max_price = dataframe['high'].rolling(window=window).max().iloc[-1]
        min_price = dataframe['low'].rolling(window=window).min().iloc[-1]
        fib_38_2 = min_price + 0.382 * (max_price - min_price)
        fib_50_0 = min_price + 0.5 * (max_price - min_price)

        if trade.is_short:
            entry_price = trade.open_rate
            take_profit_price = entry_price * (1 - self.take_profit_atr_multiplier.value * atr_value / entry_price)
            if current_rate <= take_profit_price:
                return "atr_take_profit"
            
            if current_profit > 0.1:
                if (last_candle['rsi'] > 50) or (last_candle['macd'] > last_candle['macdsignal']) or (last_candle['plus_di'] > last_candle['minus_di']):
                    return "trend_reversal_exit"
            
            if current_profit > 0.05:
                if (abs(current_rate - fib_38_2) / current_rate < 0.02 or abs(current_rate - fib_50_0) / current_rate < 0.02):
                    return "fibonacci_exit"
        
        max_profit = trade.calc_profit_ratio(trade.max_rate)
        if max_profit > 0.05 and (max_profit - current_profit) > self.max_drawdown_allowed.value / 100:
            return "drawdown_protection"
        
        if trade.is_open and current_profit < 0.05 and (current_time - trade.open_date_utc).days >= 14:
            return "time_exit"
        
        return None
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float, proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], side: str, **kwargs) -> float:
        return float(self.leverage_optimization.value)
    
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float, proposed_stake: float, min_stake: float, max_stake: float, entry_tag: Optional[str], side: str, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        custom_stake = max_stake * (self.position_size_percentage.value / 100)
        
        if not dataframe.empty:
            last_candle = dataframe.iloc[-1].squeeze()
            rsi_factor = min(1.2, max(0.8, last_candle['rsi'] / 75))
            atr_factor = min(1.2, max(0.8, 0.02 / (last_candle['atr_ratio'] or 0.02)))
            adx_factor = min(1.2, max(0.8, last_candle['adx'] / 25))
            adjustment_factor = (rsi_factor + atr_factor + adx_factor) / 3
            custom_stake = custom_stake * adjustment_factor
        
        return max(min_stake, min(custom_stake, max_stake))
    
    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float, time_in_force: str, current_time: datetime, entry_tag: Optional[str], side: str, **kwargs) -> bool:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty: return False
        
        last_candle = dataframe.iloc[-1].squeeze()
        
        if self.debug_mode and pair == self.debug_pair:
            print(f"\n--- Confirming entry for {pair} at {current_time} ---")
            print(f"Current rate: {rate}")
            check1 = side == 'sell'
            print(f"1. Side is 'sell'? -> {check1}")
            check2 = not (pd.isna(last_candle['rsi']) or pd.isna(last_candle['adx']) or pd.isna(last_candle['atr_ratio']))
            print(f"2. Data is not NaN (rsi, adx, atr_ratio)? -> {check2}")
            final_decision = check1 and check2
            print(f"--- Final Decision: {'ALLOW' if final_decision else 'REJECT'} ---")
        
        if side != 'sell': return False
        if pd.isna(last_candle['rsi']) or pd.isna(last_candle['adx']) or pd.isna(last_candle['atr_ratio']):
            return False
        
        return True 