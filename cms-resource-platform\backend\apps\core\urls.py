"""
核心功能URL配置
"""
from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    # 系统状态
    path('health/', views.health_check, name='health_check'),
    path('stats/', views.system_stats, name='system_stats'),
    
    # 文件上传
    path('upload/file/', views.FileUploadView.as_view(), name='file_upload'),
    path('upload/image/', views.ImageUploadView.as_view(), name='image_upload'),
    
    # 受保护的文件下载
    path('download/<str:token>/', views.protected_download, name='protected_download'),
]
