"""
支付系统管理后台
"""
from django.contrib import admin
from .models import PointsPackage, VIPPackage, PaymentOrder, CardCode, Withdrawal


@admin.register(PointsPackage)
class PointsPackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'points', 'bonus_points', 'price', 'final_price', 'is_active', 'sort_order']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['sort_order', 'price']


@admin.register(VIPPackage)
class VIPPackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'duration_days', 'price', 'original_price', 'bonus_points', 'is_active', 'sort_order']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['sort_order', 'duration_days']


@admin.register(PaymentOrder)
class PaymentOrderAdmin(admin.ModelAdmin):
    list_display = ['order_no', 'user', 'order_type', 'product_name', 'final_amount', 'payment_method', 'status', 'created_at']
    list_filter = ['order_type', 'payment_method', 'status', 'created_at']
    search_fields = ['order_no', 'user__email', 'product_name']
    ordering = ['-created_at']
    readonly_fields = ['order_no', 'created_at']


@admin.register(CardCode)
class CardCodeAdmin(admin.ModelAdmin):
    list_display = ['code', 'card_type', 'points_value', 'vip_days', 'is_used', 'used_by', 'expires_at']
    list_filter = ['card_type', 'is_used', 'created_at']
    search_fields = ['code', 'used_by__email']
    ordering = ['-created_at']
    readonly_fields = ['code', 'used_at']


@admin.register(Withdrawal)
class WithdrawalAdmin(admin.ModelAdmin):
    list_display = ['user', 'amount', 'fee', 'actual_amount', 'withdraw_type', 'status', 'created_at']
    list_filter = ['withdraw_type', 'status', 'created_at']
    search_fields = ['user__email']
    ordering = ['-created_at']
