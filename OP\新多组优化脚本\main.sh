#!/bin/bash

# ==============================================================================
# Main Orchestration Script for Freqtrade Strategy Optimization Workflow
# ==============================================================================
# This script automates the entire process of:
# 1. Running a group-based hyperoptimization for the strategy.
# 2. Combining the results to create a fully optimized strategy file.
# 3. Running backtesting and generating analysis reports on the new strategy.
# 4. Updating the user's pairlist based on recent volume data.
# ==============================================================================

set -e # Exit immediately if a command exits with a non-zero status.

# --- Configuration ---
# You can change these variables
STRATEGY_NAME="NFI5MOHO_WIP"
CONFIG_FILE="config.json"
BACKTEST_TIMERANGE="20210701-20210731" # Use a different period for backtesting than optimization
TOP_N_RESULTS=5 # For backtest analysis

# --- Automatically find user_data_dir and strategy_path ---
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Config file $CONFIG_FILE not found. Please ensure it is in the same directory."
    exit 1
fi

USER_DATA_DIR=$(jq -r '.user_data_dir // "user_data"' "$CONFIG_FILE")
HYPEROPT_RESULTS_DIR="$USER_DATA_DIR/hyperopt_results"
STRATEGY_PATH_IN_CONFIG=$(jq -r '.strategy_path // "user_data/strategies"' "$CONFIG_FILE")
FULL_STRATEGY_FILE_PATH="$STRATEGY_PATH_IN_CONFIG/杂/$STRATEGY_NAME.py"

# --- Define output files ---
BACKTEST_RESULTS_DIR="$USER_DATA_DIR/backtest_results"
BACKTEST_ANALYSIS_FILE="backtest_analysis_report.md"
PAIRLIST_UPDATE_LOG="pairlist_update.log"

# Create necessary directories
mkdir -p "$BACKTEST_RESULTS_DIR"

# ==============================================================================
# STEP 1: Run the Full Group-Based Hyperoptimization Process
# ==============================================================================
echo "#################################################################"
echo "### STEP 1: STARTING GROUP-BASED HYPEROPTIMIZATION            ###"
echo "#################################################################"

# This script now handles the entire loop of optimizing each group,
# processing results, and combining them into the final strategy file.
bash ./1_run_optimization.sh

if [ $? -ne 0 ]; then
    echo "ERROR: The main optimization process in '1_run_optimization.sh' failed."
    exit 1
fi

echo "SUCCESS: Group-based hyperoptimization and strategy update complete."
echo ""

# ==============================================================================
# STEP 2: Run Backtesting and Analysis on the Optimized Strategy
# ==============================================================================
echo "#################################################################"
echo "### STEP 2: RUNNING BACKTESTING & ANALYSIS                    ###"
echo "#################################################################"

# This script runs backtesting and then analyzes the results.
bash ./3_run_backtest_and_analysis.sh \
    "$STRATEGY_NAME" \
    "$CONFIG_FILE" \
    "$BACKTEST_TIMERANGE" \
    "$BACKTEST_RESULTS_DIR" \
    "$BACKTEST_ANALYSIS_FILE" \
    "$TOP_N_RESULTS"

if [ $? -ne 0 ]; then
    echo "ERROR: Backtesting and analysis process in '3_run_backtest_and_analysis.sh' failed."
    exit 1
fi

echo "SUCCESS: Backtesting and analysis complete. Report saved to '$BACKTEST_ANALYSIS_FILE'."
echo ""


# ==============================================================================
# STEP 3: Update Pairlist by Volume
# ==============================================================================
echo "#################################################################"
echo "### STEP 3: UPDATING PAIRLIST BY VOLUME                       ###"
echo "#################################################################"

# This script updates the pairlist based on volume.
# Make sure your config.json is set up for this (e.g., stake currency).
python ./6_update_pairlist_by_volume.py --config "$CONFIG_FILE" > "$PAIRLIST_UPDATE_LOG"

if [ $? -ne 0 ]; then
    echo "ERROR: Pairlist update script '6_update_pairlist_by_volume.py' failed."
    exit 1
fi

echo "SUCCESS: Pairlist updated. See '$PAIRLIST_UPDATE_LOG' for details."
echo ""

# ==============================================================================
# WORKFLOW COMPLETE
# ==============================================================================
echo "#################################################################"
echo "### ALL STEPS COMPLETED SUCCESSFULLY!                         ###"
echo "#################################################################"
echo "Final optimized strategy is in: $FULL_STRATEGY_FILE_PATH"
echo "Backtest analysis report is in: $BACKTEST_ANALYSIS_FILE"
echo "Pairlist update log is in: $PAIRLIST_UPDATE_LOG" 