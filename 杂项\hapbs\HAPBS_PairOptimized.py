# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy (HAPBS) - Pair Optimized
#
#   作者: Gemini & User
#   版本: PairOptimized
#
#   策略理念:
#   - 本策略是 HAPBS_Final 的一个变体，允许为每个交易对配置独立的优化参数。
#   - 它从一个 JSON 配置文件 (HAPBS_PairOptimized_Settings.json) 中读取每个交易对的参数设置。
#   - 核心逻辑与 Final 版本保持一致，但在指标计算和入场条件判断时，会使用对应交易对的专属参数。
# --------------------------------

class HAPBS_PairOptimized(IStrategy):

    # 将 custom_info 定义为类属性，以确保所有实例共享同一份配置
    custom_info: dict = {}

    def __init__(self, config: dict):
        super().__init__(config)
        # 仅在类属性为空时加载一次配置，避免重复加载
        if not HAPBS_PairOptimized.custom_info:
            self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                # 加载到类属性中
                HAPBS_PairOptimized.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it to function.")
            HAPBS_PairOptimized.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}. Strategy may not function correctly.")
            HAPBS_PairOptimized.custom_info = {}

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 风险控制: 动态盈亏管理系统 ---
    # Freqtrade 要求 stoploss 属性必须存在。设置一个很大的值，确保它永远不会被触发。
    # 实际的止损将由 custom_stoploss 函数动态决定。
    stoploss = -0.99
    use_custom_stoploss = True
    
    # 旧的固定止盈止损已被下方的动态逻辑替代。
    # 您需要在 HAPBS_PairOptimized_Settings.json 文件中为每个交易对配置以下新参数:
    # "cs_atr_period": 14,
    # "cs_atr_multiplier": 2.5,
    # "cs_lookback": 10,
    # "exit_rr_ratio": 1.5,
    # "exit_atr_tsl_period": 14,
    # "exit_atr_tsl_multiplier": 2.8

    # --- 图表配置 ---
    plot_config = {
        'main_plot': {
            'ema_short_long': {'color': 'blue', 'linestyle': '-'},
            'ema_long_long': {'color': 'cyan', 'linestyle': '-'},
            'ema_short_short': {'color': 'red', 'linestyle': '--'},
            'ema_long_short': {'color': 'magenta', 'linestyle': '--'},
            'ema_200': {'color': 'black', 'linestyle': ':'},
            # 添加ATR追踪止损线到图表
            'atr_tsl_long': {'color': 'green', 'linestyle': ':'},
            'atr_tsl_short': {'color': 'orange', 'linestyle': ':'},
        },
        'subplots': {
            "ADX": {
                'adx': {'color': 'green'},
            },
            "ATR": {
                'atr': {'color': 'purple'},
            },
        },
    }

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            return dataframe

        pair_settings = HAPBS_PairOptimized.custom_info[pair]

        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        
        dataframe['ha_strong_bear'] = (
            (dataframe['ha_close'] < dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_high'])
        )
        
        dataframe['ha_strong_bull'] = (
            (dataframe['ha_close'] > dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_low'])
        )

        # 添加十字星K线定义
        body_size = abs(dataframe['ha_close'] - dataframe['ha_open'])
        candle_range = dataframe['ha_high'] - dataframe['ha_low']
        dataframe['ha_doji'] = (body_size < candle_range * 0.03) # 收紧定义

        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_long'])
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_long'])
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_short'])
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_short'])
        
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        
        # ATR指标，用于止损和止盈
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=pair_settings.get('exit_atr_tsl_period', 14))

        # ATR追踪止损线
        tsl_high = dataframe['high'].rolling(pair_settings.get('exit_atr_tsl_period', 14)).max()
        dataframe['atr_tsl_long'] = tsl_high - (dataframe['atr'] * pair_settings.get('exit_atr_tsl_multiplier', 2.8))
        tsl_low = dataframe['low'].rolling(pair_settings.get('exit_atr_tsl_period', 14)).min()
        dataframe['atr_tsl_short'] = tsl_low + (dataframe['atr'] * pair_settings.get('exit_atr_tsl_multiplier', 2.8))
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            return dataframe

        pair_settings = HAPBS_PairOptimized.custom_info[pair]

        # --- 信号1：直接突破入场 ---
        long_breakout_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long'])) &
            (dataframe['ha_strong_bull']) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_breakout_conditions, ['enter_long', 'enter_tag']] = (1, 'long_breakout')

        # --- 信号1：直接破位入场 ---
        short_breakdown_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short'])) &
            (dataframe['ha_strong_bear']) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_breakdown_conditions, ['enter_short', 'enter_tag']] = (1, 'short_breakdown')

        # --- 信号2：突破后跟进入场 ---
        recent_cross_up = qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long']).shift(1).rolling(3).sum() > 0
        
        long_followup_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (dataframe['ha_strong_bull']) &
            (dataframe['ha_close'] > dataframe['ema_short_long']) &
            (recent_cross_up) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_followup_conditions, ['enter_long', 'enter_tag']] = (1, 'long_followup')

        # --- 信号2：破位后跟进入场 ---
        recent_cross_down = qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short']).shift(1).rolling(3).sum() > 0

        short_followup_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (dataframe['ha_strong_bear']) &
            (dataframe['ha_close'] < dataframe['ema_short_short']) &
            (recent_cross_down) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_followup_conditions, ['enter_short', 'enter_tag']] = (1, 'short_followup')

        # --- 信号3：十字星后跟进入场 ---
        recent_doji = dataframe['ha_doji'].shift(1).rolling(5).sum() > 0

        long_doji_followup_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (dataframe['ha_strong_bull']) &
            (dataframe['ha_close'] > dataframe['ema_short_long']) &
            (recent_doji) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_doji_followup_conditions, ['enter_long', 'enter_tag']] = (1, 'long_doji_followup')

        short_doji_followup_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (dataframe['ha_strong_bear']) &
            (dataframe['ha_close'] < dataframe['ema_short_short']) &
            (recent_doji) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_doji_followup_conditions, ['enter_short', 'enter_tag']] = (1, 'short_doji_followup')

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                        current_rate: float, current_profit: float, **kwargs) -> float:
        
        # 从类属性中获取配置
        if pair not in HAPBS_PairOptimized.custom_info:
            return -0.99 # Fallback, should not happen if pair is in whitelist

        pair_settings = HAPBS_PairOptimized.custom_info[pair]
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        lookback_period = pair_settings.get('cs_lookback', 10)
        atr_multiplier = pair_settings.get('cs_atr_multiplier', 2.5)

        # 获取入场信号蜡烛 (交易发生前的那一根)
        # 这种方法更健壮，可以应对各种索引类型和数据缺失情况
        prev_candles = dataframe.loc[dataframe['date'] < trade.open_date_utc]
        if prev_candles.empty:
            # Fallback for cases where the open date is at the start of the data.
            return -self.stoploss # Return a value that's definitely a valid stoploss
        
        trade_entry_candle = prev_candles.iloc[-1]

        if trade.is_short:
            high_stop = dataframe['high'].iloc[-lookback_period:].max()
            atr_stop = trade_entry_candle['ha_close'] + (trade_entry_candle['atr'] * atr_multiplier)
            stoploss_price = max(high_stop, atr_stop)
        else:
            low_stop = dataframe['low'].iloc[-lookback_period:].min()
            atr_stop = trade_entry_candle['ha_close'] - (trade_entry_candle['atr'] * atr_multiplier)
            stoploss_price = min(low_stop, atr_stop)
            
        return stoploss_price

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe.loc[
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['atr_tsl_long'])),
            'exit_long'] = 1
        
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['atr_tsl_short'])),
            'exit_short'] = 1

        return dataframe 