# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


class ScoringStrategy9(IStrategy):
    """
    ScoringStrategy9 - 改进版混合智能策略
    
    核心改进：
    1. 建立趋势硬性条件体系 - 将趋势判断从评分指标提升为入场必要条件
    2. 提升信号质量标准 - 收紧技术指标范围，增加多重确认机制
    3. 实施市场环境过滤 - 只在强趋势或突破状态下交易
    4. 强化风险控制 - 多层次风险过滤，避免过度交易
    
    改进目标：
    - 减少交易频率，提高信号质量
    - 避免震荡市中的频繁假信号
    - 确保每次入场都有强烈的趋势支撑
    - 实现更稳定的实盘表现
    """

    INTERFACE_VERSION = 3

    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.

    # Optimal timeframe for the strategy.
    timeframe = '1h'

    # Can this strategy go short?
    can_short: bool = False

    # ROI设置 - 支持hyperopt优化
    minimal_roi = {
        "0": 0.015,  # 提高最小收益要求
        "10": 0.010,
        "20": 0.008,
        "45": 0.006,
        "90": 0.004,
        "150": 0.002
    }

    # 优化的止损设置 - 基于回测结果调整
    stoploss = -0.08  # 收紧止损

    # Trailing stoploss
    trailing_stop = False

    # Run "populate_indicators" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    # Strategy parameters - 智能融合系统参数
    
    # === 策略权重参数 ===
    adx_slope_weight = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True)
    adaptive_weight = DecimalParameter(0.2, 0.6, default=0.3, space="buy", optimize=True)
    candlestick_weight = DecimalParameter(0.1, 0.4, default=0.2, space="buy", optimize=True)
    
    # === 智能融合参数 ===
    signal_quality_threshold = DecimalParameter(0.6, 0.9, default=0.7, space="buy", optimize=True)  # 提高信号质量要求
    fusion_confidence_threshold = DecimalParameter(0.7, 0.9, default=0.8, space="buy", optimize=True)  # 提高融合置信度要求
    market_regime_sensitivity = DecimalParameter(0.5, 2.0, default=1.0, space="buy", optimize=True)
    
    # === 趋势硬性条件参数 ===
    trend_strength_min = DecimalParameter(0.5, 0.8, default=0.6, space="buy", optimize=True)  # 最小趋势强度
    trend_consistency_periods = IntParameter(2, 5, default=3, space="buy", optimize=True)  # 趋势一致性周期
    volume_confirmation_multiplier = DecimalParameter(1.2, 2.0, default=1.5, space="buy", optimize=True)  # 成交量确认倍数
    
    # === ADX斜率策略参数 ===
    adx_period = IntParameter(10, 20, default=14, space="buy", optimize=True)
    adx_slope_threshold = DecimalParameter(0.01, 0.05, default=0.02, space="buy", optimize=True)
    adx_strength_threshold = DecimalParameter(20, 40, default=25, space="buy", optimize=True)
    
    # === 自适应系统参数 ===
    ema_short = IntParameter(8, 15, default=12, space="buy", optimize=True)
    ema_long = IntParameter(20, 30, default=26, space="buy", optimize=True)
    adaptive_threshold = DecimalParameter(1.5, 2.5, default=2.0, space="buy", optimize=True)
    cooldown_period = IntParameter(3, 10, default=5, space="buy", optimize=True)
    
    # === K线形态参数 ===
    candlestick_threshold = DecimalParameter(1.2, 2.0, default=1.5, space="buy", optimize=True)
    pattern_strength_threshold = DecimalParameter(0.6, 1.0, default=0.8, space="buy", optimize=True)
    
    # === 动态风控参数 ===
    dynamic_risk_multiplier = DecimalParameter(0.5, 2.0, default=1.0, space="sell", optimize=True)
    emergency_exit_threshold = DecimalParameter(-0.06, -0.04, default=-0.05, space="sell", optimize=True)  # 收紧紧急出场
    volatility_adjustment = DecimalParameter(0.5, 1.5, default=1.0, space="sell", optimize=True)

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pairs will automatically be available in populate_indicators.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        
        Performance Note: For maximum performance be frugal on the number of indicators
        you are using. Let uncomment only the indicator you are using in your strategies
        or your hyperopt configuration, otherwise you will waste your memory and CPU usage.
        :param dataframe: Dataframe with data from the exchange
        :param metadata: Additional information, like the currently traded pair
        :return: a Dataframe with all mandatory indicators for the strategies
        """
        
        # === 基础指标计算 ===
        dataframe = self.calculate_base_indicators(dataframe)
        
        # === ADX斜率指标 ===
        dataframe = self.calculate_adx_slope_indicators(dataframe)
        
        # === 自适应系统指标 ===
        dataframe = self.calculate_adaptive_indicators(dataframe)
        
        # === K线形态指标 ===
        dataframe = self.calculate_candlestick_indicators(dataframe)
        
        # === 市场环境分析指标 ===
        dataframe = self.calculate_market_regime_indicators(dataframe)
        
        # === 智能融合指标 ===
        dataframe = self.calculate_fusion_indicators(dataframe)
        
        # === 趋势硬性条件检查 ===
        dataframe = self.calculate_trend_hard_conditions(dataframe)
        
        return dataframe

    def calculate_base_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算基础技术指标"""
        
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # MACD
        macd = ta.MACD(dataframe)
        dataframe['macd'] = macd['macd']
        dataframe['macdsignal'] = macd['macdsignal']
        dataframe['macdhist'] = macd['macdhist']
        
        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(dataframe['close'], window=20, stds=2)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']
        dataframe['bb_percent'] = (dataframe['close'] - dataframe['bb_lowerband']) / (dataframe['bb_upperband'] - dataframe['bb_lowerband'])
        dataframe['bb_width'] = (dataframe['bb_upperband'] - dataframe['bb_lowerband']) / dataframe['bb_middleband']
        
        # ATR for volatility
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        
        # Volume indicators
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        return dataframe

    def calculate_adx_slope_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算ADX斜率相关指标 - 恢复严格条件"""
        
        # ADX calculation
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_period.value)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=self.adx_period.value)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=self.adx_period.value)
        
        # ADX斜率计算 - 核心创新指标
        dataframe['adx_slope'] = (dataframe['adx'] - dataframe['adx'].shift(1)) / dataframe['adx'].shift(1)
        dataframe['adx_slope_sma'] = ta.SMA(dataframe['adx_slope'], timeperiod=3)
        
        # ADX强度评估
        dataframe['adx_strength'] = np.where(
            dataframe['adx'] > self.adx_strength_threshold.value,
            1, 0
        )
        
        # 趋势方向确认
        dataframe['trend_direction'] = np.where(
            dataframe['plus_di'] > dataframe['minus_di'], 1, -1
        )
        
        # ADX斜率信号（恢复严格条件）
        dataframe['adx_slope_signal'] = np.where(
            (dataframe['adx_slope'] > self.adx_slope_threshold.value) &  # 恢复原始斜率要求
            (dataframe['adx'] > self.adx_strength_threshold.value) &     # 恢复原始强度要求
            (dataframe['trend_direction'] > 0) &
            (dataframe['adx'] > dataframe['adx'].shift(1)),  # ADX递增确认
            1, 0
        )
        
        return dataframe

    def calculate_adaptive_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算自适应系统指标 - 收紧条件"""
        
        # EMA计算
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=self.ema_short.value)
        dataframe['ema_long'] = ta.EMA(dataframe, timeperiod=self.ema_long.value)
        
        # EMA趋势过滤
        dataframe['ema_trend'] = np.where(
            dataframe['ema_short'] > dataframe['ema_long'], 1, 0
        )
        
        # 市场状态识别（简化版）
        dataframe['volatility_regime'] = np.where(
            dataframe['bb_width'] > dataframe['bb_width'].rolling(20).mean(), 1, 0
        )
        
        dataframe['trend_regime'] = np.where(
            dataframe['adx'] > 25, 1, 0
        )
        
        # 自适应信号（收紧条件）
        dataframe['rsi_trend'] = dataframe['rsi'] > dataframe['rsi'].rolling(3).mean()  # RSI趋势确认
        dataframe['adaptive_signal'] = np.where(
            (dataframe['ema_trend'] == 1) &
            (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70) &  # 收紧RSI范围
            (dataframe['rsi_trend'] == True) &  # RSI趋势确认
            (dataframe['macd'] > dataframe['macdsignal']) &
            (dataframe['macdhist'] > 0) &  # MACD柱状图为正
            (dataframe['macdhist'] > dataframe['macdhist'].shift(1)),  # MACD柱状图递增
            1, 0
        )
        
        return dataframe

    def calculate_candlestick_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算K线形态指标 - 收紧条件"""
        
        # 基础K线形态
        dataframe['hammer'] = ta.CDLHAMMER(dataframe)
        dataframe['doji'] = ta.CDLDOJI(dataframe)
        dataframe['engulfing'] = ta.CDLENGULFING(dataframe)
        dataframe['morning_star'] = ta.CDLMORNINGSTAR(dataframe)
        dataframe['piercing'] = ta.CDLPIERCING(dataframe)
        
        # 形态强度评估
        bullish_patterns = ['hammer', 'engulfing', 'morning_star', 'piercing']
        dataframe['bullish_pattern_count'] = 0
        
        for pattern in bullish_patterns:
            dataframe['bullish_pattern_count'] += np.where(dataframe[pattern] > 0, 1, 0)
        
        # K线形态信号（收紧条件）
        dataframe['candlestick_signal'] = np.where(
            (dataframe['bullish_pattern_count'] >= 2) &  # 至少2个看涨形态
            (dataframe['close'] > dataframe['bb_middleband']) &  # 价格在中轨之上
            (dataframe['bb_width'] > dataframe['bb_width'].rolling(20).mean()),  # 布林带宽度大于平均值
            1, 0
        )
        
        return dataframe

    def calculate_market_regime_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算市场环境分析指标"""

        # 趋势强度
        dataframe['trend_strength'] = dataframe['adx'] / 50.0  # 归一化到0-1

        # 波动率水平
        dataframe['volatility_level'] = dataframe['bb_width'] / dataframe['bb_width'].rolling(50).mean()

        # 市场效率（价格变化与波动的比率）
        price_change = abs(dataframe['close'] - dataframe['close'].shift(10))
        path_length = dataframe['atr'].rolling(10).sum()
        dataframe['market_efficiency'] = price_change / path_length

        # 市场状态分类
        conditions = [
            (dataframe['trend_strength'] > 0.5) & (dataframe['volatility_level'] < 1.2),  # STRONG_TREND
            (dataframe['volatility_level'] > 1.5),  # VOLATILE_RANGE
            (dataframe['market_efficiency'] > 0.8),  # BREAKOUT
        ]
        choices = ['STRONG_TREND', 'VOLATILE_RANGE', 'BREAKOUT']
        dataframe['market_regime'] = np.select(conditions, choices, default='UNCERTAIN')

        return dataframe

    def calculate_fusion_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算智能融合指标"""

        # 信号质量评估
        dataframe['adx_quality'] = np.where(
            (dataframe['adx'] > 20) & (dataframe['adx_slope'] > 0), 0.8, 0.4
        )

        dataframe['adaptive_quality'] = np.where(
            (dataframe['ema_trend'] == 1) & (dataframe['volatility_regime'] == 0), 0.9, 0.5
        )

        dataframe['candlestick_quality'] = np.where(
            dataframe['bullish_pattern_count'] >= 2, 0.7, 0.3
        )

        # 动态权重计算
        dataframe['weight_adx'] = self.adx_slope_weight.value
        dataframe['weight_adaptive'] = self.adaptive_weight.value
        dataframe['weight_candlestick'] = self.candlestick_weight.value

        # 根据市场环境调整权重
        dataframe.loc[dataframe['market_regime'] == 'STRONG_TREND', 'weight_adx'] *= 1.5
        dataframe.loc[dataframe['market_regime'] == 'VOLATILE_RANGE', 'weight_adaptive'] *= 1.4
        dataframe.loc[dataframe['market_regime'] == 'BREAKOUT', 'weight_candlestick'] *= 1.3

        # 权重归一化
        total_weight = dataframe['weight_adx'] + dataframe['weight_adaptive'] + dataframe['weight_candlestick']
        dataframe['weight_adx'] = dataframe['weight_adx'] / total_weight
        dataframe['weight_adaptive'] = dataframe['weight_adaptive'] / total_weight
        dataframe['weight_candlestick'] = dataframe['weight_candlestick'] / total_weight

        # 改进的智能信号融合 - 使用加权平均而非最大值
        dataframe['fusion_score'] = (
            dataframe['adx_slope_signal'] * dataframe['adx_quality'] * dataframe['weight_adx'] +
            dataframe['adaptive_signal'] * dataframe['adaptive_quality'] * dataframe['weight_adaptive'] +
            dataframe['candlestick_signal'] * dataframe['candlestick_quality'] * dataframe['weight_candlestick']
        )

        # 融合置信度
        dataframe['fusion_confidence'] = (
            dataframe['adx_quality'] + dataframe['adaptive_quality'] + dataframe['candlestick_quality']
        ) / 3

        return dataframe

    def calculate_trend_hard_conditions(self, dataframe: DataFrame) -> DataFrame:
        """计算趋势硬性条件 - 入场的必要条件"""

        # 强趋势确认
        dataframe['strong_trend'] = np.where(
            (dataframe['adx'] > self.adx_strength_threshold.value) &
            (dataframe['ema_short'] > dataframe['ema_long']) &
            (dataframe['close'] > dataframe['ema_short']) &  # 价格在短期EMA之上
            (dataframe['trend_strength'] >= self.trend_strength_min.value),
            1, 0
        )

        # 趋势一致性检查（连续N个周期的趋势确认）
        dataframe['trend_consistency'] = dataframe['strong_trend'].rolling(
            window=self.trend_consistency_periods.value
        ).sum() >= self.trend_consistency_periods.value

        # 成交量确认
        dataframe['volume_confirmation'] = np.where(
            dataframe['volume'] > dataframe['volume_sma'] * self.volume_confirmation_multiplier.value,
            1, 0
        )

        # 市场环境过滤（只在强趋势或突破状态下交易）
        dataframe['market_regime_filter'] = np.where(
            (dataframe['market_regime'] == 'STRONG_TREND') |
            (dataframe['market_regime'] == 'BREAKOUT'),
            1, 0
        )

        # 综合趋势硬性条件
        dataframe['trend_hard_condition'] = np.where(
            (dataframe['strong_trend'] == 1) &
            (dataframe['trend_consistency'] == True) &
            (dataframe['volume_confirmation'] == 1) &
            (dataframe['market_regime_filter'] == 1),
            1, 0
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with entry columns populated
        """

        # 改进的入场条件 - 严格的趋势硬性条件 + 高质量信号确认
        entry_conditions = (
            # 趋势硬性条件（必要条件）
            (dataframe['trend_hard_condition'] == 1) &

            # 多重信号确认（至少2个子策略确认）
            (
                (dataframe['adx_slope_signal'] + dataframe['adaptive_signal'] + dataframe['candlestick_signal']) >= 2
            ) &

            # 严格的技术条件
            (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70) &
            (dataframe['close'] > dataframe['bb_middleband']) &  # 价格必须在中轨之上
            (dataframe['bb_width'] > dataframe['bb_width'].rolling(20).mean()) &  # 避免震荡市

            # 高质量融合信号
            (dataframe['fusion_score'] >= self.signal_quality_threshold.value) &
            (dataframe['fusion_confidence'] >= self.fusion_confidence_threshold.value)
        )

        dataframe.loc[entry_conditions, 'enter_long'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with exit columns populated
        """

        # 改进的出场条件 - 趋势反转确认
        exit_conditions = (
            # 趋势硬性条件失效
            (dataframe['trend_hard_condition'] == 0) |

            # 强烈的反转信号
            (dataframe['rsi'] > 80) |  # 极度超买

            # 明确的趋势反转（需要确认）
            (
                (dataframe['ema_short'] < dataframe['ema_long']) &
                (dataframe['ema_short'].shift(1) >= dataframe['ema_long'].shift(1)) &  # 刚刚死叉
                (dataframe['adx'] < self.adx_strength_threshold.value * 0.8)  # ADX走弱
            ) |

            # 严重的技术破位
            (dataframe['close'] < dataframe['bb_lowerband'])  # 跌破下轨
        )

        dataframe.loc[exit_conditions, 'exit_long'] = 1

        return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        Customize leverage for each new trade. This method is only called in futures mode.
        """
        return 1.0

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic - 动态止损：根据市场波动率调整
        """

        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) == 0:
            return self.stoploss

        current_atr = dataframe['atr'].iloc[-1]
        avg_atr = dataframe['atr'].rolling(20).mean().iloc[-1]

        # 根据波动率调整止损
        volatility_multiplier = min(current_atr / avg_atr, 2.0) if avg_atr > 0 else 1.0
        dynamic_stoploss = self.stoploss * volatility_multiplier * self.dynamic_risk_multiplier.value

        # 确保不超过最大止损
        return max(dynamic_stoploss, self.stoploss)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                            time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                            side: str, **kwargs) -> bool:
        """
        Called right before placing a entry order - 最终入场确认（严格风控）
        """

        # 获取当前市场数据进行最终确认
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) == 0:
            return False

        # 最新数据检查
        latest = dataframe.iloc[-1]

        # 趋势硬性条件最终确认
        if latest['trend_hard_condition'] != 1:
            return False

        # 高质量信号确认
        if latest['fusion_confidence'] < self.fusion_confidence_threshold.value:
            return False

        # 市场异常检查
        if latest['atr'] > dataframe['atr'].rolling(20).mean().iloc[-1] * 2:  # ATR异常放大
            return False

        # 成交量支撑确认
        if latest['volume'] < latest['volume_sma'] * self.volume_confirmation_multiplier.value:
            return False

        # 避免在阻力位附近入场
        if latest['close'] > latest['bb_upperband'] * 0.95:  # 接近上轨
            return False

        return True

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        Custom exit logic - 智能出场逻辑
        """

        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) == 0:
            return None

        latest = dataframe.iloc[-1]

        # 紧急出场条件（收紧）
        if current_profit < self.emergency_exit_threshold.value:
            return "emergency_exit"

        # 趋势硬性条件失效
        if latest['trend_hard_condition'] == 0:
            return "trend_condition_failed"

        # 市场环境急剧恶化（更严格的条件）
        if latest['market_regime'] == 'VOLATILE_RANGE' and current_profit < -0.03:
            return "market_regime_change"

        return None
