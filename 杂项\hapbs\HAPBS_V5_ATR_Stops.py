# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import logging
import pandas_ta as pta
from freqtrade.persistence import Trade
from datetime import datetime

logger = logging.getLogger(__name__)

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy - V5 (ATR Stops)
#
#   作者: Gemini & User
#   版本: V5.1-Final
#
#   策略理念:
#   - HAPBS系列终极版，引入了业界标准的基于ATR的动态风险管理系统。
#   - 核心能力:
#     - 1. ATR动态初始止损: 在 custom_stoploss 中设置初始止损。
#     - 2. 高水位(High-water Mark)追踪止损: 在 custom_exit 中动态管理追踪止损。
#     - 3. 灵活的入场过滤: 通过优化ADX和CHOP参数，平衡交易频率与质量。
#     - 4. 金字塔式加仓: 保留了V4中成熟的加仓逻辑。
# --------------------------------

class HAPBS_V5_ATR_Stops(IStrategy):

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 高级仓位管理配置 ---
    position_adjustment_enable = True
    max_entry_position_adjustment = 1

    # --- Hyperopt 参数空间定义 ---
    # -- 做多参数 --
    ema_short_period_long = IntParameter(10, 50, default=21, space="buy", optimize=True)
    ema_long_period_long = IntParameter(50, 100, default=75, space="buy", optimize=True)
    adx_threshold_long = IntParameter(18, 40, default=25, space="buy", optimize=True)
    obv_period_long = IntParameter(10, 50, default=20, space="buy", optimize=True)
    chop_threshold_long = IntParameter(50, 80, default=65, space="buy", optimize=True)
    pyramiding_profit_pct_long = DecimalParameter(0.01, 0.05, default=0.02, decimals=3, space="buy", optimize=True)
    pyramiding_lock_candles_long = IntParameter(1, 6, default=3, space="buy", optimize=True)
    
    # -- 做空参数 --
    ema_short_period_short = IntParameter(10, 50, default=21, space="sell", optimize=True)
    ema_long_period_short = IntParameter(50, 100, default=75, space="sell", optimize=True)
    adx_threshold_short = IntParameter(18, 40, default=25, space="sell", optimize=True)
    obv_period_short = IntParameter(10, 50, default=20, space="sell", optimize=True)
    chop_threshold_short = IntParameter(50, 80, default=65, space="sell", optimize=True)
    pyramiding_profit_pct_short = DecimalParameter(0.01, 0.05, default=0.02, decimals=3, space="sell", optimize=True)
    pyramiding_lock_candles_short = IntParameter(1, 6, default=3, space="sell", optimize=True)
    exit_max_duration = IntParameter(100, 1000, default=500, space="sell", optimize=True)

    # -- ATR动态止损参数 (通用) --
    atr_stoploss_multiplier = DecimalParameter(1.5, 4.0, default=2.5, decimals=1, space="stoploss", optimize=True)
    atr_trailing_profit_pct = DecimalParameter(0.02, 0.08, default=0.03, decimals=3, space="trailing", optimize=True)
    atr_trailing_offset_multiplier = DecimalParameter(1.0, 3.0, default=1.5, decimals=1, space="trailing", optimize=True)

    # --- 初始止损 (V5.1核心) ---
    use_custom_stoploss = True
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 使用交易开仓那一刻的ATR值来计算初始止损
        trade_entry_candle = dataframe.loc[dataframe['date'] == trade.open_date_utc]
        if not trade_entry_candle.empty:
            atr_val = trade_entry_candle.iloc[0].get('atr')
            if atr_val:
            if trade.enter_tag == 'long_entry':
                    return trade.open_rate - (atr_val * self.atr_stoploss_multiplier.value)
                else:
                    return trade.open_rate + (atr_val * self.atr_stoploss_multiplier.value)

        # Fallback: 如果无法获取ATR，则使用固定的99%止损。
        # 注意：这几乎等于没有止损，主要是为了防止在异常情况下交易被意外取消。
        if trade.enter_tag == 'long_entry':
            return trade.open_rate * (1 - 0.99)
        else:
            return trade.open_rate * (1 + 0.99)

    # --- 动态追踪止损 (V5.1核心) ---
    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1]

        # --- 时间止损 ---
        trade_duration_minutes = (current_time.timestamp() - trade.open_date_utc.timestamp()) / 60
        max_duration_minutes = self.timeframe_to_minutes() * self.exit_max_duration.value
        if trade_duration_minutes > max_duration_minutes:
            return 'time_exit'

        # 激活追踪止损
        if current_profit > self.atr_trailing_profit_pct.value:
            atr_val = last_candle['atr']
            if trade.enter_tag == 'long_entry':
                # 计算新的止损价格
                new_stop_price = current_rate - (atr_val * self.atr_trailing_offset_multiplier.value)
                # 高水位逻辑: 止损只上移
                trade.stop_loss = max(trade.stop_loss, new_stop_price)
            elif trade.enter_tag == 'short_entry':
                new_stop_price = current_rate + (atr_val * self.atr_trailing_offset_multiplier.value)
                # 高水位逻辑: 止损只下移
                trade.stop_loss = min(trade.stop_loss, new_stop_price)
        
        return None # 本函数只用于修改止损，不产生离场信号

    # --- 指标计算 ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        dataframe['ha_strong_bull'] = (dataframe['ha_close'] > dataframe['ha_open']) & (dataframe['ha_open'] == dataframe['ha_low'])
        dataframe['ha_strong_bear'] = (dataframe['ha_close'] < dataframe['ha_open']) & (dataframe['ha_open'] == dataframe['ha_high'])
        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=self.ema_short_period_long.value)
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=self.ema_long_period_long.value)
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=self.ema_short_period_short.value)
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=self.ema_long_period_short.value)
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=14)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=14)
        dataframe['chop'] = pta.chop(dataframe['high'], dataframe['low'], dataframe['close'], length=14)
        
        # OBV (On-Balance Volume)
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_sma_long'] = ta.SMA(dataframe['obv'], timeperiod=self.obv_period_long.value)
        dataframe['obv_sma_short'] = ta.SMA(dataframe['obv'], timeperiod=self.obv_period_short.value)

        return dataframe

    # --- 入场逻辑 ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        long_prerequisites = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (dataframe['adx'] > self.adx_threshold_long.value) &
            (qtpylib.crossed_above(dataframe['obv'], dataframe['obv_sma_long'])) &
            (dataframe['chop'] < self.chop_threshold_long.value) &
            (dataframe['plus_di'] > dataframe['minus_di'])
        )
        cond_long_entry_signal = (
            qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long']) |
            dataframe['ha_strong_bull']
        )
        dataframe.loc[long_prerequisites & cond_long_entry_signal, ['enter_long', 'enter_tag']] = (1, 'long_entry')

        short_prerequisites = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (dataframe['adx'] > self.adx_threshold_short.value) &
            (qtpylib.crossed_below(dataframe['obv'], dataframe['obv_sma_short'])) &
            (dataframe['chop'] < self.chop_threshold_short.value) &
            (dataframe['minus_di'] > dataframe['plus_di'])
        )
        cond_short_entry_signal = (
            qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short']) |
            dataframe['ha_strong_bear']
        )
        dataframe.loc[short_prerequisites & cond_short_entry_signal, ['enter_short', 'enter_tag']] = (1, 'short_entry')
        return dataframe

    # --- 加仓逻辑 ---
    def adjust_trade_position(self, trade: Trade, current_time: datetime, current_rate: float,
                              current_profit: float, **kwargs) -> float:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 简化版时间锁: 使用 open_trades 计数
        if trade.nr_of_successful_entries > self.pyramiding_lock_candles_long.value and trade.enter_tag == 'long_entry':
            if current_profit > self.pyramiding_profit_pct_long.value:
                if last_candle['ha_strong_bull']:
                    return trade.stake_amount
        
        elif trade.nr_of_successful_entries > self.pyramiding_lock_candles_short.value and trade.enter_tag == 'short_entry':
            if current_profit > self.pyramiding_profit_pct_short.value:
                if last_candle['ha_strong_bear']:
                    return trade.stake_amount

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return dataframe

    def timeframe_to_minutes(self) -> int:
        val = int(self.timeframe[:-1])
        unit = self.timeframe[-1]
        if unit == 'm': return val
        if unit == 'h': return val * 60
        if unit == 'd': return val * 1440
        return 0 