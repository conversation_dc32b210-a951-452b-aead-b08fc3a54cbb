{"name": "cms-resource-platform-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "antd": "^5.12.8", "axios": "^1.6.2", "zustand": "^4.4.7", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "dayjs": "^1.11.10", "react-dropzone": "^14.2.3", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}}