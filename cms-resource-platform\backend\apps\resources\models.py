"""
资源管理模型
"""
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel, Category, Tag
from apps.users.models import User
import uuid
import os


def resource_upload_path(instance, filename):
    """资源文件上传路径"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4().hex}.{ext}"
    return f"resources/{instance.created_at.year}/{instance.created_at.month:02d}/{filename}"


def cover_upload_path(instance, filename):
    """封面图片上传路径"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4().hex}.{ext}"
    return f"covers/{instance.created_at.year}/{instance.created_at.month:02d}/{filename}"


class Resource(BaseModel):
    """资源模型"""
    title = models.CharField('标题', max_length=200)
    description = models.TextField('描述', blank=True)
    content = models.TextField('详细内容', blank=True)
    
    # 分类和标签
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resources',
        verbose_name='分类'
    )
    tags = models.ManyToManyField(
        Tag,
        blank=True,
        related_name='resources',
        verbose_name='标签'
    )
    
    # 文件信息
    cover_image = models.ImageField('封面图片', upload_to=cover_upload_path, blank=True)
    resource_file = models.FileField('资源文件', upload_to=resource_upload_path, blank=True)
    file_size = models.PositiveIntegerField('文件大小(字节)', default=0)
    file_type = models.CharField('文件类型', max_length=50, blank=True)
    file_format = models.CharField('文件格式', max_length=20, blank=True)
    
    # 外部链接
    external_url = models.URLField('外部链接', blank=True)
    download_url = models.URLField('下载链接', blank=True)
    
    # 权限设置
    is_public = models.BooleanField('是否公开', default=True)
    is_featured = models.BooleanField('是否推荐', default=False)
    is_premium = models.BooleanField('是否付费', default=False)
    required_points = models.PositiveIntegerField('所需积分', default=0)
    required_vip = models.BooleanField('是否需要VIP', default=False)
    
    # 状态
    status = models.CharField(
        '状态',
        max_length=20,
        choices=[
            ('draft', '草稿'),
            ('pending', '待审核'),
            ('published', '已发布'),
            ('rejected', '已拒绝'),
            ('archived', '已归档'),
        ],
        default='draft'
    )
    
    # 用户信息
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='resources',
        verbose_name='作者'
    )
    reviewer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_resources',
        verbose_name='审核人'
    )
    reviewed_at = models.DateTimeField('审核时间', null=True, blank=True)
    review_note = models.TextField('审核备注', blank=True)
    
    # 统计信息
    view_count = models.PositiveIntegerField('浏览次数', default=0)
    download_count = models.PositiveIntegerField('下载次数', default=0)
    favorite_count = models.PositiveIntegerField('收藏次数', default=0)
    rating_score = models.DecimalField(
        '评分',
        max_digits=3,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(5)]
    )
    rating_count = models.PositiveIntegerField('评分人数', default=0)
    
    # SEO
    slug = models.SlugField('URL别名', max_length=200, blank=True)
    meta_keywords = models.CharField('关键词', max_length=200, blank=True)
    meta_description = models.CharField('SEO描述', max_length=300, blank=True)
    
    # 发布时间
    published_at = models.DateTimeField('发布时间', null=True, blank=True)
    
    class Meta:
        verbose_name = '资源'
        verbose_name_plural = '资源'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['category', '-created_at']),
            models.Index(fields=['author', '-created_at']),
            models.Index(fields=['is_featured', '-created_at']),
            models.Index(fields=['-view_count']),
            models.Index(fields=['-download_count']),
        ]
    
    def __str__(self):
        return self.title
    
    @property
    def file_size_mb(self):
        """文件大小(MB)"""
        return round(self.file_size / 1024 / 1024, 2) if self.file_size else 0
    
    def can_download(self, user):
        """检查用户是否可以下载"""
        if not self.is_public or self.status != 'published':
            return False
        
        if not user or not user.is_authenticated:
            return False
        
        # 检查VIP权限
        if self.required_vip and not user.is_vip_active:
            return False
        
        # 检查积分
        if self.required_points > 0 and user.points < self.required_points:
            return False
        
        return True


class ResourceImage(BaseModel):
    """资源图片"""
    resource = models.ForeignKey(
        Resource,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name='资源'
    )
    image = models.ImageField('图片', upload_to='resource_images/')
    caption = models.CharField('说明', max_length=200, blank=True)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    class Meta:
        verbose_name = '资源图片'
        verbose_name_plural = '资源图片'
        ordering = ['sort_order', 'created_at']
    
    def __str__(self):
        return f"{self.resource.title} - 图片{self.id}"


class ResourceDownload(BaseModel):
    """下载记录"""
    resource = models.ForeignKey(
        Resource,
        on_delete=models.CASCADE,
        related_name='downloads',
        verbose_name='资源'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='downloads',
        verbose_name='用户'
    )
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)
    points_cost = models.PositiveIntegerField('消费积分', default=0)
    download_token = models.CharField('下载令牌', max_length=100, blank=True)
    token_expires_at = models.DateTimeField('令牌过期时间', null=True, blank=True)
    is_successful = models.BooleanField('是否成功', default=True)
    
    class Meta:
        verbose_name = '下载记录'
        verbose_name_plural = '下载记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['resource', '-created_at']),
            models.Index(fields=['download_token']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 下载 {self.resource.title}"


class ResourceRating(BaseModel):
    """资源评分"""
    resource = models.ForeignKey(
        Resource,
        on_delete=models.CASCADE,
        related_name='ratings',
        verbose_name='资源'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='ratings',
        verbose_name='用户'
    )
    score = models.PositiveIntegerField(
        '评分',
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    comment = models.TextField('评论', blank=True)
    
    class Meta:
        verbose_name = '资源评分'
        verbose_name_plural = '资源评分'
        unique_together = ['resource', 'user']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['resource', '-created_at']),
            models.Index(fields=['user', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 给 {self.resource.title} 评分 {self.score}"


class ResourceComment(BaseModel):
    """资源评论"""
    resource = models.ForeignKey(
        Resource,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='资源'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='用户'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name='父评论'
    )
    content = models.TextField('内容')
    is_approved = models.BooleanField('是否审核通过', default=True)
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    
    class Meta:
        verbose_name = '资源评论'
        verbose_name_plural = '资源评论'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['resource', '-created_at']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['is_approved', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 评论 {self.resource.title}"


class Collection(BaseModel):
    """资源合集"""
    title = models.CharField('标题', max_length=200)
    description = models.TextField('描述', blank=True)
    cover_image = models.ImageField('封面图片', upload_to='collections/', blank=True)
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='collections',
        verbose_name='作者'
    )
    resources = models.ManyToManyField(
        Resource,
        through='CollectionResource',
        related_name='collections',
        verbose_name='资源'
    )
    is_public = models.BooleanField('是否公开', default=True)
    view_count = models.PositiveIntegerField('浏览次数', default=0)
    
    class Meta:
        verbose_name = '资源合集'
        verbose_name_plural = '资源合集'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title


class CollectionResource(BaseModel):
    """合集资源关联"""
    collection = models.ForeignKey(Collection, on_delete=models.CASCADE)
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    class Meta:
        verbose_name = '合集资源'
        verbose_name_plural = '合集资源'
        unique_together = ['collection', 'resource']
        ordering = ['sort_order', 'created_at']
