# -*- coding: utf-8 -*-
import re
import sys
import json
from pathlib import Path

def update_strategy_with_combined_params(strategy_file, params_dir):
    """
    Parses all group_*.json files in a directory, combines them, and updates
    the 'buy_params' in the specified strategy file.
    """
    strategy_path = Path(strategy_file)
    params_path = Path(params_dir)

    if not strategy_path.is_file():
        print(f"Strategy file not found: {strategy_file}", file=sys.stderr)
        sys.exit(1)

    if not params_path.is_dir():
        print(f"Parameters directory not found: {params_dir}", file=sys.stderr)
        sys.exit(1)

    # --- Combine parameters from all group JSON files ---
    combined_buy_params = {}
    
    group_files = sorted(params_path.glob('group_*.json'))
    if not group_files:
        print(f"No group parameter files found in {params_dir}", file=sys.stderr)
        sys.exit(1)
        
    print(f"Found {len(group_files)} group parameter files to combine.")

    for param_file in group_files:
        with open(param_file, 'r') as f:
            try:
                params = json.load(f)
                combined_buy_params.update(params)
            except json.JSONDecodeError:
                print(f"Warning: Could not decode JSON from {param_file}. Skipping.", file=sys.stderr)

    if not combined_buy_params:
        print("No parameters were collected after combining files. Exiting.", file=sys.stderr)
        sys.exit(1)
        
    # --- Read strategy content ---
    with open(strategy_path, 'r', encoding='utf-8') as f:
        strategy_content = f.read()

    # --- Replace the buy_params block ---
    strategy_content = _replace_param_block(strategy_content, "buy_params", combined_buy_params)

    # --- Write back to the strategy file ---
    with open(strategy_path, 'w', encoding='utf-8') as f:
        f.write(strategy_content)
        
    print(f"Successfully updated strategy '{strategy_file}' with combined parameters.")


def _replace_param_block(content, param_type, params_dict):
    """
    Replaces a block of parameters (like buy_params) in the strategy file content.
    """
    # Create the new parameter block string with 4-space indentation
    params_str = f"    {param_type} = {{\n"
    # Add a comment indicating the change
    params_str += "        # Optimized parameters merged by optimization script\n"
    
    # Sort keys for consistent output
    for key in sorted(params_dict.keys()):
        value = params_dict[key]
        if isinstance(value, str):
            params_str += f'        "{key}": "{value}",\n'
        elif isinstance(value, bool):
            # Python bool is capitalized
            params_str += f'        "{key}": {str(value)},\n'
        else:
            # Floats and Ints
            params_str += f'        "{key}": {value},\n'
    params_str += "    }"

    # Regex to find the whole block from "buy_params = {" to the matching "}"
    # This is more robust and handles nested structures or comments within the block.
    regex = re.compile(f"(^\\s*{param_type}\\s*=\\s*{{[\\s\\S]*?^\\s*}})", re.MULTILINE)
    
    new_content, num_replacements = regex.subn(params_str, content, 1)
    
    if num_replacements == 0:
        print(f"Warning: '{param_type}' block not found or not replaced in the strategy file.", file=sys.stderr)
        # Exit with an error code if the block wasn't found, as this is critical
        sys.exit(1)

    return new_content

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python 5_update_config_from_report.py <path_to_strategy_file> <path_to_params_dir>", file=sys.stderr)
        sys.exit(1)
    
    strategy_file = sys.argv[1]
    params_dir = sys.argv[2]
    update_strategy_with_combined_params(strategy_file, params_dir) 