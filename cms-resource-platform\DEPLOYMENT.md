# 部署指南

## 生产环境部署

### 1. 服务器要求

**最低配置：**
- CPU: 2核心
- 内存: 4GB
- 存储: 50GB SSD
- 带宽: 5Mbps

**推荐配置（支持1K并发）：**
- CPU: 4核心
- 内存: 8GB
- 存储: 100GB SSD + 500GB 数据盘
- 带宽: 10Mbps

### 2. 环境准备

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建应用目录
sudo mkdir -p /opt/cms-platform
sudo chown $USER:$USER /opt/cms-platform
cd /opt/cms-platform
```

### 3. 配置文件

创建生产环境配置文件：

```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
vim .env.production
```

**重要配置项：**

```bash
# 基础配置
DEBUG=False
SECRET_KEY=your-super-secret-key-change-in-production
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 数据库配置
DATABASE_URL=****************************************/cms_db

# Redis配置
REDIS_URL=redis://redis:6379/0

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# 文件存储
MEDIA_ROOT=/var/www/media
STATIC_ROOT=/var/www/static

# 安全配置
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

### 4. SSL证书配置

使用Let's Encrypt获取免费SSL证书：

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 5. 部署步骤

```bash
# 1. 克隆代码
git clone <repository-url> .

# 2. 构建镜像
docker-compose -f docker-compose.prod.yml build

# 3. 启动数据库
docker-compose -f docker-compose.prod.yml up -d postgres redis

# 4. 运行迁移
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py migrate

# 5. 创建超级用户
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py createsuperuser

# 6. 收集静态文件
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py collectstatic --noinput

# 7. 启动所有服务
docker-compose -f docker-compose.prod.yml up -d
```

### 6. Nginx配置

创建Nginx配置文件 `/etc/nginx/sites-available/cms-platform`：

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    client_max_body_size 100M;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /admin/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /var/www/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /var/www/media/;
        expires 7d;
        add_header Cache-Control "public";
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/cms-platform /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 7. 监控和日志

**查看服务状态：**
```bash
docker-compose -f docker-compose.prod.yml ps
```

**查看日志：**
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
```

**系统监控：**
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控Docker容器
docker stats
```

### 8. 备份策略

**数据库备份：**
```bash
# 创建备份脚本
cat > /opt/cms-platform/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/cms-platform"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U cms_user cms_db > $BACKUP_DIR/db_$DATE.sql

# 备份媒体文件
tar -czf $BACKUP_DIR/media_$DATE.tar.gz /var/www/media/

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/cms-platform/backup.sh

# 设置定时备份
crontab -e
# 添加以下行（每天凌晨2点备份）
0 2 * * * /opt/cms-platform/backup.sh
```

### 9. 更新部署

```bash
# 使用部署脚本
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 10. 故障排除

**常见问题：**

1. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose -f docker-compose.prod.yml logs postgres
   
   # 重启数据库
   docker-compose -f docker-compose.prod.yml restart postgres
   ```

2. **静态文件404**
   ```bash
   # 重新收集静态文件
   docker-compose -f docker-compose.prod.yml run --rm backend python manage.py collectstatic --noinput
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   docker stats
   
   # 重启服务释放内存
   docker-compose -f docker-compose.prod.yml restart
   ```

4. **磁盘空间不足**
   ```bash
   # 清理Docker镜像
   docker system prune -a
   
   # 清理日志文件
   sudo journalctl --vacuum-time=7d
   ```

### 11. 性能优化

**数据库优化：**
```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_resources_status_created ON resources_resource(status, created_at);
CREATE INDEX CONCURRENTLY idx_users_is_active ON users_user(is_active);
```

**Redis优化：**
```bash
# 在redis.conf中设置
maxmemory 1gb
maxmemory-policy allkeys-lru
```

**应用优化：**
- 启用数据库连接池
- 配置适当的缓存策略
- 使用CDN加速静态资源
- 启用Gzip压缩
