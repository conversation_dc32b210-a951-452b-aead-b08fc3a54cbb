{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 10, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 100, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "password": "Wen5520..", "ccxt_config": {"httpsProxy": "http://127.0.0.1:1080", "wsProxy": "http://127.0.01:1080"}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "XRP/USDT:USDT", "DOGE/USDT:USDT", "ENA/USDT:USDT", "TRX/USDT:USDT", "BNB/USDT:USDT", "SUI/USDT:USDT", "PENGU/USDT:USDT", "ADA/USDT:USDT", "HBAR/USDT:USDT", "UNI/USDT:USDT", "BCH/USDT:USDT", "LTC/USDT:USDT", "WIF/USDT:USDT", "LINK/USDT:USDT", "AVAX/USDT:USDT", "AAVE/USDT:USDT", "XLM/USDT:USDT", "WLD/USDT:USDT", "ARB/USDT:USDT", "NEAR/USDT:USDT", "APT/USDT:USDT", "SEI/USDT:USDT", "TAO/USDT:USDT", "ONDO/USDT:USDT", "DOT/USDT:USDT", "PNUT/USDT:USDT", "OP/USDT:USDT", "CAKE/USDT:USDT", "NEIRO/USDT:USDT", "ETHFI/USDT:USDT", "FET/USDT:USDT", "ENS/USDT:USDT", "LDO/USDT:USDT", "FIL/USDT:USDT", "TIA/USDT:USDT", "MKR/USDT:USDT", "PENDLE/USDT:USDT", "RUNE/USDT:USDT", "ETC/USDT:USDT", "MOVE/USDT:USDT", "RAY/USDT:USDT", "TON/USDT:USDT", "JUP/USDT:USDT", "INJ/USDT:USDT", "OM/USDT:USDT", "EIGEN/USDT:USDT", "RENDER/USDT:USDT", "ALGO/USDT:USDT", "GALA/USDT:USDT", "ORDI/USDT:USDT", "POL/USDT:USDT", "XTZ/USDT:USDT", "ICP/USDT:USDT", "ATOM/USDT:USDT", "ARKM/USDT:USDT", "USUAL/USDT:USDT", "BOME/USDT:USDT", "PEOPLE/USDT:USDT", "VET/USDT:USDT", "TURBO/USDT:USDT", "IO/USDT:USDT", "SAGA/USDT:USDT", "AR/USDT:USDT", "SUSHI/USDT:USDT", "JTO/USDT:USDT", "ALT/USDT:USDT", "ZRO/USDT:USDT", "COMP/USDT:USDT", "STRK/USDT:USDT", "DYDX/USDT:USDT", "LISTA/USDT:USDT", "SAND/USDT:USDT", "GRT/USDT:USDT", "JASMY/USDT:USDT", "W/USDT:USDT", "OMNI/USDT:USDT", "1MBABYDOGE/USDT:USDT", "TRB/USDT:USDT", "ZK/USDT:USDT", "LPT/USDT:USDT", "1000SATS/USDT:USDT", "STX/USDT:USDT", "IOTA/USDT:USDT", "NEO/USDT:USDT", "BLUR/USDT:USDT", "THETA/USDT:USDT", "BEAMX/USDT:USDT", "RSR/USDT:USDT", "KAIA/USDT:USDT", "ACT/USDT:USDT", "XAI/USDT:USDT", "SUPER/USDT:USDT", "THE/USDT:USDT", "KAVA/USDT:USDT", "PERP/USDT:USDT", "NOT/USDT:USDT", "PYTH/USDT:USDT", "SSV/USDT:USDT", "REZ/USDT:USDT", "PORTAL/USDT:USDT", "MANTA/USDT:USDT", "ROSE/USDT:USDT", "MANA/USDT:USDT", "HFT/USDT:USDT", "CELO/USDT:USDT", "CKB/USDT:USDT", "AXS/USDT:USDT", "ZEN/USDT:USDT", "QNT/USDT:USDT", "IMX/USDT:USDT", "YFI/USDT:USDT", "GMT/USDT:USDT"], "pair_blacklist": [], "skip_pair_validation": true}, "dataformat_ohlcv": "json", "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": false, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "Scoring", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}