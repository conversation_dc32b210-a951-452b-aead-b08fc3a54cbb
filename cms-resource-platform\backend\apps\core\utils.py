"""
核心工具函数
"""
import os
import uuid
import hashlib
import base64
import time
import logging
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)


def generate_unique_filename(original_filename):
    """生成唯一文件名"""
    ext = os.path.splitext(original_filename)[1]
    unique_name = f"{uuid.uuid4().hex}{ext}"
    return unique_name


def get_file_type(filename):
    """根据文件名获取文件类型"""
    ext = os.path.splitext(filename)[1].lower()
    
    image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    video_formats = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
    audio_formats = ['.mp3', '.wav', '.flac', '.aac', '.ogg']
    document_formats = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
    archive_formats = ['.zip', '.rar', '.7z', '.tar', '.gz']
    
    if ext in image_formats:
        return 'image'
    elif ext in video_formats:
        return 'video'
    elif ext in audio_formats:
        return 'audio'
    elif ext in document_formats:
        return 'document'
    elif ext in archive_formats:
        return 'archive'
    else:
        return 'other'


def is_valid_file_type(filename):
    """检查文件类型是否允许"""
    ext = os.path.splitext(filename)[1].lower().lstrip('.')
    allowed_types = getattr(settings, 'ALLOWED_FILE_TYPES', [])
    return ext in allowed_types


def get_file_size_mb(file_size):
    """将文件大小转换为MB"""
    return round(file_size / 1024 / 1024, 2)


def generate_download_token(resource_id, user_id):
    """生成下载令牌"""
    token = uuid.uuid4().hex
    expires_at = timezone.now() + timezone.timedelta(minutes=5)
    
    # 保存令牌到缓存
    cache_key = f"download_token_{token}"
    cache_data = {
        'resource_id': resource_id,
        'user_id': user_id,
        'expires_at': expires_at.timestamp()
    }
    cache.set(cache_key, cache_data, 300)  # 5分钟过期
    
    return token


def validate_download_token(token, resource_id, user_id):
    """验证下载令牌"""
    cache_key = f"download_token_{token}"
    cache_data = cache.get(cache_key)
    
    if not cache_data:
        return False
    
    # 检查令牌是否过期
    if time.time() > cache_data['expires_at']:
        cache.delete(cache_key)
        return False
    
    # 检查资源和用户是否匹配
    if (cache_data['resource_id'] != resource_id or
            cache_data['user_id'] != user_id):
        return False
    
    # 使用后删除令牌（一次性使用）
    cache.delete(cache_key)
    return True


def encrypt_file_path(file_path):
    """加密文件路径"""
    key = settings.SECRET_KEY[:32].encode()
    key = base64.urlsafe_b64encode(hashlib.sha256(key).digest())
    fernet = Fernet(key)
    
    encrypted = fernet.encrypt(file_path.encode())
    return base64.urlsafe_b64encode(encrypted).decode()


def decrypt_file_path(encrypted_path):
    """解密文件路径"""
    try:
        key = settings.SECRET_KEY[:32].encode()
        key = base64.urlsafe_b64encode(hashlib.sha256(key).digest())
        fernet = Fernet(key)
        
        encrypted = base64.urlsafe_b64decode(encrypted_path)
        decrypted = fernet.decrypt(encrypted)
        return decrypted.decode()
    except Exception as e:
        logger.error(f"Failed to decrypt file path: {e}")
        return None


def rate_limit_key(user_id, action, resource_id=None):
    """生成频率限制的缓存键"""
    if resource_id:
        return f"rate_limit_{action}_{user_id}_{resource_id}"
    return f"rate_limit_{action}_{user_id}"


def check_rate_limit(user_id, action, limit, period, resource_id=None):
    """检查频率限制
    
    Args:
        user_id: 用户ID
        action: 操作类型（如download, upload, login）
        limit: 限制次数
        period: 时间段（秒）
        resource_id: 资源ID（可选）
    
    Returns:
        bool: 是否允许操作
    """
    cache_key = rate_limit_key(user_id, action, resource_id)
    count = cache.get(cache_key, 0)
    
    if count >= limit:
        return False
    
    # 增加计数
    if count == 0:
        cache.set(cache_key, 1, period)
    else:
        cache.incr(cache_key)
    
    return True
