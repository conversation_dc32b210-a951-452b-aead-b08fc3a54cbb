# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy (HAPBS)
#
#   作者: Gemini & User
#   最终版本: Final (基于 V26)
#
#   策略理念 (Final):
#   - 本策略是多次迭代和优化的最终成果，融合了所有成功版本的核心思想。
#   - 入场逻辑: 
#     做空 - 采用在 V24 中被验证的最严格、最可靠的纯做空信号，
#     只在极度明确的空头市场中 (EMA 短中长期均线完全空头排列) 寻找机会。
#     做多 - 采用与做空相反的逻辑，在明确的多头市场中 (EMA 短中长期均线完全多头排列) 寻找突破机会。
#   - 出场逻辑: 采用在 V26 中被证明最高效的"让利润奔跑"机制，
#     通过分级的 ROI 表来确保在不同阶段锁定利润，实现盈利最大化。
#   - 风险控制: 拥有极低的最大回撤，在获得稳健回报的同时，将资金风险降至最低。
# --------------------------------

class HAPBS_FinalV2(IStrategy):

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 风险控制: 可优化的风控参数 ---
    # Stoploss
    stoploss_param = DecimalParameter(-0.10, -0.01, default=-0.02, space='stoploss')
    stoploss = -0.02

    # Trailing Stop
    opt_trailing_stop = CategoricalParameter([True, False], default=True, space='trailing')
    opt_trailing_stop_positive = DecimalParameter(0.001, 0.05, default=0.02, decimals=3, space='trailing')
    opt_trailing_stop_positive_offset = DecimalParameter(0.01, 0.1, default=0.03, decimals=2, space='trailing')
    opt_trailing_only_offset_is_reached = CategoricalParameter([True, False], default=True, space='trailing')
    # Set default values for the main attributes
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.03
    trailing_only_offset_is_reached = True

    # ROI: Freqtrade hyperopt 会在使用 --spaces roi 时自动优化此项
    # 下面的字典仅作为默认值或回测时使用
    minimal_roi = {
        "0": 0.05,
        "30": 0.03,
        "60": 0.02
    }

    # --- 可配置参数 ---
    # 将买入和卖出参数分开，以实现更精细的优化
    # --- 做多参数 ---
    ema_short_period_long = IntParameter(10, 30, default=25, space='buy')
    ema_long_period_long = IntParameter(30, 60, default=57, space='buy')
    adx_threshold_long = IntParameter(20, 35, default=30, space='buy')
    volume_factor_long = DecimalParameter(1.0, 2.0, default=1.035, space='buy')

    # --- 做空参数 ---
    ema_short_period_short = IntParameter(10, 30, default=25, space='sell')
    ema_long_period_short = IntParameter(30, 60, default=57, space='sell')
    adx_threshold_short = IntParameter(20, 35, default=30, space='sell')
    volume_factor_short = DecimalParameter(1.0, 2.0, default=1.035, space='sell')

    # --- 长期趋势过滤 ---
    ema_long_filter_period = CategoricalParameter([200, 180, 160, 140, 120], default=200, space='buy')

    # --- 图表配置 ---
    plot_config = {
        'main_plot': {
            # 做多 EMA
            'ema_short_long': {'color': 'blue', 'linestyle': '-'},
            'ema_long_long': {'color': 'cyan', 'linestyle': '-'},
            # 做空 EMA
            'ema_short_short': {'color': 'red', 'linestyle': '--'},
            'ema_long_short': {'color': 'magenta', 'linestyle': '--'},
            # 长期 EMA
            'ema_200': {'color': 'black', 'linestyle': ':'},
        },
        'subplots': {
            "ADX": {
                'adx': {'color': 'green'},
            },
        },
    }

    def generate_stoploss_params(self, params: dict) -> None:
        """
        将止损优化空间链接到我们的可优化参数。
        """
        params['stoploss'] = self.stoploss_param

    def generate_trailing_params(self, params: dict) -> None:
        """
        将追踪止损优化空间链接到我们的可优化参数。
        """
        params['trailing_stop'] = self.opt_trailing_stop
        params['trailing_stop_positive'] = self.opt_trailing_stop_positive
        params['trailing_stop_positive_offset'] = self.opt_trailing_stop_positive_offset
        params['trailing_only_offset_is_reached'] = self.opt_trailing_only_offset_is_reached

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        
        # 添加强劲看跌K线定义
        dataframe['ha_strong_bear'] = (
            (dataframe['ha_close'] < dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_high'])
        )
        
        # 添加强劲看涨K线定义
        dataframe['ha_strong_bull'] = (
            (dataframe['ha_close'] > dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_low'])
        )

        # 为做多和做空计算独立的EMA指标
        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=self.ema_short_period_long.value)
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=self.ema_long_period_long.value)
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=self.ema_short_period_short.value)
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=self.ema_long_period_short.value)
        
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=self.ema_long_filter_period.value)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 做多条件 - 使用做多参数
        long_conditions = (
            # 趋势过滤: 严格的EMA多头排列 (short > long > 200)
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            
            # 突破信号: 强劲的看涨K线上穿短期EMA
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long'])) &
            (dataframe['ha_strong_bull']) &
            
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_long.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_long.value)
        )
        dataframe.loc[long_conditions, ['enter_long', 'enter_tag']] = (1, 'long_final')

        # 做空条件 - 使用做空参数
        short_conditions = (
            # 趋势过滤: 严格的EMA空头排列 (short < long < 200)
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            
            # 破位信号: 强劲的看跌K线下穿短期EMA
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short'])) &
            (dataframe['ha_strong_bear']) &
            
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold_short.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor_short.value)
        )
        dataframe.loc[short_conditions, ['enter_short', 'enter_tag']] = (1, 'short_final')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Freqtrade 在某些版本中，如果一个策略在回测期间没有产生任何亏损，
        # 可能会在统计报告生成时因 'NaN' 值而出错。
        # 添加一个永远不会达成的条件来"强制"产生一个理论上的退出信号，
        # 这有助于避免优化器在处理全胜策略时崩溃。
        # 这不会影响实际的交易表现，只是一种技术上的规避手段。
        dataframe.loc[
            (dataframe['close'] > 999999),
            'exit_long'] = 1
        dataframe.loc[
            (dataframe['close'] < 0.000001),
            'exit_short'] = 1
            
        return dataframe 