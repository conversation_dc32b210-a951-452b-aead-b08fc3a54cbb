"""
资源管理序列化器
"""
from rest_framework import serializers
from django.db import transaction
from apps.core.models import Category, Tag
from apps.users.serializers import UserSerializer
from .models import (
    Resource, ResourceImage, ResourceDownload, ResourceRating,
    ResourceComment, Collection, CollectionResource
)


class CategorySerializer(serializers.ModelSerializer):
    """分类序列化器"""
    children = serializers.SerializerMethodField()
    full_name = serializers.CharField(read_only=True)
    
    class Meta:
        model = Category
        fields = [
            'id', 'name', 'slug', 'description', 'icon', 'color',
            'parent', 'sort_order', 'is_active', 'full_name', 'children'
        ]
    
    def get_children(self, obj):
        if obj.children.exists():
            return CategorySerializer(obj.children.all(), many=True).data
        return []


class TagSerializer(serializers.ModelSerializer):
    """标签序列化器"""
    
    class Meta:
        model = Tag
        fields = ['id', 'name', 'slug', 'color', 'usage_count']


class ResourceImageSerializer(serializers.ModelSerializer):
    """资源图片序列化器"""
    
    class Meta:
        model = ResourceImage
        fields = ['id', 'image', 'caption', 'sort_order']


class ResourceSerializer(serializers.ModelSerializer):
    """资源序列化器"""
    category = CategorySerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    author = UserSerializer(read_only=True)
    images = ResourceImageSerializer(many=True, read_only=True)
    file_size_mb = serializers.CharField(read_only=True)
    can_download = serializers.SerializerMethodField()
    
    class Meta:
        model = Resource
        fields = [
            'id', 'title', 'description', 'content', 'category', 'tags',
            'cover_image', 'resource_file', 'file_size', 'file_size_mb',
            'file_type', 'file_format', 'external_url', 'download_url',
            'is_public', 'is_featured', 'is_premium', 'required_points',
            'required_vip', 'status', 'author', 'published_at',
            'view_count', 'download_count', 'favorite_count',
            'rating_score', 'rating_count', 'slug', 'meta_keywords',
            'meta_description', 'images', 'can_download', 'created_at'
        ]
        read_only_fields = [
            'author', 'view_count', 'download_count', 'favorite_count',
            'rating_score', 'rating_count', 'created_at'
        ]
    
    def get_can_download(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.can_download(request.user)
        return False


class ResourceCreateSerializer(serializers.ModelSerializer):
    """资源创建序列化器"""
    category_id = serializers.IntegerField(write_only=True, required=False)
    tag_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    images = ResourceImageSerializer(many=True, required=False)
    
    class Meta:
        model = Resource
        fields = [
            'title', 'description', 'content', 'category_id', 'tag_ids',
            'cover_image', 'resource_file', 'external_url', 'download_url',
            'is_public', 'is_premium', 'required_points', 'required_vip',
            'slug', 'meta_keywords', 'meta_description', 'images'
        ]
    
    def validate_category_id(self, value):
        if value and not Category.objects.filter(id=value, is_active=True).exists():
            raise serializers.ValidationError("分类不存在或已禁用")
        return value
    
    def validate_tag_ids(self, value):
        if value:
            existing_tags = Tag.objects.filter(id__in=value).count()
            if existing_tags != len(value):
                raise serializers.ValidationError("部分标签不存在")
        return value
    
    @transaction.atomic
    def create(self, validated_data):
        category_id = validated_data.pop('category_id', None)
        tag_ids = validated_data.pop('tag_ids', [])
        images_data = validated_data.pop('images', [])
        
        # 设置作者
        validated_data['author'] = self.context['request'].user
        
        # 设置分类
        if category_id:
            validated_data['category'] = Category.objects.get(id=category_id)
        
        # 创建资源
        resource = Resource.objects.create(**validated_data)
        
        # 设置标签
        if tag_ids:
            tags = Tag.objects.filter(id__in=tag_ids)
            resource.tags.set(tags)
            # 更新标签使用次数
            tags.update(usage_count=models.F('usage_count') + 1)
        
        # 创建图片
        for image_data in images_data:
            ResourceImage.objects.create(resource=resource, **image_data)
        
        return resource


class ResourceUpdateSerializer(serializers.ModelSerializer):
    """资源更新序列化器"""
    category_id = serializers.IntegerField(write_only=True, required=False)
    tag_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Resource
        fields = [
            'title', 'description', 'content', 'category_id', 'tag_ids',
            'cover_image', 'external_url', 'download_url', 'is_public',
            'is_premium', 'required_points', 'required_vip',
            'slug', 'meta_keywords', 'meta_description'
        ]
    
    @transaction.atomic
    def update(self, instance, validated_data):
        category_id = validated_data.pop('category_id', None)
        tag_ids = validated_data.pop('tag_ids', None)
        
        # 更新分类
        if category_id is not None:
            if category_id:
                instance.category = Category.objects.get(id=category_id)
            else:
                instance.category = None
        
        # 更新标签
        if tag_ids is not None:
            # 减少旧标签使用次数
            old_tags = instance.tags.all()
            old_tags.update(usage_count=models.F('usage_count') - 1)
            
            # 设置新标签
            if tag_ids:
                new_tags = Tag.objects.filter(id__in=tag_ids)
                instance.tags.set(new_tags)
                # 增加新标签使用次数
                new_tags.update(usage_count=models.F('usage_count') + 1)
            else:
                instance.tags.clear()
        
        # 更新其他字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        return instance


class ResourceRatingSerializer(serializers.ModelSerializer):
    """资源评分序列化器"""
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = ResourceRating
        fields = ['id', 'user', 'score', 'comment', 'created_at']
        read_only_fields = ['user']
    
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ResourceCommentSerializer(serializers.ModelSerializer):
    """资源评论序列化器"""
    user = UserSerializer(read_only=True)
    replies = serializers.SerializerMethodField()
    
    class Meta:
        model = ResourceComment
        fields = [
            'id', 'user', 'parent', 'content', 'is_approved',
            'replies', 'created_at'
        ]
        read_only_fields = ['user', 'is_approved']
    
    def get_replies(self, obj):
        if obj.replies.exists():
            return ResourceCommentSerializer(
                obj.replies.filter(is_approved=True),
                many=True
            ).data
        return []
    
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class CollectionSerializer(serializers.ModelSerializer):
    """资源合集序列化器"""
    author = UserSerializer(read_only=True)
    resource_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Collection
        fields = [
            'id', 'title', 'description', 'cover_image', 'author',
            'is_public', 'view_count', 'resource_count', 'created_at'
        ]
        read_only_fields = ['author', 'view_count']
    
    def get_resource_count(self, obj):
        return obj.resources.count()
    
    def create(self, validated_data):
        validated_data['author'] = self.context['request'].user
        return super().create(validated_data)


class ResourceDownloadSerializer(serializers.ModelSerializer):
    """下载记录序列化器"""
    user = UserSerializer(read_only=True)
    resource = ResourceSerializer(read_only=True)
    
    class Meta:
        model = ResourceDownload
        fields = [
            'id', 'resource', 'user', 'points_cost', 'is_successful',
            'created_at'
        ]
