# -*- coding: utf-8 -*-
"""
This script provides an interactive workflow to update the Freqtrade config file
based on the results from a ranked backtest report.

It allows the user to select the top N pairs from the overall ranking
and updates the 'pair_whitelist' in the specified config.json file, as well as
cleaning the associated strategy parameter settings file.
A backup of the original config file is created automatically.
"""
import json
import re
import argparse
import shutil
from pathlib import Path
import logging

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s',
)

def parse_ranked_report(report_path: Path) -> list:
    """
    Parses the Markdown report to extract the list of pairs from the 
    'Overall Ranking' table.
    """
    if not report_path.is_file():
        logging.error(f"Report file not found at: {report_path}")
        return []

    with open(report_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Find the 'Overall Ranking' section
    ranking_section_match = re.search(r'##\s*🏆\s*Overall Ranking.*?\n(.*?)(?=\n##|$)', content, re.DOTALL)
    if not ranking_section_match:
        logging.error("Could not find the 'Overall Ranking' section in the report.")
        return []

    section_content = ranking_section_match.group(1)
    
    # Extract pairs from the table rows. Assumes pairs are enclosed in backticks ``.
    pairs = re.findall(r'\|\s*\d+\s*\|\s*`([^`]+)`', section_content)
    
    if not pairs:
        logging.error("No pairs could be parsed from the 'Overall Ranking' table.")

    return pairs

def update_configuration_files(config_path: Path, settings_path: Path, new_whitelist: list):
    """
    Updates the 'pair_whitelist' in the config file and cleans the settings file.
    Creates backups before writing new content.
    """
    # --- Update Main Config File ---
    if not config_path.is_file():
        logging.error(f"Config file not found at: {config_path}")
        return

    logging.info(f"--- Updating main config file: {config_path.name} ---")
    backup_path = config_path.with_suffix(config_path.suffix + '.bak')
    shutil.copy(config_path, backup_path)
    logging.info(f"Backup of original config created at: {backup_path}")

    with open(config_path, 'r+', encoding='utf-8') as f:
        config_data = json.load(f)
        
        logging.info(f"Original pair_whitelist count: {len(config_data.get('exchange', {}).get('pair_whitelist', []))}")
        config_data['exchange']['pair_whitelist'] = new_whitelist
        logging.info(f"New pair_whitelist count: {len(new_whitelist)}")
        
        f.seek(0)
        json.dump(config_data, f, indent=4)
        f.truncate()

    logging.info(f"Successfully updated '{config_path.name}'.")

    # --- Update Strategy Settings File ---
    if not settings_path.is_file():
        logging.warning(f"Strategy settings file not found at '{settings_path}'. Skipping update for this file.")
        return
        
    logging.info(f"--- Updating strategy settings file: {settings_path.name} ---")
    settings_backup_path = settings_path.with_suffix(settings_path.suffix + '.bak')
    shutil.copy(settings_path, settings_backup_path)
    logging.info(f"Backup of settings file created at: {settings_backup_path}")

    with open(settings_path, 'r+', encoding='utf-8') as f:
        settings_data = json.load(f)
        
        original_pair_count = len(settings_data)
        logging.info(f"Original pair count in settings: {original_pair_count}")

        filtered_settings = {pair: params for pair, params in settings_data.items() if pair in new_whitelist}
        logging.info(f"New pair count in settings: {len(filtered_settings)}")
        
        f.seek(0)
        json.dump(filtered_settings, f, indent=4)
        f.truncate()
        
    logging.info(f"Successfully updated '{settings_path.name}'.")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(
        description="Update Freqtrade config whitelist from a ranked report.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '-r', '--report-file',
        required=True,
        type=Path,
        help="Path to the ranked Markdown report file (e.g., HAPBS_ranked_report.md)"
    )
    parser.add_argument(
        '-c', '--config-file',
        required=True,
        type=Path,
        help="Path to the Freqtrade JSON config file to update (e.g., HAPBS_config.json)"
    )
    args = parser.parse_args()

    ranked_pairs = parse_ranked_report(args.report_file)
    if not ranked_pairs:
        return

    print("-" * 50)
    print(f"Found {len(ranked_pairs)} ranked pairs in the report.")
    print("-" * 50)

    while True:
        try:
            num_to_select = input(f"How many top pairs do you want to select? (Enter a number, or 'q' to quit): ")
            if num_to_select.lower() == 'q':
                print("Operation cancelled.")
                return
                
            num_to_select = int(num_to_select)
            if 0 < num_to_select <= len(ranked_pairs):
                break
            else:
                logging.warning(f"Please enter a number between 1 and {len(ranked_pairs)}.")
        except ValueError:
            logging.warning("Invalid input. Please enter a number.")

    top_pairs = ranked_pairs[:num_to_select]
    
    print("\n" + "="*50)
    logging.info(f"The following {len(top_pairs)} pairs will be set as the new whitelist:")
    print(top_pairs)
    print("="*50 + "\n")

    # --- Derive strategy settings file path from the report file ---
    try:
        report_p = args.report_file
        # Assumes report file is named like 'STRATEGYNAME_ranked_report.md'
        strategy_name = report_p.name.split('_ranked_report.md')[0]
        settings_file = report_p.parent / f"{strategy_name}_PairOptimized_Settings.json"
        logging.info(f"Inferred strategy settings file path: {settings_file}")
    except Exception as e:
        logging.error(f"Could not derive settings file path from report file name: {e}")
        settings_file = None

    while True:
        confirm = input(f"Are you sure you want to update '{args.config_file.name}' and potentially '{settings_file.name if settings_file else ''}'? (y/n): ").lower()
        if confirm in ['y', 'yes']:
            if settings_file:
                update_configuration_files(args.config_file, settings_file, top_pairs)
            else:
                logging.error("Update cancelled as the settings file path could not be determined.")
            break
        elif confirm in ['n', 'no']:
            print("Operation cancelled by user.")
            break
        else:
            logging.warning("Invalid input. Please enter 'y' or 'n'.")

if __name__ == "__main__":
    main() 