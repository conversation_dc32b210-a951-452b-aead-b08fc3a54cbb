{"strategy_name": "GaussianTrendFilter", "params": {"buy": {"gaussian_length": 15, "poles": 3, "linreg_length": 22, "flatten_multiplier": 7, "volume_filter_length": 33, "volume_threshold": 70, "risk_per_trade": 0.02, "leverage_factor": 3, "trend_strength_threshold": 70, "use_multi_timeframe": true, "min_timeframes_aligned": 3, "mid_trnd_sig": false}, "sell": {"atr_period": 14, "atr_multiplier_sl": 2.0, "atr_multiplier_tp": 3.0}, "protection": {}, "roi": {"0": 0.1, "30": 0.05, "60": 0.03, "120": 0.01}, "stoploss": {"stoploss": -0.15}, "trailing": {"trailing_stop": false, "trailing_stop_positive": null, "trailing_stop_positive_offset": 0.0, "trailing_only_offset_is_reached": false}}, "ft_stratparam_v": 1, "export_time": "2023-07-01 00:00:00+00:00"}