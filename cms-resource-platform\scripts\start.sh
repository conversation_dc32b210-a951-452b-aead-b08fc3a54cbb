#!/bin/bash

# CMS资源管理平台启动脚本

set -e

echo "🚀 启动CMS资源管理平台..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置相关参数"
fi

# 构建并启动服务
echo "🔨 构建Docker镜像..."
docker-compose build

echo "🗄️  启动数据库服务..."
docker-compose up -d postgres redis

echo "⏳ 等待数据库启动..."
sleep 10

echo "📊 运行数据库迁移..."
docker-compose run --rm backend python manage.py migrate

echo "👤 创建超级用户（可选）..."
read -p "是否创建超级用户？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose run --rm backend python manage.py createsuperuser
fi

echo "📦 收集静态文件..."
docker-compose run --rm backend python manage.py collectstatic --noinput

echo "🚀 启动所有服务..."
docker-compose up -d

echo "✅ 启动完成！"
echo ""
echo "🌐 访问地址："
echo "   前端: http://localhost:3000"
echo "   后端API: http://localhost:8000"
echo "   管理后台: http://localhost:8000/admin"
echo "   API文档: http://localhost:8000/api/docs/"
echo ""
echo "📋 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
