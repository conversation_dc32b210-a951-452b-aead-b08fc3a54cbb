# -*- coding: utf-8 -*-
"""
This script analyzes the backtest summary file for the SuperTrend strategy,
calculates rankings based on various metrics, and generates a clean, sorted
Markdown report for easy pair selection.
"""
import re
import argparse
from pathlib import Path
import logging
from datetime import datetime

# --- Constants ---
MINIMUM_TRADES = 5 # Minimum number of trades for a pair to be included in rankings
STRATEGY_NAME = "SuperTrend_MACD_RSI_PairOptimized" # The name of the strategy in the backtest report

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def parse_summary_file(file_path: Path) -> list:
    """
    Parses the raw backtest summary text file and extracts key metrics for each pair.
    """
    if not file_path.exists():
        logging.error(f"Input file not found: {file_path}")
        return []

    results = []
    current_pair = None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            pair_match = re.match(r'={10,}\s+(.*?)\s+={10,}', line)
            if pair_match:
                current_pair = pair_match.group(1).strip()
                continue
            
            if line.strip().startswith(f'│ {STRATEGY_NAME}'):
                if not current_pair:
                    continue

                parts = [p.strip() for p in line.split('│')[1:-1]]
                
                try:
                    trades = int(parts[1])
                    tot_profit_pct = float(parts[4])
                    win_pct_str = parts[6].split()[-1]
                    win_pct = float(win_pct_str)
                    drawdown_match = re.search(r'([\d.]+)\s*%$', parts[7])
                    drawdown_pct = float(drawdown_match.group(1)) if drawdown_match else 0.0
                    
                    results.append({
                        'pair': current_pair,
                        'trades': trades,
                        'profit_pct': tot_profit_pct,
                        'win_rate': win_pct,
                        'drawdown_pct': drawdown_pct,
                    })
                    
                except (ValueError, IndexError) as e:
                    logging.warning(f"Could not parse line for pair {current_pair}: {line.strip()} - Error: {e}")
                finally:
                    current_pair = None
                    
    return results

def calculate_composite_score(data: list) -> list:
    """
    Calculates a risk-adjusted composite score for each pair.
    """
    for entry in data:
        drawdown = entry['drawdown_pct'] if entry['drawdown_pct'] > 0.1 else 0.1
        profit = entry['profit_pct']
        win_factor = entry['win_rate'] / 100.0
        score = (profit * win_factor) / drawdown
        entry['score'] = score
    return data

def generate_report(data: list, output_path: Path):
    """
    Generates a visually-aligned Markdown report with multiple tables sorted by different metrics.
    """
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"# {STRATEGY_NAME} Backtest Analysis Report\n\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Analyzed pairs with a minimum of **{MINIMUM_TRADES} trades**.\n\n")

        # --- Helper function to create and write a single formatted table ---
        def write_formatted_table(file_handle, title, description, sorted_data, bold_column_key=None):
            file_handle.write(f"## {title}\n")
            if description:
                file_handle.write(f"{description}\n\n")

            header = ["Rank", "Pair", "Trades", "Total Profit %", "Drawdown %", "Win Rate %", "Composite Score"]
            
            # Prepare all rows to calculate column widths first
            all_rows_as_strings = []
            for i, d in enumerate(sorted_data, 1):
                row_data = [
                    str(i),
                    f"`{d['pair']}`",
                    str(d['trades']),
                    f"{d['profit_pct']:.2f}%",
                    f"{d['drawdown_pct']:.2f}%",
                    f"{d['win_rate']:.1f}%",
                    f"`{d['score']:.2f}`"
                ]
                
                # Apply bolding for the highlighted column
                if bold_column_key == 'profit':
                    row_data[3] = f"**{row_data[3]}**"
                elif bold_column_key == 'drawdown':
                    row_data[4] = f"**{row_data[4]}**"
                
                all_rows_as_strings.append(row_data)

            # Calculate max width for each column
            col_widths = [len(h) for h in header]
            for row in all_rows_as_strings:
                for i, cell in enumerate(row):
                    if len(cell) > col_widths[i]:
                        col_widths[i] = len(cell)
            
            # Write header
            header_line = "| " + " | ".join([h.center(col_widths[i]) for i, h in enumerate(header)]) + " |"
            f.write(header_line + "\n")

            # Write separator (now commented out as requested)
            # f.write("|:---:|:---|---:|---:|---:|---:|---:|\n")
            
            # Write data rows with padding
            for row in all_rows_as_strings:
                line = "| "
                line += row[0].center(col_widths[0]) + " | "  # Rank: Center
                line += row[1].ljust(col_widths[1])  + " | "  # Pair: Left
                line += " | ".join([cell.rjust(col_widths[i]) for i, cell in enumerate(row[2:], 2)]) # Numeric: Right
                line += " |"
                f.write(line + "\n")
                
            f.write("\n")

        # --- Sort by Composite Score ---
        sorted_by_score = sorted(data, key=lambda x: x['score'], reverse=True)
        write_formatted_table(f, "🏆 Overall Ranking (by Composite Score)", "*(Score = (Profit % * Win Rate) / Drawdown %)*", sorted_by_score, bold_column_key='profit')

        # --- Other rankings ---
        # (Profit)
        sorted_by_profit = sorted(data, key=lambda x: x['profit_pct'], reverse=True)
        write_formatted_table(f, "💹 Top Profit Pairs", None, sorted_by_profit, bold_column_key='profit')

        # (Drawdown)
        sorted_by_drawdown = sorted(data, key=lambda x: x['drawdown_pct'])
        write_formatted_table(f, "🛡️ Top Low-Drawdown Pairs", None, sorted_by_drawdown, bold_column_key='drawdown')

    logging.info(f"Analysis report successfully generated at: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Analyze Freqtrade backtest summary.")
    parser.add_argument('-i', '--input-file', required=True, type=Path, help="Path to the backtest_results_summary.txt file.")
    parser.add_argument('-o', '--output-file', required=True, type=Path, help="Path to save the ranked Markdown report.")
    args = parser.parse_args()

    raw_data = parse_summary_file(args.input_file)
    if not raw_data:
        logging.warning("No data parsed from the input file. Exiting.")
        return

    filtered_data = [d for d in raw_data if d['trades'] >= MINIMUM_TRADES]
    logging.info(f"Found {len(raw_data)} pairs. Kept {len(filtered_data)} with >= {MINIMUM_TRADES} trades.")

    if not filtered_data:
        logging.warning("No pairs met the minimum trade requirement. No report will be generated.")
        return
        
    final_data = calculate_composite_score(filtered_data)
    generate_report(final_data, args.output_file)

if __name__ == "__main__":
    main() 