import logging
import numpy as np
import pandas as pd
import talib.abstract as ta
from pandas import DataFrame
from typing import Optional, Dict, List
from datetime import datetime, timedelta

from freqtrade.strategy import (IStrategy, IntParameter, DecimalParameter, CategoricalParameter, RealParameter, informative)
from freqtrade.persistence import Trade
from freqtrade.exchange import timeframe_to_minutes

# 忽略性能警告
from warnings import simplefilter
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)

logger = logging.getLogger(__name__)

class TRDV05(IStrategy):
    """
    Trend Recognition Dual Version 3
    - Multi-Timeframe (1h for trend, 15m for entry)
    - Core Trend: SuperTrend
    - Auxiliary/Confirmation: KAMA
    - Market Regime Detection (Initial)
    - ATR based position sizing and trailing stop loss
    """
    INTERFACE_VERSION: int = 3

    # --- 策略核心配置 ---
    timeframe = '5m'  # 交易执行时间周期
    # info_timeframe = '1h' # 主趋势判断时间周期 - now handled by @informative

    # 启用做空 (根据您的需求，可以设为True或False)
    can_short: bool = True

    # 同时开启的最大交易数量 (根据优化结果设置)
    max_open_trades: int = 4

    # ROI表 (初期可以简单设置，后续根据回测优化)
    minimal_roi = {
        "0": 0.10, # 10%
        "30": 0.05, # 30分钟后5%
        "60": 0.025, # 60分钟后2.5%
        "120": 0.01 # 120分钟后1%
    }

    # 固定止损 (作为ATR止损的最后防线，可以设得宽一些)
    stoploss = -0.25  # 从 -0.10 修改为 -0.25, custom_stoploss 将主要负责

    # 跟踪止损设置
    trailing_stop = True
    trailing_stop_positive = 0.02  # 当盈利达到2%时激活跟踪止损
    trailing_stop_positive_offset = 0.05  # 从盈利高点回撤5%时触发止损
    trailing_only_offset_is_reached = True # 只有当盈利达到 trailing_stop_positive 时才开始跟踪

    # 运行配置
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False # 出场信号触发时，即使亏损也退出
    ignore_roi_if_entry_signal = True # 如果有新的入场信号，旧的ROI仍然有效

    # --- 可优化的核心参数 ---

    # SuperTrend (5m - 执行周期)
    st_short_atr_period = IntParameter(5, 40, default=15, space='buy', optimize=True, load=True)
    st_short_multiplier = DecimalParameter(1.5, 6.0, default=5.1, decimals=1, space='buy', optimize=True, load=True)

    # KAMA (5m - 执行周期，用于过滤)
    kama_short_period = IntParameter(10, 75, default=30, space='buy', optimize=True, load=True)
    # KAMA 斜率判断 (用于过滤震荡)
    kama_short_slope_period = IntParameter(3, 10, default=8, space='buy', optimize=True, load=True)
    kama_short_slope_threshold_entry = DecimalParameter(0.000005, 0.0005, default=0.00005, decimals=6, space='buy', optimize=True, load=True)


    # ATR (用于仓位管理和跟踪止损)
    atr_period_pos_size = IntParameter(10, 60, default=30, space='buy', optimize=True, load=True) # ATR周期用于计算仓位
    atr_multiplier_pos_size = DecimalParameter(1.5, 7.0, default=7.0, decimals=1, space='buy', optimize=True, load=True) # ATR倍数用于计算仓位大小的分母

    # 风险管理
    risk_per_trade = DecimalParameter(0.01, 0.03, default=0.01, decimals=3, space='buy', optimize=True, load=True) # 每笔交易承担的风险比例 (账户的百分比)

    # Consecutive candles for 15m EMA slope confirmation
    min_ema_slope_candles = IntParameter(1, 5, default=2, space='buy', optimize=True, load=True)

    # --- MACD Parameters (5m - 执行周期) ---
    # macd_fast_period = IntParameter(5, 50, default=12, space='buy', optimize=True, load=True) # REMOVED
    # macd_slow_period = IntParameter(20, 100, default=26, space='buy', optimize=True, load=True) # REMOVED
    # macd_signal_period = IntParameter(5, 20, default=9, space='buy', optimize=True, load=True) # REMOVED

    # --- RSI Parameters (5m - 执行周期) ---
    # rsi_period = IntParameter(7, 30, default=14, space='buy', optimize=True, load=True) # REMOVED
    # rsi_entry_long_threshold = IntParameter(40, 60, default=50, space='buy', optimize=True, load=True) # REMOVED
    # rsi_entry_short_threshold = IntParameter(40, 60, default=50, space='buy', optimize=True, load=True) # REMOVED

    # --- New RSI Exit Parameters (5m - 执行周期) ---
    # rsi_exit_long_threshold = IntParameter(20, 50, default=40, space='sell', optimize=True, load=True) # REMOVED
    # rsi_exit_short_threshold = IntParameter(50, 80, default=60, space='sell', optimize=True, load=True) # REMOVED

    # --- 高时间周期 (15m) 指标参数 (通常不直接优化，或者与执行周期参数联动) ---
    # SuperTrend (15m - 主趋势周期)
    st_long_atr_period = 56 # 15m Supertrend ATR 周期 (原14 @ 1h)
    st_long_multiplier = 3.5 # 15m Supertrend 乘数

    # KAMA (15m - 主趋势周期)
    kama_long_period = 120 # 15m KAMA 周期 (原30 @ 1h)
    kama_long_slope_period = 8 # 15m KAMA 斜率周期
    kama_long_slope_threshold = DecimalParameter(0.00001, 0.0005, default=0.00006, decimals=6, space='buy', optimize=True, load=True)


    # --- 辅助方法 ---
    def calculate_supertrend(self, dataframe: DataFrame, period: int, multiplier: float) -> DataFrame:
        """
        Calculates SuperTrend indicator
        """
        df = dataframe.copy()
        df['TR'] = ta.TRANGE(df)
        df['ATR'] = ta.SMA(df['TR'], period)

        df['basic_ub'] = (df['high'] + df['low']) / 2 + multiplier * df['ATR']
        df['basic_lb'] = (df['high'] + df['low']) / 2 - multiplier * df['ATR']

        df['final_ub'] = 0.00
        df['final_lb'] = 0.00

        for i in range(period, len(df)):
            df.loc[i, 'final_ub'] = (df.loc[i, 'basic_ub'] if df.loc[i, 'basic_ub'] < df.loc[i-1, 'final_ub'] or df.loc[i-1, 'close'] > df.loc[i-1, 'final_ub'] else df.loc[i-1, 'final_ub'])
            df.loc[i, 'final_lb'] = (df.loc[i, 'basic_lb'] if df.loc[i, 'basic_lb'] > df.loc[i-1, 'final_lb'] or df.loc[i-1, 'close'] < df.loc[i-1, 'final_lb'] else df.loc[i-1, 'final_lb'])

        df['ST'] = np.nan # Initialize with NaN
        df['STX'] = ''

        # Set initial SuperTrend value at the 'period' index
        if period < len(df): # Ensure dataframe is long enough
            if df.loc[period, 'close'] > df.loc[period, 'final_lb']: # Initial guess: if close > lower band, trend is up
                df.loc[period, 'ST'] = df.loc[period, 'final_lb']
            elif df.loc[period, 'close'] < df.loc[period, 'final_ub']: # Initial guess: if close < upper band, trend is down
                df.loc[period, 'ST'] = df.loc[period, 'final_ub']
            # else: ST remains NaN if close is exactly on a band or between basic_ub/lb without clear direction yet.
            # This NaN will be forward-filled by subsequent logic or bfill at the end if it's the only point.

        for i in range(period + 1, len(df)): # Start loop from period + 1
            prev_st = df.loc[i-1, 'ST']
            # If previous ST is NaN (can happen if initial ST was not set), try to set it based on current price vs final bands
            if pd.isna(prev_st):
                if df.loc[i, 'close'] > df.loc[i, 'final_lb']:
                    df.loc[i, 'ST'] = df.loc[i, 'final_lb']
                elif df.loc[i, 'close'] < df.loc[i, 'final_ub']:
                    df.loc[i, 'ST'] = df.loc[i, 'final_ub']
                # If still can't determine, ST remains NaN for this point too
                continue # Skip to next iteration if ST can't be determined

            if prev_st == df.loc[i-1, 'final_ub']:
                df.loc[i, 'ST'] = df.loc[i, 'final_ub'] if df.loc[i, 'close'] <= df.loc[i, 'final_ub'] else df.loc[i, 'final_lb']
            elif prev_st == df.loc[i-1, 'final_lb']:
                df.loc[i, 'ST'] = df.loc[i, 'final_lb'] if df.loc[i, 'close'] >= df.loc[i, 'final_lb'] else df.loc[i, 'final_ub']
            # else: This case should ideally not be reached if prev_st was a valid final_ub or final_lb

        df.loc[df['close'] > df['ST'], 'STX'] = 'up'
        df.loc[df['close'] < df['ST'], 'STX'] = 'down'
        df.loc[df['STX'] == '', 'STX'] = pd.NA # Set to NA if no trend determined

        df['ST'] = df['ST'].bfill().ffill() # Fill any remaining NaNs robustly
        df['STX'] = df['STX'].bfill().ffill()

        return df[['ST', 'STX']]

    # --- Informative Timeframe (1h) ---
    @informative('15m')
    def populate_info_15m(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populates indicators for the 15-minute informative timeframe
        """
        # 15m SuperTrend
        supertrend_long = self.calculate_supertrend(dataframe.copy(), period=self.st_long_atr_period, multiplier=self.st_long_multiplier)
        dataframe[f'st_long'] = supertrend_long['ST'] 
        dataframe[f'stx_long'] = supertrend_long['STX'] 

        # 15m KAMA
        dataframe[f'kama_long'] = ta.KAMA(dataframe, timeperiod=self.kama_long_period) 
        dataframe[f'kama_long_slope'] = ta.LINEARREG_SLOPE(dataframe[f'kama_long'], timeperiod=self.kama_long_slope_period)
        
        # 15m EMA Filter (for L1 proposal)
        dataframe[f'ema_long_filter'] = ta.EMA(dataframe, timeperiod=50) 
        # For M2 proposal: Slope of the 15m EMA filter
        dataframe[f'ema_long_filter_slope'] = ta.LINEARREG_SLOPE(dataframe[f'ema_long_filter'], timeperiod=5) # 5-period slope of EMA50

        return dataframe

    # --- 主指标计算 (15m) ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several indicators to the main timeframe (15m) dataframe.
        Informative indicators from 1h are automatically merged by Freqtrade.
        """
        # SuperTrend (15m - 执行周期)
        supertrend_short = self.calculate_supertrend(dataframe.copy(), period=self.st_short_atr_period.value, multiplier=self.st_short_multiplier.value)
        dataframe['st_short'] = supertrend_short['ST']
        dataframe['stx_short'] = supertrend_short['STX']

        # KAMA (15m - 执行周期)
        dataframe['kama_short'] = ta.KAMA(dataframe, timeperiod=self.kama_short_period.value)
        dataframe['kama_short_slope'] = ta.LINEARREG_SLOPE(dataframe['kama_short'], timeperiod=self.kama_short_slope_period.value)

        # ATR (15m - 用于仓位和止损)
        dataframe['atr_pos_size'] = ta.ATR(dataframe, timeperiod=self.atr_period_pos_size.value)

        # MACD (5m)
        # macd = ta.MACD(dataframe, fastperiod=self.macd_fast_period.value, slowperiod=self.macd_slow_period.value, signalperiod=self.macd_signal_period.value) # REMOVED
        # dataframe['macd'] = macd['macd'] # REMOVED
        # dataframe['macdsignal'] = macd['macdsignal'] # REMOVED
        # dataframe['macdhist'] = macd['macdhist'] # REMOVED

        # RSI (5m)
        # dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value) # REMOVED

        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        return dataframe

    # --- 入场信号逻辑 ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        required_cols_15m = [
            f'stx_long_15m', f'kama_long_slope_15m',
            f'ema_long_filter_15m', f'close_15m', f'ema_long_filter_slope_15m' # Added slope
        ]
        if not all(col in dataframe.columns for col in required_cols_15m):
            # logger.warning(f"Missing informative columns for pair {metadata['pair']} in entry. Skipping signal generation.")
            return dataframe

        # Volume filter: Volume should be X% above its N-period SMA
        volume_sma = ta.SMA(dataframe['volume'], timeperiod=20)
        volume_condition = (dataframe['volume'] > volume_sma * 1.2)

        # Consecutive 15m EMA slope condition
        consecutive_ema_slope_long = True
        for i in range(self.min_ema_slope_candles.value):
            consecutive_ema_slope_long &= (dataframe[f'ema_long_filter_slope_15m'].shift(i) > 0)
        
        consecutive_ema_slope_short = True
        for i in range(self.min_ema_slope_candles.value):
            consecutive_ema_slope_short &= (dataframe[f'ema_long_filter_slope_15m'].shift(i) < 0)

        # --- 做多入场条件 ---
        long_condition_15m_strong_trend_up = (
            (dataframe[f'stx_long_15m'] == 'up') &
            (dataframe[f'kama_long_slope_15m'] > self.kama_long_slope_threshold.value) & # Use .value for DecimalParameter
            (dataframe[f'close_15m'] > dataframe[f'ema_long_filter_15m']) & # Price above 15m EMA50
            consecutive_ema_slope_long # New consecutive EMA slope condition
        )
        
        # Raw 5m long signal: 5m ST flip up, KAMA confirm, Volume confirm, MACD bullish crossover, RSI below buy threshold
        raw_5m_long_signal_conditions = (
            (dataframe['stx_short'] == 'up') &
            (dataframe['stx_short'].shift(1) != 'up') & # Check previous candle was not 'up'
            (dataframe['kama_short_slope'] > self.kama_short_slope_threshold_entry.value) &
            volume_condition # &
            # (dataframe['macd'] > dataframe['macdsignal']) & # MACD bullish crossover # REMOVED
            # (dataframe['rsi'] > self.rsi_entry_long_threshold.value)    # RSI in strong zone for long entry # REMOVED
        )

        # --- 做空入场条件 ---
        short_condition_15m_strong_trend_down = (
            (dataframe[f'stx_long_15m'] == 'down') &
            (dataframe[f'kama_long_slope_15m'] < -self.kama_long_slope_threshold.value) & # Use .value for DecimalParameter and negative for short
            (dataframe[f'close_15m'] < dataframe[f'ema_long_filter_15m']) & # Price below 15m EMA50
            consecutive_ema_slope_short # New consecutive EMA slope condition
        )

        # Raw 5m short signal: 5m ST flip down, KAMA confirm, Volume confirm, MACD bearish crossover, RSI above sell threshold
        raw_5m_short_signal_conditions = (
            (dataframe['stx_short'] == 'down') &
            (dataframe['stx_short'].shift(1) != 'down') & # Check previous candle was not 'down'
            (dataframe['kama_short_slope'] < -self.kama_short_slope_threshold_entry.value) & # Negative for short
            volume_condition # &
            # (dataframe['macd'] < dataframe['macdsignal']) & # MACD bearish crossover # REMOVED
            # (dataframe['rsi'] < self.rsi_entry_short_threshold.value)   # RSI in weak zone for short entry # REMOVED
        )

        # --- Logic to take only the first signal in a 15m trend phase ---

        # Define when a 15m strong trend is active
        dataframe['long_15m_trend_active'] = long_condition_15m_strong_trend_up
        dataframe['short_15m_trend_active'] = short_condition_15m_strong_trend_down

        # Create a unique ID for each continuous phase of an active 15m strong long trend
        dataframe['long_trend_phase_id'] = (dataframe['long_15m_trend_active'] != dataframe['long_15m_trend_active'].shift(1, fill_value=False)).cumsum()
        # Create a unique ID for each continuous phase of an active 15m strong short trend
        dataframe['short_trend_phase_id'] = (dataframe['short_15m_trend_active'] != dataframe['short_15m_trend_active'].shift(1, fill_value=False)).cumsum()

        # Potential first long signals: 15m long trend active AND raw 5m long signal occurs
        potential_first_long_signals = dataframe['long_15m_trend_active'] & raw_5m_long_signal_conditions
        # Potential first short signals: 15m short trend active AND raw 5m short signal occurs
        potential_first_short_signals = dataframe['short_15m_trend_active'] & raw_5m_short_signal_conditions
        
        # Mark rows that are the *actual* first signal in their respective 15m trend phase
        dataframe['is_actual_first_long_in_phase'] = potential_first_long_signals & \
                                                     (potential_first_long_signals.groupby(dataframe['long_trend_phase_id']).cumsum() == 1)
        
        dataframe['is_actual_first_short_in_phase'] = potential_first_short_signals & \
                                                      (potential_first_short_signals.groupby(dataframe['short_trend_phase_id']).cumsum() == 1)

        # Set enter_long only for the actual first long signal in a 15m long trend phase
        dataframe.loc[dataframe['is_actual_first_long_in_phase'], 'enter_long'] = 1
        
        if self.can_short:
            # Set enter_short only for the actual first short signal in a 15m short trend phase
            dataframe.loc[dataframe['is_actual_first_short_in_phase'], 'enter_short'] = 1
            
        # Optional: Drop helper columns if not needed for plotting or debugging later
        # dataframe.drop(columns=['long_15m_trend_active', 'short_15m_trend_active', 
        #                         'long_trend_phase_id', 'short_trend_phase_id',
        #                         'is_actual_first_long_in_phase', 'is_actual_first_short_in_phase'], inplace=True, errors='ignore')

        return dataframe

    # --- 出场信号逻辑 ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Required columns from informative timeframe
        required_cols_info = [f'stx_long_15m']
        # Required columns from main timeframe
        required_cols_main = [
            'stx_short', 'kama_short', 'close' # Added 'kama_short' and 'close'
            # MACD and RSI columns are no longer needed here
            ]

        # Check if all required columns are present
        if not all(col in dataframe.columns for col in required_cols_info + required_cols_main):
            # logger.warning(f"Missing required columns for pair {metadata['pair']} in exit. Skipping signal generation.")
            dataframe['exit_long'] = 0
            dataframe['exit_short'] = 0
            return dataframe

        # Initialize exit columns if they don't exist
        if 'exit_long' not in dataframe.columns:
            dataframe['exit_long'] = 0
        if 'exit_short' not in dataframe.columns:
            dataframe['exit_short'] = 0

        # --- 做多出场条件 ---
        # Condition 1: 15-minute SuperTrend flips to 'down' (Original slow logic)
        exit_long_condition_15m_flip = (
            (dataframe[f'stx_long_15m'] == 'down') &
            (dataframe[f'stx_long_15m'].shift(1) == 'up')
        )
        # Condition 2: 5-minute SuperTrend flips to 'down' (New, more reactive logic)
        exit_long_condition_5m_signal = (
            (dataframe['stx_short'] == 'down') &
            (dataframe['stx_short'].shift(1) == 'up')
        )
        
        # New: Price crosses below 5m KAMA as an exit condition
        exit_long_cond_kama_cross_down = (
            (dataframe['close'] < dataframe['kama_short']) &
            (dataframe['close'].shift(1) >= dataframe['kama_short'].shift(1)) # Just crossed below
        )

        # New: MACD + RSI confirmation for long exit
        # exit_long_cond_macd_rsi_confirm = ( # REMOVED
        #     (dataframe['macd'] < dataframe['macdsignal']) & # MACD bearish crossover # REMOVED
        #     (dataframe['macd'].shift(1) >= dataframe['macdsignal'].shift(1)) & # Confirmed crossover # REMOVED
        #     (dataframe['rsi'] < self.rsi_exit_long_threshold.value) # RSI enters weak zone # REMOVED
        # ) # REMOVED

        # Combine conditions: exit if EITHER the 15m ST flips OR the 5m ST signals down OR price crosses KAMA OR MACD/RSI confirm
        dataframe.loc[
            exit_long_condition_15m_flip | 
            exit_long_condition_5m_signal |
            exit_long_cond_kama_cross_down, # |
            # exit_long_cond_macd_rsi_confirm, # Added MACD/RSI exit condition # REMOVED
            'exit_long'] = 1
        
        # --- 做空出场条件 ---
        if self.can_short:
            # Condition 1: 15-minute SuperTrend flips to 'up' (Original slow logic)
            exit_short_condition_15m_flip = (
                (dataframe[f'stx_long_15m'] == 'up') &
                (dataframe[f'stx_long_15m'].shift(1) == 'down')
            )
            # Condition 2: 5-minute SuperTrend flips to 'up' (New, more reactive logic)
            exit_short_condition_5m_signal = (
                (dataframe['stx_short'] == 'up') &
                (dataframe['stx_short'].shift(1) == 'down')
            )

            # New: Price crosses above 5m KAMA as an exit condition
            exit_short_cond_kama_cross_up = (
                (dataframe['close'] > dataframe['kama_short']) &
                (dataframe['close'].shift(1) <= dataframe['kama_short'].shift(1)) # Just crossed above
            )

            # New: MACD + RSI confirmation for short exit
            # exit_short_cond_macd_rsi_confirm = ( # REMOVED
            #     (dataframe['macd'] > dataframe['macdsignal']) & # MACD bullish crossover # REMOVED
            #     (dataframe['macd'].shift(1) <= dataframe['macdsignal'].shift(1)) & # Confirmed crossover # REMOVED
            #     (dataframe['rsi'] > self.rsi_exit_short_threshold.value) # RSI enters strong zone # REMOVED
            # ) # REMOVED

            # Combine conditions: exit if EITHER the 15m ST flips OR the 5m ST signals up OR price crosses KAMA OR MACD/RSI confirm
            dataframe.loc[
                exit_short_condition_15m_flip | 
                exit_short_condition_5m_signal |
                exit_short_cond_kama_cross_up, # |
                # exit_short_cond_macd_rsi_confirm, # Added MACD/RSI exit condition # REMOVED
                'exit_short'] = 1
            
        return dataframe

    # --- 动态仓位管理 ---
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            entry_tag: Optional[str], **kwargs) -> float:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return proposed_stake
        
        last_candle = dataframe.iloc[-1]
        
        atr_value = last_candle.get('atr_pos_size') # Use .get for safety

        if atr_value is None or atr_value == 0 or self.atr_multiplier_pos_size.value == 0:
            logger.warning(f"ATR for position size is 0 or None for {pair} at {current_time}. Using proposed_stake: {proposed_stake}")
            return proposed_stake
        
        try:
            current_balance = self.wallets.get_total_stake_amount()
        except Exception as e:
            logger.warning(f"Could not get total stake amount: {e}. Defaulting to a calculated balance if possible or proposed_stake.")
            # Fallback if wallet info is not available (e.g. during plotting or some backtest modes)
            current_balance = proposed_stake / (self.risk_per_trade.value if self.risk_per_trade.value > 0 else 0.01) # Estimate total equity

        if current_balance <= 0: # Ensure current_balance is positive
             logger.warning(f"Current balance is {current_balance}. Using proposed_stake.")
             return proposed_stake

        risk_amount_per_trade = current_balance * self.risk_per_trade.value
        
        stop_loss_distance = atr_value * self.atr_multiplier_pos_size.value # This is in quote currency if ATR is from quote asset
        
        if stop_loss_distance == 0:
            logger.warning(f"Stop loss distance is 0 for {pair} (ATR or multiplier is zero). Using proposed_stake.")
            return proposed_stake
            
        position_size_in_asset = risk_amount_per_trade / stop_loss_distance
        
        calculated_stake = position_size_in_asset * current_rate
        
        final_stake = max(min_stake, calculated_stake)
        final_stake = min(max_stake, final_stake)
        
        # logger.info(f"Pair: {pair}, Rate: {current_rate}, ATR: {atr_value:.4f}, SL Dist: {stop_loss_distance:.4f}, Risk Amt: {risk_amount_per_trade:.2f}, Calc Stake: {calculated_stake:.2f}, Final Stake: {final_stake:.2f}")

        return final_stake

    # --- ATR 基于入场时间的止损 ---
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损逻辑，基于入场时的ATR设置初始止损位。
        此方法应返回一个绝对价格作为止损点。
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            # 回退到配置的固定止损百分比对应的绝对价格
            if trade.is_short:
                return trade.open_rate * (1 + abs(self.stoploss))
            else:
                return trade.open_rate * (1 - abs(self.stoploss))

        # 获取入场时的蜡烛数据
        # trade.open_date 可能不完全对齐K线时间，我们寻找trade.open_date之前（包含）的最后一根K线
        entry_candle_series = dataframe[dataframe['date'] <= trade.open_date].iloc[-1:]

        if entry_candle_series.empty:
            # logger.warning(f"Could not find entry candle for {pair} at {trade.open_date}. Defaulting stoploss.")
            if trade.is_short:
                return trade.open_rate * (1 + abs(self.stoploss))
            else:
                return trade.open_rate * (1 - abs(self.stoploss))

        atr_at_entry = entry_candle_series['atr_pos_size'].values[0]

        if pd.isna(atr_at_entry) or atr_at_entry == 0 or self.atr_multiplier_pos_size.value == 0:
            # logger.warning(f"Invalid ATR ({atr_at_entry}) or multiplier for {pair} at entry. Defaulting stoploss.")
            if trade.is_short:
                return trade.open_rate * (1 + abs(self.stoploss))
            else:
                return trade.open_rate * (1 - abs(self.stoploss))

        stop_loss_distance_at_entry = atr_at_entry * self.atr_multiplier_pos_size.value

        if trade.is_short:
            # 对于做空，止损价格在开仓价格之上
            desired_sl_rate = trade.open_rate + stop_loss_distance_at_entry
        else:
            # 对于做多，止损价格在开仓价格之下
            desired_sl_rate = trade.open_rate - stop_loss_distance_at_entry
        
        # custom_stoploss 返回的是绝对价格
        # Freqtrade会处理：如果 desired_sl_rate (for long) >= current_rate, then stop.
        #                 如果 desired_sl_rate (for short) <= current_rate, then stop.
        # 我们需要确保返回的 desired_sl_rate 不会立即触发自己 (除非ATR是0或负数, 已处理)
        # 另外，确保它不会比全局的 catastrophy stop 更宽松 (Freqtrade 会自动取最紧的那个)
        return desired_sl_rate

    # --- Freqtrade 回调函数 ---
    def version(self) -> str:
        """
        Returns strategy version.
        """
        return "TRDV3_0.2" # Incremented version

    def bot_loop_start(self, **kwargs) -> None:
        """
        Called at the start of the bot iteration (one loop).
        """
        # For informative decorator, the suffix is automatically determined by Freqtrade
        # self.info_timeframe_suffix = timeframe_to_minutes(self.info_timeframe) # Not needed like this
        logger.info(f"Starting Loop for {self.version()} on {self.timeframe} timeframe. ")
        return super().bot_loop_start(**kwargs)

    # --- 绘图配置 (可选) ---
    plot_config = {
        'main_plot': {
            'st_short': {'color': 'blue', 'type': 'line'},
            'st_long_15m': {'color': 'purple', 'type': 'line'}, 
            'kama_short': {'color': 'green'},
            'kama_long_15m': {'color': 'orange'},
            'ema_long_filter_15m': {'color': 'cyan', 'type': 'line'},
        },
        'subplots': {
            "ATR": {
                'atr_pos_size': {'color': 'brown'},
            },
            "KAMA_SLOPE":{
                'kama_short_slope': {'color': 'red'},
                'kama_long_slope_15m': {'color': 'magenta'}
            },
            "STX_15M": { # Example for visualizing 15m trend direction
                 'stx_long_15m': {'color': 'black', 'type': 'bar'} # May need conversion to 1 for up, -1 for down
            },
            "EMA_SLOPE_15M": { 'ema_long_filter_slope_15m': {'color': 'orange'} }
            # , # REMOVED
            # "MACD": { # REMOVED
            #     'macd': {'color': 'blue', 'type': 'line'}, # REMOVED
            #     'macdsignal': {'color': 'orange', 'type': 'line'}, # REMOVED
            #     'macdhist': {'color': 'green', 'type': 'bar'} # REMOVED
            # }, # REMOVED
            # "RSI": { # REMOVED
            #     'rsi': {'color': 'purple', 'type': 'line'} # REMOVED
            # } # REMOVED
        }
    }
