# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, merge_informative_pair
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime, timedelta, timezone
from freqtrade.persistence import Trade
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy

# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging
from scipy import stats
from collections import deque

log = logging.getLogger(__name__)


class PumpAndDumpshortReversed(IStrategy):
    """
    ## 反向做多策略 V1.0 - 基于Enhanced策略的完全反向

    **作者:** Claude AI & User
    **版本:** 1.0 (Reversed short Strategy)
    **核心理念:**
    基于PumpAndDumpShortEnhanced策略的完全反向版本
    当原策略发出做空信号时，我们做多

    **反向逻辑:**
    - 保持所有技术指标和计算逻辑
    - 保持所有参数设置和优化
    - 只改变交易方向：做空→做多
    - 预期胜率：与原策略互补

    **技术特点:**
    1. 16分制信号评分系统
    2. 多重技术指标确认
    3. 市场环境自适应
    4. 动态阈值调整
    5. 完整的风险管理
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '1h'
    can_short = True  # 改为只做多
    process_only_new_candles = True
    startup_candle_count: int = 50

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }

    # --- 止损和止盈设置 ---
    use_custom_stoploss = True
    stoploss = -0.99

    # 动态追踪止损 - 更激进的止盈设置
    trailing_stop = True
    trailing_stop_positive = 0.01  # 1%就开始追踪止损
    trailing_stop_positive_offset = 0.02  # 2%的偏移量
    trailing_only_offset_is_reached = True

    use_exit_signal = True
    use_custom_exit = True
    minimal_roi = {}

    # --- 优化参数 (基于回测结果重新调整) ---
    # 日线级别参数 - 降低要求增加交易频率
    consecutive_green_days = IntParameter(1, 3, default=2, space="buy", optimize=True)
    daily_rsi_threshold = IntParameter(55, 75, default=65, space="buy", optimize=True)
    daily_volume_threshold = DecimalParameter(1.2, 2.5, default=1.5, decimals=1, space="buy", optimize=True)

    # 小时级别参数 - 降低RSI阈值，提高敏感度
    hourly_rsi_threshold = IntParameter(50, 70, default=60, space="buy", optimize=True)
    volume_spike_multiplier = DecimalParameter(1.2, 3.0, default=1.8, decimals=1, space="buy", optimize=True)

    # 信号质量参数 - 大幅降低阈值提高交易频率
    signal_strength_threshold = IntParameter(2, 6, default=3, space="buy", optimize=True)
    confirmation_period = IntParameter(1, 2, default=1, space="buy", optimize=True)

    # 风险管理参数 - 更紧的止损
    atr_period = IntParameter(10, 20, default=14, space="sell", optimize=True)
    base_stoploss_atr = DecimalParameter(1.5, 3.0, default=2.0, decimals=1, space="sell", optimize=True)
    dynamic_stoploss_factor = DecimalParameter(0.8, 1.2, default=1.0, decimals=1, space="sell", optimize=True)

    # 市场环境参数 - 更宽松的波动率要求
    trend_strength_period = IntParameter(15, 40, default=25, space="buy", optimize=True)
    volatility_threshold = DecimalParameter(0.01, 0.06, default=0.03, decimals=2, space="buy", optimize=True)

    # 调试模式
    debug_mode = True

    # 统计学检测参数
    statistical_lookback = IntParameter(50, 200, default=100, space="buy", optimize=True)
    confidence_level = DecimalParameter(0.8, 0.99, default=0.95, decimals=2, space="buy", optimize=True)
    adaptive_threshold = True

    # 历史表现追踪
    signal_performance_history = {
        'distribution_short': [],
        'markup_reversal': [],
        'markdown_continuation': [],
        'accumulation_short': [],
        'neutral_short': []
    }

    # 统计学检测参数
    statistical_lookback = IntParameter(50, 200, default=100, space="buy", optimize=True)
    confidence_level = DecimalParameter(0.8, 0.99, default=0.95, decimals=2, space="buy", optimize=True)
    adaptive_threshold = True

    # 动态阈值存储
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        self.signal_performance_history = {}
        self.dynamic_thresholds = {}
        self.statistical_metrics = {}

    def informative_pairs(self):
        """定义需要获取的额外数据源"""
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, '1d') for pair in pairs]
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算增强版指标系统"""

        # --- 获取日线数据 ---
        informative_df = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe='1d')

        if not informative_df.empty:
            # 日线指标计算
            informative_df = self.calculate_daily_indicators(informative_df)
            # 合并日线数据
            dataframe = merge_informative_pair(dataframe, informative_df, self.timeframe, '1d', ffill=True)

        # 确保日线信号列存在
        if 'enhanced_daily_signal_1d' not in dataframe.columns:
            dataframe['enhanced_daily_signal_1d'] = False

        # --- 小时级别指标 ---
        dataframe = self.calculate_hourly_indicators(dataframe)

        # --- 市场环境识别 ---
        dataframe = self.identify_market_environment(dataframe)

        # --- 信号质量评分 ---
        dataframe = self.calculate_signal_quality(dataframe)

        # --- 统计学检测算法 ---
        if self.adaptive_threshold:
            dataframe = self.statistical_signal_detection(dataframe, 'enhanced')
            dataframe = self.adaptive_threshold_by_signal_type(dataframe)

        return dataframe

    def calculate_statistical_thresholds(self, df: DataFrame, metadata: dict) -> DataFrame:
        """统计学检测算法 - 动态阈值调整"""
        from scipy import stats
        import warnings
        warnings.filterwarnings('ignore')

        pair = metadata.get('pair', 'Unknown')

        # 计算各信号组件的统计特征
        lookback = min(self.statistical_lookback.value, len(df))
        if lookback < 30:  # 数据不足时使用默认值
            df['adaptive_signal_threshold'] = self.signal_strength_threshold.value
            return df

        recent_data = df.tail(lookback).copy()

        # 1. 信号质量分布分析
        signal_scores = recent_data['signal_quality_score'].fillna(0)

        # 计算统计指标
        mean_score = signal_scores.mean()
        std_score = signal_scores.std()
        median_score = signal_scores.median()

        # 2. 异常值检测 (使用Z-score和IQR方法)
        z_scores = np.abs(stats.zscore(signal_scores.fillna(mean_score)))
        q1, q3 = signal_scores.quantile([0.25, 0.75])
        iqr = q3 - q1

        # 3. 动态阈值计算
        # 方法1: 基于置信区间
        confidence_threshold = mean_score + stats.norm.ppf(self.confidence_level.value) * std_score

        # 方法2: 基于分位数
        percentile_threshold = signal_scores.quantile(0.8)  # 80分位数

        # 方法3: 基于历史表现 (如果有交易历史)
        performance_threshold = self.calculate_performance_based_threshold(pair, recent_data)

        # 4. 综合阈值 (加权平均)
        weights = [0.4, 0.3, 0.3]  # 置信区间、分位数、历史表现
        thresholds = [confidence_threshold, percentile_threshold, performance_threshold]

        # 过滤无效值
        valid_thresholds = [t for t in thresholds if not pd.isna(t) and t > 0]
        valid_weights = weights[:len(valid_thresholds)]

        if valid_thresholds:
            adaptive_threshold = np.average(valid_thresholds, weights=valid_weights)
        else:
            adaptive_threshold = self.signal_strength_threshold.value

        # 5. 阈值边界控制
        min_threshold = 1.0
        max_threshold = 8.0
        adaptive_threshold = np.clip(adaptive_threshold, min_threshold, max_threshold)

        # 6. 平滑处理 (避免阈值剧烈变化)
        if pair in self.dynamic_thresholds:
            previous_threshold = self.dynamic_thresholds[pair]
            smoothing_factor = 0.7  # 70%保留历史，30%使用新值
            adaptive_threshold = previous_threshold * smoothing_factor + adaptive_threshold * (1 - smoothing_factor)

        # 存储结果
        self.dynamic_thresholds[pair] = adaptive_threshold
        self.statistical_metrics[pair] = {
            'mean_score': mean_score,
            'std_score': std_score,
            'median_score': median_score,
            'confidence_threshold': confidence_threshold,
            'percentile_threshold': percentile_threshold,
            'performance_threshold': performance_threshold,
            'final_threshold': adaptive_threshold,
            'data_points': len(signal_scores)
        }

        # 应用到数据框
        df['adaptive_signal_threshold'] = adaptive_threshold

        # 调试信息
        if self.debug_mode:
            log.info(f"📊 {pair} 统计阈值: {adaptive_threshold:.2f} "
                    f"(均值:{mean_score:.2f}, 标准差:{std_score:.2f}, "
                    f"置信区间:{confidence_threshold:.2f}, 分位数:{percentile_threshold:.2f})")

        return df

    def calculate_performance_based_threshold(self, pair: str, recent_data: DataFrame) -> float:
        """基于历史表现计算阈值"""
        try:
            # 模拟历史交易表现分析
            # 这里可以集成实际的交易历史数据

            # 分析不同信号强度下的假设表现
            signal_scores = recent_data['signal_quality_score'].fillna(0)

            # 计算不同阈值下的信号频率
            thresholds_to_test = np.arange(1, 8, 0.5)
            optimal_threshold = self.signal_strength_threshold.value

            best_score = -float('inf')

            for threshold in thresholds_to_test:
                signals_above_threshold = (signal_scores >= threshold).sum()
                if signals_above_threshold == 0:
                    continue

                # 信号质量评分 = 平均信号强度 - 频率惩罚
                avg_signal_strength = signal_scores[signal_scores >= threshold].mean()
                frequency_penalty = signals_above_threshold / len(signal_scores) * 2  # 频率惩罚

                score = avg_signal_strength - frequency_penalty

                if score > best_score:
                    best_score = score
                    optimal_threshold = threshold

            return optimal_threshold

        except Exception as e:
            log.warning(f"性能阈值计算失败 {pair}: {e}")
            return self.signal_strength_threshold.value

    def calculate_daily_indicators(self, df: DataFrame) -> DataFrame:
        """计算日线级别的增强指标"""
        # 基础指标
        df['rsi_daily'] = ta.RSI(df, timeperiod=14)
        df['atr_daily'] = ta.ATR(df, timeperiod=14)
        df['volume_sma'] = ta.SMA(df['volume'], timeperiod=20)

        # 连续上涨天数，安全处理NaN值
        df['is_green'] = (df['close'].fillna(0) > df['open'].fillna(0)).astype(int)
        df['consecutive_green'] = df['is_green'].rolling(window=self.consecutive_green_days.value).sum().fillna(0)

        # 成交量放大，避免除零错误
        df['volume_expansion'] = np.where(
            df['volume_sma'] > 0,
            df['volume'] / df['volume_sma'],
            1.0
        )
        df['volume_expansion'] = df['volume_expansion'].fillna(1.0)

        # 价格动量，避免除零错误
        close_shifted = df['close'].shift(self.consecutive_green_days.value)
        df['price_momentum'] = np.where(
            close_shifted > 0,
            (df['close'] / close_shifted - 1) * 100,
            0.0
        )
        df['price_momentum'] = df['price_momentum'].fillna(0.0)

        # 波动率扩张，避免除零错误
        df['volatility'] = np.where(
            df['close'] > 0,
            df['atr_daily'] / df['close'],
            0.0
        )
        df['volatility'] = df['volatility'].fillna(0.0)

        volatility_mean = df['volatility'].rolling(20).mean().fillna(df['volatility'])
        df['volatility_expansion'] = (df['volatility'] > volatility_mean * 1.5).fillna(False)

        # 构建增强的日线信号，大幅放宽条件以增加交易频率
        df['enhanced_daily_signal'] = (
            (df['consecutive_green'].fillna(0) >= self.consecutive_green_days.value) &
            (df['rsi_daily'].fillna(0) >= self.daily_rsi_threshold.value) &
            (df['volume_expansion'].fillna(0) >= self.daily_volume_threshold.value) &
            (df['price_momentum'].fillna(0) > 2) &  # 降低到2%的涨幅要求
            (df['volatility_expansion'].fillna(False) | (df['volatility'].fillna(0) > 0.01))  # 放宽波动率要求
        )

        return df

    def calculate_hourly_indicators(self, df: DataFrame) -> DataFrame:
        """计算小时级别的增强指标"""
        # 基础技术指标
        df['rsi'] = ta.RSI(df, timeperiod=14)
        df['atr'] = ta.ATR(df, timeperiod=self.atr_period.value)

        # 安全计算布林带
        try:
            # 确保输入数据为数值类型
            close_clean = pd.to_numeric(df['close'], errors='coerce').ffill().bfill()

            if len(close_clean.dropna()) >= 20:  # 确保有足够的数据
                bb_upper, bb_middle, bb_lower = ta.BBANDS(close_clean, timeperiod=20)
                df['bb_upper'] = pd.to_numeric(bb_upper, errors='coerce')
                df['bb_middle'] = pd.to_numeric(bb_middle, errors='coerce')
                df['bb_lower'] = pd.to_numeric(bb_lower, errors='coerce')
            else:
                # 数据不足时使用简单方法
                sma_20 = close_clean.rolling(20, min_periods=1).mean()
                std_20 = close_clean.rolling(20, min_periods=1).std().fillna(0)
                df['bb_upper'] = sma_20 + (std_20 * 2)
                df['bb_middle'] = sma_20
                df['bb_lower'] = sma_20 - (std_20 * 2)
        except Exception:
            # 如果布林带计算失败，使用简单的移动平均线替代
            close_safe = pd.to_numeric(df['close'], errors='coerce').ffill().fillna(100)
            sma_20 = close_safe.rolling(20, min_periods=1).mean()
            std_20 = close_safe.rolling(20, min_periods=1).std().fillna(0)
            df['bb_upper'] = sma_20 + (std_20 * 2)
            df['bb_middle'] = sma_20
            df['bb_lower'] = sma_20 - (std_20 * 2)

        # 确保布林带数据为数值类型并处理NaN值
        df['bb_upper'] = pd.to_numeric(df['bb_upper'], errors='coerce').fillna(df['close'].fillna(100))
        df['bb_middle'] = pd.to_numeric(df['bb_middle'], errors='coerce').fillna(df['close'].fillna(100))
        df['bb_lower'] = pd.to_numeric(df['bb_lower'], errors='coerce').fillna(df['close'].fillna(100))

        # 成交量指标
        df['volume_sma'] = ta.SMA(df['volume'], timeperiod=20)
        df['volume_spike'] = df['volume'] > df['volume_sma'] * self.volume_spike_multiplier.value

        # K线形态识别
        df = self.identify_reversal_patterns(df)

        # 动量指标
        try:
            macd_result = ta.MACD(df)
            df['macd'] = pd.to_numeric(macd_result[0], errors='coerce').fillna(0)
            df['macd_signal'] = pd.to_numeric(macd_result[1], errors='coerce').fillna(0)
            df['macd_hist'] = pd.to_numeric(macd_result[2], errors='coerce').fillna(0)
        except Exception:
            df['macd'] = 0
            df['macd_signal'] = 0
            df['macd_hist'] = 0

        df['momentum'] = ta.MOM(df, timeperiod=10)

        # 新增技术指标
        # Stochastic RSI - 更敏感的超买超卖指标
        try:
            stoch_rsi = ta.STOCHRSI(df, timeperiod=14)
            df['stoch_rsi_k'] = pd.to_numeric(stoch_rsi[0], errors='coerce').fillna(50)
            df['stoch_rsi_d'] = pd.to_numeric(stoch_rsi[1], errors='coerce').fillna(50)
        except Exception:
            df['stoch_rsi_k'] = 50
            df['stoch_rsi_d'] = 50

        # Williams %R - 另一个超买超卖指标
        df['williams_r'] = ta.WILLR(df, timeperiod=14).fillna(-50)

        # CCI - 商品通道指数
        df['cci'] = ta.CCI(df, timeperiod=20).fillna(0)

        # 价格相对强度
        df['price_change_pct'] = df['close'].pct_change().fillna(0) * 100

        # 成交量价格趋势 (VPT)
        df['vpt'] = (df['volume'] * df['price_change_pct']).cumsum().fillna(0)
        df['vpt_sma'] = df['vpt'].rolling(20).mean().fillna(0)

        return df

    def identify_reversal_patterns(self, df: DataFrame) -> DataFrame:
        """识别反转K线形态"""
        # 射击之星
        body = abs(df['close'] - df['open'])
        upper_wick = df['high'] - np.maximum(df['open'], df['close'])
        lower_wick = np.minimum(df['open'], df['close']) - df['low']

        df['shooting_star'] = (
            (upper_wick > body * 2) &
            (lower_wick < body * 0.5) &
            (body > 0.000001)
        )

        # 十字星
        df['doji'] = (
            (body < (df['high'] - df['low']) * 0.1) &
            (df['high'] - df['low'] > 0.000001)
        )

        # 长上影线
        df['short_upper_shadow'] = upper_wick > (df['high'] - df['low']) * 0.6

        return df

    def statistical_signal_detection(self, df: DataFrame, signal_type: str) -> DataFrame:
        """统计学信号检测算法"""

        # 计算信号质量的统计特征
        signal_quality = df['signal_quality_score'].fillna(0)

        # 1. 移动平均和标准差
        rolling_mean = signal_quality.rolling(window=20, min_periods=5).mean()
        rolling_std = signal_quality.rolling(window=20, min_periods=5).std()

        # 2. Z-Score检测异常值
        z_scores = np.abs((signal_quality - rolling_mean) / (rolling_std + 1e-8))

        # 3. 分位数检测
        rolling_quantile_75 = signal_quality.rolling(window=50, min_periods=10).quantile(0.75)
        rolling_quantile_90 = signal_quality.rolling(window=50, min_periods=10).quantile(0.90)
        rolling_quantile_95 = signal_quality.rolling(window=50, min_periods=10).quantile(0.95)

        # 4. 趋势检测 (使用线性回归斜率)
        def calculate_trend_slope(series, window=10):
            slopes = []
            for i in range(len(series)):
                start_idx = max(0, i - window + 1)
                end_idx = i + 1
                if end_idx - start_idx >= 3:  # 至少需要3个点
                    x = np.arange(end_idx - start_idx)
                    y = series.iloc[start_idx:end_idx].values
                    if len(y) > 0 and not np.all(np.isnan(y)):
                        slope, _, _, _, _ = stats.linregress(x, y[~np.isnan(y)])
                        slopes.append(slope)
                    else:
                        slopes.append(0)
                else:
                    slopes.append(0)
            return pd.Series(slopes, index=series.index)

        trend_slope = calculate_trend_slope(signal_quality)

        # 5. 动态阈值计算
        base_threshold = self.signal_strength_threshold.value

        # 基于统计分布的动态调整
        dynamic_threshold_statistical = np.where(
            signal_quality > rolling_quantile_95.fillna(base_threshold),
            base_threshold * 0.8,  # 在高质量期降低阈值
            np.where(
                signal_quality < rolling_quantile_75.fillna(base_threshold),
                base_threshold * 1.2,  # 在低质量期提高阈值
                base_threshold
            )
        )

        # 基于趋势的动态调整
        dynamic_threshold_trend = np.where(
            trend_slope > 0.1,  # 信号质量上升趋势
            base_threshold * 0.9,
            np.where(
                trend_slope < -0.1,  # 信号质量下降趋势
                base_threshold * 1.1,
                base_threshold
            )
        )

        # 基于Z-Score的异常检测
        dynamic_threshold_zscore = np.where(
            z_scores > 2.0,  # 异常高的信号质量
            base_threshold * 0.7,
            np.where(
                z_scores < 0.5,  # 异常低的信号质量
                base_threshold * 1.3,
                base_threshold
            )
        )

        # 综合动态阈值 (加权平均)
        df['dynamic_threshold_statistical'] = (
            dynamic_threshold_statistical * 0.4 +
            dynamic_threshold_trend * 0.3 +
            dynamic_threshold_zscore * 0.3
        )

        # 添加统计特征到数据框
        df['signal_quality_zscore'] = z_scores
        df['signal_quality_trend'] = trend_slope
        df['signal_quality_percentile'] = signal_quality.rolling(50).rank(pct=True)

        # 置信区间计算
        confidence_interval = stats.t.interval(
            self.confidence_level.value,
            df=19,  # 20-1 degrees of freedom
            loc=rolling_mean,
            scale=rolling_std / np.sqrt(20)
        )

        df['signal_confidence_lower'] = confidence_interval[0]
        df['signal_confidence_upper'] = confidence_interval[1]

        return df

    def adaptive_threshold_by_signal_type(self, df: DataFrame) -> DataFrame:
        """基于信号类型的自适应阈值调整"""

        # 基于最新回测结果的信号类型权重 (2025-07-24更新) - 重新优化
        signal_type_weights = {
            'markdown_continuation': 0.3,  # 胜率50%，进一步降低阈值增加频率
            'markup_reversal': 0.8,        # 胜率7.7%，适度降低阈值
            'accumulation_short': 1.0,     # 给予机会，适中阈值
            'neutral_short': 1.2,          # 适度提高阈值
            'distribution_short': 1.5      # 大幅降低阈值，给予更多机会
        }

        # 为每种市场阶段设置不同的阈值
        base_threshold = df.get('dynamic_threshold_statistical', self.signal_strength_threshold.value)

        df['adaptive_signal_threshold'] = np.select(
            [
                df['market_phase'].fillna('neutral') == 'markdown',
                df['market_phase'].fillna('neutral') == 'markup',
                df['market_phase'].fillna('neutral') == 'accumulation',
                df['market_phase'].fillna('neutral') == 'distribution',
            ],
            [
                base_threshold * signal_type_weights['markdown_continuation'],
                base_threshold * signal_type_weights['markup_reversal'],
                base_threshold * signal_type_weights['accumulation_short'],
                base_threshold * signal_type_weights['distribution_short'],
            ],
            default=base_threshold * signal_type_weights['neutral_short']
        )

        return df

    def identify_market_environment(self, df: DataFrame) -> DataFrame:
        """增强版市场环境识别"""
        # 趋势强度
        df['ema_fast'] = ta.EMA(df, timeperiod=12)
        df['ema_slow'] = ta.EMA(df, timeperiod=26)
        df['ema_short'] = ta.EMA(df, timeperiod=50)
        df['trend_strength'] = (df['ema_fast'] - df['ema_slow']) / df['ema_slow'] * 100

        # 长期趋势
        df['short_term_trend'] = np.where(df['close'] > df['ema_short'], 'up', 'down')

        # 波动率环境
        df['volatility'] = df['atr'] / df['close']
        df['volatility_ma'] = df['volatility'].rolling(20).mean()
        df['volatility_regime'] = np.where(
            df['volatility'] > df['volatility_ma'] * 1.5, 'high',
            np.where(df['volatility'] < df['volatility_ma'] * 0.7, 'low', 'normal')
        )

        # 市场情绪（更精细化）
        df['market_sentiment'] = np.where(
            (df['trend_strength'] > 3) & (df['short_term_trend'] == 'up'), 'strong_bullish',
            np.where(
                (df['trend_strength'] > 1) & (df['short_term_trend'] == 'up'), 'bullish',
                np.where(
                    (df['trend_strength'] < -3) & (df['short_term_trend'] == 'down'), 'strong_bearish',
                    np.where(
                        (df['trend_strength'] < -1) & (df['short_term_trend'] == 'down'), 'bearish',
                        'neutral'
                    )
                )
            )
        )

        # 市场阶段识别
        df['market_phase'] = self.identify_market_phase(df)

        # 动态参数调整
        df = self.adjust_parameters_by_market(df)

        return df

    def identify_market_phase(self, df: DataFrame) -> pd.Series:
        """识别市场阶段"""
        # 计算价格相对位置，安全处理NaN值
        high_20 = df['high'].rolling(20).max()
        low_20 = df['low'].rolling(20).min()

        # 避免除零错误和NaN值
        price_range = high_20 - low_20
        price_position = np.where(
            price_range > 0,
            (df['close'] - low_20) / price_range,
            0.5  # 默认中位值
        )
        price_position = pd.Series(price_position, index=df.index).fillna(0.5)

        # 计算成交量趋势，安全处理NaN值
        volume_ma = df['volume'].rolling(20).mean()
        volume_trend = np.where(
            volume_ma > 0,
            df['volume'] / volume_ma,
            1.0  # 默认值
        )
        volume_trend = pd.Series(volume_trend, index=df.index).fillna(1.0)

        # 安全获取趋势强度
        trend_strength = df['trend_strength'].fillna(0)

        # 综合判断市场阶段
        conditions = [
            # 积累阶段：低位，成交量逐渐放大
            (price_position < 0.3) & (volume_trend > 1.2) & (trend_strength > -1),
            # 上升阶段：价格上涨，成交量配合
            (price_position > 0.3) & (price_position < 0.8) & (trend_strength > 1) & (volume_trend > 1.0),
            # 分发阶段：高位，成交量放大但价格滞涨
            (price_position > 0.7) & (volume_trend > 1.5) & (trend_strength < 2),
            # 下降阶段：价格下跌
            (trend_strength < -1)
        ]

        choices = ['accumulation', 'markup', 'distribution', 'markdown']

        return pd.Series(np.select(conditions, choices, default='neutral'), index=df.index)

    def adjust_parameters_by_market(self, df: DataFrame) -> DataFrame:
        """根据市场环境动态调整参数"""
        # 根据市场阶段调整信号阈值，安全处理NaN值
        df['dynamic_rsi_threshold'] = np.where(
            df['market_phase'].fillna('neutral') == 'distribution',
            self.hourly_rsi_threshold.value - 5,  # 分发阶段降低阈值
            np.where(
                df['market_phase'].fillna('neutral') == 'markup',
                self.hourly_rsi_threshold.value + 5,  # 上升阶段提高阈值
                self.hourly_rsi_threshold.value
            )
        )

        # 根据波动率调整成交量阈值，安全处理NaN值
        df['dynamic_volume_multiplier'] = np.where(
            df['volatility_regime'].fillna('normal') == 'high',
            self.volume_spike_multiplier.value * 1.2,
            np.where(
                df['volatility_regime'].fillna('normal') == 'low',
                self.volume_spike_multiplier.value * 0.8,
                self.volume_spike_multiplier.value
            )
        )

        # 根据市场情绪调整信号强度要求，安全处理NaN值
        df['dynamic_signal_threshold'] = np.where(
            df['market_sentiment'].fillna('neutral').isin(['strong_bullish', 'bullish']),
            self.signal_strength_threshold.value + 1,  # 牛市中提高要求
            np.where(
                df['market_sentiment'].fillna('neutral').isin(['strong_bearish', 'bearish']),
                self.signal_strength_threshold.value - 1,  # 熊市中降低要求
                self.signal_strength_threshold.value
            )
        )

        return df

    def calculate_signal_quality(self, df: DataFrame) -> DataFrame:
        """计算增强版信号质量评分 - 重新平衡权重"""
        signal_components = []

        # 降低日线信号权重，避免过度依赖: 2分
        if 'enhanced_daily_signal_1d' in df.columns:
            daily_signal = df['enhanced_daily_signal_1d'].fillna(False).infer_objects(copy=False).astype(bool).astype(int) * 2
            signal_components.append(daily_signal)
        else:
            signal_components.append(pd.Series(0, index=df.index))

        # RSI超买信号权重: 2分 (保持重要性)
        dynamic_rsi_threshold = df.get('dynamic_rsi_threshold', self.hourly_rsi_threshold.value)
        if isinstance(dynamic_rsi_threshold, pd.Series):
            dynamic_rsi_signal = (df['rsi'].fillna(0) >= dynamic_rsi_threshold.fillna(self.hourly_rsi_threshold.value)).astype(int) * 2
        else:
            dynamic_rsi_signal = (df['rsi'].fillna(0) >= dynamic_rsi_threshold).astype(int) * 2
        signal_components.append(dynamic_rsi_signal)

        # 成交量激增权重: 1分 (降低权重)
        dynamic_volume_multiplier = df.get('dynamic_volume_multiplier', self.volume_spike_multiplier.value)
        if isinstance(dynamic_volume_multiplier, pd.Series):
            dynamic_volume_signal = (df['volume'].fillna(0) > df['volume_sma'].fillna(0) * dynamic_volume_multiplier.fillna(self.volume_spike_multiplier.value)).astype(int) * 1
        else:
            dynamic_volume_signal = (df['volume'].fillna(0) > df['volume_sma'].fillna(0) * dynamic_volume_multiplier).astype(int) * 1
        signal_components.append(dynamic_volume_signal)

        # K线形态权重: 2分 (提高权重，更重视技术形态)
        pattern_score = (df['shooting_star'].fillna(False).astype(int) +
                        df['doji'].fillna(False).astype(int) +
                        df['short_upper_shadow'].fillna(False).astype(int))
        signal_components.append(np.minimum(pattern_score, 2))

        # MACD背离权重: 2分 (提高权重)
        macd_bearish = (df['macd'].fillna(0) < df['macd_signal'].fillna(0)).astype(int) * 2
        signal_components.append(macd_bearish)

        # 新增：价格动量信号: 1分
        price_momentum = (df['close'].fillna(0) < df['close'].shift(1).fillna(0)).astype(int)
        signal_components.append(price_momentum)

        # 新增：布林带位置信号: 1分
        if 'bb_upper' in df.columns:
            bb_signal = (df['close'].fillna(0) > df['bb_upper'].fillna(0) * 0.98).astype(int)
            signal_components.append(bb_signal)
        else:
            signal_components.append(pd.Series(0, index=df.index))

        # 新增：Stochastic RSI超买信号: 1分
        stoch_rsi_signal = (df['stoch_rsi_k'].fillna(50) > 80).astype(int)
        signal_components.append(stoch_rsi_signal)

        # 新增：Williams %R超买信号: 1分
        williams_signal = (df['williams_r'].fillna(-50) > -20).astype(int)
        signal_components.append(williams_signal)

        # 新增：CCI超买信号: 1分
        cci_signal = (df['cci'].fillna(0) > 100).astype(int)
        signal_components.append(cci_signal)

        # 新增：成交量价格趋势背离: 1分
        vpt_divergence = (df['vpt'].fillna(0) < df['vpt_sma'].fillna(0)).astype(int)
        signal_components.append(vpt_divergence)

        # 计算总分，确保没有NaN值 (总分范围: 0-16分)
        df['signal_quality_score'] = sum(signal_components).fillna(0)

        # 调试信息：显示各组件得分
        if self.debug_mode and len(df) > 0:
            avg_scores = []
            component_names = [
                '日线信号(2分)', 'RSI超买(2分)', '成交量激增(1分)', 'K线形态(2分)',
                'MACD背离(2分)', '价格动量(1分)', '布林带位置(1分)',
                'Stoch RSI(1分)', 'Williams %R(1分)', 'CCI(1分)', 'VPT背离(1分)'
            ]
            for i, component in enumerate(signal_components):
                avg_score = component.mean()
                avg_scores.append(avg_score)
                if i < len(component_names):
                    log.info(f"   {component_names[i]}: 平均{avg_score:.2f}分")

            total_avg = df['signal_quality_score'].mean()
            max_score = df['signal_quality_score'].max()
            log.info(f"   总分: 平均{total_avg:.2f}分, 最高{max_score:.2f}分 (满分16分)")

        return df

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """反向做多入场条件 - 当原策略发出做空信号时做多"""

        # 初始化入场信号 - 改为做多
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''

        # 使用自适应统计学阈值，但降低阈值以增加交易频率
        if self.adaptive_threshold and 'adaptive_signal_threshold' in dataframe.columns:
            # 降低20%的阈值要求
            adaptive_threshold = dataframe['adaptive_signal_threshold'].fillna(self.signal_strength_threshold.value) * 0.8
        else:
            # 回退到原始动态阈值，同样降低
            adaptive_threshold = dataframe.get('dynamic_signal_threshold', self.signal_strength_threshold.value)
            if isinstance(adaptive_threshold, pd.Series):
                adaptive_threshold = adaptive_threshold.fillna(self.signal_strength_threshold.value) * 0.8
            else:
                adaptive_threshold = pd.Series(adaptive_threshold * 0.8, index=dataframe.index)

        # 基础入场条件，使用降低的自适应阈值
        entry_conditions = [
            # 自适应信号质量评分达标
            dataframe['signal_quality_score'].fillna(0) >= adaptive_threshold,

            # 基础数据有效性检查
            dataframe['close'].notna() & dataframe['open'].notna() & dataframe['volume'].notna()
        ]

        # 放宽确认K线要求，允许任何K线形态
        # 不再要求收盘价低于开盘价，这会大幅增加交易频率

        # 安全处理布林带条件
        if 'bb_upper' in dataframe.columns:
            # 确保数据类型正确并处理NaN值
            bb_upper_safe = pd.to_numeric(dataframe['bb_upper'], errors='coerce')
            bb_upper_safe = bb_upper_safe.fillna(bb_upper_safe.median()).fillna(0)

            close_safe = pd.to_numeric(dataframe['close'], errors='coerce')
            close_safe = close_safe.fillna(close_safe.median()).fillna(0)

            # 确保数据有效性
            if bb_upper_safe.max() > 0 and close_safe.max() > 0:
                # 放宽布林带条件，价格接近上轨即可
                try:
                    bb_condition = np.where(
                        dataframe['market_phase'].fillna('neutral') == 'distribution',
                        close_safe > bb_upper_safe * 0.90,  # 分发阶段大幅放宽条件
                        close_safe > bb_upper_safe * 0.93   # 其他阶段也放宽条件
                    )
                    entry_conditions.append(bb_condition)
                except Exception:
                    # 如果布林带条件失败，使用简单的价格条件
                    entry_conditions.append(close_safe > close_safe.rolling(20).mean().fillna(close_safe) * 0.98)
            else:
                # 如果布林带数据无效，跳过此条件
                entry_conditions.append(pd.Series(True, index=dataframe.index))

        # 安全处理波动率条件，大幅放宽
        if 'volatility' in dataframe.columns:
            volatility_safe = pd.to_numeric(dataframe['volatility'], errors='coerce').fillna(0)

            # 根据波动率环境调整入场条件，大幅放宽
            volatility_condition = np.where(
                dataframe['volatility_regime'].fillna('normal') == 'high',
                volatility_safe < self.volatility_threshold.value * 5,  # 高波动率时大幅放宽
                volatility_safe < self.volatility_threshold.value * 4   # 正常波动率也放宽
            )
            entry_conditions.append(volatility_condition)

        # 放宽市场情绪过滤，只过滤极端牛市
        entry_conditions.append(
            ~dataframe['market_sentiment'].fillna('neutral').isin(['strong_bullish'])
        )

        # 放宽趋势强度过滤
        if 'trend_strength' in dataframe.columns:
            trend_strength_safe = pd.to_numeric(dataframe['trend_strength'], errors='coerce').fillna(0)
            entry_conditions.append(trend_strength_safe < 8)  # 从5提高到8

        # 新增：多重技术指标确认
        # 至少有2个超买指标确认
        overbought_indicators = []
        if 'rsi' in dataframe.columns:
            overbought_indicators.append(dataframe['rsi'].fillna(50) > 60)
        if 'stoch_rsi_k' in dataframe.columns:
            overbought_indicators.append(dataframe['stoch_rsi_k'].fillna(50) > 70)
        if 'williams_r' in dataframe.columns:
            overbought_indicators.append(dataframe['williams_r'].fillna(-50) > -30)
        if 'cci' in dataframe.columns:
            overbought_indicators.append(dataframe['cci'].fillna(0) > 50)

        if len(overbought_indicators) >= 2:
            # 至少2个指标显示超买
            overbought_count = sum(overbought_indicators)
            entry_conditions.append(overbought_count >= 2)

        # 组合所有条件 - 改为做多信号
        enter_short = pd.Series(True, index=dataframe.index)
        for i, condition in enumerate(entry_conditions):
            enter_short &= condition

        # 增强的调试信息 - 包含统计学检测结果
        if self.debug_mode and len(dataframe) > 0:
            pair = metadata.get('pair', 'Unknown')
            total_signals = enter_short.sum()
            signal_quality_avg = dataframe['signal_quality_score'].fillna(0).mean()

            # 统计学检测信息
            if self.adaptive_threshold and 'adaptive_signal_threshold' in dataframe.columns:
                adaptive_threshold_avg = dataframe['adaptive_signal_threshold'].fillna(0).mean()
                static_threshold = self.signal_strength_threshold.value
                threshold_adjustment = adaptive_threshold_avg - static_threshold

                log.info(f"📊 {pair} 统计学检测:")
                log.info(f"   - 静态阈值: {static_threshold:.2f}")
                log.info(f"   - 自适应阈值: {adaptive_threshold_avg:.2f} (调整: {threshold_adjustment:+.2f})")

                if 'signal_quality_zscore' in dataframe.columns:
                    avg_zscore = dataframe['signal_quality_zscore'].fillna(0).mean()
                    log.info(f"   - 平均Z-Score: {avg_zscore:.2f}")

                if 'signal_quality_trend' in dataframe.columns:
                    avg_trend = dataframe['signal_quality_trend'].fillna(0).mean()
                    trend_direction = "上升" if avg_trend > 0 else "下降" if avg_trend < 0 else "平稳"
                    log.info(f"   - 信号质量趋势: {trend_direction} ({avg_trend:.3f})")

                if 'signal_quality_percentile' in dataframe.columns:
                    avg_percentile = dataframe['signal_quality_percentile'].fillna(0).mean()
                    log.info(f"   - 平均分位数: {avg_percentile:.2f}")

            if total_signals > 0:
                log.info(f"🎯 {pair}: 生成 {total_signals} 个入场信号, 平均信号质量: {signal_quality_avg:.2f}")

                # 按市场阶段分析信号分布
                if 'market_phase' in dataframe.columns:
                    phase_signals = {}
                    for phase in ['distribution', 'markup', 'markdown', 'accumulation', 'neutral']:
                        phase_mask = (dataframe['market_phase'].fillna('neutral') == phase) & enter_short
                        phase_count = phase_mask.sum()
                        if phase_count > 0:
                            phase_signals[phase] = phase_count

                    if phase_signals:
                        phase_info = ", ".join([f"{k}:{v}" for k, v in phase_signals.items()])
                        log.info(f"   - 信号分布: {phase_info}")
            else:
                current_threshold = adaptive_threshold.iloc[-1] if hasattr(adaptive_threshold, 'iloc') else adaptive_threshold
                log.info(f"❌ {pair}: 无入场信号, 平均信号质量: {signal_quality_avg:.2f}, 当前阈值: {current_threshold:.2f}")

                # 详细诊断
                quality_above_threshold = (dataframe['signal_quality_score'].fillna(0) >= current_threshold).sum()
                log.info(f"   - 信号质量达标的K线数: {quality_above_threshold}/{len(dataframe)}")

                confirm_candles = (dataframe['close'].fillna(0) < dataframe['open'].fillna(0)).sum()
                log.info(f"   - 确认K线数: {confirm_candles}/{len(dataframe)}")

        # 根据市场阶段设置不同的入场标签 - 改为做多标签
        market_phase_tags = {
            'distribution': 'distribution_short',
            'markup': 'markup_continuation',
            'markdown': 'markdown_reversal',
            'accumulation': 'accumulation_short',
            'neutral': 'neutral_short'
        }

        for phase, tag in market_phase_tags.items():
            phase_mask = enter_short & (dataframe['market_phase'].fillna('neutral') == phase)
            dataframe.loc[phase_mask, 'enter_short'] = 1
            dataframe.loc[phase_mask, 'enter_tag'] = tag

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义出场条件 - 反向做多版"""
        # 初始化出场信号 - 改为做多出场
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = ''

        # 安全处理出场条件
        exit_conditions = []

        # RSI超卖出场 - 更敏感
        if 'rsi' in dataframe.columns:
            rsi_safe = pd.to_numeric(dataframe['rsi'], errors='coerce').fillna(50)
            exit_conditions.append(rsi_safe < 35)  # 从30提高到35

        # 价格跌破布林带下轨
        if 'bb_lower' in dataframe.columns and 'close' in dataframe.columns:
            bb_lower_safe = pd.to_numeric(dataframe['bb_lower'], errors='coerce').fillna(0)
            close_safe = pd.to_numeric(dataframe['close'], errors='coerce').fillna(0)
            exit_conditions.append(close_safe < bb_lower_safe * 1.01)  # 稍微放宽条件

        # MACD金叉
        if 'macd' in dataframe.columns and 'macd_signal' in dataframe.columns:
            macd_safe = pd.to_numeric(dataframe['macd'], errors='coerce').fillna(0)
            macd_signal_safe = pd.to_numeric(dataframe['macd_signal'], errors='coerce').fillna(0)
            exit_conditions.append(macd_safe > macd_signal_safe)

        # 新增：Stochastic RSI超卖出场
        if 'stoch_rsi_k' in dataframe.columns:
            stoch_rsi_safe = pd.to_numeric(dataframe['stoch_rsi_k'], errors='coerce').fillna(50)
            exit_conditions.append(stoch_rsi_safe < 20)

        # 新增：Williams %R超卖出场
        if 'williams_r' in dataframe.columns:
            williams_safe = pd.to_numeric(dataframe['williams_r'], errors='coerce').fillna(-50)
            exit_conditions.append(williams_safe < -80)

        # 新增：CCI超卖出场
        if 'cci' in dataframe.columns:
            cci_safe = pd.to_numeric(dataframe['cci'], errors='coerce').fillna(0)
            exit_conditions.append(cci_safe < -100)

        # 组合出场条件 - 改为做多出场
        if exit_conditions:
            exit_short = pd.Series(False, index=dataframe.index)
            for condition in exit_conditions:
                exit_short |= condition

            dataframe.loc[exit_short, 'exit_short'] = 1
            dataframe.loc[exit_short, 'exit_tag'] = 'technical_exit'

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """动态止损系统 - 更紧的止损控制"""
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return 0.15  # 默认15%止损

        last_candle = dataframe.iloc[-1]

        # 基础ATR止损，更紧的控制
        base_stoploss_distance = last_candle['atr'] * self.base_stoploss_atr.value

        # 根据市场环境动态调整，更保守
        volatility_factor = min(1.5, max(0.7, last_candle['volatility'] / self.volatility_threshold.value))
        dynamic_stoploss_distance = base_stoploss_distance * volatility_factor * self.dynamic_stoploss_factor.value

        # 计算止损价格
        stoploss_price = current_rate + dynamic_stoploss_distance
        stoploss_pct = (stoploss_price / current_rate) - 1

        # 更紧的止损控制，最大15%
        return min(stoploss_pct, 0.15)

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        """自定义出场逻辑 - 更积极的止盈策略"""
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        last_candle = dataframe.iloc[-1]

        # 更积极的利润保护出场
        if current_profit > 0.05:  # 5%以上利润就开始保护
            if (last_candle['rsi'] < 45 or
                last_candle['macd'] > last_candle['macd_signal'] or
                last_candle['close'] < last_candle['bb_lower']):
                return "profit_protection"

        # 快速止盈
        if current_profit > 0.08:  # 8%利润快速止盈
            return "quick_profit"

        # 更短的时间止损
        if (current_time - trade.open_date_utc).total_seconds() > 3 * 24 * 3600:  # 3天
            if current_profit < 0.01:  # 利润小于1%
                return "time_exit"

        # 更敏感的趋势反转出场
        if (last_candle['trend_strength'] > 2 and
            last_candle['market_sentiment'] in ['bullish', 'strong_bullish']):
            return "trend_reversal"

        # 新增：多重技术指标反转出场
        oversold_count = 0
        if last_candle['rsi'] < 35:
            oversold_count += 1
        if last_candle.get('stoch_rsi_k', 50) < 20:
            oversold_count += 1
        if last_candle.get('williams_r', -50) < -80:
            oversold_count += 1
        if last_candle.get('cci', 0) < -100:
            oversold_count += 1

        # 如果有2个或以上指标显示超卖，出场
        if oversold_count >= 2:
            return "multi_indicator_reversal"

        return None

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:
        """动态杠杆设置"""
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return 1.0

        last_candle = dataframe.iloc[-1]

        # 基础杠杆
        base_leverage = 2.0

        # 根据信号质量调整杠杆
        signal_quality = last_candle.get('signal_quality_score', 0)
        if signal_quality >= 7:
            leverage_multiplier = 1.5
        elif signal_quality >= 5:
            leverage_multiplier = 1.2
        else:
            leverage_multiplier = 0.8

        # 根据波动率调整杠杆
        volatility = last_candle.get('volatility', 0.04)
        if volatility > self.volatility_threshold.value * 1.5:
            volatility_multiplier = 0.7  # 高波动率降低杠杆
        elif volatility < self.volatility_threshold.value * 0.5:
            volatility_multiplier = 1.3  # 低波动率提高杠杆
        else:
            volatility_multiplier = 1.0

        final_leverage = base_leverage * leverage_multiplier * volatility_multiplier
        return min(max(final_leverage, 1.0), max_leverage)

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                           proposed_stake: float, min_stake: float, max_stake: float,
                           entry_tag: str, side: str, **kwargs) -> float:
        """动态仓位管理"""
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return proposed_stake

        last_candle = dataframe.iloc[-1]

        # 基础仓位比例
        base_position_ratio = 0.1  # 10%的资金

        # 根据信号质量调整仓位
        signal_quality = last_candle.get('signal_quality_score', 0)
        if signal_quality >= 8:
            quality_multiplier = 2.0  # 最高质量信号，加大仓位
        elif signal_quality >= 6:
            quality_multiplier = 1.5
        elif signal_quality >= 4:
            quality_multiplier = 1.0
        else:
            quality_multiplier = 0.5  # 低质量信号，减小仓位

        # 根据市场环境调整仓位
        market_sentiment = last_candle.get('market_sentiment', 'neutral')
        if market_sentiment == 'bearish':
            sentiment_multiplier = 1.3  # 熊市环境有利于做空
        elif market_sentiment == 'bullish':
            sentiment_multiplier = 0.7  # 牛市环境不利于做空
        else:
            sentiment_multiplier = 1.0

        # 计算最终仓位
        final_position_ratio = base_position_ratio * quality_multiplier * sentiment_multiplier
        final_stake = max_stake * min(final_position_ratio, 0.3)  # 最大不超过30%

        return max(min_stake, min(final_stake, max_stake))

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: str,
                           side: str, **kwargs) -> bool:
        """简化的交易确认机制 - 反向做多版"""
        # 基础检查 - 改为只允许做多交易
        if side not in ['buy', 'short']:  # 允许做多交易
            log.info(f"拒绝交易 {pair}: 不是做多交易 (side={side})")
            return False

        # 简化检查，主要依赖populate_entry_trend的逻辑
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            log.info(f"拒绝交易 {pair}: 数据为空")
            return False

        last_candle = dataframe.iloc[-1]

        # 基本数据有效性检查
        if pd.isna(last_candle.get('close', 0)) or last_candle.get('close', 0) <= 0:
            log.info(f"拒绝交易 {pair}: 价格数据无效")
            return False

        # 极端波动率保护
        volatility = last_candle.get('volatility', 0)
        if volatility > self.volatility_threshold.value * 5:  # 放宽到5倍
            log.info(f"拒绝交易 {pair}: 极端波动率 ({volatility:.4f})")
            return False

        signal_quality = last_candle.get('signal_quality_score', 0)
        rsi = last_candle.get('rsi', 50)

        log.info(f"✅ 确认做多交易 {pair}: 信号质量={signal_quality:.2f}, RSI={rsi:.2f}, 波动率={volatility:.4f}")
        return True