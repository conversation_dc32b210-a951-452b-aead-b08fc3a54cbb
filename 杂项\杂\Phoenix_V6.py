import logging
import numpy as np
import talib.abstract as ta
from freqtrade.strategy import IntParameter, IStrategy, DecimalParameter, CategoricalParameter
from pandas import DataFrame
import freqtrade.vendor.qtpylib.indicators as qtpylib
import pandas as pd
from datetime import datetime
from freqtrade.persistence import Trade

class Phoenix_V6(IStrategy):
    """
    Phoenix V6: 波动突破策略（高胜率优化版）
    作者: Gemini Pro & USER
    
    基于Phoenix V5的改进版本，重点优化止损机制和入场条件:
    1. 降低止损频率，提高止损触发的精确性
    2. 增强入场信号质量过滤
    3. 优化追踪止盈机制，更好保护利润
    4. 引入价格行为确认，降低假突破风险
    5. 增加交易频率过滤，避免过度交易
    
    核心逻辑:
    1. 低波动期后的高波动突破入场
    2. 多重趋势确认过滤
    3. 分级动态止损和追踪止盈
    4. 基于价格行为的智能退出机制
    """
    INTERFACE_VERSION: int = 3

    # --- 策略配置 ---
    can_short: bool = True
    stoploss = -0.025  # 优化默认止损至2.5%
    
    # --- 追踪止损配置（优化） ---
    trailing_stop = True
    trailing_stop_positive = 0.006  # 提高至0.6%利润时激活追踪止损
    trailing_stop_positive_offset = 0.012  # 降低至1.2%利润时开始追踪
    trailing_only_offset_is_reached = True  # 仅在达到offset后激活
    
    # --- 退出信号配置 ---
    use_exit_signal = True  # 启用基于趋势的退出

    # --- 时间框架 ---
    timeframe = "15m"
    informative_timeframe = "1h"  # 1小时时间框架用于趋势确认
    startup_candle_count = 200  # 增加以计算更多指标

    # --- 布林带参数 ---
    bb_period = IntParameter(15, 30, default=20, load=True, space='buy', optimize=True)
    bb_stddev = DecimalParameter(1.5, 3.0, default=2.0, decimals=1, load=True, space='buy', optimize=True)
    
    # --- 挤压检测参数 ---
    bbw_squeeze_factor = DecimalParameter(0.1, 1.0, default=0.6, decimals=2, load=True, space='buy', optimize=True)

    # --- 趋势过滤参数 ---
    ema_short_period = IntParameter(5, 50, default=20, load=True, space='buy', optimize=True)
    ema_long_period = IntParameter(50, 200, default=100, load=True, space='buy', optimize=True)
    
    # --- ATR参数 ---
    atr_period = IntParameter(7, 30, default=14, load=True, space='buy', optimize=True)
    atr_multiplier = DecimalParameter(1.0, 4.0, default=1.5, decimals=1, load=True, space='buy', optimize=True)
    
    # --- 交易量过滤参数 ---
    volume_threshold = DecimalParameter(1.0, 5.0, default=1.5, decimals=1, load=True, space='buy', optimize=True)
    
    # --- RSI参数 ---
    rsi_period = IntParameter(7, 30, default=14, load=True, space='buy', optimize=True)
    rsi_overbought = IntParameter(70, 90, default=75, load=True, space='sell', optimize=True)
    rsi_oversold = IntParameter(10, 30, default=25, load=True, space='buy', optimize=True)
    
    # --- ADX参数 ---
    adx_period = IntParameter(7, 30, default=14, load=True, space='buy', optimize=True)
    adx_threshold = IntParameter(15, 40, default=25, load=True, space='buy', optimize=True)
    
    # --- 突破确认参数 ---
    breakout_guard_candles = IntParameter(1, 3, default=2, load=True, space='buy', optimize=True)
    profit_threshold = DecimalParameter(0.005, 0.03, default=0.012, decimals=3, load=True, space='sell', optimize=True)
    
    # --- 高级趋势过滤参数 ---
    exit_trend_timeframe = "1h"  # 1小时作为大趋势判断框架
    trend_ema_period = IntParameter(20, 100, default=50, load=True, space='buy', optimize=True)

    # --- 新增: 价格行为确认参数 ---
    candle_body_percentage = DecimalParameter(0.3, 0.8, default=0.6, decimals=1, load=True, space='buy', optimize=True)
    min_breakout_percentage = DecimalParameter(0.1, 1.0, default=0.3, decimals=1, load=True, space='buy', optimize=True)
    
    # --- 新增: 交易频率控制 ---
    cooldown_lookback_periods = IntParameter(2, 10, default=5, load=True, space='buy', optimize=True)

    @property
    def plot_config(self):
        return {
            "main_plot": {
                "bb_upperband": {"color": "blue"},
                "bb_middleband": {"color": "orange", "linestyle": "dashed"},
                "bb_lowerband": {"color": "blue"},
                "ema_short": {"color": "green"},
                "ema_long": {"color": "red"},
                "atr_upper": {"color": "purple"},
                "atr_lower": {"color": "purple"},
                "daily_ema": {"color": "darkred"},
            },
            "subplots": {
                "BollingerBandwidth": {
                    "bbw_squeeze_threshold": {"color": "red", "linestyle": "dashed"},
                    "bb_bandwidth": {"color": "green"},
                },
                "RSI": {
                    "rsi": {"color": "blue"},
                },
                "ADX": {
                    "adx": {"color": "orange"},
                },
                "Volume": {
                    "volume": {"color": "blue"},
                    "volume_ma": {"color": "orange"},
                }
            },
        }

    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, self.exit_trend_timeframe) for pair in pairs]  # 添加大趋势时间框架
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- 布林带指标 ---
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=self.bb_period.value, stds=self.bb_stddev.value)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']

        # --- 布林带宽度 ---
        dataframe['bb_bandwidth'] = (
            (dataframe['bb_upperband'] - dataframe['bb_lowerband']) / dataframe['bb_middleband']
        )
        
        # --- 挤压检测 ---
        dataframe['bbw_rolling_avg'] = dataframe['bb_bandwidth'].rolling(self.bb_period.value).mean()
        dataframe['bbw_squeeze_threshold'] = dataframe['bbw_rolling_avg'] * self.bbw_squeeze_factor.value
        dataframe['in_squeeze'] = dataframe['bb_bandwidth'] < dataframe['bbw_squeeze_threshold']
        
        # --- 连续挤压检测 ---
        dataframe['squeeze_count'] = dataframe['in_squeeze'].rolling(window=3).sum()

        # --- 趋势过滤: EMA交叉系统 ---
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=self.ema_short_period.value)
        dataframe['ema_long'] = ta.EMA(dataframe, timeperiod=self.ema_long_period.value)
        
        # 确定趋势方向
        dataframe['uptrend'] = dataframe['ema_short'] > dataframe['ema_long']
        dataframe['downtrend'] = dataframe['ema_short'] < dataframe['ema_long']
        
        # --- EMA趋势强度 ---
        dataframe['trend_strength'] = (dataframe['ema_short'] - dataframe['ema_long']).abs() / dataframe['ema_long'] * 100
        
        # --- ATR动态止损和止盈 ---
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['atr_upper'] = dataframe['close'] + dataframe['atr'] * self.atr_multiplier.value
        dataframe['atr_lower'] = dataframe['close'] - dataframe['atr'] * self.atr_multiplier.value
        
        # --- 交易量分析 ---
        dataframe['volume_ma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_increased'] = dataframe['volume'] > dataframe['volume_ma'] * self.volume_threshold.value
        
        # --- 异常交易量检测 ---
        dataframe['volume_std'] = dataframe['volume'].rolling(window=50).std()
        dataframe['volume_zscore'] = (dataframe['volume'] - dataframe['volume_ma']) / dataframe['volume_std']
        dataframe['volume_anomaly'] = dataframe['volume_zscore'] > 3.0  # Z分数大于3视为异常
        
        # --- 突破强度 ---
        dataframe['breakout_strength'] = (dataframe['close'] - dataframe['bb_upperband']) / dataframe['bb_upperband'] * 100
        dataframe['breakdown_strength'] = (dataframe['bb_lowerband'] - dataframe['close']) / dataframe['bb_lowerband'] * 100
        
        # --- RSI超买超卖 ---
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        
        # --- RSI趋势方向 ---
        dataframe['rsi_uptrend'] = dataframe['rsi'] > dataframe['rsi'].shift(1)
        dataframe['rsi_downtrend'] = dataframe['rsi'] < dataframe['rsi'].shift(1)
        
        # --- ADX趋势强度 ---
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_period.value)
        dataframe['strong_trend'] = dataframe['adx'] > self.adx_threshold.value
        
        # --- 价格动量 ---
        dataframe['momentum'] = dataframe['close'].pct_change(3) * 100
        
        # --- 市场状态 ---
        dataframe['volatility'] = dataframe['close'].rolling(20).std() / dataframe['close'] * 100
        dataframe['is_volatile'] = dataframe['volatility'] > dataframe['volatility'].rolling(100).mean()
        
        # --- 布林带突破强度 ---
        dataframe['bb_width_pct'] = dataframe['bb_bandwidth'] * 100  # 布林带宽度百分比
        
        # --- 新增: 价格行为分析 ---
        dataframe['body_size'] = (dataframe['close'] - dataframe['open']).abs()
        dataframe['candle_range'] = dataframe['high'] - dataframe['low']
        dataframe['body_percentage'] = dataframe['body_size'] / dataframe['candle_range']
        dataframe['upper_wick'] = dataframe['high'] - dataframe[['open', 'close']].max(axis=1)
        dataframe['lower_wick'] = dataframe[['open', 'close']].min(axis=1) - dataframe['low']
        
        # 判断强势蜡烛
        dataframe['strong_bullish'] = (dataframe['close'] > dataframe['open']) & (dataframe['body_percentage'] > self.candle_body_percentage.value)
        dataframe['strong_bearish'] = (dataframe['close'] < dataframe['open']) & (dataframe['body_percentage'] > self.candle_body_percentage.value)
        
        # --- 新增: 突破确认 ---
        dataframe['upper_breakout_pct'] = (dataframe['close'] - dataframe['bb_upperband']) / dataframe['bb_upperband'] * 100
        dataframe['lower_breakout_pct'] = (dataframe['bb_lowerband'] - dataframe['close']) / dataframe['bb_lowerband'] * 100
        dataframe['confirmed_upper_breakout'] = dataframe['upper_breakout_pct'] > self.min_breakout_percentage.value
        dataframe['confirmed_lower_breakout'] = dataframe['lower_breakout_pct'] > self.min_breakout_percentage.value
        
        # 获取大趋势时间框架数据 (仅用于退出逻辑)
        exit_trend_df = self.dp.get_pair_dataframe(metadata['pair'], self.exit_trend_timeframe)
        
        # 计算大趋势EMA趋势
        exit_trend_df['ema'] = ta.EMA(exit_trend_df, timeperiod=self.trend_ema_period.value)
        
        # 计算大趋势方向
        exit_trend_df['uptrend'] = exit_trend_df['close'] > exit_trend_df['ema']
        exit_trend_df['downtrend'] = exit_trend_df['close'] < exit_trend_df['ema']
        
        # 计算大趋势强度
        exit_trend_df['trend_strength'] = (exit_trend_df['close'] - exit_trend_df['ema']).abs() / exit_trend_df['ema'] * 100
        
        # 重命名大趋势列
        exit_trend_df = exit_trend_df.rename(columns={
            'ema': f'ema_{self.exit_trend_timeframe}',
            'uptrend': f'uptrend_{self.exit_trend_timeframe}',
            'downtrend': f'downtrend_{self.exit_trend_timeframe}',
            'trend_strength': f'trend_strength_{self.exit_trend_timeframe}'
        })
        
        # 合并大趋势数据
        dataframe = pd.merge(dataframe, exit_trend_df, how='left', left_on='date', right_on='date', suffixes=('', f'_{self.exit_trend_timeframe}'))
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- 突破条件 ---
        
        # 检查前几根K线是否处于挤压状态
        was_in_squeeze = dataframe['squeeze_count'] >= self.breakout_guard_candles.value
        
        # --- 多头入场 ---
        # 1. 前几根K线处于挤压状态
        # 2. 当前K线收盘价突破上轨
        # 3. 日线和小时线趋势都向上
        # 4. 交易量增加但不异常
        # 5. RSI不处于超买区且方向向上
        # 6. 强势看涨蜡烛确认
        long_breakout = qtpylib.crossed_above(dataframe['close'], dataframe['bb_upperband'].shift(1))
        
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # 计算最近的突破信号数量，用于控制交易频率
        dataframe['recent_breakouts'] = 0
        for i in range(len(dataframe)):
            if i >= self.cooldown_lookback_periods.value:
                # 检查前几个周期的突破情况，而不是依赖enter_long/enter_short列
                long_breaks = sum(1 for j in range(i-self.cooldown_lookback_periods.value, i) 
                              if (dataframe.iloc[j]['close'] > dataframe.iloc[j]['bb_upperband']))
                short_breaks = sum(1 for j in range(i-self.cooldown_lookback_periods.value, i) 
                               if (dataframe.iloc[j]['close'] < dataframe.iloc[j]['bb_lowerband']))
                dataframe.loc[dataframe.index[i], 'recent_breakouts'] = long_breaks + short_breaks
        
        dataframe.loc[
            was_in_squeeze & 
            long_breakout & 
            dataframe['uptrend'] & # 使用当前时间框架的趋势
            dataframe['volume_increased'] &
            ~dataframe['volume_anomaly'] &  # 排除异常交易量
            dataframe['rsi_uptrend'] &  # RSI方向向上
            (dataframe['rsi'] < self.rsi_overbought.value) &
            (dataframe['adx'] > self.adx_threshold.value) &
            dataframe['strong_bullish'] &  # 强势看涨蜡烛确认
            (dataframe['recent_breakouts'] == 0) &  # 冷却期内无其他突破信号
            (dataframe['volume'] > 0),
            'enter_long'
        ] = 1

        # --- 空头入场 ---
        # 1. 前几根K线处于挤压状态
        # 2. 当前K线收盘价突破下轨
        # 3. 日线和小时线趋势都向下
        # 4. 交易量增加但不异常
        # 5. RSI不处于超卖区且方向向下
        # 6. 强势看跌蜡烛确认
        short_breakout = qtpylib.crossed_below(dataframe['close'], dataframe['bb_lowerband'].shift(1))

        dataframe.loc[
            was_in_squeeze & 
            short_breakout & 
            dataframe['downtrend'] & # 使用当前时间框架的趋势
            dataframe['volume_increased'] &
            ~dataframe['volume_anomaly'] &  # 排除异常交易量
            dataframe['rsi_downtrend'] &  # RSI方向向下
            (dataframe['rsi'] > self.rsi_oversold.value) &
            (dataframe['adx'] > self.adx_threshold.value) &
            dataframe['strong_bearish'] &  # 强势看跌蜡烛确认
            (dataframe['recent_breakouts'] == 0) &  # 冷却期内无其他突破信号
            (dataframe['volume'] > 0),
            'enter_short'
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- 多头退出 ---
        # 1. 价格突破ATR下轨
        # 2. 趋势反转(短期EMA < 长期EMA)且有足够强度
        # 3. RSI超买
        # 4. 小时线趋势转向下降
        # 5. 强势看跌蜡烛确认
        dataframe.loc[
            (qtpylib.crossed_below(dataframe['close'], dataframe['atr_lower'])) |
            (qtpylib.crossed_below(dataframe['ema_short'], dataframe['ema_long']) & (dataframe['trend_strength'] > 1.0)) |
            (dataframe['rsi'] > self.rsi_overbought.value) |
            qtpylib.crossed_below(dataframe[f'uptrend_{self.exit_trend_timeframe}'], 0.5) |  # 小时线趋势转向
            (dataframe['strong_bearish'] & dataframe['close'] < dataframe['ema_short']),  # 强势看跌蜡烛确认
            'exit_long'
        ] = 1
        
        # --- 空头退出 ---
        # 1. 价格突破ATR上轨
        # 2. 趋势反转(短期EMA > 长期EMA)且有足够强度
        # 3. RSI超卖
        # 4. 小时线趋势转向上升
        # 5. 强势看涨蜡烛确认
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['close'], dataframe['atr_upper'])) |
            (qtpylib.crossed_above(dataframe['ema_short'], dataframe['ema_long']) & (dataframe['trend_strength'] > 1.0)) |
            (dataframe['rsi'] < self.rsi_oversold.value) |
            qtpylib.crossed_above(dataframe[f'uptrend_{self.exit_trend_timeframe}'], 0.5) |  # 小时线趋势转向
            (dataframe['strong_bullish'] & dataframe['close'] > dataframe['ema_short']),  # 强势看涨蜡烛确认
            'exit_short'
        ] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        改进的动态止损系统
        1. 基于ATR的初始止损
        2. 根据利润动态调整止损级别
        3. 加入更多止损级别
        4. 优化止损比例
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if not len(dataframe) > 0:
            return self.stoploss
            
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 使用ATR值计算动态止损
        atr_stop = current_candle['atr'] * self.atr_multiplier.value
        
        # 将ATR值转换为百分比止损
        atr_stop_pct = atr_stop / current_rate
        
        # 根据当前利润调整止损（更多级别）
        if current_profit >= self.profit_threshold.value * 5:  # 利润超过阈值的5倍
            return max(-atr_stop_pct * 0.15, -0.002)  # 收紧止损至ATR的15%或0.2%，取较大值
        elif current_profit >= self.profit_threshold.value * 4:  # 利润超过阈值的4倍
            return max(-atr_stop_pct * 0.2, -0.003)  # 收紧止损至ATR的20%或0.3%，取较大值
        elif current_profit >= self.profit_threshold.value * 3:  # 利润超过阈值的3倍
            return max(-atr_stop_pct * 0.3, -0.005)  # 收紧止损至ATR的30%或0.5%，取较大值
        elif current_profit >= self.profit_threshold.value * 2:  # 利润超过阈值的2倍
            return max(-atr_stop_pct * 0.5, -0.008)  # 收紧止损至ATR的50%或0.8%，取较大值
        elif current_profit >= self.profit_threshold.value:  # 利润超过阈值
            return max(-atr_stop_pct * 0.7, -0.012)  # 收紧止损至ATR的70%或1.2%，取较大值
        else:
            return max(-atr_stop_pct, self.stoploss)  # 使用完整ATR止损或默认止损，取较大值
    
    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float, 
                           time_in_force: str, current_time: datetime, entry_tag: str, 
                           side: str, **kwargs) -> bool:
        """
        交易确认逻辑，不再检查大趋势方向，因为已在populate_entry_trend中处理
        """
        # dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # if not len(dataframe) > 0:
        #     return False
            
        # current_candle = dataframe.iloc[-1].squeeze()
        
        # # 移除大趋势检查，因为入场信号已包含所有必要条件
        # if side == 'long':
        #     if (current_candle['adx'] > self.adx_threshold.value and
        #         current_candle['strong_bullish']):
        #         return True
        # else:
        #     if (current_candle['adx'] > self.adx_threshold.value and
        #         current_candle['strong_bearish']):
        #         return True
        
        # return False
        
        # 由于所有检查已在 populate_entry_trend 中完成，此函数可以直接返回True
        return True
        
    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                   current_profit: float, **kwargs):
        """
        自定义退出逻辑，用于处理特殊情况
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if not len(dataframe) > 0:
            return None
            
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 如果利润已经很高，但市场开始出现反转迹象，提前退出
        if trade.is_short:
            if (current_profit > self.profit_threshold.value * 3 and 
                last_candle['rsi_uptrend'] and 
                last_candle['strong_bullish']):
                return "take_profit_reversal_short"
        else:  # 多头
            if (current_profit > self.profit_threshold.value * 3 and 
                last_candle['rsi_downtrend'] and 
                last_candle['strong_bearish']):
                return "take_profit_reversal_long"
                
        return None 