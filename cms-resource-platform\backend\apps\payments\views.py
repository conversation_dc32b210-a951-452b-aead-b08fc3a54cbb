"""
支付系统视图
"""
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from .models import PointsPackage, VIPPackage, PaymentOrder, CardCode, Withdrawal
from .serializers import (
    PointsPackageSerializer, VIPPackageSerializer, PaymentOrderSerializer,
    OrderCreateSerializer, CardRedeemSerializer, WithdrawalSerializer
)


class PointsPackageListView(generics.ListAPIView):
    """积分套餐列表"""
    queryset = PointsPackage.objects.filter(is_active=True).order_by('sort_order', 'price')
    serializer_class = PointsPackageSerializer
    permission_classes = [permissions.AllowAny]


class VIPPackageListView(generics.ListAPIView):
    """VIP套餐列表"""
    queryset = VIPPackage.objects.filter(is_active=True).order_by('sort_order', 'duration_days')
    serializer_class = VIPPackageSerializer
    permission_classes = [permissions.AllowAny]


class OrderCreateView(APIView):
    """创建订单"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = OrderCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        package_id = serializer.validated_data['package_id']
        package_type = serializer.validated_data['package_type']
        payment_method = serializer.validated_data['payment_method']
        
        with transaction.atomic():
            # 获取套餐信息
            if package_type == 'points':
                package = get_object_or_404(PointsPackage, id=package_id, is_active=True)
                product_name = f"积分充值 - {package.name}"
                product_description = f"{package.total_points}积分"
                amount = package.final_price
            else:  # vip
                package = get_object_or_404(VIPPackage, id=package_id, is_active=True)
                product_name = f"VIP会员 - {package.name}"
                product_description = f"{package.duration_days}天VIP会员"
                amount = package.price
            
            # 创建订单
            order = PaymentOrder.objects.create(
                user=request.user,
                order_type=package_type,
                product_id=str(package_id),
                product_name=product_name,
                product_description=product_description,
                amount=amount,
                final_amount=amount,
                payment_method=payment_method,
                expires_at=timezone.now() + timezone.timedelta(minutes=30)
            )
            
            # 这里应该调用支付接口
            # payment_url = create_payment(order)
            
            return Response({
                'order_no': order.order_no,
                'amount': order.final_amount,
                'payment_method': payment_method,
                # 'payment_url': payment_url,
                'expires_at': order.expires_at
            })


class OrderDetailView(generics.RetrieveAPIView):
    """订单详情"""
    serializer_class = PaymentOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'order_no'
    
    def get_queryset(self):
        return PaymentOrder.objects.filter(user=self.request.user)


class CardRedeemView(APIView):
    """卡密兑换"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = CardRedeemSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        code = serializer.validated_data['code']
        user = request.user
        
        try:
            with transaction.atomic():
                card = CardCode.objects.select_for_update().get(code=code)
                
                if not card.is_valid():
                    return Response(
                        {'error': '卡密无效或已过期'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # 使用卡密
                card.use(user)
                
                return Response({
                    'message': '兑换成功',
                    'card_type': card.get_card_type_display(),
                    'points_value': card.points_value,
                    'vip_days': card.vip_days
                })
                
        except CardCode.DoesNotExist:
            return Response(
                {'error': '卡密不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class MyOrdersView(generics.ListAPIView):
    """我的订单"""
    serializer_class = PaymentOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return PaymentOrder.objects.filter(
            user=self.request.user
        ).order_by('-created_at')


class WithdrawalCreateView(generics.CreateAPIView):
    """申请提现"""
    serializer_class = WithdrawalSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def perform_create(self, serializer):
        # 计算手续费（5%）
        amount = serializer.validated_data['amount']
        fee = amount * 0.05
        actual_amount = amount - fee
        
        serializer.save(
            user=self.request.user,
            fee=fee,
            actual_amount=actual_amount
        )


class WithdrawalListView(generics.ListAPIView):
    """提现记录"""
    serializer_class = WithdrawalSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Withdrawal.objects.filter(
            user=self.request.user
        ).order_by('-created_at')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def payment_callback(request):
    """支付回调"""
    # 这里处理支付平台的回调
    # 验证签名、更新订单状态、发放商品等
    
    order_no = request.data.get('order_no')
    if not order_no:
        return Response({'error': 'Missing order_no'}, status=400)
    
    try:
        with transaction.atomic():
            order = PaymentOrder.objects.select_for_update().get(order_no=order_no)
            
            if order.status != 'pending':
                return Response({'message': 'Order already processed'})
            
            # 更新订单状态
            order.status = 'paid'
            order.paid_at = timezone.now()
            order.save()
            
            # 发放商品
            if order.order_type == 'points':
                # 发放积分
                package = PointsPackage.objects.get(id=order.product_id)
                order.user.add_points(
                    package.total_points,
                    f'购买积分套餐: {package.name}'
                )
            elif order.order_type == 'vip':
                # 开通VIP
                package = VIPPackage.objects.get(id=order.product_id)
                user = order.user
                
                if user.vip_expire_date and user.vip_expire_date > timezone.now():
                    user.vip_expire_date += timezone.timedelta(days=package.duration_days)
                else:
                    user.vip_expire_date = timezone.now() + timezone.timedelta(days=package.duration_days)
                
                user.is_vip = True
                user.save()
                
                # 赠送积分
                if package.bonus_points > 0:
                    user.add_points(
                        package.bonus_points,
                        f'VIP套餐赠送: {package.name}'
                    )
            
            # 完成订单
            order.status = 'completed'
            order.completed_at = timezone.now()
            order.save()
            
            return Response({'message': 'Payment processed successfully'})
            
    except PaymentOrder.DoesNotExist:
        return Response({'error': 'Order not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)
