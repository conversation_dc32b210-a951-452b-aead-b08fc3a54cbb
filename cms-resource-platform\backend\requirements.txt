# Django核心
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3
django-extensions==3.2.3

# 数据库
psycopg2-binary==2.9.9
redis==5.0.1
django-redis==5.4.0

# 认证和安全
djangorestframework-simplejwt==5.3.0
django-allauth==0.57.0
cryptography==41.0.7
bcrypt==4.1.2

# 文件处理
Pillow==10.1.0
python-magic==0.4.27
django-storages==1.14.2
boto3==1.34.0  # AWS S3支持

# 异步任务
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1

# API文档
drf-spectacular==0.26.5
drf-spectacular-sidecar==2023.10.1

# 缓存和性能
django-cachalot==2.6.1
django-debug-toolbar==4.2.0
django-silk==5.0.4

# 工具库
python-decouple==3.8
python-dotenv==1.0.0
requests==2.31.0
httpx==0.25.2

# 数据验证
marshmallow==3.20.1
pydantic==2.5.0

# 支付集成
alipay-sdk-python==3.7.1
wechatpay-python==1.2.1

# 图像处理
opencv-python-headless==********
qrcode==7.4.2

# 监控和日志
sentry-sdk==1.38.0
django-prometheus==2.3.1

# 开发工具
ipython==8.17.2
pytest==7.4.3
pytest-django==4.7.0
factory-boy==3.3.0
coverage==7.3.2

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 生产环境
gunicorn==21.2.0
whitenoise==6.6.0
django-health-check==3.17.0

# 数据导入导出
django-import-export==3.3.1
openpyxl==3.1.2

# 全文搜索
django-haystack==3.2.1
whoosh==2.7.4

# 国际化
django-modeltranslation==0.18.11

# 限流
django-ratelimit==4.1.0

# 验证码
django-simple-captcha==0.5.20

# 富文本编辑
django-ckeditor==6.7.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3
