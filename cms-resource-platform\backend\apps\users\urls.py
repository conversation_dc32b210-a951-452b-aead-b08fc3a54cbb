"""
用户系统URL配置
"""
from django.urls import path, include
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)
from . import views

app_name = 'users'

urlpatterns = [
    # 认证相关
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    
    # JWT令牌
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    
    # 密码管理
    path('password/change/', views.PasswordChangeView.as_view(), name='password_change'),
    path('password/reset/', views.PasswordResetView.as_view(), name='password_reset'),
    
    # 用户资料
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('stats/', views.UserStatsView.as_view(), name='stats'),
    
    # 积分系统
    path('points/history/', views.PointsHistoryView.as_view(), name='points_history'),
    path('signin/', views.SignInView.as_view(), name='signin'),
    path('signin/records/', views.SignInRecordView.as_view(), name='signin_records'),
]
