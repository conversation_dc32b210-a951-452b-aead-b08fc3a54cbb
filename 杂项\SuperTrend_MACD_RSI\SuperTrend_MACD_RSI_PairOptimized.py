# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these imports ---
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Dict, Optional, Union, Tuple, List
from freqtrade.optimize.space import Categorical, Dimension, Integer, SKDecimal
import logging
from functools import reduce
import json
from pathlib import Path
import os

logger = logging.getLogger(__name__)

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)


# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


# ==========================================
# Supertrend + MACD + RSI Strategy | Backtest 299% Profit Using Python & Freqtrade
# https://youtu.be/71EA1u4K_Zk
# ==========================================


# ================================
# Download Historical Data
# ================================

"""
freqtrade download-data \
    -c user_data/binance_futures_SuperTrend_MACD_RSI_PairOptimized.json \
    --timerange 20230101- \
    -t 1m 5m 15m 30m 1h 2h 4h 1d
"""

# ================================
# Backtesting
# ================================

"""
freqtrade backtesting \
    --strategy SuperTrend_MACD_RSI_PairOptimized \
    --timeframe 1h \
    --timerange 20240401-20250401 \
    --breakdown month \
    -c user_data/binance_futures_SuperTrend_MACD_RSI_PairOptimized.json \
    --max-open-trades 3 \
    --cache none \
    --timeframe-detail 5m
"""

# ================================
# Start FreqUI Web Interface
# ================================

"""
freqtrade webserver \
    --config user_data/binance_futures_SuperTrend_MACD_RSI_PairOptimized.json
"""


class SuperTrend_MACD_RSI_PairOptimized(IStrategy):
    
    # --- HYPEROPT SETTINGS ---
    # (This section is intentionally left blank as settings are loaded from JSON)

    # --- STRATEGY INITIALIZATION ---
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        
        # Determine the settings file path
        settings_file_path_env = os.getenv('STRATEGY_SETTINGS_FILE')
        
        if settings_file_path_env:
            # Use the absolute path from environment variable if available
            settings_path = Path(settings_file_path_env)
        else:
            # Fallback to the original relative path logic
            strategy_name = self.__class__.__name__
            settings_filename = f"{strategy_name}_Settings.json"
            settings_path = Path(self.config['user_data_dir']) / "strategies" / "SuperTrend" / settings_filename

        try:
            with open(settings_path) as f:
                self.custom_info = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            # Provide a more descriptive error message
            print(f"CRITICAL: Could not load strategy settings file from '{settings_path}'. "
                  f"Please ensure the file exists and is a valid JSON. Error: {e}")
            # Exit if the settings file is essential for the strategy to run
            exit()

    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = "30m"

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi".
    minimal_roi = {}

    # Dictionary defining the exit points for take profit and stop loss levels.
    exit_loss_profit = {}
    
    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.99

    # Trailing stoploss
    trailing_stop = True
    trailing_only_offset_is_reached = True
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.03
    
    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    @property
    def plot_config(self):

        plot_config = {
            "main_plot": {
                "supertrend": {
                        "color": "#4caf50",
                        "type": "line",
                        "fill_to": "close"
                },
            },
            "subplots": {
                "RSI": {
                    "rsi": {"color": "#9e57c2", "type": "line"}
                },
                "MACD": {
                    'macd': {'color': '#2962ff', 'fill_to': 'macdhist'},
                    'macdsignal': {'color': '#ff6d00'},
                    'macdhist': {'type': 'bar', 'plotly': {'opacity': 0.9}}
                }
            }
        }
        
        return plot_config
    
    
    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pair/interval combinations are non-tradeable, unless they are part
        of the whitelist as well.
        For more information, please consult the documentation
        :return: List of tuples in the format (pair, interval)
            Sample: return [("ETH/USDT", "5m"),
                            ("BTC/USDT", "15m"),
                            ]
        """
        
        # get access to all pairs available in whitelist.
        # pairs = self.dp.current_whitelist()

        # # Assign tf to each pair so they can be downloaded and cached for strategy.
        # informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        
        return []
    
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        pair = metadata["pair"]
        
        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
            
            macd = ta.MACD(dataframe)
            dataframe["macd"] = macd["macd"]
            dataframe["macdsignal"] = macd["macdsignal"]
            dataframe["macdhist"] = macd["macdhist"]
            
            dataframe["rsi"] = ta.RSI(dataframe)

            dataframe["atr"] = ta.ATR(dataframe)
            
            superTrend = pta.supertrend(dataframe['high'], dataframe['low'], dataframe['close'], length=pair_settings["supertrend_length"], multiplier= pair_settings["supertrend_multiplier"])
            dataframe['supertrend'] = superTrend[f'SUPERT_{pair_settings["supertrend_length"]}_{pair_settings["supertrend_multiplier"]}.0']
            dataframe['supertrend_direction'] = superTrend[f'SUPERTd_{pair_settings["supertrend_length"]}_{pair_settings["supertrend_multiplier"]}.0']
        
            return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:        
        
        pair = metadata["pair"]
        
        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
                        
            dataframe.loc[
                (   
                    (dataframe['supertrend_direction'] == 1) &
                    (dataframe['supertrend_direction'].shift(1) == -1) &
                    (dataframe['rsi'] > pair_settings["rsi_threshold"]) &
                    (dataframe['macd'] > dataframe['macdsignal']) &
                    (abs(dataframe['close'] - dataframe['open']) / dataframe['open'] < pair_settings["max_candle_size"]) &
                    (dataframe["volume"] > 0) 
                ),
                'enter_long'] = 1

            dataframe.loc[
                (
                    (dataframe['supertrend_direction'] == -1) &
                    (dataframe['supertrend_direction'].shift(1) == 1) &
                    (dataframe['rsi'] < (100 - pair_settings["rsi_threshold"])) &
                    (dataframe['macd'] < dataframe['macdsignal']) &
                    (abs(dataframe['close'] - dataframe['open']) / dataframe['open'] < pair_settings["max_candle_size"]) &
                    (dataframe["volume"] > 0) 
                ),
                'enter_short'] = 1

            return dataframe
    

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        
        pair = metadata["pair"]
        
        if pair in self.custom_info:
            pair_settings = self.custom_info[pair]
            
            # Exit long when supertrend flips to sell
            dataframe.loc[
                (
                    (dataframe['supertrend_direction'] == -1) &
                    (dataframe['supertrend_direction'].shift(1) == 1)
                ),
                'exit_long'] = 1

            # Exit short when supertrend flips to buy
            dataframe.loc[
                (
                    (dataframe['supertrend_direction'] == 1) &
                    (dataframe['supertrend_direction'].shift(1) == -1)
                ),
                'exit_short'] = 1
        else:
            dataframe.loc[:, 'exit_long'] = 0
            dataframe.loc[:, 'exit_short'] = 0

        return dataframe
    
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        
        if pair in self.custom_info:
            pair_settings = self.custom_info[pair]
            lookback_period = pair_settings.get("stoploss_lookback", pair_settings.get("swing_point_lookback", 6)) # Fallback for older settings
            
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            trade_date = timeframe_to_prev_date(self.timeframe, trade.open_date_utc)
            trade_candle = dataframe.loc[dataframe['date'] < trade_date]
            
            if not trade_candle.empty:
                if trade.is_short:
                    swing_high = trade_candle['high'].rolling(lookback_period).max().iloc[-1]
                    stop_loss_price = swing_high * (1 + 0.003)
                else:
                    swing_low = trade_candle['low'].rolling(lookback_period).min().iloc[-1]
                    stop_loss_price = swing_low * (1 - 0.003)
                    
                return stop_loss_price

        # Fallback if pair not in settings or no candle data
        return current_rate * (1.25 if trade.is_short else 0.75)

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:

        if pair in self.custom_info:
            
            pair_settings = self.custom_info[pair]
            
            return pair_settings["leverage_level"]