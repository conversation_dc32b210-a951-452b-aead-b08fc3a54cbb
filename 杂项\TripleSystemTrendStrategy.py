import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from pandas import DataFrame
from datetime import datetime, timedelta
from freqtrade.persistence import Trade
from typing import Dict, List, Optional
from functools import reduce
import logging
import math

logger = logging.getLogger(__name__)

class TripleSystemTrendStrategy(IStrategy):
    """
    三系统趋势策略 (Triple System Trend Strategy)
    
    该策略综合了三个独立策略的优势:
    1. 高斯趋势滤波器 - 用于主要趋势识别，平滑价格噪音
    2. VIDYA指标 - 用于趋势确认和动态支撑阻力区域
    3. DSL振荡器 - 用于精确入场信号识别
    
    核心特点:
    - 多层趋势过滤确保信号质量
    - 多时间周期趋势确认提高胜率
    - 成交量分析确认趋势强度
    - ATR动态止盈止损和仓位管理
    - 支持多空双向交易
    - 自适应杠杆优化
    
    作者: Claude 3.7 Sonnet
    """

    # 策略参数
    # 高斯滤波器参数
    gaussian_length = IntParameter(10, 30, default=15, space="buy", optimize=True)
    gaussian_poles = IntParameter(1, 4, default=3, space="buy", optimize=True)
    linreg_length = IntParameter(15, 30, default=22, space="buy", optimize=True)
    
    # VIDYA参数
    vidya_length = IntParameter(5, 30, default=10, space="buy", optimize=True)
    vidya_momentum = IntParameter(10, 40, default=20, space="buy", optimize=True)
    band_distance = DecimalParameter(1.0, 3.0, default=2.0, decimals=1, space="buy", optimize=True)
    
    # DSL振荡器参数
    dsl_length = IntParameter(8, 14, default=10, space="buy", optimize=True)
    dsl_mode = CategoricalParameter(["Fast", "Slow"], default="Fast", space="buy", optimize=True)
    zlema_length = IntParameter(8, 14, default=10, space="buy", optimize=True)
    
    # 成交量过滤参数
    volume_filter_length = IntParameter(20, 50, default=33, space="buy", optimize=True)
    volume_threshold = DecimalParameter(1.0, 3.0, default=1.5, decimals=1, space="buy", optimize=True)
    
    # 多时间周期确认参数
    use_multi_timeframe = CategoricalParameter([True, False], default=True, space="buy", optimize=True)
    min_timeframes_aligned = IntParameter(1, 2, default=2, space="buy", optimize=True)
    
    # ATR参数
    atr_period = IntParameter(10, 30, default=14, space="protection", optimize=True)
    atr_stop_loss = DecimalParameter(1.0, 3.0, default=1.5, decimals=1, space="protection", optimize=True)
    atr_trailing_stop = DecimalParameter(1.0, 3.0, default=1.5, decimals=1, space="protection", optimize=True)
    atr_take_profit = DecimalParameter(2.0, 5.0, default=2.5, decimals=1, space="protection", optimize=True)
    
    # 仓位管理参数
    max_leverage = DecimalParameter(1.0, 10.0, default=3.0, decimals=1, space="protection", optimize=True)
    risk_per_trade = DecimalParameter(0.01, 0.05, default=0.02, decimals=2, space="protection", optimize=True)
    
    # 时间框架设置
    timeframe = '5m'
    informative_timeframes = ['15m', '1h']
    
    # 基本设置
    minimal_roi = {
        "0": 0.05,  # 0分钟后，如果利润达到5%，则卖出
        "20": 0.03,  # 20分钟后，如果利润达到3%，则卖出
        "40": 0.02,  # 40分钟后，如果利润达到2%，则卖出
        "60": 0.01  # 60分钟后，如果利润达到1%，则卖出
    }
    
    # 止损设置 - 将在代码中动态设置
    stoploss = -0.99  # 初始止损设置为较大值，实际会通过custom_stoploss动态调整
    
    # 其他设置
    startup_candle_count: int = 200  # 需要的历史K线数量
    process_only_new_candles = True  # 只处理新的K线
    use_exit_signal = True  # 使用卖出信号
    can_short = True  # 允许做空
    
    # 订单类型和超时设置
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }
    
    @property
    def plot_config(self):
        """
        为策略设置图表配置
        """
        return {
            'main_plot': {
                'gauss_trend': {'color': 'green'},
                'vidya': {'color': 'blue'},
                'upper_band': {'color': 'red'},
                'lower_band': {'color': 'purple'},
            },
            'subplots': {
                "DSL": {
                    'dsl_osc': {'color': 'orange'},
                },
                "Volume": {
                    'volume_ratio': {'color': 'yellow'},
                },
                "ATR": {
                    'atr': {'color': 'pink'},
                }
            }
        }
    
    def vidya_calc(self, dataframe: DataFrame, src_column: str, length: int, momentum_length: int) -> np.array:
        """
        计算VIDYA (Variable Index Dynamic Average)
        
        参数:
            dataframe: 包含价格数据的DataFrame
            src_column: 源数据列名
            length: VIDYA长度
            momentum_length: 动量长度
        
        返回:
            np.array: VIDYA值的数组
        """
        src = dataframe[src_column].values
        momentum = np.diff(src, prepend=src[0])
        
        # 计算正向和负向动量
        pos_momentum = np.where(momentum >= 0, momentum, 0)
        neg_momentum = np.where(momentum < 0, -momentum, 0)
        
        # 计算动量的滚动和
        sum_pos_momentum = pd.Series(pos_momentum).rolling(momentum_length).sum().values
        sum_neg_momentum = pd.Series(neg_momentum).rolling(momentum_length).sum().values
        
        # 计算CMO (Chande Momentum Oscillator)
        cmo = np.zeros_like(src)
        divisor = sum_pos_momentum + sum_neg_momentum
        valid_idx = divisor != 0
        cmo[valid_idx] = 100 * (sum_pos_momentum[valid_idx] - sum_neg_momentum[valid_idx]) / divisor[valid_idx]
        abs_cmo = np.abs(cmo)
        
        # 计算VIDYA
        alpha = 2 / (length + 1)
        vidya = np.zeros_like(src)
        vidya[0] = src[0]
        
        for i in range(1, len(src)):
            k = alpha * abs_cmo[i] / 100
            vidya[i] = k * src[i] + (1 - k) * vidya[i-1]
        
        # 应用简单移动平均进行平滑
        vidya_smooth = pd.Series(vidya).rolling(15).mean().values
        
        return vidya_smooth
    
    def calc_gaussian_alpha(self, length, order):
        """
        计算高斯滤波器的alpha参数
        """
        freq = (2.0 * np.pi) / length
        factor_b = (1.0 - np.cos(freq)) / (np.power(1.414, (2.0 / order)) - 1.0)
        alpha_val = -factor_b + np.sqrt(factor_b * factor_b + 2.0 * factor_b)
        return alpha_val
    
    def gaussian_smooth(self, data, filter_level, alpha_coeff):
        """
        应用高斯平滑滤波器
        """
        result = np.zeros_like(data)
        one_minus_alpha = 1.0 - alpha_coeff
        alpha_squared = alpha_coeff * alpha_coeff
        alpha_cubed = alpha_coeff * alpha_coeff * alpha_coeff
        alpha4 = alpha_coeff * alpha_coeff * alpha_coeff * alpha_coeff
        oma_squared = one_minus_alpha * one_minus_alpha
        oma_cubed = oma_squared * one_minus_alpha
        oma4 = oma_cubed * one_minus_alpha
        
        # 初始化第一个值
        result[0] = data[0]
        
        # 根据滤波器级别应用不同的平滑算法
        if filter_level == 1:
            for i in range(1, len(data)):
                result[i] = alpha_coeff * data[i] + one_minus_alpha * result[i-1]
        elif filter_level == 2:
            result[1] = data[1]  # 初始化第二个值
            for i in range(2, len(data)):
                result[i] = alpha_squared * data[i] + 2.0 * one_minus_alpha * result[i-1] - oma_squared * result[i-2]
        elif filter_level == 3:
            result[1] = data[1]  # 初始化第二个值
            result[2] = data[2]  # 初始化第三个值
            for i in range(3, len(data)):
                result[i] = alpha_cubed * data[i] + 3.0 * one_minus_alpha * result[i-1] - 3.0 * oma_squared * result[i-2] + oma_cubed * result[i-3]
        elif filter_level == 4:
            result[1] = data[1]  # 初始化第二个值
            result[2] = data[2]  # 初始化第三个值
            result[3] = data[3]  # 初始化第四个值
            for i in range(4, len(data)):
                result[i] = alpha4 * data[i] + 4.0 * one_minus_alpha * result[i-1] - 6.0 * oma_squared * result[i-2] + 4.0 * oma_cubed * result[i-3] - oma4 * result[i-4]
        
        return result
    
    def zlema(self, src: pd.Series, length: int) -> pd.Series:
        """
        计算零延迟指数移动平均线 (Zero-Lag Exponential Moving Average)
        """
        lag = math.floor((length - 1) / 2)
        ema_data = 2 * src - src.shift(lag)
        ema2 = ta.EMA(ema_data, length)
        return ema2
    
    def dsl_lines(self, src: pd.Series, length: int, dsl_mode: int = 2) -> tuple:
        """
        计算非连续信号线 (Discontinued Signal Lines)
        """
        # 将SMA结果转换为pandas Series
        sma_values = ta.SMA(src, length)
        sma = pd.Series(sma_values, index=src.index)
        
        up = pd.Series(index=src.index, dtype='float64')
        dn = pd.Series(index=src.index, dtype='float64')
        
        # 初始化第一个值
        up.iloc[0] = src.iloc[0]
        dn.iloc[0] = src.iloc[0]
        
        for i in range(1, len(src)):
            if src.iloc[i] > sma.iloc[i]:
                up.iloc[i] = up.iloc[i-1] + dsl_mode / length * (src.iloc[i] - up.iloc[i-1])
                dn.iloc[i] = dn.iloc[i-1]
            else:
                up.iloc[i] = up.iloc[i-1]
                dn.iloc[i] = dn.iloc[i-1] + dsl_mode / length * (src.iloc[i] - dn.iloc[i-1])
        
        return up, dn
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        为策略计算技术指标
        """
        # 1. 高斯滤波器指标
        # 计算高斯滤波器的alpha参数
        alpha_value = self.calc_gaussian_alpha(self.gaussian_length.value, self.gaussian_poles.value)
        
        # 应用高斯平滑滤波器
        close_array = dataframe['close'].values
        gma_output = self.gaussian_smooth(close_array, self.gaussian_poles.value, alpha_value)
        dataframe['gma_output'] = gma_output
        
        # 应用线性回归平滑
        dataframe['gauss_trend'] = ta.LINEARREG(dataframe['gma_output'], timeperiod=self.linreg_length.value)
        
        # 趋势方向
        dataframe['gauss_trend_direction'] = np.where(dataframe['gauss_trend'] > dataframe['gauss_trend'].shift(1), 1, -1)
        
        # 计算趋势强度指标
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['trend_strength'] = abs(dataframe['gauss_trend'] - dataframe['gauss_trend'].shift(10)) / (dataframe['atr'] * 10) * 100
        dataframe['trend_strength'] = dataframe['trend_strength'].clip(0, 100)
        
        # 2. VIDYA指标
        dataframe['vidya'] = self.vidya_calc(
            dataframe, 'close', self.vidya_length.value, self.vidya_momentum.value
        )
        
        # 计算上下通道
        dataframe['upper_band'] = dataframe['vidya'] + dataframe['atr'] * self.band_distance.value
        dataframe['lower_band'] = dataframe['vidya'] - dataframe['atr'] * self.band_distance.value
        
        # 计算趋势状态
        dataframe['vidya_trend_up'] = False
        dataframe['vidya_trend_down'] = False
        
        # 判断趋势
        for i in range(1, len(dataframe)):
            if dataframe['close'].iloc[i] > dataframe['upper_band'].iloc[i-1]:
                dataframe.loc[i, 'vidya_trend_up'] = True
            elif dataframe['close'].iloc[i] < dataframe['lower_band'].iloc[i-1]:
                dataframe.loc[i, 'vidya_trend_down'] = True
            else:
                dataframe.loc[i, 'vidya_trend_up'] = dataframe['vidya_trend_up'].iloc[i-1]
                dataframe.loc[i, 'vidya_trend_down'] = dataframe['vidya_trend_down'].iloc[i-1]
        
        # 计算趋势变化点
        dataframe['vidya_trend_change_up'] = False
        dataframe['vidya_trend_change_down'] = False
        
        for i in range(1, len(dataframe)):
            dataframe.loc[i, 'vidya_trend_change_up'] = not dataframe['vidya_trend_up'].iloc[i-1] and dataframe['vidya_trend_up'].iloc[i]
            dataframe.loc[i, 'vidya_trend_change_down'] = not dataframe['vidya_trend_down'].iloc[i-1] and dataframe['vidya_trend_down'].iloc[i]
        
        # 3. DSL振荡器指标
        # 计算RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # 计算DSL模式值
        dsl_mode_value = 2 if self.dsl_mode.value == "Fast" else 1
        
        # 计算DSL线
        lvlu, lvld = self.dsl_lines(dataframe['rsi'], self.dsl_length.value, dsl_mode_value)
        dataframe['lvlu'] = lvlu
        dataframe['lvld'] = lvld
        
        # 计算DSL振荡器
        dataframe['dsl_avg'] = (dataframe['lvlu'] + dataframe['lvld']) / 2
        dataframe['dsl_osc'] = self.zlema(dataframe['dsl_avg'], self.zlema_length.value)
        
        # 计算DSL振荡器的DSL线
        level_up, level_dn = self.dsl_lines(dataframe['dsl_osc'], self.dsl_length.value, dsl_mode_value)
        dataframe['level_up'] = level_up
        dataframe['level_dn'] = level_dn
        
        # 计算交叉信号
        dataframe['dsl_cross_up'] = qtpylib.crossed_above(dataframe['dsl_osc'], dataframe['level_dn'])
        dataframe['dsl_cross_dn'] = qtpylib.crossed_below(dataframe['dsl_osc'], dataframe['level_up'])
        
        # 4. 成交量分析
        dataframe['volume_ma'] = ta.SMA(dataframe['volume'], timeperiod=self.volume_filter_length.value)
        dataframe['volume_min'] = dataframe['volume'].rolling(window=self.volume_filter_length.value).min()
        dataframe['volume_max'] = dataframe['volume'].rolling(window=self.volume_filter_length.value).max()
        dataframe['volume_ratio'] = (dataframe['volume'] - dataframe['volume_min']) / (dataframe['volume_max'] - dataframe['volume_min'])
        
        # 计算成交量差值
        dataframe['up_volume'] = np.where(dataframe['close'] > dataframe['open'], dataframe['volume'], 0)
        dataframe['down_volume'] = np.where(dataframe['close'] < dataframe['open'], dataframe['volume'], 0)
        
        # 计算成交量差值百分比
        dataframe['up_vol_sum'] = dataframe['up_volume'].rolling(20).sum()
        dataframe['down_vol_sum'] = dataframe['down_volume'].rolling(20).sum()
        dataframe['avg_volume'] = (dataframe['up_vol_sum'] + dataframe['down_vol_sum']) / 2
        dataframe['volume_delta'] = (dataframe['up_vol_sum'] - dataframe['down_vol_sum']) / dataframe['avg_volume'].replace(0, 1)
        
        # 5. 多时间周期分析
        if self.use_multi_timeframe.value:
            for tf in self.informative_timeframes:
                informative = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=tf)
                
                # 计算该时间周期的高斯趋势
                alpha_value_inf = self.calc_gaussian_alpha(self.gaussian_length.value, self.gaussian_poles.value)
                gma_output_inf = self.gaussian_smooth(informative['close'].values, self.gaussian_poles.value, alpha_value_inf)
                informative['gma_output'] = gma_output_inf
                informative['gauss_trend'] = ta.LINEARREG(informative['gma_output'], timeperiod=self.linreg_length.value)
                
                # 计算该时间周期的VIDYA
                informative['vidya'] = self.vidya_calc(
                    informative, 'close', self.vidya_length.value, self.vidya_momentum.value
                )
                
                # 计算趋势方向
                informative['trend_direction'] = np.where(informative['gauss_trend'] > informative['vidya'], 1, -1)
                
                # 合并到主数据框
                dataframe[f'trend_direction_{tf}'] = informative['trend_direction']
        
        # 计算EMA作为额外的趋势确认
        dataframe['ema50'] = ta.EMA(dataframe, timeperiod=50)
        dataframe['ema200'] = ta.EMA(dataframe, timeperiod=200)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入和卖空信号
        """
        # 多头入场条件
        long_conditions = []
        
        # 1. 高斯趋势过滤器条件
        long_conditions.append(dataframe['gauss_trend_direction'] > 0)  # 高斯趋势向上
        long_conditions.append(dataframe['close'] > dataframe['gauss_trend'])  # 价格在高斯趋势线上方
        
        # 2. VIDYA条件
        long_conditions.append(dataframe['vidya_trend_change_up'] | dataframe['vidya_trend_up'])  # VIDYA趋势向上或刚转向上
        
        # 3. DSL振荡器条件 - 可选条件，不强制要求
        # long_conditions.append(dataframe['dsl_cross_up'])  # DSL振荡器上穿下轨
        
        # 4. 趋势强度过滤
        long_conditions.append(dataframe['trend_strength'] > 15)  # 确保趋势足够强，降低要求
        
        # 5. 成交量过滤
        long_conditions.append(dataframe['volume'] > 0)  # 确保有交易量
        long_conditions.append(dataframe['volume_ratio'] > self.volume_threshold.value / 10)  # 成交量比率大于阈值
        
        # 6. 多时间周期确认
        if self.use_multi_timeframe.value:
            # 计算有多少个更高时间周期是看涨的
            aligned_timeframes = 0
            for tf in self.informative_timeframes:
                dataframe[f'is_bullish_{tf}'] = dataframe[f'trend_direction_{tf}'] > 0
                aligned_timeframes += dataframe[f'is_bullish_{tf}'].astype(int)
            
            # 要求至少指定数量的时间周期是看涨的
            long_conditions.append(aligned_timeframes >= self.min_timeframes_aligned.value)
        
        # 7. 额外确认
        long_conditions.append(dataframe['close'] > dataframe['ema50'])  # 价格在均线上方
        long_conditions.append(dataframe['rsi'] > 30)  # RSI不在超卖区，降低限制
        
        # 8. 价格上升趋势确认
        long_conditions.append(dataframe['close'] > dataframe['close'].shift(1))  # 价格高于上一K线
        
        # 组合所有多头条件
        if long_conditions:
            # 至少满足大多数条件，不必全部满足
            min_conditions = len(long_conditions) - 2  # 可以不满足2个条件
            combined_condition = reduce(lambda x, y: x + y, [c.astype(int) for c in long_conditions]) >= min_conditions
            dataframe.loc[
                combined_condition,
                'enter_long'
            ] = 1
        
        # 空头入场条件
        short_conditions = []
        
        # 1. 高斯趋势过滤器条件
        short_conditions.append(dataframe['gauss_trend_direction'] < 0)  # 高斯趋势向下
        short_conditions.append(dataframe['close'] < dataframe['gauss_trend'])  # 价格在高斯趋势线下方
        
        # 2. VIDYA条件
        short_conditions.append(dataframe['vidya_trend_change_down'] | dataframe['vidya_trend_down'])  # VIDYA趋势向下或刚转向下
        
        # 3. DSL振荡器条件 - 可选条件，不强制要求
        # short_conditions.append(dataframe['dsl_cross_dn'])  # DSL振荡器下穿上轨
        
        # 4. 趋势强度过滤
        short_conditions.append(dataframe['trend_strength'] > 15)  # 确保趋势足够强，降低要求
        
        # 5. 成交量过滤
        short_conditions.append(dataframe['volume'] > 0)  # 确保有交易量
        short_conditions.append(dataframe['volume_ratio'] > self.volume_threshold.value / 10)  # 成交量比率大于阈值
        
        # 6. 多时间周期确认
        if self.use_multi_timeframe.value:
            # 计算有多少个更高时间周期是看跌的
            aligned_timeframes = 0
            for tf in self.informative_timeframes:
                dataframe[f'is_bearish_{tf}'] = dataframe[f'trend_direction_{tf}'] < 0
                aligned_timeframes += dataframe[f'is_bearish_{tf}'].astype(int)
            
            # 要求至少指定数量的时间周期是看跌的
            short_conditions.append(aligned_timeframes >= self.min_timeframes_aligned.value)
        
        # 7. 额外确认
        short_conditions.append(dataframe['close'] < dataframe['ema50'])  # 价格在均线下方
        short_conditions.append(dataframe['rsi'] < 70)  # RSI不在超买区，提高上限
        
        # 8. 价格下降趋势确认
        short_conditions.append(dataframe['close'] < dataframe['close'].shift(1))  # 价格低于上一K线
        
        # 组合所有空头条件
        if short_conditions:
            # 至少满足大多数条件，不必全部满足
            min_conditions = len(short_conditions) - 2  # 可以不满足2个条件
            combined_condition = reduce(lambda x, y: x + y, [c.astype(int) for c in short_conditions]) >= min_conditions
            dataframe.loc[
                combined_condition,
                'enter_short'
            ] = 1
            
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成卖出和平空信号
        """
        # 多头退出条件
        dataframe.loc[
            (
                # 1. 高斯趋势转向且确认 - 至少连续2根K线向下
                ((dataframe['gauss_trend_direction'] < 0) & 
                 (dataframe['gauss_trend_direction'].shift(1) < 0)) |
                # 2. VIDYA趋势转向且价格低于VIDYA
                ((dataframe['vidya_trend_change_down']) & 
                 (dataframe['close'] < dataframe['vidya'])) |
                # 3. DSL振荡器下穿且趋势确认 - 增加确认条件
                ((dataframe['dsl_cross_dn']) & 
                 (dataframe['rsi'] > 60)) |
                # 4. RSI进入超买区且价格开始回落
                ((dataframe['rsi'] > 75) & 
                 (dataframe['close'] < dataframe['close'].shift(1)))
            ),
            'exit_long'
        ] = 1
        
        # 空头退出条件
        dataframe.loc[
            (
                # 1. 高斯趋势转向且确认 - 至少连续2根K线向上
                ((dataframe['gauss_trend_direction'] > 0) & 
                 (dataframe['gauss_trend_direction'].shift(1) > 0)) |
                # 2. VIDYA趋势转向且价格高于VIDYA
                ((dataframe['vidya_trend_change_up']) & 
                 (dataframe['close'] > dataframe['vidya'])) |
                # 3. DSL振荡器上穿且趋势确认 - 增加确认条件
                ((dataframe['dsl_cross_up']) & 
                 (dataframe['rsi'] < 40)) |
                # 4. RSI进入超卖区且价格开始反弹
                ((dataframe['rsi'] < 25) & 
                 (dataframe['close'] > dataframe['close'].shift(1)))
            ),
            'exit_short'
        ] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        基于ATR的动态止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr_value = current_candle['atr']
        
        # 计算动态止损距离
        if trade.is_short:
            # 空头止损 - 当前价格 + ATR * 倍数
            stop_distance = current_rate + (atr_value * self.atr_stop_loss.value)
            stop_loss_pct = (stop_distance / current_rate - 1)
            
            # 如果已经盈利，使用更紧的止损
            if current_profit < -0.01:  # 空头盈利为负值
                # 使用更小的ATR乘数作为止损
                stop_distance = current_rate + (atr_value * (self.atr_stop_loss.value * 0.7))
                stop_loss_pct = (stop_distance / current_rate - 1)
        else:
            # 多头止损 - 当前价格 - ATR * 倍数
            stop_distance = current_rate - (atr_value * self.atr_stop_loss.value)
            stop_loss_pct = (stop_distance / current_rate - 1)
            
            # 如果已经盈利，使用更紧的止损
            if current_profit > 0.01:
                # 使用更小的ATR乘数作为止损
                stop_distance = current_rate - (atr_value * (self.atr_stop_loss.value * 0.7))
                stop_loss_pct = (stop_distance / current_rate - 1)
        
        # 转换为负数百分比
        return stop_loss_pct
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[str]:
        """
        自定义退出逻辑，包括ATR止盈和追踪止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr_value = current_candle['atr']
        
        # ATR止盈
        take_profit_threshold = self.atr_take_profit.value * atr_value / current_rate
        
        if trade.is_short and current_profit <= -take_profit_threshold:
            return "short_atr_take_profit"
        
        if not trade.is_short and current_profit >= take_profit_threshold:
            return "long_atr_take_profit"
        
        # ATR追踪止损
        # 如果利润已经达到一定水平，启用追踪止损
        min_profit_threshold = self.atr_trailing_stop.value * atr_value / current_rate / 2
        
        if trade.is_short and current_profit < -min_profit_threshold:
            # 获取交易期间的最大利润
            max_profit = trade.calc_profit_ratio(trade.max_rate)
            
            # 如果从最大利润回撤超过ATR的一定比例，触发止损
            trailing_threshold = self.atr_trailing_stop.value * atr_value / current_rate
            
            if current_profit > max_profit + trailing_threshold:
                return "short_trailing_stop_loss"
        
        if not trade.is_short and current_profit > min_profit_threshold:
            # 获取交易期间的最大利润
            max_profit = trade.calc_profit_ratio(trade.max_rate)
            
            # 如果从最大利润回撤超过ATR的一定比例，触发止损
            trailing_threshold = self.atr_trailing_stop.value * atr_value / current_rate
            
            if current_profit < max_profit - trailing_threshold:
                return "long_trailing_stop_loss"
        
        return None
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        根据市场状况动态调整杠杆
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取当前趋势强度
        trend_strength = current_candle['trend_strength']
        
        # 根据趋势强度调整杠杆
        if trend_strength > 70:  # 强趋势
            adjusted_leverage = self.max_leverage.value
        elif trend_strength > 50:  # 中等趋势
            adjusted_leverage = self.max_leverage.value * 0.75
        elif trend_strength > 30:  # 弱趋势
            adjusted_leverage = self.max_leverage.value * 0.5
        else:  # 不明确趋势
            adjusted_leverage = self.max_leverage.value * 0.25
        
        # 确保杠杆在合理范围内
        adjusted_leverage = max(1.0, min(adjusted_leverage, max_leverage))
        
        return adjusted_leverage
    
    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                           proposed_stake: float, min_stake: Optional[float], max_stake: float,
                           entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        基于风险管理的仓位大小计算
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr_value = current_candle['atr']
        
        # 计算止损点位
        if side == "long":
            stop_price = current_rate - (atr_value * self.atr_stop_loss.value)
            risk_per_unit = current_rate - stop_price
        else:  # short
            stop_price = current_rate + (atr_value * self.atr_stop_loss.value)
            risk_per_unit = stop_price - current_rate
        
        # 计算账户总价值
        total_portfolio_value = self.wallets.get_total_stake_amount()
        
        # 计算风险金额
        risk_amount = total_portfolio_value * self.risk_per_trade.value
        
        # 计算仓位大小
        position_size = risk_amount / risk_per_unit
        
        # 转换为交易金额
        stake_amount = position_size * current_rate
        
        # 确保在最小和最大仓位之间
        if min_stake is not None:
            stake_amount = max(min_stake, min(stake_amount, max_stake))
        else:
            stake_amount = min(stake_amount, max_stake)
        
        return stake_amount
    
    def informative_pairs(self):
        """
        定义需要的信息对
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, timeframe) for pair in pairs for timeframe in self.informative_timeframes]
        return informative_pairs 