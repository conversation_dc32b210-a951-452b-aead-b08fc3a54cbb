{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "timeframe": "5m", "strategy": "DSL_Oscillator_Strategy", "order_types": {"entry": "market", "exit": "market", "emergency_exit": "market", "force_exit": "market", "force_entry": "market", "stoploss": "market", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "BNB/USDT", "SOL/USDT", "XRP/USDT", "ADA/USDT", "DOGE/USDT", "MATIC/USDT", "DOT/USDT", "LTC/USDT"], "pair_blacklist": [".*UP/USDT", ".*DOWN/USDT", ".*BEAR/USDT", ".*BULL/USDT"]}, "pairlists": [{"method": "StaticPairList"}, {"method": "VolumePairList", "number_assets": 20, "sort_key": "quoteVolume", "refresh_period": 1800}, {"method": "<PERSON><PERSON><PERSON>er", "min_days_listed": 30}, {"method": "Sp<PERSON><PERSON><PERSON>er", "max_spread_ratio": 0.005}, {"method": "RangeStabilityFilter", "lookback_days": 3, "min_rate_of_change": 0.05, "refresh_period": 1440}, {"method": "VolatilityFilter", "lookback_days": 3, "min_volatility": 0.02, "max_volatility": 0.75, "refresh_period": 1440}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 14, "allowed_risk": 0.02, "stoploss_range_min": -0.15, "stoploss_range_max": -0.02, "stoploss_range_step": 0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.1, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": true, "jwt_secret_key": "", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "DSL_Oscillator_Bot", "initial_state": "running", "forcebuy_enable": false, "internals": {"process_throttle_secs": 5}}