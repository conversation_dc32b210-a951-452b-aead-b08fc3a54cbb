# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy (HAPBS)
#
#   作者: Gemini & User
#   版本: 23.0
#
#   策略理念 (V23):
#   - V22 的做多逻辑（追高）失败，但做空逻辑（破位）盈利。
#   - 本版本保留盈利的做空逻辑。
#   - 核心改动：重构做多逻辑，采用"上升趋势中的回调买入"策略。
#   - 做多信号：在严格的上升趋势中（EMA多头排列），等待Heikin Ashi出现短暂回调（前一根为阴线），
#     然后在出现强劲的看涨恢复K线（ha_strong_bull）并重新上穿短期EMA时入场。
# --------------------------------

class HAPBS_V23(IStrategy):

    # --- Freqtrade 核心配置 (与V22一致) ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # 风险控制 (与V22一致)
    stoploss = -0.02

    trailing_stop = True
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.015
    trailing_only_offset_is_reached = True

    minimal_roi = {
        "0": 0.03,
        "10": 0.02,
        "20": 0.01
    }

    # --- 可配置参数 (与V22一致) ---
    ema_short_period = IntParameter(10, 30, default=20, space='buy')
    ema_long_period = IntParameter(30, 60, default=50, space='buy')
    adx_threshold = IntParameter(20, 35, default=25, space='buy')
    volume_factor = DecimalParameter(1.0, 2.0, default=1.2, space='buy')

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Heikin Ashi K线
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        
        dataframe['ha_strong_bull'] = (
            (dataframe['ha_close'] > dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_low'])
        )
        dataframe['ha_strong_bear'] = (
            (dataframe['ha_close'] < dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_high'])
        )

        # 趋势指标: EMAs 和 ADX
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=self.ema_short_period.value)
        dataframe['ema_long'] = ta.EMA(dataframe, timeperiod=self.ema_long_period.value)
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        
        # 成交量
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- V23: 采用回调买入逻辑做多，保留原破位逻辑做空 ---

        # 1. 做多条件 (long_pullback_v23): 上升趋势中的回调买入
        long_conditions = (
            # 趋势过滤: 严格的EMA多头排列
            (dataframe['ema_short'] > dataframe['ema_long']) &
            (dataframe['ema_long'] > dataframe['ema_200']) &

            # 回调信号: 前一根HA K线是下跌的
            (dataframe['ha_close'].shift(1) < dataframe['ha_open'].shift(1)) &
            
            # 恢复信号: 当前是一根强劲的看涨K线，并且价格上穿了短期EMA
            (dataframe['ha_strong_bull']) &
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short'])) &

            # 动量过滤
            (dataframe['adx'] > self.adx_threshold.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor.value)
        )
        dataframe.loc[long_conditions, ['enter_long', 'enter_tag']] = (1, 'long_pullback_v23')

        # 2. 做空条件 (short_breakdown_v23): 维持V22的成功逻辑
        short_conditions = (
            # 趋势过滤: 均线空头排列 & 价格在长期均线下方
            (dataframe['ema_short'] < dataframe['ema_long']) &
            (dataframe['ha_close'] < dataframe['ema_200']) &
            
            # 破位信号: 强劲的看跌K线下穿短期EMA
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short'])) &
            (dataframe['ha_strong_bear']) &
            
            # 动量过滤
            (dataframe['adx'] > self.adx_threshold.value) &
            (dataframe['volume'] > dataframe['volume_mean'] * self.volume_factor.value)
        )
        dataframe.loc[short_conditions, ['enter_short', 'enter_tag']] = (1, 'short_breakdown_v23')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 简化出场逻辑，完全依赖ROI和止损
        return dataframe 