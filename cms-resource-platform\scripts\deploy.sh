#!/bin/bash

# 生产环境部署脚本

set -e

echo "🚀 开始部署CMS资源管理平台到生产环境..."

# 检查必要的环境变量
required_vars=("DB_HOST" "DB_NAME" "DB_USER" "DB_PASSWORD" "SECRET_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 环境变量 $var 未设置"
        exit 1
    fi
done

# 备份数据库
echo "💾 备份数据库..."
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin main

# 构建Docker镜像
echo "🔨 构建生产环境镜像..."
docker-compose -f docker-compose.prod.yml build

# 停止旧服务
echo "🛑 停止旧服务..."
docker-compose -f docker-compose.prod.yml down

# 运行数据库迁移
echo "📊 运行数据库迁移..."
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py migrate

# 收集静态文件
echo "📦 收集静态文件..."
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py collectstatic --noinput

# 启动新服务
echo "🚀 启动新服务..."
docker-compose -f docker-compose.prod.yml up -d

# 健康检查
echo "🔍 健康检查..."
sleep 30
if curl -f http://localhost/health/; then
    echo "✅ 部署成功！"
else
    echo "❌ 健康检查失败，回滚..."
    docker-compose -f docker-compose.prod.yml down
    # 这里可以添加回滚逻辑
    exit 1
fi

echo "🎉 部署完成！"
