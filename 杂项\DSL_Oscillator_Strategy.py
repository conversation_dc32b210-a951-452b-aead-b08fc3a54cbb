import numpy as np
import pandas as pd
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from pandas import DataFrame
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, CategoricalParameter
from typing import Dict, List, Optional
import logging
from functools import reduce
import math

logger = logging.getLogger(__name__)


class DSL_Oscillator_Strategy(IStrategy):
    """
    DSL Oscillator Strategy
    
    该策略基于DSL Oscillator指标，结合ATR进行止盈止损管理。
    
    策略逻辑：
    - 使用DSL Oscillator指标的交叉信号作为入场条件
    - 使用ATR进行动态止盈止损设置
    - 实现仓位管理和杠杆控制
    - 支持多空交易
    
    作者: Claude 3.7 Sonnet
    """
    
    # 策略参数
    INTERFACE_VERSION = 3
    
    # 买入参数
    buy_rsi_length = IntParameter(8, 14, default=10, space="buy")
    buy_dsl_length = IntParameter(8, 14, default=10, space="buy")
    buy_dsl_mode = CategoricalParameter(["Fast", "Slow"], default="Fast", space="buy")
    buy_zlema_length = IntParameter(8, 14, default=10, space="buy")
    buy_threshold = IntParameter(45, 60, default=55, space="buy")
    
    # 卖出参数
    sell_rsi_length = IntParameter(8, 14, default=10, space="sell")
    sell_dsl_length = IntParameter(8, 14, default=10, space="sell")
    sell_dsl_mode = CategoricalParameter(["Fast", "Slow"], default="Fast", space="sell")
    sell_zlema_length = IntParameter(8, 14, default=10, space="sell")
    sell_threshold = IntParameter(45, 55, default=50, space="sell")
    
    # 止盈止损参数
    atr_period = IntParameter(10, 20, default=14, space="protection")
    atr_stop_loss_multiplier = DecimalParameter(0.8, 2.0, default=1.2, space="protection")  # 降低止损乘数
    atr_take_profit_multiplier = DecimalParameter(2.0, 4.0, default=3.0, space="protection")
    
    # 仓位管理参数
    max_leverage = DecimalParameter(1.0, 10.0, default=2.0, space="protection")  # 降低默认杠杆
    risk_per_trade = DecimalParameter(0.01, 0.05, default=0.01, space="protection")  # 降低每笔交易风险
    
    # 时间框架设置
    timeframe = '5m'
    
    # 回测设置
    minimal_roi = {
        "0": 0.15,  # 提高最小ROI阈值
        "30": 0.08,
        "60": 0.05,
        "120": 0.03
    }
    
    # 止损设置 (动态止损将覆盖此设置)
    stoploss = -0.05  # 降低静态止损
    
    # 其他设置
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False
    can_short = True
    
    entry_score_threshold_trending = IntParameter(2, 4, default=3)
    entry_score_threshold_ranging = IntParameter(1, 3, default=2)
    
    st1_period = IntParameter(2, 4, default=3)
    st2_period = IntParameter(3, 6, default=5)
    st3_period = IntParameter(5, 8, default=6)

    buy_macd_fast = IntParameter(3, 6, default=4)
    buy_macd_slow = IntParameter(8, 16, default=12)
    
    exit_time_in_hours = IntParameter(1, 4, default=2)
    
    tp1_atr_multiplier = DecimalParameter(0.8, 1.5, default=1.0)
    tp2_atr_multiplier = DecimalParameter(1.5, 2.5, default=1.8)
    tp3_atr_multiplier = DecimalParameter(2.0, 3.5, default=2.8)
    
    def zlema(self, src: pd.Series, length: int) -> pd.Series:
        """
        计算零延迟指数移动平均线 (Zero-Lag Exponential Moving Average)
        """
        lag = math.floor((length - 1) / 2)
        ema_data = 2 * src - src.shift(lag)
        ema2 = ta.EMA(ema_data, length)
        # 确保返回的是pandas Series
        if not isinstance(ema2, pd.Series):
            ema2 = pd.Series(ema2, index=src.index)
        return ema2
    
    def dsl_lines(self, src: pd.Series, length: int, dsl_mode: int = 2) -> tuple:
        """
        计算非连续信号线 (Discontinued Signal Lines)
        """
        # 计算SMA并确保返回的是pandas Series
        sma_values = ta.SMA(src, length)
        if not isinstance(sma_values, pd.Series):
            sma = pd.Series(sma_values, index=src.index)
        else:
            sma = sma_values

        # Find first valid index after SMA calculation
        first_valid_idx = sma.first_valid_index()
        if first_valid_idx is None:
            return pd.Series(index=src.index, dtype='float64'), pd.Series(index=src.index, dtype='float64')
        
        up = pd.Series(index=src.index, dtype='float64')
        dn = pd.Series(index=src.index, dtype='float64')

        # Get integer position of first valid index
        first_valid_loc = src.index.get_loc(first_valid_idx)
        
        # Initialize first values
        if first_valid_loc > 0:
            up.iloc[first_valid_loc - 1] = src.iloc[first_valid_loc]
            dn.iloc[first_valid_loc - 1] = src.iloc[first_valid_loc]
        else:
            up.iloc[first_valid_loc] = src.iloc[first_valid_loc]
            dn.iloc[first_valid_loc] = src.iloc[first_valid_loc]

        for i in range(first_valid_loc, len(src)):
            if src.iloc[i] > sma.iloc[i]:
                up.iloc[i] = up.iloc[i-1] + dsl_mode / length * (src.iloc[i] - up.iloc[i-1]) if not pd.isna(up.iloc[i-1]) else src.iloc[i]
                dn.iloc[i] = dn.iloc[i-1]
            else:
                up.iloc[i] = up.iloc[i-1]
                dn.iloc[i] = dn.iloc[i-1] + dsl_mode / length * (src.iloc[i] - dn.iloc[i-1]) if not pd.isna(dn.iloc[i-1]) else src.iloc[i]
        
        return up, dn
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算策略所需的技术指标
        """
        # 计算RSI
        rsi_values = ta.RSI(dataframe, timeperiod=self.buy_rsi_length.value)
        if not isinstance(rsi_values, pd.Series):
            dataframe['rsi'] = pd.Series(rsi_values, index=dataframe.index)
        else:
            dataframe['rsi'] = rsi_values
        
        # 计算DSL模式值
        dsl_mode_value = 2 if self.buy_dsl_mode.value == "Fast" else 1
        
        # 计算DSL线
        lvlu, lvld = self.dsl_lines(dataframe['rsi'], self.buy_dsl_length.value, dsl_mode_value)
        dataframe['lvlu'] = lvlu
        dataframe['lvld'] = lvld
        
        # 计算DSL振荡器
        dataframe['dsl_avg'] = (dataframe['lvlu'] + dataframe['lvld']) / 2
        dataframe['dsl_osc'] = self.zlema(dataframe['dsl_avg'], self.buy_zlema_length.value)
        
        # 计算DSL振荡器的DSL线
        level_up, level_dn = self.dsl_lines(dataframe['dsl_osc'], self.buy_dsl_length.value, dsl_mode_value)
        dataframe['level_up'] = level_up
        dataframe['level_dn'] = level_dn
        
        # 计算交叉信号
        dataframe['cross_up'] = qtpylib.crossed_above(dataframe['dsl_osc'], dataframe['level_dn'])
        dataframe['cross_dn'] = qtpylib.crossed_below(dataframe['dsl_osc'], dataframe['level_up'])
        
        # 计算ATR用于止盈止损
        atr_values = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        if not isinstance(atr_values, pd.Series):
            dataframe['atr'] = pd.Series(atr_values, index=dataframe.index)
        else:
            dataframe['atr'] = atr_values
            
        # 添加额外的过滤指标
        # 1. 计算趋势方向 - EMA50和EMA200
        dataframe['ema50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['ema200'] = ta.EMA(dataframe['close'], timeperiod=200)
        dataframe['trend_long'] = dataframe['ema50'] > dataframe['ema200']
        dataframe['trend_short'] = dataframe['ema50'] < dataframe['ema200']
        
        # 2. 计算波动率 - 布林带宽度
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']
        dataframe['bb_width'] = (dataframe['bb_upperband'] - dataframe['bb_lowerband']) / dataframe['bb_middleband']
        
        # 3. 计算成交量趋势
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=30).mean()
        dataframe['volume_increase'] = dataframe['volume'] > dataframe['volume_mean'] * 1.5
        
        # 增强均线交叉信号
        ema_cross_up = (dataframe['cross_up'] & (dataframe['dsl_osc'] < self.buy_threshold.value) & (dataframe['volume'] > 0) & (dataframe['volume_increase']) & ((dataframe['trend_long']) | (dataframe['bb_width'] > 0.05)))
        ema_cross_down = (dataframe['cross_dn'] & (dataframe['dsl_osc'] > self.sell_threshold.value) & (dataframe['volume'] > 0) & (dataframe['volume_increase']) & ((dataframe['trend_short']) | (dataframe['bb_width'] > 0.05)))
        
        dataframe.loc[ema_cross_up, 'buy_score'] += 8
        dataframe.loc[ema_cross_down, 'sell_score'] += 8
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入和卖空信号
        """
        # 多头入场条件
        dataframe.loc[
            (
                (dataframe['cross_up']) &  # DSL振荡器上穿下轨
                (dataframe['dsl_osc'] < self.buy_threshold.value) &  # 振荡器值小于阈值
                (dataframe['volume'] > 0) &  # 确保有交易量
                (dataframe['volume_increase']) &  # 成交量增加
                (
                    (dataframe['trend_long']) |  # 长期趋势向上
                    (dataframe['bb_width'] > 0.05)  # 或者波动率足够
                )
            ),
            'enter_long'] = 1
        
        # 空头入场条件
        dataframe.loc[
            (
                (dataframe['cross_dn']) &  # DSL振荡器下穿上轨
                (dataframe['dsl_osc'] > self.sell_threshold.value) &  # 振荡器值大于阈值
                (dataframe['volume'] > 0) &  # 确保有交易量
                (dataframe['volume_increase']) &  # 成交量增加
                (
                    (dataframe['trend_short']) |  # 长期趋势向下
                    (dataframe['bb_width'] > 0.05)  # 或者波动率足够
                )
            ),
            'enter_short'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成卖出和平空信号
        """
        # 多头出场条件
        dataframe.loc[
            (
                (dataframe['cross_dn']) &  # DSL振荡器下穿上轨
                (dataframe['dsl_osc'] > self.sell_threshold.value) &  # 振荡器值大于阈值
                (dataframe['volume'] > 0)  # 确保有交易量
            ),
            'exit_long'] = 1
        
        # 空头出场条件
        dataframe.loc[
            (
                (dataframe['cross_up']) &  # DSL振荡器上穿下轨
                (dataframe['dsl_osc'] < self.buy_threshold.value) &  # 振荡器值小于阈值
                (dataframe['volume'] > 0)  # 确保有交易量
            ),
            'exit_short'] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        基于ATR的动态止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr = last_candle['atr']
        
        # 计算动态止损价格
        if trade.is_short:
            # 做空时，止损价格 = 入场价格 + (ATR * 乘数)
            stop_price = trade.open_rate + (atr * self.atr_stop_loss_multiplier.value)
            # 转换为相对百分比
            stop_percent = (stop_price / current_rate) - 1
        else:
            # 做多时，止损价格 = 入场价格 - (ATR * 乘数)
            stop_price = trade.open_rate - (atr * self.atr_stop_loss_multiplier.value)
            # 转换为相对百分比
            stop_percent = (stop_price / current_rate) - 1
            
        # 添加止损调整 - 随着利润增加，收紧止损
        if current_profit > 0.02:  # 如果利润超过2%
            # 将止损调整到保本或更好
            trailing_stop = max(stop_percent, -0.01)  # 最多允许回撤1%
            return trailing_stop
        
        return stop_percent
    
    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        基于ATR的止盈和其他自定义退出条件
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr = last_candle['atr']
        
        # 计算止盈目标
        if trade.is_short:
            # 做空时，止盈价格 = 入场价格 - (ATR * 乘数)
            take_profit_price = trade.open_rate - (atr * self.atr_take_profit_multiplier.value)
            # 如果当前价格低于止盈价格，触发止盈
            if current_rate <= take_profit_price:
                return 'atr_take_profit_short'
        else:
            # 做多时，止盈价格 = 入场价格 + (ATR * 乘数)
            take_profit_price = trade.open_rate + (atr * self.atr_take_profit_multiplier.value)
            # 如果当前价格高于止盈价格，触发止盈
            if current_rate >= take_profit_price:
                return 'atr_take_profit_long'
                
        # 添加时间退出条件 - 如果交易时间过长且利润为负，提前退出
        if trade.open_date_utc < current_time - pd.Timedelta(hours=4) and current_profit < 0:
            return 'timeout_exit'
        
        return None
    
    def leverage(self, pair: str, current_time: 'datetime', current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        设置杠杆倍数，不超过最大杠杆
        """
        return min(self.max_leverage.value, max_leverage)
    
    def custom_stake_amount(self, pair: str, current_time: 'datetime', current_rate: float,
                           proposed_stake: float, min_stake: float, max_stake: float,
                           entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        基于风险计算仓位大小
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 获取ATR值
        atr = last_candle['atr']
        
        # 计算止损点位
        if side == "long":
            stop_price = current_rate - (atr * self.atr_stop_loss_multiplier.value)
            risk_per_unit = current_rate - stop_price
        else:  # side == "short"
            stop_price = current_rate + (atr * self.atr_stop_loss_multiplier.value)
            risk_per_unit = stop_price - current_rate
        
        # 计算账户总价值
        total_portfolio_value = self.wallets.get_total_stake_amount()
        
        # 计算风险金额
        risk_amount = total_portfolio_value * self.risk_per_trade.value
        
        # 计算仓位大小
        position_size = risk_amount / risk_per_unit
        
        # 转换为标的货币金额
        stake_amount = position_size * current_rate
        
        # 确保在最小和最大仓位之间
        stake_amount = max(min_stake, min(max_stake, stake_amount))
        
        return stake_amount
    
    def informative_pairs(self) -> List[tuple]:
        """
        定义要获取的信息性对
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, self.timeframe) for pair in pairs]
        return informative_pairs 