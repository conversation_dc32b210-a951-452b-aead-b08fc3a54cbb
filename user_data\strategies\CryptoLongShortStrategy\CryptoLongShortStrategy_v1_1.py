# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


class CryptoLongShortStrategy_v1_1(IStrategy):
    """
    Crypto Long-Short Strategy v1.1 - 信号质量优化版本
    
    第一次迭代改进重点:
    - 提高信号强度阈值，减少低质量交易
    - 增加更严格的入场条件
    - 优化信号冲突处理机制
    - 引入信号确认机制
    
    目标:
    - 降低日均交易频率至25次左右
    - 提高平均利润至1.2%
    - 保持胜率85%以上
    """

    # Strategy interface version
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy
    timeframe = '1h'
    
    # Can our strategy go long and short?
    can_short: bool = True
    
    # Leverage settings
    leverage_optimize = False
    leverage_num = 3.0
    
    # ROI table - 提高利润目标
    minimal_roi = {
        "0": 0.12,   # 12% profit target (提高)
        "30": 0.08,  # 8% after 30 minutes
        "60": 0.05,  # 5% after 60 minutes
        "120": 0.025 # 2.5% after 120 minutes
    }

    # Stoploss - 收紧止损
    stoploss = -0.12  # 12% base stop loss (从15%收紧)

    # Trailing stoploss - 更保守的追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.015  # Start trailing at 1.5% profit (提高)
    trailing_stop_positive_offset = 0.025  # Trail by 2.5% (提高)
    trailing_only_offset_is_reached = True
    
    # === 优化后的策略参数 ===
    
    # === 信号强度参数 - 提高阈值 ===
    min_signal_strength = DecimalParameter(0.55, 0.75, default=0.65, space="buy", optimize=True, load=True)  # 提高最低信号强度
    priority_signal_threshold = DecimalParameter(0.75, 0.9, default=0.85, space="buy", optimize=True, load=True)  # 提高优先信号阈值
    
    # === 更严格的RSI参数 ===
    rsi_overbought_threshold = DecimalParameter(75.0, 85.0, default=80.0, space="buy", optimize=True, load=True)  # 提高超买阈值
    rsi_oversold_threshold = DecimalParameter(15.0, 25.0, default=20.0, space="buy", optimize=True, load=True)  # 降低超卖阈值
    
    # === 成交量确认参数 - 更严格 ===
    volume_surge_multiplier = DecimalParameter(3.0, 6.0, default=5.0, space="buy", optimize=True, load=True)  # 提高成交量要求
    min_volume_ratio = DecimalParameter(1.8, 3.0, default=2.5, space="buy", optimize=True, load=True)  # 新增最低成交量比率
    
    # === 价格变化阈值 - 更严格 ===
    pump_threshold_1h = DecimalParameter(0.08, 0.18, default=0.15, space="buy", optimize=True, load=True)  # 提高pump阈值
    dump_threshold_1h = DecimalParameter(0.08, 0.18, default=0.15, space="buy", optimize=True, load=True)  # 提高dump阈值
    
    # === 信号确认参数 - 新增 ===
    signal_confirmation_period = IntParameter(2, 4, default=3, space="buy", optimize=True, load=True)  # 信号确认周期
    trend_strength_threshold = DecimalParameter(0.6, 0.9, default=0.75, space="buy", optimize=True, load=True)  # 趋势强度阈值
    
    # === 市场环境过滤参数 - 更严格 ===
    market_volatility_threshold = DecimalParameter(0.03, 0.08, default=0.05, space="buy", optimize=True, load=True)  # 市场波动率阈值
    liquidity_filter_strength = DecimalParameter(0.7, 0.95, default=0.85, space="buy", optimize=True, load=True)  # 流动性过滤强度

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 100  # 增加启动蜡烛数

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        v1.1 指标计算 - 增加更多确认指标
        """
        # 基础技术指标
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=14)
        dataframe['rsi_smooth'] = ta.EMA(dataframe['rsi'], timeperiod=3)  # 平滑RSI
        
        dataframe['ema_20'] = ta.EMA(dataframe['close'], timeperiod=20)
        dataframe['ema_50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['ema_100'] = ta.EMA(dataframe['close'], timeperiod=100)  # 新增
        dataframe['sma_200'] = ta.SMA(dataframe['close'], timeperiod=200)
        
        # ATR and volatility
        dataframe['atr'] = ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=14)
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']
        
        # 成交量指标 - 增强
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ema'] = ta.EMA(dataframe['volume'], timeperiod=10)  # 新增
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        dataframe['volume_trend'] = dataframe['volume_ema'] / dataframe['volume_sma']  # 成交量趋势
        
        # 价格变化指标
        dataframe['price_change_1h'] = dataframe['close'].pct_change(1)
        dataframe['price_change_4h'] = dataframe['close'].pct_change(4)
        dataframe['price_change_24h'] = dataframe['close'].pct_change(24)  # 新增
        
        # 趋势强度指标 - 新增
        dataframe['trend_strength'] = self.calculate_trend_strength(dataframe)
        
        # Z-score计算
        dataframe = self.calculate_zscore_indicators(dataframe)
        
        # 多空信号指标
        dataframe = self.calculate_long_short_indicators(dataframe)
        
        # 信号质量评分 - 优化
        dataframe = self.calculate_enhanced_signal_quality_score(dataframe)
        
        # 市场环境评估 - 新增
        dataframe = self.calculate_market_environment(dataframe)
        
        return dataframe
    
    def calculate_trend_strength(self, dataframe: DataFrame) -> pd.Series:
        """计算趋势强度"""
        # EMA排列强度
        ema_alignment = np.where(
            (dataframe['ema_20'] > dataframe['ema_50']) & (dataframe['ema_50'] > dataframe['ema_100']), 1,
            np.where((dataframe['ema_20'] < dataframe['ema_50']) & (dataframe['ema_50'] < dataframe['ema_100']), -1, 0)
        )
        
        # 价格相对EMA位置
        price_position = (dataframe['close'] - dataframe['ema_20']) / dataframe['ema_20']
        
        # 综合趋势强度
        trend_strength = (abs(ema_alignment) * 0.6 + np.clip(abs(price_position) * 10, 0, 1) * 0.4)
        
        return trend_strength
    
    def calculate_zscore_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算Z-score异常检测指标 - 优化版本"""
        # 使用更长周期计算Z-score，提高稳定性
        lookback_period = 48  # 48小时回看期
        
        dataframe['price_1h_zscore'] = (
            (dataframe['price_change_1h'] - dataframe['price_change_1h'].rolling(lookback_period).mean()) / 
            dataframe['price_change_1h'].rolling(lookback_period).std()
        )
        dataframe['price_4h_zscore'] = (
            (dataframe['price_change_4h'] - dataframe['price_change_4h'].rolling(lookback_period).mean()) / 
            dataframe['price_change_4h'].rolling(lookback_period).std()
        )
        
        # 成交量Z-score
        dataframe['volume_zscore'] = (
            (dataframe['volume_ratio'] - dataframe['volume_ratio'].rolling(lookback_period).mean()) / 
            dataframe['volume_ratio'].rolling(lookback_period).std()
        )
        
        # 填充NaN值
        dataframe['price_1h_zscore'] = dataframe['price_1h_zscore'].fillna(0)
        dataframe['price_4h_zscore'] = dataframe['price_4h_zscore'].fillna(0)
        dataframe['volume_zscore'] = dataframe['volume_zscore'].fillna(0)
        
        return dataframe
    
    def calculate_long_short_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算多空信号指标 - 增强版本"""
        
        # === 做多信号指标 - 更严格 ===
        dataframe['rsi_oversold_rising'] = (
            (dataframe['rsi_smooth'] < self.rsi_oversold_threshold.value) & 
            (dataframe['rsi_smooth'] > dataframe['rsi_smooth'].shift(1)) &
            (dataframe['rsi_smooth'] > dataframe['rsi_smooth'].shift(2))  # 连续上升确认
        )
        
        dataframe['support_bounce'] = (
            (dataframe['close'] > dataframe['low'].rolling(20).min() * 1.015) &  # 更严格的支撑位
            (dataframe['close'] < dataframe['low'].rolling(20).min() * 1.04) &
            (dataframe['volume_ratio'] > self.min_volume_ratio.value)  # 成交量确认
        )
        
        dataframe['bullish_momentum'] = (
            (dataframe['close'] > dataframe['ema_20']) &
            (dataframe['ema_20'] > dataframe['ema_50']) &
            (dataframe['ema_50'] > dataframe['ema_100']) &  # 三重EMA确认
            (dataframe['trend_strength'] > self.trend_strength_threshold.value)
        )
        
        dataframe['volume_confirms_bounce'] = (
            (dataframe['volume_ratio'] > self.min_volume_ratio.value) &
            (dataframe['volume_trend'] > 1.1) &  # 成交量趋势确认
            (dataframe['price_change_1h'] > 0)
        )
        
        # === 做空信号指标 - 更严格 ===
        dataframe['rsi_overbought_declining'] = (
            (dataframe['rsi_smooth'] > self.rsi_overbought_threshold.value) & 
            (dataframe['rsi_smooth'] < dataframe['rsi_smooth'].shift(1)) &
            (dataframe['rsi_smooth'] < dataframe['rsi_smooth'].shift(2))  # 连续下降确认
        )
        
        dataframe['resistance_rejection'] = (
            (dataframe['close'] < dataframe['high'].rolling(20).max() * 0.985) &  # 更严格的阻力位
            (dataframe['close'] > dataframe['high'].rolling(20).max() * 0.96) &
            (dataframe['volume_ratio'] > self.min_volume_ratio.value)  # 成交量确认
        )
        
        dataframe['bearish_momentum'] = (
            (dataframe['close'] < dataframe['ema_20']) &
            (dataframe['ema_20'] < dataframe['ema_50']) &
            (dataframe['ema_50'] < dataframe['ema_100']) &  # 三重EMA确认
            (dataframe['trend_strength'] > self.trend_strength_threshold.value)
        )
        
        dataframe['volume_confirms_weakness'] = (
            (dataframe['volume_ratio'] > self.min_volume_ratio.value) &
            (dataframe['volume_trend'] > 1.1) &  # 成交量趋势确认
            (dataframe['price_change_1h'] < 0)
        )
        
        return dataframe
    
    def calculate_enhanced_signal_quality_score(self, dataframe: DataFrame) -> DataFrame:
        """计算增强的信号质量评分"""
        
        # === 做多信号质量评分 - 优化 ===
        long_score_components = []
        
        # 基础技术指标得分 (0-25分) - 降低权重
        long_score_components.append(
            np.where(dataframe['rsi_oversold_rising'], 12, 0) +
            np.where(dataframe['support_bounce'], 8, 0) +
            np.where(dataframe['bullish_momentum'], 5, 0)
        )
        
        # 成交量确认得分 (0-30分) - 提高权重
        long_score_components.append(
            np.where(dataframe['volume_confirms_bounce'], 30, 0)
        )
        
        # 市场环境得分 (0-25分)
        long_score_components.append(
            np.where(dataframe['strong_uptrend'], 25, 
                    np.where(~dataframe['strong_downtrend'], 10, 0))
        )
        
        # Z-score异常检测得分 (0-20分) - 更严格
        long_score_components.append(
            np.where(dataframe['price_1h_zscore'] < -2.5, 20,
                    np.where(dataframe['price_1h_zscore'] < -2, 15,
                            np.where(dataframe['price_1h_zscore'] < -1.5, 5, 0)))
        )
        
        dataframe['long_signal_quality'] = np.clip(sum(long_score_components), 0, 100)
        
        # === 做空信号质量评分 - 优化 ===
        short_score_components = []
        
        # 基础技术指标得分 (0-25分) - 降低权重
        short_score_components.append(
            np.where(dataframe['rsi_overbought_declining'], 12, 0) +
            np.where(dataframe['resistance_rejection'], 8, 0) +
            np.where(dataframe['bearish_momentum'], 5, 0)
        )
        
        # 成交量确认得分 (0-30分) - 提高权重
        short_score_components.append(
            np.where(dataframe['volume_confirms_weakness'], 30, 0)
        )
        
        # 市场环境得分 (0-25分)
        short_score_components.append(
            np.where(dataframe['strong_downtrend'], 25, 
                    np.where(~dataframe['strong_uptrend'], 10, 0))
        )
        
        # Z-score异常检测得分 (0-20分) - 更严格
        short_score_components.append(
            np.where(dataframe['price_1h_zscore'] > 2.5, 20,
                    np.where(dataframe['price_1h_zscore'] > 2, 15,
                            np.where(dataframe['price_1h_zscore'] > 1.5, 5, 0)))
        )
        
        dataframe['short_signal_quality'] = np.clip(sum(short_score_components), 0, 100)
        
        return dataframe
    
    def calculate_market_environment(self, dataframe: DataFrame) -> DataFrame:
        """计算市场环境指标"""
        
        # 市场趋势强度
        dataframe['strong_uptrend'] = (
            (dataframe['close'] > dataframe['sma_200']) &
            (dataframe['ema_20'] > dataframe['ema_50']) &
            (dataframe['ema_50'] > dataframe['ema_100']) &
            (dataframe['price_change_4h'] > 0.06) &  # 提高阈值
            (dataframe['trend_strength'] > 0.7)
        )
        
        dataframe['strong_downtrend'] = (
            (dataframe['close'] < dataframe['sma_200']) &
            (dataframe['ema_20'] < dataframe['ema_50']) &
            (dataframe['ema_50'] < dataframe['ema_100']) &
            (dataframe['price_change_4h'] < -0.06) &  # 提高阈值
            (dataframe['trend_strength'] > 0.7)
        )
        
        # 市场波动率过滤
        dataframe['high_volatility'] = dataframe['volatility'] > self.market_volatility_threshold.value
        
        # 流动性评估
        dataframe['good_liquidity'] = (
            (dataframe['volume_ratio'] > 1.2) &
            (dataframe['volume_trend'] > 0.9)
        )
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        v1.1 入场信号生成 - 更严格的信号筛选
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''
        
        # === 基础市场环境过滤 ===
        market_filter = (
            dataframe['good_liquidity'] &
            (~dataframe['high_volatility'] | (dataframe['volume_ratio'] > 3.0))  # 高波动时需要更高成交量
        )
        
        # === 做多入场条件 - 更严格 ===
        # Priority Long Signal (最高优先级) - 大幅提高要求
        priority_long = (
            (dataframe['long_signal_quality'] >= self.priority_signal_threshold.value * 100) &
            (dataframe['rsi_oversold_rising']) &
            (dataframe['support_bounce']) &
            (dataframe['volume_confirms_bounce']) &
            (dataframe['price_1h_zscore'] < -2.5) &  # 更严格的Z-score要求
            (~dataframe['strong_downtrend']) &
            (dataframe['trend_strength'] > 0.6) &  # 趋势强度确认
            market_filter
        )
        
        # Standard Long Signal (标准信号) - 提高要求
        standard_long = (
            (dataframe['long_signal_quality'] >= self.min_signal_strength.value * 100) &
            (dataframe['rsi_oversold_rising'] | dataframe['support_bounce']) &
            (dataframe['volume_confirms_bounce']) &
            (dataframe['price_1h_zscore'] < -2) &  # 提高Z-score要求
            (~dataframe['strong_downtrend']) &
            (dataframe['bullish_momentum'] | (dataframe['trend_strength'] > 0.5)) &
            market_filter
        )
        
        # === 做空入场条件 - 更严格 ===
        # Priority Short Signal (最高优先级) - 大幅提高要求
        priority_short = (
            (dataframe['short_signal_quality'] >= self.priority_signal_threshold.value * 100) &
            (dataframe['rsi_overbought_declining']) &
            (dataframe['resistance_rejection']) &
            (dataframe['volume_confirms_weakness']) &
            (dataframe['price_1h_zscore'] > 2.5) &  # 更严格的Z-score要求
            (~dataframe['strong_uptrend']) &
            (dataframe['trend_strength'] > 0.6) &  # 趋势强度确认
            market_filter
        )
        
        # Standard Short Signal (标准信号) - 提高要求
        standard_short = (
            (dataframe['short_signal_quality'] >= self.min_signal_strength.value * 100) &
            (dataframe['rsi_overbought_declining'] | dataframe['resistance_rejection']) &
            (dataframe['volume_confirms_weakness']) &
            (dataframe['price_1h_zscore'] > 2) &  # 提高Z-score要求
            (~dataframe['strong_uptrend']) &
            (dataframe['bearish_momentum'] | (dataframe['trend_strength'] > 0.5)) &
            market_filter
        )
        
        # === 信号确认机制 - 新增 ===
        # 确保信号在连续几个周期内保持一致
        for i in range(1, self.signal_confirmation_period.value):
            priority_long &= (dataframe['long_signal_quality'].shift(i) >= 70)
            priority_short &= (dataframe['short_signal_quality'].shift(i) >= 70)
        
        # === 设置信号 ===
        dataframe.loc[priority_long, ['enter_long', 'enter_tag']] = (1, 'priority_long_v1.1')
        dataframe.loc[priority_short, ['enter_short', 'enter_tag']] = (1, 'priority_short_v1.1')
        
        dataframe.loc[standard_long & ~priority_long, ['enter_long', 'enter_tag']] = (1, 'standard_long_v1.1')
        dataframe.loc[standard_short & ~priority_short, ['enter_short', 'enter_tag']] = (1, 'standard_short_v1.1')
        
        # === 多空信号冲突处理 - 优化 ===
        conflict_mask = (dataframe['enter_long'] == 1) & (dataframe['enter_short'] == 1)
        
        # 选择信号质量更高的，如果差距不大则都取消
        quality_diff = abs(dataframe['long_signal_quality'] - dataframe['short_signal_quality'])
        
        dataframe.loc[conflict_mask & (quality_diff < 10), ['enter_long', 'enter_short']] = 0  # 质量差距小于10分则都取消
        dataframe.loc[conflict_mask & (quality_diff >= 10) & (dataframe['long_signal_quality'] > dataframe['short_signal_quality']), 'enter_short'] = 0
        dataframe.loc[conflict_mask & (quality_diff >= 10) & (dataframe['short_signal_quality'] > dataframe['long_signal_quality']), 'enter_long'] = 0
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        v1.1 出场信号生成 - 优化出场时机
        """
        # 初始化出场信号
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = ''
        
        # === 做多出场条件 - 优化 ===
        # 利润锁定出场 - 更积极
        long_profit_exit = (
            (dataframe['rsi'] > 65) &  # 降低RSI要求
            (dataframe['price_change_1h'] < -0.015) &  # 降低回调要求
            (dataframe['volume_ratio'] > 1.8) &  # 提高成交量要求
            (dataframe['long_signal_quality'] < 40)  # 信号质量下降
        )
        
        # 趋势反转出场 - 更敏感
        long_reversal_exit = (
            (dataframe['bearish_momentum']) &
            (dataframe['rsi_overbought_declining']) &
            (dataframe['volume_confirms_weakness']) &
            (dataframe['trend_strength'] > 0.5)  # 确保是强趋势反转
        )
        
        # === 做空出场条件 - 优化 ===
        # 利润锁定出场 - 更积极
        short_profit_exit = (
            (dataframe['rsi'] < 35) &  # 提高RSI要求
            (dataframe['price_change_1h'] > 0.015) &  # 降低反弹要求
            (dataframe['volume_ratio'] > 1.8) &  # 提高成交量要求
            (dataframe['short_signal_quality'] < 40)  # 信号质量下降
        )
        
        # 趋势反转出场 - 更敏感
        short_reversal_exit = (
            (dataframe['bullish_momentum']) &
            (dataframe['rsi_oversold_rising']) &
            (dataframe['volume_confirms_bounce']) &
            (dataframe['trend_strength'] > 0.5)  # 确保是强趋势反转
        )
        
        # 设置出场信号
        dataframe.loc[long_profit_exit, ['exit_long', 'exit_tag']] = (1, 'long_profit_v1.1')
        dataframe.loc[long_reversal_exit, ['exit_long', 'exit_tag']] = (1, 'long_reversal_v1.1')
        
        dataframe.loc[short_profit_exit, ['exit_short', 'exit_tag']] = (1, 'short_profit_v1.1')
        dataframe.loc[short_reversal_exit, ['exit_short', 'exit_tag']] = (1, 'short_reversal_v1.1')
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        v1.1 动态止损 - 更智能的止损管理
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return self.stoploss
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 基础止损
        base_stoploss = self.stoploss
        
        # 根据波动性调整止损 - 优化
        if 'volatility' in current_candle:
            volatility = current_candle['volatility']
            if volatility > 0.06:  # 高波动
                base_stoploss = base_stoploss * 1.3  # 放宽止损
            elif volatility < 0.02:  # 低波动
                base_stoploss = base_stoploss * 0.8  # 收紧止损
        
        # 根据信号质量调整止损
        if trade.is_short:
            signal_quality = current_candle.get('short_signal_quality', 50)
            if current_profit > 0.08:  # 8%以上利润
                return -0.02  # 收紧至2%
            elif current_profit > 0.04:  # 4%以上利润
                return -0.05  # 收紧至5%
            elif signal_quality < 30:  # 信号质量下降
                return base_stoploss * 0.7  # 收紧止损
        else:
            signal_quality = current_candle.get('long_signal_quality', 50)
            if current_profit > 0.08:  # 8%以上利润
                return -0.02  # 收紧至2%
            elif current_profit > 0.04:  # 4%以上利润
                return -0.05  # 收紧至5%
            elif signal_quality < 30:  # 信号质量下降
                return base_stoploss * 0.7  # 收紧止损
        
        return base_stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], 
                side: str, **kwargs) -> float:
        """
        v1.1 动态杠杆管理 - 更保守的杠杆策略
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return 1.0
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 基础杠杆 - 降低
        base_leverage = 2.5  # 从3.0降低到2.5
        
        # 根据信号类型调整杠杆 - 更保守
        if entry_tag:
            if 'priority' in entry_tag:
                leverage_multiplier = 1.0  # 最高质量信号
            elif 'standard' in entry_tag:
                leverage_multiplier = 0.7  # 标准信号更保守
            else:
                leverage_multiplier = 0.5
        else:
            leverage_multiplier = 0.5
        
        # 根据波动性调整 - 更严格
        if 'volatility' in current_candle:
            volatility = current_candle['volatility']
            if volatility > 0.06:  # 高波动
                leverage_multiplier *= 0.6  # 大幅降低杠杆
            elif volatility > 0.04:  # 中等波动
                leverage_multiplier *= 0.8
            elif volatility < 0.02:  # 低波动
                leverage_multiplier *= 1.1
        
        final_leverage = min(max_leverage, base_leverage * leverage_multiplier)
        return max(1.0, final_leverage)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                           side: str, **kwargs) -> bool:
        """
        v1.1 交易确认 - 更严格的最终检查
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return False
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 流动性检查 - 更严格
        if 'volume_ratio' in current_candle:
            if current_candle['volume_ratio'] < 1.5:  # 提高最低成交量要求
                return False
        
        # 波动率检查
        if 'volatility' in current_candle:
            if current_candle['volatility'] > 0.08:  # 过高波动率拒绝
                return False
        
        # 信号质量最终检查 - 更严格
        if side == 'long':
            if 'long_signal_quality' in current_candle:
                if current_candle['long_signal_quality'] < self.min_signal_strength.value * 100:
                    return False
                # 额外检查：确保不是在强下跌趋势中
                if current_candle.get('strong_downtrend', False):
                    return False
        else:  # short
            if 'short_signal_quality' in current_candle:
                if current_candle['short_signal_quality'] < self.min_signal_strength.value * 100:
                    return False
                # 额外检查：确保不是在强上涨趋势中
                if current_candle.get('strong_uptrend', False):
                    return False
        
        # 趋势强度检查
        if 'trend_strength' in current_candle:
            if current_candle['trend_strength'] < 0.3:  # 趋势太弱
                return False
        
        return True

    def informative_pairs(self):
        """
        定义需要的额外数据对
        """
        return []