# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, stoploss_from_open
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime
from freqtrade.persistence import Trade
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy

# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging
import pytz

log = logging.getLogger(__name__)


class PumpAndDumpLongShort1h(IStrategy):
    """
    ## Pump and Dump Long/Short Strategy (1h-Only Version) V1.6

    **作者:** Gemini 2.5 Pro & User
    **版本:** 1.6
    **核心理念:**
    - V1.6 (Profit-locking TSL): 重大重构止损逻辑。追踪止损(TSL)现在只在能够锁定确定利润（即止损价优于开仓价）时才会激活。这修复了TSL在盈利刚超过阈值后，因微小回调导致亏损离场（被“洗出场”）的问题，完美实现了“硬止损为底线，追踪止损为利润保护”的设计意图。
    - V1.5 (Cooldown Logic Unification): 统一并优化了冷却期逻辑，确保其在所有环境中都能稳健运行，从根本上解决信号丢失问题。
    - V1.4 (Compatibility Fix): 兼容旧版 Freqtrade，通过手动计算最大利润来修复 `trade.max_profit` 不存在的属性错误。
    - V1.3 (Pytz Fix): 改用 `pytz` 库处理时区，以修复在旧版 `pandas` 环境中出现的 `tz` 参数错误，增强兼容性。
    - V1.2 (Column Name Fix): 修复了 'populate_indicators' 中一个因列名错误 ('range_1d' vs 'range_1h') 导致的致命BUG，该BUG是 `void()` 错误的根源。
    - V1.1 (Cooldown Fix): 重构了冷却期逻辑，避免在循环中修改DataFrame，以修复一个可能导致底层数据错误的罕见Bug。
    - 基于 PumpAndDumpShort_1h V2.4 版本构建。
    - **做空逻辑:** 完全继承自原版策略，旨在捕捉价格暴涨后的力竭反转机会。
    - **做多逻辑:** 与做空逻辑完全相反，旨在捕捉价格暴跌（恐慌抛售）后的反弹机会。
    - **统一止损:** `custom_stoploss` 现已全面升级，可同时为做多和做空交易提供初始硬止损、追踪止损和杠杆感知后备止损。

    **时间框架:** 1小时 (1h)
    
    ---

    ### 做空入场条件:
    1.  **力竭信号 (前一根K线):** "高潮K线" - 连续量价齐升、RSI超买、射击之星形态、成交量和振幅激增。
    2.  **入场确认 (当前K线):** 下跌的"确认K线"，或在高潮与确认之间出现一根"停顿K线"。

    ### 做多入场条件:
    1.  **恐慌信号 (前一根K线):** "恐慌K线" - 连续量价齐跌、RSI超卖、锤子线形态、成交量和振幅激增。
    2.  **入场确认 (当前K线):** 上涨的"确认K线"，或在恐慌与确认之间出现一根"停顿K线"。
    
    ---

    **出场条件 (平仓):**
    1.  **统一动态止损 (Unified Custom Stop Loss):**
        - **初始止损:** 做空为止损回看期内的最高点；做多为最低点。
        - **追踪止损:** 利润超过阈值后自动激活，保护利润。
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '1h'
    can_short = True
    process_only_new_candles = True

    # --- 启动和回测所需的数据量 ---
    startup_candle_count: int = 40

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }

    # Optional plot configuration
    plot_config = {
        'main_plot': {},
        'subplots': {
            "Signals": {
                'climax_candle': {'color': '#FFDD32', 'type': 'bar', 'name': 'Climax Candle (Short)'},
                'panic_candle': {'color': '#FF00FF', 'type': 'bar', 'name': 'Panic Candle (Long)'},
                'pause_candle_short': {'color': '#800080', 'type': 'bar', 'name': 'Pause (Short)'},
                'pause_candle_long': {'color': '#4B0082', 'type': 'bar', 'name': 'Pause (Long)'}
            },
            "Conditions Met": {
                'climax_conditions_met': {'color': 'purple', 'type': 'bar', 'name': 'Short Conditions'},
                'panic_conditions_met': {'color': 'magenta', 'type': 'bar', 'name': 'Long Conditions'},
            },
            "RSI": {
                'rsi': {'color': 'orange'},
            }
        }
    }

    # --- 止损和止盈设置 ---
    stoploss = -0.10 
    use_custom_stoploss = True
    trailing_stop = False

    # =========================================================================================================
    # --- HYPEROPT PARAMETERS ---
    # =========================================================================================================

    # --- 做空 (Short) 参数 ---
    short_consecutive_green_candles = IntParameter(2, 4, default=3, space="buy", optimize=True)
    short_pump_rsi_threshold = IntParameter(60, 80, default=65, space="buy", optimize=True)
    short_upper_wick_body_ratio = DecimalParameter(1.2, 1.8, default=1.3, decimals=1, space="buy", optimize=True)
    short_climax_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    short_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    short_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    short_min_climax_conditions = IntParameter(3, 5, default=4, space="buy", optimize=True)
    
    # --- 做多 (Long) 参数 (与做空相反) ---
    long_consecutive_red_candles = IntParameter(2, 4, default=3, space="buy", optimize=True)
    long_dump_rsi_threshold = IntParameter(20, 40, default=35, space="buy", optimize=True)
    long_lower_wick_body_ratio = DecimalParameter(1.2, 1.8, default=1.3, decimals=1, space="buy", optimize=True)
    long_panic_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    long_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    long_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    long_min_panic_conditions = IntParameter(3, 5, default=4, space="buy", optimize=True)

    # --- 通用风险管理和冷却参数 ---
    # Sell-side parameters
    stoploss_lookback = IntParameter(5, 20, default=10, space="sell", optimize=True)
    tsl_positive = DecimalParameter(0.005, 0.02, default=0.008, decimals=3, space="sell", optimize=True)
    tsl_offset = DecimalParameter(0.01, 0.03, default=0.015, decimals=3, space="sell", optimize=True)
    leverage_stop_margin = DecimalParameter(0.80, 0.95, default=0.9, decimals=2, space="sell", optimize=True)
    
    # Cooldown (applied to both long and short)
    cooldown_period = IntParameter(6, 48, default=12, space="buy", optimize=True)
    
    # 杠杆设置
    leverage_optimize = CategoricalParameter([1.0, 2.0, 3.0, 5.0, 10.0], default=1.0, space="buy", optimize=True, load=True)

    def __init__(self, config: dict) -> None:
        super().__init__(config)
        # V1.1: last_trade_time 现在存储 datetime 对象
        self.last_trade_time: dict[str, datetime] = {}

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 安全检查：确保DataFrame不为空
        if dataframe.empty:
            return dataframe
            
        # 创建一个新的DataFrame进行操作，避免就地修改
        result_df = dataframe.copy()
        
        result_df['is_green_candle'] = (result_df['close'] > result_df['open']).astype(int)
        result_df['is_red_candle'] = (result_df['close'] < result_df['open']).astype(int)
        result_df['rsi'] = ta.RSI(result_df, timeperiod=14)

        # --- 做空信号 (Short Signals) ---
        result_df['volume_is_rising'] = result_df['volume'] > result_df['volume'].shift(1)
        result_df['green_with_rising_vol'] = (result_df['is_green_candle'] == 1) & (result_df['volume_is_rising'] == 1)
        result_df['consecutive_green_rising_vol'] = result_df['green_with_rising_vol'].rolling(
            window=self.short_consecutive_green_candles.value).sum()
        
        body = abs(result_df['close'] - result_df['open'])
        upper_wick = result_df['high'] - np.maximum(result_df['open'], result_df['close'])
        result_df['shooting_star'] = ((upper_wick > body * self.short_upper_wick_body_ratio.value) & (body > 0.000001)).astype('int')
        
        result_df['range_1h'] = result_df['high'] - result_df['low']
        short_range_sma = ta.SMA(result_df['range_1h'], timeperiod=self.short_climax_sma_period.value)
        short_volume_sma = ta.SMA(result_df['volume'], timeperiod=self.short_climax_sma_period.value)
        
        result_df['short_range_spike'] = (result_df['range_1h'] > short_range_sma * self.short_range_spike_multiplier.value).astype('int')
        result_df['short_volume_spike'] = (result_df['volume'] > short_volume_sma * self.short_volume_spike_multiplier.value).astype('int')
        
        short_trend_cond = (result_df['consecutive_green_rising_vol'] >= self.short_consecutive_green_candles.value).astype('int')
        short_rsi_cond = (result_df['rsi'] >= self.short_pump_rsi_threshold.value).astype('int')
        
        result_df['climax_conditions_met'] = (short_trend_cond + short_rsi_cond + result_df['shooting_star'] + result_df['short_volume_spike'] + result_df['short_range_spike'])
        result_df['climax_candle'] = (result_df['climax_conditions_met'] >= self.short_min_climax_conditions.value).astype('int')

        is_shrinking_volume = result_df['volume'] < result_df['volume'].shift(1)
        is_lower_high = result_df['high'] <= result_df['high'].shift(1)
        result_df['pause_candle_short'] = ((result_df['is_green_candle'] == 1) & (is_shrinking_volume == 1) & (is_lower_high == 1)).astype('int')

        # --- 做多信号 (Long Signals) ---
        result_df['red_with_rising_vol'] = (result_df['is_red_candle'] == 1) & (result_df['volume_is_rising'] == 1)
        result_df['consecutive_red_rising_vol'] = result_df['red_with_rising_vol'].rolling(
            window=self.long_consecutive_red_candles.value).sum()

        lower_wick = np.minimum(result_df['open'], result_df['close']) - result_df['low']
        result_df['hammer'] = ((lower_wick > body * self.long_lower_wick_body_ratio.value) & (body > 0.000001)).astype('int')

        long_range_sma = ta.SMA(result_df['range_1h'], timeperiod=self.long_panic_sma_period.value)
        long_volume_sma = ta.SMA(result_df['volume'], timeperiod=self.long_panic_sma_period.value)

        result_df['long_range_spike'] = (result_df['range_1h'] > long_range_sma * self.long_range_spike_multiplier.value).astype('int')
        result_df['long_volume_spike'] = (result_df['volume'] > long_volume_sma * self.long_volume_spike_multiplier.value).astype('int')
        
        long_trend_cond = (result_df['consecutive_red_rising_vol'] >= self.long_consecutive_red_candles.value).astype('int')
        long_rsi_cond = (result_df['rsi'] <= self.long_dump_rsi_threshold.value).astype('int')

        result_df['panic_conditions_met'] = (long_trend_cond + long_rsi_cond + result_df['hammer'] + result_df['long_volume_spike'] + result_df['long_range_spike'])
        result_df['panic_candle'] = (result_df['panic_conditions_met'] >= self.long_min_panic_conditions.value).astype('int')
        
        is_higher_low = result_df['low'] >= result_df['low'].shift(1)
        result_df['pause_candle_long'] = ((result_df['is_red_candle'] == 1) & (is_shrinking_volume == 1) & (is_higher_low == 1)).astype('int')

        return result_df

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 创建一个空的 DataFrame，复制原始数据帧的结构但不包含任何数据
        result_df = dataframe.copy()
        
        # 初始化入场信号列
        result_df['enter_short'] = 0
        result_df['enter_long'] = 0
        result_df['enter_tag'] = ''
        
        pair = metadata['pair']
        cooldown_seconds = self.cooldown_period.value * 3600
        
        # --- 冷却期逻辑 ---
        last_trade_time = self.last_trade_time.get(pair)
        if last_trade_time is not None and \
           (dataframe['date'].max() - last_trade_time).total_seconds() < cooldown_seconds:
            return result_df
        
        # --- 做空入场逻辑 ---
        short_trigger_1 = (dataframe['climax_candle'].shift(1) == 1) & (dataframe['is_red_candle'] == 1)
        short_trigger_2 = (dataframe['climax_candle'].shift(2) == 1) & (dataframe['pause_candle_short'].shift(1) == 1) & (dataframe['is_red_candle'] == 1)
        
        # 标记符合条件的行
        short_mask = short_trigger_1 | short_trigger_2
        result_df.loc[short_mask, 'enter_short'] = 1
        
        # --- 做多入场逻辑 ---
        long_trigger_1 = (dataframe['panic_candle'].shift(1) == 1) & (dataframe['is_green_candle'] == 1)
        long_trigger_2 = (dataframe['panic_candle'].shift(2) == 1) & (dataframe['pause_candle_long'].shift(1) == 1) & (dataframe['is_green_candle'] == 1)
        
        # 标记符合条件的行
        long_mask = long_trigger_1 | long_trigger_2
        result_df.loc[long_mask, 'enter_long'] = 1
        
        # 更新入场标签
        result_df.loc[short_mask, 'enter_tag'] = 'short_pump_dump_1h_v1.6'
        result_df.loc[long_mask, 'enter_tag'] = 'long_dump_dip_1h_v1.6'

        # 如果有新的入场信号，则更新最后交易时间
        if result_df['enter_short'].sum() > 0 or result_df['enter_long'].sum() > 0:
            try:
                if short_mask.any():
                    self.last_trade_time[pair] = dataframe.loc[short_mask, 'date'].iloc[-1]
                elif long_mask.any():
                    self.last_trade_time[pair] = dataframe.loc[long_mask, 'date'].iloc[-1]
            except (IndexError, KeyError) as e:
                # 防止任何可能的错误
                pass
                
        return result_df

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 创建一个结果DataFrame
        result_df = dataframe.copy()
        # 初始化退出信号
        result_df['exit_short'] = 0
        result_df['exit_long'] = 0
        return result_df

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        leverage_fallback_stop_price = self.calculate_leverage_fallback_stop(trade)

        # 确保所有必要的变量都存在
        if dataframe is None or dataframe.empty:
            return leverage_fallback_stop_price
        
        if trade is None or trade.open_date is None:
            return leverage_fallback_stop_price

        # V1.3: 使用 pytz 修复时区比较错误，兼容旧版 pandas
        try:
            trade_open_date_utc = pytz.utc.localize(trade.open_date)
            candles_before_trade = dataframe[dataframe['date'] < trade_open_date_utc]
        except (TypeError, ValueError, AttributeError):
            # 如果时区处理出现问题，使用备用方案
            return leverage_fallback_stop_price
            
        if candles_before_trade.empty:
            return leverage_fallback_stop_price

        signal_candle_index = candles_before_trade.index[-1]
        lookback_period = self.stoploss_lookback.value
        start_index = max(0, signal_candle_index - lookback_period + 1)
        end_index = signal_candle_index + 1

        if start_index >= end_index:
            return leverage_fallback_stop_price
            
        lookback_df = dataframe.iloc[start_index:end_index]
        if lookback_df.empty:
            return leverage_fallback_stop_price
        
        # --- 根据交易方向进行不同的止损计算 ---
        # V1.4: 兼容旧版 Freqtrade，手动计算 max_profit
        max_profit = 0.0
        if trade.is_short:
            if hasattr(trade, 'min_rate') and trade.min_rate is not None:
                max_profit = (trade.open_rate - trade.min_rate) / trade.open_rate
        else:  # Long
            if hasattr(trade, 'max_rate') and trade.max_rate is not None:
                max_profit = (trade.max_rate - trade.open_rate) / trade.open_rate
        
        if trade.is_short:
            # --- 做空止损逻辑 ---
            hard_stop_price = lookback_df['high'].max()
            if max_profit > self.tsl_positive.value:
                lowest_rate = getattr(trade, 'min_rate', None) or current_rate
                trailing_stop_price = lowest_rate * (1 + self.tsl_offset.value)
                # 只有在追踪止损能够锁定利润时（止损价低于开仓价），才考虑收紧止损
                if trailing_stop_price < trade.open_rate:
                    # 在硬止损和盈利追踪止损之间，选择更严格（价格更低）的那个
                    return min(hard_stop_price, trailing_stop_price)
            # 如果不满足盈利追踪条件，则坚守初始硬止损
            return hard_stop_price
        else:
            # --- 做多止损逻辑 ---
            hard_stop_price = lookback_df['low'].min()
            if max_profit > self.tsl_positive.value:
                highest_rate = getattr(trade, 'max_rate', None) or current_rate
                trailing_stop_price = highest_rate * (1 - self.tsl_offset.value)
                # 只有在追踪止损能够锁定利润时（止损价高于开仓价），才考虑收紧止损
                if trailing_stop_price > trade.open_rate:
                    # 在硬止损和盈利追踪止损之间，选择更严格（价格更高）的那个
                    return max(hard_stop_price, trailing_stop_price)
            # 如果不满足盈利追踪条件，则坚守初始硬止损
            return hard_stop_price

    def calculate_leverage_fallback_stop(self, trade: 'Trade') -> float:
        stop_pct = abs(self.stoploss)
        leverage_val = self.leverage_optimize.value
        if leverage_val > 1:
            liquidation_pct = (1 / leverage_val)
            stop_pct = liquidation_pct * self.leverage_stop_margin.value
        
        if trade.is_short:
            stoploss_price = trade.open_rate * (1 + stop_pct)
        else:
            stoploss_price = trade.open_rate * (1 - stop_pct)
            
        return stoploss_price

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:
        return self.leverage_optimize.value


class PumpAndDumpLongShort1hHyperopt(PumpAndDumpLongShort1h):
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return super().populate_indicators(dataframe, metadata)
        
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return super().populate_entry_trend(dataframe, metadata)
        
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return super().populate_exit_trend(dataframe, metadata)
        
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        return super().custom_stoploss(pair, trade, current_time, current_rate, current_profit, **kwargs)
        
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                **kwargs) -> float:
        return super().leverage(pair, current_time, current_rate, proposed_leverage, max_leverage, entry_tag, side, **kwargs) 