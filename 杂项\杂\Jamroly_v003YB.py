import numpy as np
import pandas as pd
from pandas import DataFrame, Series
from typing import Optional
from datetime import datetime
import math

from freqtrade.strategy import (DecimalParameter, IStrategy, IntParameter, informative)
from freqtrade.persistence import Trade, Order

# --------------------------------
# Add your lib to import here
import talib.abstract as ta

import logging
logger = logging.getLogger(__name__)

# Ignore Performance Warnings
from warnings import simplefilter
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)
simplefilter(action="ignore", category=FutureWarning)


class Jamroly_v003YB(IStrategy):
    """
    ATR-based DCA strategy with partial exits. Experimental strategy for testing only.
    """
    INTERFACE_VERSION = 3

    def version(self) -> str:
        return "v0.0.3YB-M1"

    # Strategy settings
    can_short = True
    timeframe = '3m'
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False
    startup_candle_count: int = 480
    use_custom_stoploss = False

    ############################################################################
    # Strategy Parameters and Hyperopt Spaces
    ############################################################################

    # Buy hyperspace params
    buy_params = {
        "leverage": 5,
    }

    # Sell hyperspace params
    sell_params = {
    }

    # ROI table
    minimal_roi = {
        "0": 100,  # Disable ROI
    }

    # Stoploss
    stoploss = -9.99

    # Trailing stop
    trailing_stop = False
    trailing_stop_positive = 0.001
    trailing_stop_positive_offset = 0.028
    trailing_only_offset_is_reached = True

    ############################################################################
    # Hyperopt Parameters
    ############################################################################

    # Entry parameters
    lev = IntParameter(0, 25, default=buy_params['leverage'], space='buy', optimize=False)

    # Exit parameters

    # Custom dict for plotting purposes
    custom_info = {}

    ############################################################################
    # DCA Parameters and Calculations
    ############################################################################

    # Enable position adjustment
    position_adjustment_enable = True

    # ATR-based parameters
    initial_so_atr_multiplier = DecimalParameter(1.0, 3.0, default=3.0, space='buy', optimize=True)
    max_safety_orders = 4 #4
    safety_order_step_scale = 1.36 #2          # SS - applied to ATR multiplier
    safety_order_volume_scale = 1.42 #1.8      # OS - applied to order size
    base_order_safety_order_ratio = 1    # BO:SO ratio

    # Calculate max DCA multiplier for stake sizing
    max_dca_multiplier = (1 + max_safety_orders)
    if (max_safety_orders > 0):
        if (safety_order_volume_scale > 1):
            max_dca_multiplier = 1 + (1 - math.pow(safety_order_volume_scale, max_safety_orders)) / (1 - safety_order_volume_scale) * base_order_safety_order_ratio
        elif (safety_order_volume_scale < 1):
            max_dca_multiplier = 1 + (1 - math.pow(safety_order_volume_scale, max_safety_orders)) / (1 - safety_order_volume_scale) / base_order_safety_order_ratio

    ############################################################################
    # Plot Config
    ############################################################################

    @property
    def plot_config(self):
        """
        Customize indicators plotted on FreqUI
        """
        plot_config = {
            'main_plot': {
                # Exit levels (blue shades)
                'exit_1': {'color': '#7CA1CC'},  # Light blue
                'exit_2': {'color': '#4A7AB8'},  # Medium blue
                'exit_3': {'color': '#1853A4'},  # Dark blue
                
                # Safety Order levels (red shades)
                'so_long_1': {'color': '#FFB6B6'},  # Light red
                'so_long_2': {'color': '#FF8080'},  # Medium red
                'so_long_3': {'color': '#FF4D4D'},  # Dark red
                'so_long_4': {'color': '#FF4D4D'},  # Dark red
                'so_short_1': {'color': '#FFB6B6'},  # Light red
                'so_short_2': {'color': '#FF8080'},  # Medium red
                'so_short_3': {'color': '#FF4D4D'},  # Dark red
                'so_short_4': {'color': '#FF4D4D'},  # Dark red
                
                # Break-even price
                'break_even_price': {'color': '#FFD700'},  # Gold
                
            },
            'subplots': {
                # ATR subplot
                "ATR": {
                    'atr': {'color': '#FF69B4'},       # Pink
                    'atr_mean': {'color': '#DB7093'},  # Pale violet red
                },
                # Returns subplot
                "Returns": {
                    'returns_roll_mean_cumsum': {'color': '#4169E1'},         # Royal blue
                    'returns_roll_mean_cumsum_upper': {'color': '#98FB98'},   # Pale green
                    'returns_roll_mean_cumsum_lower': {'color': '#FFA07A'},   # Light salmon
                }
            }
        }
        return plot_config

    ############################################################################
    # Helper Methods for DCA and Exits
    ############################################################################

    def calculate_exit_levels(self, trade: Trade, atr_value: float) -> dict:
        """
        Calculate ATR-based exit levels for both partial and full exits
        """
        multiplier = -1 if trade.is_short else 1
        return {
            'exit_1': trade.open_rate * (1 + multiplier * 1.5 * (atr_value/trade.open_rate)),  # First partial (33%)
            'exit_2': trade.open_rate * (1 + multiplier * 2.5 * (atr_value/trade.open_rate)),  # Second partial (50% of remaining)
            'exit_3': trade.open_rate * (1 + multiplier * 3.0 * (atr_value/trade.open_rate))   # Full exit
        }

    def calculate_so_levels(self, trade: Trade, atr_value: float, filled_entries: list) -> dict:
        """
        Calculate ATR-based safety order levels
        """
        initial_trigger = self.initial_so_atr_multiplier.value * (atr_value / filled_entries[0].price)
        so_levels = {}
        
        for i in range(1, self.max_safety_orders + 1):
            if self.safety_order_step_scale > 1:
                so_trigger = initial_trigger * (
                    1 + self.safety_order_step_scale * (
                        math.pow(self.safety_order_step_scale, (i - 1)) - 1
                    ) / (self.safety_order_step_scale - 1)
                )
            else:
                so_trigger = initial_trigger * (
                    1 + self.safety_order_step_scale * (
                        1 - math.pow(self.safety_order_step_scale, (i - 1))
                    ) / (1 - self.safety_order_step_scale)
                )
            
            if trade.is_short:
                so_price = filled_entries[0].price * (1 + so_trigger)
                key = f'so_short_{i}'
            else:
                so_price = filled_entries[0].price * (1 - so_trigger)
                key = f'so_long_{i}'
                
            so_levels[key] = so_price
        
        return so_levels

    ############################################################################
    # Indicator Calculations
    ############################################################################

    @informative('15m', 'BTC/USDT:USDT')
    def populate_indicators_btc_15m(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        dataframe['returns'] = np.log(dataframe['close']/dataframe['close'].shift())

        periods = [4, 6, 8, 16, 32, 64, 128]
        for t in periods:
            t = int(t)
            # returns mean
            dataframe[f'returns_roll_{t}'] = dataframe['returns'].rolling(t).mean()

        # returns [-1,1]
        dataframe['returns_roll_mean'] = dataframe[[f'returns_roll_{t}' for t in periods]].mean(axis=1)
        dataframe['returns_roll_mean_cumsum'] = dataframe['returns_roll_mean'].rolling(4).sum()
        dataframe['returns_roll_mean_cumsum_upper'] = dataframe['returns_roll_mean_cumsum'].rolling(480).mean() + dataframe['returns_roll_mean_cumsum'].rolling(480).std() * 3.5
        dataframe['returns_roll_mean_cumsum_lower'] = dataframe['returns_roll_mean_cumsum'].rolling(480).mean() - dataframe['returns_roll_mean_cumsum'].rolling(480).std() * 3.5
        
        # New BTC safety conditions
        dataframe['trending_down_sharply'] = dataframe['returns_roll_mean_cumsum'] < dataframe['returns_roll_mean_cumsum_lower']
        dataframe['trending_up_sharply'] = dataframe['returns_roll_mean_cumsum'] > dataframe['returns_roll_mean_cumsum_upper']

        return dataframe


    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame

        Performance Note: For the best performance be frugal on the number of indicators
        you are using. Let uncomment only the indicator you are using in your strategies
        or your hyperopt configuration, otherwise you will waste your memory and CPU usage.
        :param dataframe: Dataframe with data from the exchange
        :param metadata: Additional information, like the currently traded pair
        :return: a Dataframe with all mandatory indicators for the strategies
        """

        # Add data from custom_info to the main dataframe for plotting purposes
        if metadata["pair"] in self.custom_info:
            # Add exit levels
            for key in ['exit_1', 'exit_2', 'exit_3']:
                if key in self.custom_info[metadata["pair"]]:
                    dataframe[key] = self.custom_info[metadata["pair"]][key]
            
            # Add SO levels
            for i in range(1, self.max_safety_orders + 1):
                for direction in ['long', 'short']:
                    so_key = f'so_{direction}_{i}'
                    if so_key in self.custom_info[metadata["pair"]]:
                        dataframe[so_key] = self.custom_info[metadata["pair"]][so_key]
            
            # Add break-even price
            dataframe['break_even_price'] = self.custom_info[metadata["pair"]]['break_even_price']

        ###############################
        ######### Constants ###########
        ###############################

        dataframe['const_0'] = 0

        ###############################
        ##### ATR FOR CUSTOM EXIT #####
        ###############################

        # Calculate ATR for custom_exit and SO levels
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=120)
        dataframe['atr_short'] = ta.ATR(dataframe, timeperiod=20)
        dataframe['atr_mean'] = ta.SMA(dataframe['atr_short'], timeperiod=480)  # 20-day mean of ATR

        ##############################
        ######## Protections #########
        ##############################

        # YangZhang Volatility Estimator Prep
        dataframe['log_ho'] = np.log(dataframe['high'] / dataframe['open'])
        dataframe['log_lo'] = np.log(dataframe['low'] / dataframe['open'])
        dataframe['log_co'] = np.log(dataframe['close'] / dataframe['open'])
        dataframe['log_oc'] = np.log(dataframe['open'] / dataframe['close'].shift(1))
        dataframe['log_oc_sq'] = dataframe['log_oc']**2
        dataframe['log_cc'] = np.log(dataframe['close'] / dataframe['close'].shift(1))
        dataframe['log_cc_sq'] = dataframe['log_cc']**2
        dataframe['rs'] = dataframe['log_ho'] * (dataframe['log_ho'] - dataframe['log_co']) + dataframe['log_lo'] * (dataframe['log_lo'] - dataframe['log_co'])

        dataframe['returns'] = np.log(dataframe['close']/dataframe['close'].shift())

        periods = [4, 6, 8, 16, 32, 64, 128]
        for t in periods:
            t = int(t)
            # volume median [0,1]
            dataframe[f'vol_med_{t}'] = dataframe['volume'].rolling(t).median()
            # Yang Zhang Volatility Estimator [0,1]
            dataframe[f'close_vol_{t}'] = dataframe['log_cc_sq'].rolling(t).sum() * (1.0 / (t - 1.0))
            dataframe[f'open_vol_{t}'] = dataframe['log_oc_sq'].rolling(t).sum() * (1.0 / (t - 1.0))
            dataframe[f'window_rs_{t}'] = dataframe['rs'].rolling(t).sum() * (1.0 / (t - 1.0))
            # Returns rolling mean
            dataframe[f'returns_roll_{t}'] = dataframe['returns'].rolling(t).mean()

        # volume median [0,1]
        dataframe['vol_med_mean'] = dataframe[[f'vol_med_{t}' for t in periods]].mean(axis=1)
        dataframe['norm_vol_med_mean'] = indicator_normalization(dataframe, col='vol_med_mean', length=480, norm_range='zero_to_one')

        # YangZhang Volatility Estimator [0,1]
        dataframe['close_vol_mean'] = dataframe[[f'close_vol_{t}' for t in periods]].mean(axis=1)
        dataframe['open_vol_mean'] = dataframe[[f'open_vol_{t}' for t in periods]].mean(axis=1)
        dataframe['window_rs_mean'] = dataframe[[f'window_rs_{t}' for t in periods]].mean(axis=1)
        dataframe['yang_zhang_vol'] = np.sqrt(dataframe['open_vol_mean'] + 0.164333 * dataframe['close_vol_mean'] + 0.835667 * dataframe['window_rs_mean'])
        dataframe['yang_zhang_vol'] = dataframe['yang_zhang_vol'].bfill()
        dataframe['norm_yang_zhang_vol'] = indicator_normalization(dataframe, col='yang_zhang_vol', length=480, norm_range='zero_to_one')

        # returns [-1,1] rolling mean + upper and lower bounds
        dataframe['returns_roll_mean'] = dataframe[[f'returns_roll_{t}' for t in periods]].mean(axis=1)
        dataframe['returns_roll_mean_cumsum'] = dataframe['returns_roll_mean'].rolling(4).sum()
        dataframe['returns_roll_mean_cumsum_upper'] = dataframe['returns_roll_mean_cumsum'].rolling(480).mean() + dataframe['returns_roll_mean_cumsum'].rolling(480).std() * 3.5
        dataframe['returns_roll_mean_cumsum_lower'] = dataframe['returns_roll_mean_cumsum'].rolling(480).mean() - dataframe['returns_roll_mean_cumsum'].rolling(480).std() * 3.5
        dataframe['returns_roll_mean_cumsum_save'] = (pd.Series(index=dataframe.index, data=(np.where(dataframe['returns_roll_mean_cumsum'].between(dataframe['returns_roll_mean_cumsum_lower'], dataframe['returns_roll_mean_cumsum_upper']), 1, 0))).rolling(4).sum() == 4)

        ##############################
        ##### Signal Indicators ######
        ##############################

        # RSI indicators
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['rsi_slow'] = ta.RSI(dataframe, timeperiod=21)
        dataframe['low_rsi'] = ta.RSI(dataframe['low'], timeperiod=14)
        dataframe['high_rsi'] = ta.RSI(dataframe['high'], timeperiod=14)

        # Moving Averages
        dataframe['sma_15'] = ta.SMA(dataframe, timeperiod=15)

        # Regression
        dataframe['regression_mid'] = ta.LINEARREG(dataframe['close'], timeperiod=14)
        dataframe['regression_upper'] = dataframe['regression_mid'] + 2 * ta.LINEARREG_SLOPE(dataframe['close'], timeperiod=14)
        dataframe['regression_lower'] = dataframe['regression_mid'] - 2 * ta.LINEARREG_SLOPE(dataframe['close'], timeperiod=14)

        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastk'] = stoch_fast['fastk']

        # Commodity Channel Index
        dataframe['cti'] = ta.CCI(dataframe, timeperiod=20)

        return dataframe

    ############################################################################
    # Entry and Exit Signals
    ############################################################################

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with entry columns populated
        """

        dataframe.loc[:, 'enter_tag'] = ''
        dataframe.loc[:, 'enter_long'] = 0
        dataframe.loc[:, 'enter_short'] = 0

        ##############################
        ##### LONG ENTRY SIGNALS #####
        ##############################

        # ----------------------------
        long_condition = (
            (dataframe['close'] < dataframe['regression_lower']) &
            (dataframe['low_rsi'] < 35) &
            (dataframe['rsi'] < dataframe['rsi_slow']) &
            (dataframe['fastk'] < 30) &
            (dataframe['close'] < dataframe['sma_15']) &
            (dataframe['cti'] < -0.4) &
            (~dataframe['btc_usdt_trending_down_sharply_15m']) &
            (dataframe['norm_yang_zhang_vol'] < 0.75) &
            (dataframe['volume'] > 0)
        )
        dataframe.loc[long_condition, 'enter_long'] = 1
        dataframe.loc[long_condition, 'enter_tag'] += 'enter_long'
        # ----------------------------

        ###############################
        ##### SHORT ENTRY SIGNALS #####
        ###############################

        # ----------------------------
        short_condition = (
            (dataframe['close'] > dataframe['regression_upper']) &
            (dataframe['high_rsi'] > 65) &
            (dataframe['rsi'] > dataframe['rsi_slow']) &
            (dataframe['fastk'] > 70) &
            (dataframe['close'] > dataframe['sma_15']) &
            (dataframe['cti'] > 0.4) &
            (~dataframe['btc_usdt_trending_up_sharply_15m']) &
            (dataframe['norm_yang_zhang_vol'] < 0.75) &
            (dataframe['volume'] > 0)
        )
        dataframe.loc[short_condition, 'enter_short'] = 1
        dataframe.loc[short_condition, 'enter_tag'] += 'enter_short'
        # ----------------------------

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with exit columns populated
        """

        dataframe.loc[:, 'exit_tag'] = ''
        dataframe.loc[:, 'exit_long'] = 0
        dataframe.loc[:, 'exit_short'] = 0

        ###############################
        ##### LONG EXIT SIGNALS #####
        ###############################

        # ----------------------------
        # Rely on custom_exit
        # ----------------------------

        ##############################
        ##### SHORT EXIT SIGNALS #####
        ##############################

        # ----------------------------
        # Rely on custom_exit
        # ----------------------------

        #############################
        ###### Emergency Exits ######
        #############################

        #### BTC doing shit

        # ----------------------------
        exit_long_emergency = (
            (dataframe['btc_usdt_returns_roll_mean_cumsum_15m'] < dataframe['btc_usdt_returns_roll_mean_cumsum_lower_15m']) &
            (dataframe['volume'] > 0)
        )
        dataframe.loc[exit_long_emergency, 'exit_long'] = 1
        dataframe.loc[exit_long_emergency, 'exit_tag'] += 'exit_long_btc_bounds_protection'
        # ----------------------------

        # ----------------------------
        exit_short_emergency = (
            (dataframe['btc_usdt_returns_roll_mean_cumsum_15m'] > dataframe['btc_usdt_returns_roll_mean_cumsum_upper_15m']) &
            (dataframe['volume'] > 0)
        )
        dataframe.loc[exit_short_emergency, 'exit_short'] = 1
        dataframe.loc[exit_short_emergency, 'exit_tag'] += 'exit_short_btc_bounds_protection'
        # ----------------------------

        # #### Pair doing shit

        # # ----------------------------
        # exit_long_emergency = (
        #     (dataframe['returns_roll_mean_cumsum'] < dataframe['returns_roll_mean_cumsum_lower']) &
        #     (dataframe['volume'] > 0)
        # )
        # dataframe.loc[exit_long_emergency, 'exit_long'] = 1
        # dataframe.loc[exit_long_emergency, 'exit_tag'] += 'exit_long_pair_bounds_protection'
        # # ----------------------------

        # # ----------------------------
        # exit_short_emergency = (
        #     (dataframe['returns_roll_mean_cumsum'] > dataframe['returns_roll_mean_cumsum_upper']) &
        #     (dataframe['volume'] > 0)
        # )
        # dataframe.loc[exit_short_emergency, 'exit_short'] = 1
        # dataframe.loc[exit_short_emergency, 'exit_tag'] += 'exit_short_pair_bounds_protection'
        # # ----------------------------

        # #### Volatility or Volume above threshold

        # # ----------------------------
        # exit_long_emergency_vol = (
        #     (
        #         (dataframe['norm_yang_zhang_vol'] >= 0.85) |
        #         (dataframe['norm_vol_med_mean'] >= 0.85)
        #     ) &
        #     (dataframe['btc_usdt_returns_roll_mean_cumsum_15m'] < dataframe['btc_usdt_returns_roll_mean_cumsum_15m'].shift()) &
        #     (dataframe['volume'] > 0)
        # )
        # dataframe.loc[exit_long_emergency_vol, 'exit_long'] = 1
        # dataframe.loc[exit_long_emergency_vol, 'exit_tag'] += 'exit_long_vol_protection;'
        # # ----------------------------


        # # ----------------------------
        # exit_short_emergency_vol = (
        #     (
        #         (dataframe['norm_yang_zhang_vol'] >= 0.85) |
        #         (dataframe['norm_vol_med_mean'] >= 0.85)
        #     ) &
        #     (dataframe['btc_usdt_returns_roll_mean_cumsum_15m'] > dataframe['btc_usdt_returns_roll_mean_cumsum_15m'].shift()) &
        #     (dataframe['volume'] > 0)
        # )
        # dataframe.loc[exit_short_emergency_vol, 'exit_short'] = 1
        # dataframe.loc[exit_short_emergency_vol, 'exit_tag'] += 'exit_short_vol_protection;'
        # # ----------------------------

        return dataframe

    ############################################################################
    # Trade Management Methods
    ############################################################################

    def bot_loop_start(self, current_time: datetime, **kwargs) -> None:
        """
        Called at the start of each bot iteration.
        Stores break-even price in custom_info dict for plotting.
        """
        # Get all open trades
        for trade in Trade.get_trades_proxy(is_open=True):
            # Initialize custom_info for this pair if it doesn't exist
            if trade.pair not in self.custom_info:
                self.custom_info[trade.pair] = {}
                
            # Store current break-even (average entry) price for plotting
            self.custom_info[trade.pair]['break_even_price'] = trade.open_rate

    def leverage(self, pair: str, current_time: 'datetime', current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """
        Customize leverage for each new trade.

        :param pair: Pair that's currently analyzed
        :param current_time: datetime object, containing the current datetime
        :param current_rate: Rate, calculated based on pricing settings in exit_pricing.
        :param proposed_leverage: A leverage proposed by the bot.
        :param max_leverage: Max leverage allowed on this pair
        :param side: 'long' or 'short' - indicating the direction of the proposed trade
        :return: A leverage amount, which is between 1.0 and max_leverage.
        """
        return self.lev.value

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                          proposed_stake: float, min_stake: float, max_stake: float,
                          **kwargs) -> float:
        """
        Adjust stake amount to account for possible DCA orders
        """
        if self.config['stake_amount'] == 'unlimited':
            stake = (proposed_stake / self.max_dca_multiplier) / self.base_order_safety_order_ratio
            logger.info(f'Pair: {pair}, Proposed stake: {proposed_stake}, Max DCA Multiplier: {self.max_dca_multiplier}, Calculated stake: {stake}')
            return stake

        return proposed_stake

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time,
        entry_tag,
        side: str,
        **kwargs,
    ) -> bool:

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        # Check slippage
        if side == "long":
            if rate > (last_candle["close"] * (1 + 0.003)):
                # self.dp.send_msg(f"{pair} - Not entering trade, slippage too high. Last candle close: {last_candle['close']}, Entry: {rate}, Slippage (%): {(rate / last_candle['close']) - 1}")
                logger.info(f"{pair} - Not entering trade, slippage too high. Last candle close: {last_candle['close']}, Entry: {rate}, Slippage (%): {(rate / last_candle['close']) - 1}")
                return False
        else:
            if rate < (last_candle["close"] * (1 - 0.003)):
                # self.dp.send_msg(f"{pair} - Not entering trade, slippage too high. Last candle close: {last_candle['close']}, Entry: {rate}, Slippage (%): {(rate / last_candle['close']) - 1}")
                logger.info(f"{pair} - Not entering trade, slippage too high. Last candle close: {last_candle['close']}, Entry: {rate}, Slippage (%): {(rate / last_candle['close']) - 1}")
                return False

        return True

    def order_filled(self, pair: str, trade: Trade, order: Order, current_time: datetime, **kwargs) -> None:
        """
        Callback that's triggered after an order has been filled (completed).
        Used to track DCA entry and partial exit counts and to reset partialexit counters.
        This ensures that after a DCA order, the exit sequence starts fresh (1->2->3).
        """
        
        # If this is a filled entry order (initial or DCA)
        if (order.ft_order_side == trade.entry_side and 
                order.status == 'closed' and 
                order.filled is not None and 
                order.filled > 0 and
                trade.is_open):
            # Store the entry count when this entry happened
            trade.set_custom_data('dca_entry_count', trade.nr_of_successful_entries)
            # Reset exits counter since last entry
            trade.set_custom_data('exits_since_last_dca', 0)
            # Store this order's ID as the last entry
            trade.set_custom_data('last_entry_order_id', order.order_id)
            logger.info(f"{pair} - {'Initial entry' if trade.nr_of_successful_entries == 1 else 'DCA'} "
                    f"#{trade.nr_of_successful_entries} detected, resetting exit counter")

            # Only increment counter for DCA orders (nr_of_successful_entries > 1)
            if trade.nr_of_successful_entries > 1:
                # Increment SO counter
                sos_filled = trade.get_custom_data('sos_filled') or 1
                trade.set_custom_data('sos_filled', sos_filled + 1)
                logger.info(f"{pair} - DCA order filled at SO level {sos_filled}")
            else:
                # Initial entry - set counter to 1
                trade.set_custom_data('sos_filled', 1)
                logger.info(f"{pair} - Initial entry order filled")

            # Clean up opposite direction SO levels for cleaner plotting
            opposite_direction = 'short' if trade.trade_direction == 'long' else 'long'
            if pair in self.custom_info:
                # Clean from custom_info dict
                keys_to_remove = [key for key in self.custom_info[pair].keys() 
                                if key.startswith(f'so_{opposite_direction}_')]
                for key in keys_to_remove:
                    del self.custom_info[pair][key]

        # If this is a filled exit order
        elif (order.ft_order_side == trade.exit_side and 
            order.status == 'closed' and 
            order.filled is not None and 
            order.filled > 0):
            # Increment exit counter
            exits = trade.get_custom_data('exits_since_last_dca') or 0
            trade.set_custom_data('exits_since_last_dca', exits + 1)
            logger.info(f"{pair} - Exit #{exits + 1} since last entry")
            
            # Count how many SO levels are beyond current price
            # We can use the stored SO levels from trade custom data
            if trade.is_short:
                # Count levels below current price for shorts
                levels_beyond = sum(1 for i in range(1, self.max_safety_orders + 1)
                                if trade.get_custom_data(f'so_short_{i}', 0) <= trade.open_rate)
            else:
                # Count levels above current price for longs
                levels_beyond = sum(1 for i in range(1, self.max_safety_orders + 1)
                                if trade.get_custom_data(f'so_long_{i}', 0) >= trade.open_rate)
            
            # Reset SO counter to the next available level
            trade.set_custom_data('sos_filled', levels_beyond + 1)
            logger.info(f"{pair} - Exit detected, reset SO level to {levels_beyond + 1}")

    ############################################################################
    # Position Adjustment and Exit Methods
    ############################################################################

    def adjust_trade_position(self, trade: Trade, current_time: datetime,
                            current_rate: float, current_profit: float, min_stake: float,
                            max_stake: float, **kwargs):
        """
        Adjust trade position.
        Partial exits based on ATR levels.
        DCA mimicing the old 3commas DCA logic but based on ATR deviation instead of fixed percentage.
        """
        filled_entries = trade.select_filled_orders(trade.entry_side)
        count_of_buys = trade.nr_of_successful_entries
        side = trade.entry_side

        # Initialize custom_info for this pair if it doesn't exist
        if trade.pair not in self.custom_info:
            self.custom_info[trade.pair] = {}

        # Get ATR value
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        last_candle_2 = dataframe.iloc[-2].squeeze()
        atr_value = last_candle['atr']

        # Calculate and store exit levels
        exit_levels = self.calculate_exit_levels(trade, atr_value)
        for key, value in exit_levels.items():
            trade.set_custom_data(key, value)

        # Add stored exit levels to custom_info for plotting purposes
        for level in ['exit_1', 'exit_2', 'exit_3']:
            self.custom_info[trade.pair][level] = trade.get_custom_data(level)

        # Get our custom exit counter
        exits_since_last_dca = trade.get_custom_data('exits_since_last_dca') or 0
        last_entry_id = trade.get_custom_data('last_entry_order_id')
        
        # Count only exits that happened after our last entry
        if last_entry_id:
            last_entry_idx = None
            for i, order in enumerate(trade.orders):
                if order.order_id == last_entry_id:
                    last_entry_idx = i
                    break
            
            if last_entry_idx is not None:
                exits_since_last_dca = len([
                    o for o in trade.orders[last_entry_idx:]
                    if (o.ft_order_side == trade.exit_side and 
                        o.status == 'closed' and 
                        o.filled is not None and 
                        o.filled > 0)
                ])
        # # Commented out for now, but can be used to debugging
        # logger.info(f"{'SHORT' if trade.is_short else 'LONG'} - {trade.pair} - "
        #         f"Current rate: {current_rate}, Exit 1: {exit_levels['exit_1']}, "
        #         f"Exit 2: {exit_levels['exit_2']}, Number of exits: {trade.nr_of_successful_exits}, Exits since last entry: {exits_since_last_dca}")
        
        ############### PARTIAL EXITS #################
        if trade.is_short:
            if current_rate <= exit_levels['exit_1']:
                if exits_since_last_dca == 0:
                    logger.info(f"Triggering first partial exit for {trade.pair} at {current_rate}")
                    trade.set_custom_data('exits_since_last_dca', 1)
                    return -(trade.stake_amount * 0.33), f'partial_exit_short_1'
                
            if current_rate <= exit_levels['exit_2']:
                if exits_since_last_dca == 1:
                    logger.info(f"Triggering second partial exit for {trade.pair} at {current_rate}")
                    trade.set_custom_data('exits_since_last_dca', 2)
                    return -(trade.stake_amount * 0.5), "partial_exit_short_2"
                # else: # Commented out for now, but can be used to debugging
                #     logger.info(f"Not triggering second exit for {trade.pair} - nr_of_successful_exits: {trade.nr_of_successful_exits}")

        else:
            if current_rate >= exit_levels['exit_1']:
                if exits_since_last_dca == 0:
                    logger.info(f"Triggering first partial exit for {trade.pair} at {current_rate}")
                    trade.set_custom_data('exits_since_last_dca', 1)
                    return -(trade.stake_amount * 0.33), "partial_exit_long_1"
                    
            if current_rate >= exit_levels['exit_2']:
                if exits_since_last_dca == 1:
                    logger.info(f"Triggering second partial exit for {trade.pair} at {current_rate}")
                    return -(trade.stake_amount * 0.5), "partial_exit_long_2"
                # else: # Commented out for now, but can be used to debugging
                #     logger.info(f"Not triggering second exit for {trade.pair} - nr_of_successful_exits: {trade.nr_of_successful_exits}")

        ############### OPPOSITE ENTRY SIGNAL Position Reduction Exit #################
        # Get number of filled SOs
        sos_filled = trade.get_custom_data('sos_filled') or 1
        # Get number of position reductions already triggered
        reduce_position_exit = trade.get_custom_data('reduce_position_exit') or 0

        # Only check when we have 4 or 5 SOs filled and we haven't already triggered a reduction exit
        if sos_filled >= 4 and reduce_position_exit == 0:
            if trade.is_short and last_candle['enter_long']:
                logger.info(f"Triggering opposite signal partial exit for {trade.pair} at {current_rate} (Short position, Long signal)")
                trade.set_custom_data('reduce_position_exit', 1)
                return -(trade.stake_amount * 0.4), "partial_exit_short_reduce_position"
            elif not trade.is_short and last_candle['enter_short']:
                logger.info(f"Triggering opposite signal partial exit for {trade.pair} at {current_rate} (Long position, Short signal)")
                trade.set_custom_data('reduce_position_exit', 1)
                return -(trade.stake_amount * 0.4), "partial_exit_long_reduce_position"

        ############### DCA #################
        # Calculate and store SO levels
        so_levels = self.calculate_so_levels(trade, atr_value, filled_entries)
        for key, value in so_levels.items():
            trade.set_custom_data(key, value)

        # Add stored SO levels to custom_info for plotting purposes
        for i in range(1, self.max_safety_orders + 1):
            so_key = f'so_{"short" if trade.is_short else "long"}_{i}'
            self.custom_info[trade.pair][so_key] = trade.get_custom_data(so_key)

        # Get our custom SO tracking
        sos_filled = trade.get_custom_data('sos_filled') or 1  # Start at 1 for first SO level

        # Check if we should trigger a SO
        if 1 <= sos_filled <= self.max_safety_orders:
            current_so_key = f'so_{"short" if trade.is_short else "long"}_{sos_filled}'
            
            # Check if we should trigger a SO
            if ((side == "buy" and 
                current_rate <= so_levels[current_so_key] and 
                last_candle['rsi'] < 55) or
                (side == "sell" and 
                current_rate >= so_levels[current_so_key] and 
                last_candle['rsi'] > 45)):
                try:
                    # Calculate stake amount with volume scaling
                    stake_amount = self.wallets.get_trade_stake_amount(trade.pair, self.config['max_open_trades'])
                    stake_amount = (stake_amount / self.max_dca_multiplier) * self.base_order_safety_order_ratio
                    stake_amount = stake_amount * math.pow(self.safety_order_volume_scale, (sos_filled - 1))
                    amount = stake_amount / current_rate
                    
                    logger.info(f"Initiating ATR-based safety order {'buy' if side == 'buy' else 'sell'} "
                            f"at SO level {sos_filled} for {trade.pair} "
                            f"(ATR: {atr_value:.8f}, Level: {so_levels[current_so_key]:.4f}) "
                            f"Stake: {stake_amount:.4f}, Amount: {amount:.8f}")
                    return stake_amount, f"so_{'short' if trade.is_short else 'long'}_{sos_filled}"
                    
                except Exception as exception:
                    logger.error(f'Error calculating stake amount for {trade.pair}: {str(exception)}')
                    return None

        return None

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                   current_profit: float, **kwargs) -> Optional[str]:
        """
        Custom exit signal logic.
        Final exit based on ATR deviation.
        Volatility exit based on ATR increase.
        Time-based exit after 24 hours.
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        atr_value = last_candle['atr']

        if trade.is_short:
            # Final profit target (3x ATR)
            if current_rate <= trade.open_rate * (1 - 3.0 * (atr_value/trade.open_rate)):
                return "exit_short_profit_target"
            
            # Volatility expansion (ATR increased by 100%)
            if atr_value >= last_candle['atr_mean'] * 1.5:
                return "exit_short_volatility_expansion"
            
        else:
            # Final profit target (3x ATR)
            if current_rate >= trade.open_rate * (1 + 3.0 * (atr_value/trade.open_rate)):
                return "exit_long_profit_target"
            
            # Volatility expansion (ATR increased by 100%)
            if atr_value >= last_candle['atr_mean'] * 1.5:
                return "exit_long_volatility_expansion"

        # Time-based exit (12 hours)
        if (current_time - trade.open_date_utc).total_seconds() > 12 * 3600:
            return f"exit_{'short' if trade.is_short else 'long'}_time"

        return None

##############################
###### Helper Functions# #####
##############################

def indicator_normalization(df: DataFrame, col: str, length: int, norm_range: str) -> Series:

    rolling_min = df[col].rolling(window=length, min_periods=1).min()
    rolling_max = df[col].rolling(window=length, min_periods=1).max()

    # Apply [0,1] scale
    if norm_range == 'zero_to_one':
        normalized_indicator = (df[col] - rolling_min) / (rolling_max - rolling_min)

    # Apply [-1,1] scale
    elif norm_range == 'minus_one_to_one':
        normalized_indicator = 2 * ((df[col] - rolling_min) / (rolling_max - rolling_min)) - 1

    return normalized_indicator