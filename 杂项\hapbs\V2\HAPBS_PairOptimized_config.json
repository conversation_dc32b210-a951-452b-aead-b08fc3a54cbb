{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 10, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 100, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "FmcCdoVScHkILgHVxp4LEuEJjmks3MPx6XWvKl5Y1qFwyAjPuPNRjz8BtvjrzGpM", "secret": "dvLerCYTrkzxaig7nU4JLq6IIKBvND2lGOmFH6DqOAy3BfQFUhzdT3VhDTxdQfv8", "password": "Wen5520..", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["ETH/USDT:USDT", "BTC/USDT:USDT", "SOL/USDT:USDT", "1000PEPE/USDT:USDT", "DOGE/USDT:USDT", "XRP/USDT:USDT", "PENGU/USDT:USDT", "SUI/USDT:USDT", "FARTCOIN/USDT:USDT", "WIF/USDT:USDT", "ADA/USDT:USDT", "SEI/USDT:USDT", "AAVE/USDT:USDT", "BANANAS31/USDT:USDT", "ENA/USDT:USDT", "BCH/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT", "OP/USDT:USDT", "UNI/USDT:USDT", "BNB/USDT:USDT", "TRUMP/USDT:USDT", "PNUT/USDT:USDT", "VIRTUAL/USDT:USDT", "APT/USDT:USDT", "MOODENG/USDT:USDT", "LTC/USDT:USDT", "LPT/USDT:USDT", "ETHFI/USDT:USDT", "NEAR/USDT:USDT", "TIA/USDT:USDT", "CRV/USDT:USDT", "NEIRO/USDT:USDT", "DOT/USDT:USDT", "WLD/USDT:USDT", "FIL/USDT:USDT", "MOVE/USDT:USDT", "TAO/USDT:USDT", "1000SHIB/USDT:USDT", "FUN/USDT:USDT", "SPX/USDT:USDT", "INJ/USDT:USDT", "FET/USDT:USDT", "POPCAT/USDT:USDT", "GALA/USDT:USDT", "KAITO/USDT:USDT", "1000BONK/USDT:USDT", "TRX/USDT:USDT", "JUP/USDT:USDT", "ONDO/USDT:USDT", "EIGEN/USDT:USDT", "ETC/USDT:USDT", "LDO/USDT:USDT", "PYTH/USDT:USDT", "DYDX/USDT:USDT", "TRB/USDT:USDT", "W/USDT:USDT", "MKR/USDT:USDT", "KAIA/USDT:USDT", "HBAR/USDT:USDT", "PARTI/USDT:USDT", "JTO/USDT:USDT", "ZRO/USDT:USDT", "1000CAT/USDT:USDT", "1000FLOKI/USDT:USDT", "LQTY/USDT:USDT", "ORDI/USDT:USDT", "PAXG/USDT:USDT", "OM/USDT:USDT", "XLM/USDT:USDT", "AIXBT/USDT:USDT", "ATOM/USDT:USDT", "BSW/USDT:USDT", "BERA/USDT:USDT", "AI16Z/USDT:USDT", "IMX/USDT:USDT", "S/USDT:USDT", "SWARMS/USDT:USDT", "TURBO/USDT:USDT", "TON/USDT:USDT", "BROCCOLI714/USDT:USDT", "ALPHA/USDT:USDT", "PEOPLE/USDT:USDT", "SAND/USDT:USDT", "OGN/USDT:USDT", "MUBARAK/USDT:USDT", "TUT/USDT:USDT", "LAYER/USDT:USDT", "RENDER/USDT:USDT", "BOME/USDT:USDT", "ENS/USDT:USDT", "ICP/USDT:USDT", "COOKIE/USDT:USDT", "PENDLE/USDT:USDT", "HIFI/USDT:USDT", "RVN/USDT:USDT", "1000000MOG/USDT:USDT", "GOAT/USDT:USDT", "AERO/USDT:USDT", "BMT/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8082, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "ea6e62fa52289932648c49d353476a017aad46062f04e7d85adce94bb11ced9d", "ws_token": "7QxYS4ZNJQLz11pyZwYrvys626IDChEIYw", "CORS_origins": [], "username": "bin", "password": "510568"}, "bot_name": "bin", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}