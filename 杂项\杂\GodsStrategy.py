# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, merge_informative_pair
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import pandas_ta as pta
from freqtrade.strategy import IntParameter, DecimalParameter, CategoricalParameter
from freqtrade.persistence import Trade
from datetime import datetime
import pandas as pd

# --------------------------------
#   GodsStrategy by HaveF
#
#   author@: HaveF
#   github@: https://github.com/freqtrade/freqtrade-strategies
#
#   The strategy is designed to be a trend-following strategy.
#   It uses a combination of indicators to identify the trend and confirm entries.
#
#   Indicators:
#   - Supertrend: Main trend direction.
#   - QQE (Quantitative Qualitative Estimation): Entry trigger.
#   - ADX (Average Directional Index): Trend strength filter.
#   - HMA (Hull Moving Average): Short-term trend confirmation.
#   - RSI (Relative Strength Index): Momentum filter.
#
# --------------------------------


class GodsStrategy(IStrategy):
    """
    GodsStrategy
    """
    # --- Strategy Configuration ---
    INTERFACE_VERSION = 3
    # Optimal timeframe for the strategy
    timeframe = '15m'

    # Can this strategy go short?
    can_short = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi"
    minimal_roi = {
        "0": 100
    }

    # Required even if use_custom_stoploss is True.
    # This attribute is overridden by the custom_stoploss function.
    stoploss = -0.99

    # Use the custom stoploss implemented in custom_stoploss()
    use_custom_stoploss = True

    # Trailing stoploss
    trailing_stop = False
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True

    # --- Hyperparameters ---
    # QQE
    qqe_rsi_period = IntParameter(6, 20, default=14, space='protection')
    qqe_smoothing = IntParameter(3, 10, default=5, space='protection')

    # Supertrend
    st_period = IntParameter(7, 21, default=10, space='protection')
    st_multiplier = DecimalParameter(2.0, 5.0, default=3.0, space='protection')


    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        """
        # -- Supertrend --
        supertrend = pta.supertrend(
            high=dataframe['high'],
            low=dataframe['low'],
            close=dataframe['close'],
            length=self.st_period.value,
            multiplier=self.st_multiplier.value
        )
        dataframe['supertrend_direction'] = supertrend[f'SUPERTd_{self.st_period.value}_{self.st_multiplier.value}']
        dataframe['supertrend_long'] = supertrend[f'SUPERTl_{self.st_period.value}_{self.st_multiplier.value}']
        dataframe['supertrend_short'] = supertrend[f'SUPERTs_{self.st_period.value}_{self.st_multiplier.value}']
        
        # -- QQE --
        qqe = pta.qqe(
            close=dataframe['close'],
            length=self.qqe_rsi_period.value,
            smooth=self.qqe_smoothing.value
        )
        # To be robust against pandas_ta column name changes, we access QQE columns by position.
        if qqe is not None and qqe.shape[1] > 1:
            dataframe['qqe_line'] = qqe.iloc[:, 0]
            dataframe['qqe_signal'] = qqe.iloc[:, 1]
        else:
            dataframe['qqe_line'] = 0
            dataframe['qqe_signal'] = 0
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the buy signal for the given dataframe
        """
        # --- LONG ENTRY CONDITIONS ---
        dataframe.loc[
            (
                # Core Signal: price just crossed above supertrend
                qtpylib.crossed_above(dataframe['close'], dataframe['supertrend_long']) &
                # Momentum Confirmation: QQE is bullish
                (dataframe['qqe_line'] > dataframe['qqe_signal']) &
                
                # Guards
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # --- SHORT ENTRY CONDITIONS ---
        dataframe.loc[
            (
                # Core Signal: price just crossed below supertrend
                qtpylib.crossed_below(dataframe['close'], dataframe['supertrend_short']) &
                # Momentum Confirmation: QQE is bearish
                (dataframe['qqe_line'] < dataframe['qqe_signal']) &

                # Guards
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Exit logic is now handled by the custom_stoploss function.
        This method is intentionally left empty.
        """
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        return dataframe

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic, implementing trailing stoploss based on Supertrend.
        This function is now the sole exit mechanism for the strategy.
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        if trade.trade_direction == 'long':
            new_stoploss = last_candle['supertrend_long']
            # On the first run, `stop_loss_abs` does not exist, so we use `new_stoploss` as the initial value
            return max(new_stoploss, getattr(trade, 'stop_loss_abs', new_stoploss))
            
        elif trade.trade_direction == 'short':
            new_stoploss = last_candle['supertrend_short']
            # On the first run, `stop_loss_abs` does not exist, so we use `new_stoploss` as the initial value
            return min(new_stoploss, getattr(trade, 'stop_loss_abs', new_stoploss))

        # This line should not be reached, but is required for mypy.
        return self.stoploss 