# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


class CryptoLongShortStrategy(IStrategy):
    """
    Crypto Long-Short Strategy - 基于CryptoShortStrategy成功逻辑的多空策略
    
    核心特点:
    - 多空双向交易，适应不同市场环境
    - 基于成功的异常检测和反转逻辑
    - 智能的多空信号互斥机制
    - 动态风险管理和仓位控制
    """

    # Strategy interface version
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy
    timeframe = '1h'
    
    # Can our strategy go long and short?
    can_short: bool = True
    
    # Leverage settings
    leverage_optimize = False
    leverage_num = 3.0  # Fixed leverage multiplier
    
    # ROI table - 平衡多空收益
    minimal_roi = {
        "0": 0.08,   # 8% profit target
        "20": 0.05,  # 5% after 20 minutes
        "40": 0.03,  # 3% after 40 minutes
        "80": 0.015  # 1.5% after 80 minutes
    }

    # Stoploss - 基于优化结果
    stoploss = -0.15  # 15% base stop loss

    # Trailing stoploss
    trailing_stop = True
    trailing_stop_positive = 0.008  # Start trailing at 0.8% profit
    trailing_stop_positive_offset = 0.02  # Trail by 2%
    trailing_only_offset_is_reached = True 
   # === 多空策略参数 ===
    
    # === Pump/Dump Detection Parameters ===
    pump_threshold_1h = DecimalParameter(0.05, 0.15, default=0.12, space="buy", optimize=True, load=True)
    dump_threshold_1h = DecimalParameter(0.05, 0.15, default=0.12, space="buy", optimize=True, load=True)
    pump_threshold_4h = DecimalParameter(0.10, 0.25, default=0.15, space="buy", optimize=True, load=True)
    dump_threshold_4h = DecimalParameter(0.10, 0.25, default=0.15, space="buy", optimize=True, load=True)
    
    volume_surge_multiplier = DecimalParameter(2.0, 5.0, default=4.0, space="buy", optimize=True, load=True)
    
    # === RSI Parameters ===
    rsi_overbought_threshold = DecimalParameter(70.0, 85.0, default=75.0, space="buy", optimize=True, load=True)
    rsi_oversold_threshold = DecimalParameter(15.0, 30.0, default=25.0, space="buy", optimize=True, load=True)
    
    # === Reversal Detection Parameters ===
    reversal_confirmation_candles = IntParameter(1, 3, default=2, space="buy", optimize=True, load=True)
    
    upper_shadow_ratio = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True, load=True)
    lower_shadow_ratio = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True, load=True)
    
    pullback_threshold = DecimalParameter(0.02, 0.08, default=0.06, space="buy", optimize=True, load=True)
    bounce_threshold = DecimalParameter(0.02, 0.08, default=0.06, space="buy", optimize=True, load=True)
    
    # === Liquidity Filtering Parameters ===
    min_liquidity_score = DecimalParameter(25.0, 65.0, default=30.0, space="buy", optimize=True, load=True)
    liquidity_consistency_threshold = DecimalParameter(0.3, 0.7, default=0.6, space="buy", optimize=True, load=True)
    
    # === Signal Strength Parameters ===
    min_signal_strength = DecimalParameter(0.25, 0.6, default=0.45, space="buy", optimize=True, load=True)
    priority_signal_threshold = DecimalParameter(0.45, 0.7, default=0.65, space="buy", optimize=True, load=True)
    
    daily_signal_weight = DecimalParameter(0.6, 0.85, default=0.72, space="buy", optimize=True, load=True)
    hourly_signal_weight = DecimalParameter(0.15, 0.4, default=0.28, space="buy", optimize=True, load=True)
    
    # === Risk Management Parameters ===
    max_position_risk = DecimalParameter(0.008, 0.03, default=0.025, space="buy", optimize=True, load=True)
    account_risk_limit = DecimalParameter(0.03, 0.12, default=0.08, space="buy", optimize=True, load=True)
    
    # === Dynamic Risk Control Parameters ===
    consecutive_loss_limit = IntParameter(2, 5, default=3, space="buy", optimize=True, load=True)
    risk_reduction_factor = DecimalParameter(0.3, 0.7, default=0.6, space="buy", optimize=True, load=True)
    
    # === Market Condition Parameters ===
    bullish_avoidance_threshold = DecimalParameter(0.6, 0.9, default=0.75, space="buy", optimize=True, load=True)
    bearish_avoidance_threshold = DecimalParameter(0.6, 0.9, default=0.75, space="buy", optimize=True, load=True)
    
    # === 多空平衡参数 ===
    long_short_balance = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True, load=True)
    signal_conflict_resolution = CategoricalParameter(['long_priority', 'short_priority', 'strongest'], 
                                                     default='strongest', space="buy", optimize=True, load=True)

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 50

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        多空策略指标计算
        """
        # 基础技术指标
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=14)
        dataframe['ema_20'] = ta.EMA(dataframe['close'], timeperiod=20)
        dataframe['ema_50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['sma_200'] = ta.SMA(dataframe['close'], timeperiod=200)
        
        # ATR for volatility
        dataframe['atr'] = ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=14)
        
        # 成交量指标
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # 价格变化指标
        dataframe['price_change_1h'] = dataframe['close'].pct_change(1)
        dataframe['price_change_4h'] = dataframe['close'].pct_change(4)
        
        # Z-score计算 (异常检测)
        dataframe = self.calculate_zscore_indicators(dataframe)
        
        # 多空信号指标
        dataframe = self.calculate_long_short_indicators(dataframe)
        
        # 信号质量评分
        dataframe = self.calculate_signal_quality_score(dataframe)
        
        return dataframe
    
    def calculate_zscore_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算Z-score异常检测指标"""
        # 价格Z-score
        dataframe['price_1h_zscore'] = (dataframe['price_change_1h'] - dataframe['price_change_1h'].rolling(24).mean()) / dataframe['price_change_1h'].rolling(24).std()
        dataframe['price_4h_zscore'] = (dataframe['price_change_4h'] - dataframe['price_change_4h'].rolling(24).mean()) / dataframe['price_change_4h'].rolling(24).std()
        
        # 成交量Z-score
        dataframe['volume_zscore'] = (dataframe['volume_ratio'] - dataframe['volume_ratio'].rolling(24).mean()) / dataframe['volume_ratio'].rolling(24).std()
        
        # 填充NaN值
        dataframe['price_1h_zscore'] = dataframe['price_1h_zscore'].fillna(0)
        dataframe['price_4h_zscore'] = dataframe['price_4h_zscore'].fillna(0)
        dataframe['volume_zscore'] = dataframe['volume_zscore'].fillna(0)
        
        return dataframe
    
    def calculate_long_short_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算多空信号指标"""
        
        # === 做多信号指标 ===
        # 超卖反弹检测
        dataframe['rsi_oversold_rising'] = (
            (dataframe['rsi'] < self.rsi_oversold_threshold.value) & 
            (dataframe['rsi'] > dataframe['rsi'].shift(1))
        )
        
        # 支撑位反弹
        dataframe['support_bounce'] = (
            (dataframe['close'] > dataframe['low'].rolling(20).min() * 1.02) &
            (dataframe['close'] < dataframe['low'].rolling(20).min() * 1.05)
        )
        
        # 上涨动量
        dataframe['bullish_momentum'] = (
            (dataframe['close'] > dataframe['ema_20']) &
            (dataframe['ema_20'] > dataframe['ema_50'])
        )
        
        # 成交量确认 (做多)
        dataframe['volume_confirms_bounce'] = (
            (dataframe['volume_ratio'] > 1.5) &
            (dataframe['price_change_1h'] > 0)
        )
        
        # === 做空信号指标 (保持原有逻辑) ===
        # 超买回调检测
        dataframe['rsi_overbought_declining'] = (
            (dataframe['rsi'] > self.rsi_overbought_threshold.value) & 
            (dataframe['rsi'] < dataframe['rsi'].shift(1))
        )
        
        # 阻力位回调
        dataframe['resistance_rejection'] = (
            (dataframe['close'] < dataframe['high'].rolling(20).max() * 0.98) &
            (dataframe['close'] > dataframe['high'].rolling(20).max() * 0.95)
        )
        
        # 下跌动量
        dataframe['bearish_momentum'] = (
            (dataframe['close'] < dataframe['ema_20']) &
            (dataframe['ema_20'] < dataframe['ema_50'])
        )
        
        # 成交量确认 (做空)
        dataframe['volume_confirms_weakness'] = (
            (dataframe['volume_ratio'] > 1.5) &
            (dataframe['price_change_1h'] < 0)
        )
        
        # === 市场环境判断 ===
        dataframe['strong_uptrend'] = (
            (dataframe['close'] > dataframe['sma_200']) &
            (dataframe['ema_20'] > dataframe['ema_50']) &
            (dataframe['price_change_4h'] > 0.05)
        )
        
        dataframe['strong_downtrend'] = (
            (dataframe['close'] < dataframe['sma_200']) &
            (dataframe['ema_20'] < dataframe['ema_50']) &
            (dataframe['price_change_4h'] < -0.05)
        )
        
        return dataframe
    
    def calculate_signal_quality_score(self, dataframe: DataFrame) -> DataFrame:
        """计算信号质量评分"""
        
        # === 做多信号质量评分 ===
        long_score_components = []
        
        # 基础技术指标得分 (0-30分)
        long_score_components.append(
            np.where(dataframe['rsi_oversold_rising'], 15, 0) +
            np.where(dataframe['support_bounce'], 10, 0) +
            np.where(dataframe['bullish_momentum'], 5, 0)
        )
        
        # 成交量确认得分 (0-25分)
        long_score_components.append(
            np.where(dataframe['volume_confirms_bounce'], 25, 0)
        )
        
        # 市场环境得分 (0-25分)
        long_score_components.append(
            np.where(dataframe['strong_uptrend'], 25, 
                    np.where(~dataframe['strong_downtrend'], 15, 0))
        )
        
        # Z-score异常检测得分 (0-20分)
        long_score_components.append(
            np.where(dataframe['price_1h_zscore'] < -2, 20,
                    np.where(dataframe['price_1h_zscore'] < -1.5, 10, 0))
        )
        
        dataframe['long_signal_quality'] = np.clip(sum(long_score_components), 0, 100)
        
        # === 做空信号质量评分 ===
        short_score_components = []
        
        # 基础技术指标得分 (0-30分)
        short_score_components.append(
            np.where(dataframe['rsi_overbought_declining'], 15, 0) +
            np.where(dataframe['resistance_rejection'], 10, 0) +
            np.where(dataframe['bearish_momentum'], 5, 0)
        )
        
        # 成交量确认得分 (0-25分)
        short_score_components.append(
            np.where(dataframe['volume_confirms_weakness'], 25, 0)
        )
        
        # 市场环境得分 (0-25分)
        short_score_components.append(
            np.where(dataframe['strong_downtrend'], 25, 
                    np.where(~dataframe['strong_uptrend'], 15, 0))
        )
        
        # Z-score异常检测得分 (0-20分)
        short_score_components.append(
            np.where(dataframe['price_1h_zscore'] > 2, 20,
                    np.where(dataframe['price_1h_zscore'] > 1.5, 10, 0))
        )
        
        dataframe['short_signal_quality'] = np.clip(sum(short_score_components), 0, 100)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        多空入场信号生成
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''
        
        # === 做多入场条件 ===
        # Priority Long Signal (最高优先级)
        priority_long = (
            (dataframe['long_signal_quality'] >= self.priority_signal_threshold.value * 100) &
            (dataframe['rsi_oversold_rising']) &
            (dataframe['support_bounce']) &
            (dataframe['volume_confirms_bounce']) &
            (dataframe['price_1h_zscore'] < -2) &
            (~dataframe['strong_downtrend'])
        )
        
        # Emergency Long Signal (紧急信号)
        emergency_long = (
            (dataframe['long_signal_quality'] >= 70) &
            (dataframe['rsi'] < 25) &
            (dataframe['price_change_1h'] < -self.dump_threshold_1h.value) &
            (dataframe['volume_ratio'] > self.volume_surge_multiplier.value) &
            (~dataframe['strong_downtrend'])
        )
        
        # Standard Long Signal (标准信号)
        standard_long = (
            (dataframe['long_signal_quality'] >= self.min_signal_strength.value * 100) &
            (dataframe['rsi_oversold_rising'] | dataframe['support_bounce']) &
            (dataframe['volume_confirms_bounce']) &
            (~dataframe['strong_downtrend'])
        )
        
        # Backup Long Signal (备用信号)
        backup_long = (
            (dataframe['long_signal_quality'] >= 35) &
            (dataframe['rsi'] < 35) &
            (dataframe['bullish_momentum']) &
            (dataframe['volume_ratio'] > 1.2)
        )
        
        # === 做空入场条件 (保持原有成功逻辑) ===
        # Priority Short Signal (最高优先级)
        priority_short = (
            (dataframe['short_signal_quality'] >= self.priority_signal_threshold.value * 100) &
            (dataframe['rsi_overbought_declining']) &
            (dataframe['resistance_rejection']) &
            (dataframe['volume_confirms_weakness']) &
            (dataframe['price_1h_zscore'] > 2) &
            (~dataframe['strong_uptrend'])
        )
        
        # Emergency Short Signal (紧急信号)
        emergency_short = (
            (dataframe['short_signal_quality'] >= 70) &
            (dataframe['rsi'] > 75) &
            (dataframe['price_change_1h'] > self.pump_threshold_1h.value) &
            (dataframe['volume_ratio'] > self.volume_surge_multiplier.value) &
            (~dataframe['strong_uptrend'])
        )
        
        # Standard Short Signal (标准信号)
        standard_short = (
            (dataframe['short_signal_quality'] >= self.min_signal_strength.value * 100) &
            (dataframe['rsi_overbought_declining'] | dataframe['resistance_rejection']) &
            (dataframe['volume_confirms_weakness']) &
            (~dataframe['strong_uptrend'])
        )
        
        # Backup Short Signal (备用信号)
        backup_short = (
            (dataframe['short_signal_quality'] >= 35) &
            (dataframe['rsi'] > 65) &
            (dataframe['bearish_momentum']) &
            (dataframe['volume_ratio'] > 1.2)
        )
        
        # === 信号优先级设置 ===
        # Priority signals (最高优先级)
        dataframe.loc[priority_long, ['enter_long', 'enter_tag']] = (1, 'priority_long')
        dataframe.loc[priority_short, ['enter_short', 'enter_tag']] = (1, 'priority_short')
        
        # Emergency signals (紧急信号)
        dataframe.loc[emergency_long & ~priority_long, ['enter_long', 'enter_tag']] = (1, 'emergency_long')
        dataframe.loc[emergency_short & ~priority_short, ['enter_short', 'enter_tag']] = (1, 'emergency_short')
        
        # Standard signals (标准信号)
        dataframe.loc[standard_long & ~emergency_long & ~priority_long, ['enter_long', 'enter_tag']] = (1, 'standard_long')
        dataframe.loc[standard_short & ~emergency_short & ~priority_short, ['enter_short', 'enter_tag']] = (1, 'standard_short')
        
        # Backup signals (备用信号)
        dataframe.loc[backup_long & ~standard_long & ~emergency_long & ~priority_long, ['enter_long', 'enter_tag']] = (1, 'backup_long')
        dataframe.loc[backup_short & ~standard_short & ~emergency_short & ~priority_short, ['enter_short', 'enter_tag']] = (1, 'backup_short')
        
        # === 多空信号冲突处理 ===
        conflict_mask = (dataframe['enter_long'] == 1) & (dataframe['enter_short'] == 1)
        
        if self.signal_conflict_resolution.value == 'strongest':
            # 选择信号质量更高的
            dataframe.loc[conflict_mask & (dataframe['long_signal_quality'] > dataframe['short_signal_quality']), 'enter_short'] = 0
            dataframe.loc[conflict_mask & (dataframe['short_signal_quality'] > dataframe['long_signal_quality']), 'enter_long'] = 0
            # 如果质量相同，都取消
            dataframe.loc[conflict_mask & (dataframe['long_signal_quality'] == dataframe['short_signal_quality']), ['enter_long', 'enter_short']] = 0
        elif self.signal_conflict_resolution.value == 'long_priority':
            # 做多优先
            dataframe.loc[conflict_mask, 'enter_short'] = 0
        elif self.signal_conflict_resolution.value == 'short_priority':
            # 做空优先
            dataframe.loc[conflict_mask, 'enter_long'] = 0
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        多空出场信号生成
        """
        # 初始化出场信号
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = ''
        
        # === 做多出场条件 ===
        # 利润锁定出场
        long_profit_exit = (
            (dataframe['rsi'] > 70) &
            (dataframe['price_change_1h'] < -0.02) &
            (dataframe['volume_ratio'] > 1.5)
        )
        
        # 趋势反转出场
        long_reversal_exit = (
            (dataframe['strong_downtrend']) &
            (dataframe['rsi_overbought_declining']) &
            (dataframe['volume_confirms_weakness'])
        )
        
        # 止损出场
        long_stop_exit = (
            (dataframe['rsi'] < 30) &
            (dataframe['price_change_4h'] < -0.08) &
            (dataframe['bearish_momentum'])
        )
        
        # === 做空出场条件 ===
        # 利润锁定出场
        short_profit_exit = (
            (dataframe['rsi'] < 30) &
            (dataframe['price_change_1h'] > 0.02) &
            (dataframe['volume_ratio'] > 1.5)
        )
        
        # 趋势反转出场
        short_reversal_exit = (
            (dataframe['strong_uptrend']) &
            (dataframe['rsi_oversold_rising']) &
            (dataframe['volume_confirms_bounce'])
        )
        
        # 止损出场
        short_stop_exit = (
            (dataframe['rsi'] > 70) &
            (dataframe['price_change_4h'] > 0.08) &
            (dataframe['bullish_momentum'])
        )
        
        # 设置出场信号
        dataframe.loc[long_profit_exit, ['exit_long', 'exit_tag']] = (1, 'long_profit')
        dataframe.loc[long_reversal_exit, ['exit_long', 'exit_tag']] = (1, 'long_reversal')
        dataframe.loc[long_stop_exit, ['exit_long', 'exit_tag']] = (1, 'long_stop')
        
        dataframe.loc[short_profit_exit, ['exit_short', 'exit_tag']] = (1, 'short_profit')
        dataframe.loc[short_reversal_exit, ['exit_short', 'exit_tag']] = (1, 'short_reversal')
        dataframe.loc[short_stop_exit, ['exit_short', 'exit_tag']] = (1, 'short_stop')
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        动态止损 - 多空不同逻辑
        """
        # 获取数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return self.stoploss
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 基础止损
        base_stoploss = self.stoploss
        
        # 根据波动性调整止损
        if 'atr' in current_candle:
            volatility = current_candle['atr'] / current_rate
            volatility_multiplier = max(1.0, min(2.0, volatility * 100))
            base_stoploss = base_stoploss * volatility_multiplier
        
        # 多空不同的止损逻辑
        if trade.is_short:
            # 做空止损逻辑
            if current_profit > 0.05:  # 5%以上利润时收紧止损
                return -0.03
            elif current_profit > 0.02:  # 2%以上利润时适度收紧
                return -0.08
            else:
                return base_stoploss
        else:
            # 做多止损逻辑
            if current_profit > 0.05:  # 5%以上利润时收紧止损
                return -0.03
            elif current_profit > 0.02:  # 2%以上利润时适度收紧
                return -0.08
            else:
                return base_stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], 
                side: str, **kwargs) -> float:
        """
        动态杠杆管理 - 根据信号质量调整
        """
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return 1.0
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 基础杠杆
        base_leverage = self.leverage_num
        
        # 根据信号类型调整杠杆
        if entry_tag:
            if 'priority' in entry_tag:
                leverage_multiplier = 1.0  # 最高质量信号使用满杠杆
            elif 'emergency' in entry_tag:
                leverage_multiplier = 0.8  # 紧急信号适度降低
            elif 'standard' in entry_tag:
                leverage_multiplier = 0.6  # 标准信号保守使用
            elif 'backup' in entry_tag:
                leverage_multiplier = 0.4  # 备用信号最保守
            else:
                leverage_multiplier = 0.5
        else:
            leverage_multiplier = 0.5
        
        # 根据波动性调整
        if 'atr' in current_candle:
            volatility = current_candle['atr'] / current_rate
            if volatility > 0.05:  # 高波动时降低杠杆
                leverage_multiplier *= 0.7
            elif volatility < 0.02:  # 低波动时可以适度提高
                leverage_multiplier *= 1.2
        
        final_leverage = min(max_leverage, base_leverage * leverage_multiplier)
        return max(1.0, final_leverage)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                           side: str, **kwargs) -> bool:
        """
        交易确认 - 最后的风险检查
        """
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return False
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 流动性检查
        if 'volume_ratio' in current_candle:
            if current_candle['volume_ratio'] < 0.5:  # 成交量过低
                return False
        
        # 信号质量最终检查
        if side == 'long':
            if 'long_signal_quality' in current_candle:
                if current_candle['long_signal_quality'] < self.min_signal_strength.value * 100:
                    return False
        else:  # short
            if 'short_signal_quality' in current_candle:
                if current_candle['short_signal_quality'] < self.min_signal_strength.value * 100:
                    return False
        
        # 市场环境最终检查
        if side == 'long' and 'strong_downtrend' in current_candle:
            if current_candle['strong_downtrend']:
                return False
        elif side == 'short' and 'strong_uptrend' in current_candle:
            if current_candle['strong_uptrend']:
                return False
        
        return True

    def informative_pairs(self):
        """
        定义需要的额外数据对
        """
        return []