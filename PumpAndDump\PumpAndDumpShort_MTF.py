# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, stoploss_from_open, merge_informative_pair
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime
from freqtrade.persistence import Trade
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy

# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging
import pytz
from functools import reduce

log = logging.getLogger(__name__)


class PumpAndDumpShortMTF(IStrategy):
    """
    ## Multi-Timeframe Pump and Dump Shorting Strategy V1.0

    **作者:** Gemini 2.5 Pro & User
    **版本:** 1.0
    **核心理念:**
    - **多时间周期分析:** 本策略采用双时间周期协同作战。
        - **1小时 (1h) 侦察周期:** 在1h图表上捕捉核心的“暴力拉升”信号，定义为成交量和K线振幅的同步剧烈放大。
        - **15分钟 (15m) 入场周期:** 一旦1h信号确认，策略会切换到15m图表，等待RSI指标从超买区回落，以寻找更精准、更安全的做空入场点。
    - **精准动态止损:**
        - **初始止损:** 止损位被精确地设置为触发信号的那根1h K线的最高点，为交易提供了一个坚实且逻辑清晰的风险上限。
        - **利润锁定追踪止损:** 继承并优化了前代策略的明星功能。当利润超过阈值后，追踪止损会被激活以锁定利润，且只有在能确保盈利的前提下才会收紧止损，避免被无谓的震荡“洗出场”。
    
    **时间框架:** 15分钟 (15m)

    **入场条件 (做空):**
    1.  **宏观信号 (1h K线):**
        - 前一根或当前1h K线必须是“暴力拉升K线”，即同时满足：
            - **振幅飙升:** K线振幅远超近期平均值。
            - **成交量飙升:** 成交量远超近期平均值。
    2.  **入场确认 (15m K线):**
        - 15m的RSI指标刚刚从超买区域（如 >75）回落。
        - 当前15m K线为阴线，作为下跌确认。

    **出场条件 (平仓):**
    - 由统一的自定义止损 `custom_stoploss` 管理，结合了“1h高点硬止损”和“利润锁定追踪止损”。
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '15m'
    informative_timeframe = '1h'
    can_short = True
    process_only_new_candles = True

    # --- 启动和回测所需的数据量 ---
    # 1h需要40根, 15m是1h的4倍, 所以 40*4=160, 再加上15m自身的指标计算需要，给200
    startup_candle_count: int = 200

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }

    # Optional plot configuration
    plot_config = {
        'main_plot': {
            'pump_high_1h': {
                'color': '#FF5733',
                'linestyle': '--',
                'name': '1h Pump High (Stop)'
            }
        },
        'subplots': {
            "Signals": {
                'is_pump_1h': {'color': '#FFDD32', 'type': 'bar', 'name': '1h Pump Signal'}
            },
            "RSI_15m": {
                'rsi_15m': {'color': 'orange'},
            }
        }
    }

    # --- 止损和止盈设置 ---
    stoploss = -0.99 # 使用自定义止损，这个值设为一个较大的后备值

    # 启用自定义止损
    use_custom_stoploss = True
    
    # 禁用系统自带的追踪止损
    trailing_stop = False

    # --- Hyperopt 参数 (可优化参数) ---
    
    # 1h 拉升信号参数
    pump_sma_period = IntParameter(20, 40, default=20, space="buy", optimize=True)
    pump_volume_multiplier = DecimalParameter(1.5, 3.0, default=2.0, decimals=1, space="buy", optimize=True)
    pump_range_multiplier = DecimalParameter(1.5, 3.0, default=2.0, decimals=1, space="buy", optimize=True)
    
    # 15m 入场信号参数
    rsi_timeperiod = IntParameter(10, 20, default=14, space="buy", optimize=True)
    rsi_entry_level = IntParameter(65, 85, default=75, space="buy", optimize=True)

    # 风险管理参数
    tsl_positive = DecimalParameter(0.005, 0.02, default=0.01, decimals=3, space="sell", optimize=True)
    tsl_offset = DecimalParameter(0.01, 0.03, default=0.02, decimals=3, space="sell", optimize=True)

    # 杠杆设置
    leverage_optimize = CategoricalParameter([1.0, 2.0, 3.0, 5.0, 10.0], default=1.0, space="buy", optimize=True, load=True)

    def informative_pairs(self):
        """
        定义需要获取的额外数据源 (1h数据)
        """
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, self.informative_timeframe) for pair in pairs]
        return informative_pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算指标 - MTF版本. V1.2: 仿照成熟策略重构数据合并逻辑，解决 `KeyError: 'date'`。
        """
        # --- 1. 获取并计算1h侦察周期的指标 ---
        informative_df = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=self.informative_timeframe)
        
        if informative_df.empty:
            dataframe['is_pump_1h'] = 0
            dataframe['pump_high_1h'] = np.nan
            dataframe['rsi_15m'] = ta.RSI(dataframe, timeperiod=self.rsi_timeperiod.value)
            return dataframe

        # 振幅飙升
        informative_df['range_1h'] = informative_df['high'] - informative_df['low']
        range_sma = ta.SMA(informative_df['range_1h'], timeperiod=self.pump_sma_period.value)
        informative_df['range_spike'] = (informative_df['range_1h'] > range_sma * self.pump_range_multiplier.value)

        # 成交量飙升
        volume_sma = ta.SMA(informative_df['volume'], timeperiod=self.pump_sma_period.value)
        informative_df['volume_spike'] = (informative_df['volume'] > volume_sma * self.pump_volume_multiplier.value)
        
        # 定义1h的 "Pump" 信号
        informative_df['is_pump'] = (informative_df['range_spike'] & informative_df['volume_spike']).astype('int')
        
        # 我们需要这个1h K线的高点作为止损位
        informative_df['pump_high'] = informative_df['high']

        # --- 2. 将1h的信号和数据合并到15m周期 ---
        # 只选择我们需要的列进行合并，避免冲突
        informative_df = informative_df[['date', 'is_pump', 'pump_high']]
        dataframe = merge_informative_pair(dataframe, informative_df, self.timeframe, self.informative_timeframe, ffill=True)
        
        # --- 3. 准备绘图和后续逻辑所需的数据 ---
        # 在合并后，列名会自动加上后缀，例如 is_pump_1h
        if f'is_pump_{self.informative_timeframe}' in dataframe.columns:
            dataframe['is_pump_1h'] = dataframe[f'is_pump_{self.informative_timeframe}']
            dataframe['pump_high_1h'] = dataframe[f'pump_high_{self.informative_timeframe}']
        else:
            # 如果合并失败或1h数据不存在，创建空列以防后续出错
            dataframe['is_pump_1h'] = 0
            dataframe['pump_high_1h'] = np.nan

        # --- 4. 计算15m入场周期的指标 ---
        dataframe['rsi_15m'] = ta.RSI(dataframe, timeperiod=self.rsi_timeperiod.value)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        定义做空入场条件 - MTF版本
        """
        enter_short_conditions = [
            # 1h周期出现了暴力拉升信号
            dataframe[f'is_pump_{self.informative_timeframe}'] == 1,
            
            # 15m周期的RSI从超买区回落
            qtpylib.crossed_below(dataframe['rsi_15m'], self.rsi_entry_level.value),

            # K线收跌作为确认
            dataframe['close'] < dataframe['open']
        ]
        
        if not enter_short_conditions:
            dataframe.loc[:,'enter_short'] = 0
        else:
            dataframe.loc[
                reduce(lambda x, y: x & y, enter_short_conditions),
                ['enter_short', 'enter_tag']
            ] = (1, f'mtf_pump_{self.informative_timeframe}')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        做空离场信号 (此处为空，使用custom_stoploss)
        """
        dataframe.loc[(), 'exit_short'] = 0
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义动态止损 - MTF版本
        """
        if not trade.is_short:
            return -1

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # --- Section 1: 获取初始硬止损 (Hard Stop) ---
        # 止损位是我们在合并数据时从1h周期继承过来的 'pump_high_1h'
        trade_entry_candle = dataframe.loc[dataframe['date'] < pytz.utc.localize(trade.open_date)]
        if trade_entry_candle.empty:
            # 如果找不到交易前的K线，返回一个安全的后备值
            return trade.open_rate * (1 + 0.1) # 后备止损：开仓价上方10%

        # 获取交易发生时，dataframe中记录的1h pump的最高点
        hard_stop_price = trade_entry_candle.iloc[-1][f'pump_high_{self.informative_timeframe}']

        # --- Section 2: 手动追踪止损 (Manual Trailing Stop) ---
        # 兼容旧版 Freqtrade，手动计算 max_profit
        max_profit = 0.0
        if trade.min_rate is not None:
            max_profit = (trade.open_rate - trade.min_rate) / trade.open_rate

        if max_profit > self.tsl_positive.value:
            # 计算追踪止损价格
            lowest_rate = trade.min_rate or current_rate
            trailing_stop_price = lowest_rate * (1 + self.tsl_offset.value)

            # V3.0 核心逻辑: 只有在追踪止损能够锁定利润时（止损价低于开仓价），才考虑收紧止损
            if trailing_stop_price < trade.open_rate:
                # 返回初始硬止损和追踪止损中，更严格（价格更低）的那个
                return min(hard_stop_price, trailing_stop_price)

        # 如果不满足追踪止损条件，则返回固定的硬止损
        return hard_stop_price

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:
        return self.leverage_optimize.value

class PumpAndDumpShortMTFHyperopt(PumpAndDumpShortMTF):
    pass 