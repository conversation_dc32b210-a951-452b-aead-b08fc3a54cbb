# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# --- 策略说明 ---
# 改进型MACD+均线共振策略 V4
# 集成ATR动态止损以优化风险管理
# 针对回测结果进行优化，改进多空策略平衡性

import numpy as np
import pandas as pd
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, informative
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from freqtrade.persistence import Trade

logger = logging.getLogger(__name__)

class MACDEMAHarmonyStrategyV4(IStrategy):
    """
    改进型MACD+均线共振策略 V4 - 基于回测结果优化
    - 改进空头表现
    - 优化止损参数
    - 增强趋势过滤
    """
    # 策略基本参数设置
    INTERFACE_VERSION = 3
    
    # 允许做空
    can_short = True
    
    # 时间框架配置
    timeframe = '15m'
    
    # 长时间框架信号
    informative_timeframe = '1h'
    
    # 最小历史K线数量
    startup_candle_count = 200
    
    # 订单类型设置
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': True,
        'stoploss_on_exchange_interval': 60,
        'stoploss_on_exchange_limit_ratio': 0.99
    }

    # --- 策略核心属性 ---
    
    # 启用自定义ATR止损
    use_custom_stoploss = True
    
    # 止损设置 (使用优化后的值作为默认，但会被 custom_stoploss 覆盖)
    stoploss = -0.03
    
    # 追踪止损设置 (我们将在 custom_stoploss 中实现自己的逻辑)
    trailing_stop = False
    
    # 最大开仓数量
    max_open_trades = 6

    # --- 超参数优化空间 ---
    # 定义止损和追踪止损的可优化范围
    
    hopt_stoploss = DecimalParameter(-0.05, -0.01, default=-0.03, space='stoploss')
    
    # 我们自定义的追踪止损逻辑依然需要这些参数
    hopt_trailing_stop_positive = DecimalParameter(0.01, 0.05, default=0.02, decimals=3, space='trailing')
    hopt_trailing_stop_positive_offset = DecimalParameter(0.03, 0.1, default=0.05, decimals=3, space='trailing')
    
    # 优化参数设置 (基于V3回测结果调整默认值)
    # --- 多头参数 ---
    buy_macd_fast = IntParameter(7, 21, default=12, space="buy")
    buy_macd_slow = IntParameter(14, 42, default=31, space="buy")
    buy_macd_signal = IntParameter(6, 18, default=14, space="buy")
    buy_ema_short = IntParameter(5, 20, default=12, space="buy")
    buy_ema_medium = IntParameter(15, 40, default=16, space="buy")
    buy_ema_long = IntParameter(35, 90, default=65, space="buy")
    buy_vwap_period = IntParameter(14, 50, default=15, space="buy")

    # --- 空头参数 (基于回测结果调整) ---
    sell_macd_fast = IntParameter(7, 21, default=10, space="sell")  # 调整为更敏感
    sell_macd_slow = IntParameter(14, 42, default=26, space="sell")  # 调整为更敏感
    sell_macd_signal = IntParameter(6, 18, default=9, space="sell")  # 调整为更敏感
    sell_ema_short = IntParameter(5, 20, default=20, space="sell")
    sell_ema_medium = IntParameter(15, 40, default=28, space="sell")
    sell_ema_long = IntParameter(35, 90, default=67, space="sell")
    sell_vwap_period = IntParameter(14, 50, default=16, space="sell")

    # --- 通用保护/过滤器参数 ---
    atr_period = IntParameter(7, 21, default=12, space="protection")
    atr_multiplier = DecimalParameter(1.0, 5.0, default=2.5, space="protection")  # 调整为更保守
    adx_period = IntParameter(10, 30, default=16, space="protection")
    adx_threshold = IntParameter(15, 35, default=25, space="protection")  # 调整为更严格
    
    # 布林带参数
    bb_period = IntParameter(15, 40, default=34, space="protection")
    bb_std = DecimalParameter(1.5, 3.0, default=2.5, space="protection")
    bb_width_threshold = DecimalParameter(0.01, 0.1, default=0.03, space="protection")  # 调整为更严格
    
    # 成交量过滤
    volume_threshold = DecimalParameter(1.0, 5.0, default=3.5, space="protection")  # 调整为更严格
    
    # 仓位管理参数
    custom_max_entry_position_adjustment = DecimalParameter(1.0, 3.0, default=2.0, decimals=1, space="protection")
    
    # 斐波那契参数
    fib_period = IntParameter(50, 400, default=148, space="protection")
    
    # 策略初始化
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        
    # 数据处理和指标计算
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算所有策略需要的指标
        """
        # 基础指标计算
        
        # --- 基础指标计算 (多头) ---
        
        # MACD
        for val in self.buy_macd_fast.range:
            dataframe[f'buy_macd_{val}'] = ta.MACD(dataframe, fastperiod=val, 
                                            slowperiod=self.buy_macd_slow.value, 
                                            signalperiod=self.buy_macd_signal.value)['macd']
            dataframe[f'buy_macdsignal_{val}'] = ta.MACD(dataframe, fastperiod=val, 
                                                  slowperiod=self.buy_macd_slow.value, 
                                                  signalperiod=self.buy_macd_signal.value)['macdsignal']
            dataframe[f'buy_macdhist_{val}'] = ta.MACD(dataframe, fastperiod=val, 
                                               slowperiod=self.buy_macd_slow.value, 
                                               signalperiod=self.buy_macd_signal.value)['macdhist']
        
        # 均线系统
        for val in self.buy_ema_short.range:
            dataframe[f'buy_ema_short_{val}'] = ta.EMA(dataframe, timeperiod=val)
            
        for val in self.buy_ema_medium.range:
            dataframe[f'buy_ema_medium_{val}'] = ta.EMA(dataframe, timeperiod=val)
            
        for val in self.buy_ema_long.range:
            dataframe[f'buy_ema_long_{val}'] = ta.EMA(dataframe, timeperiod=val)
            
        # 计算VWAP (多头)
        dataframe['buy_vwap'] = qtpylib.rolling_vwap(dataframe, window=self.buy_vwap_period.value)
        
        # --- 基础指标计算 (空头) ---

        # MACD
        for val in self.sell_macd_fast.range:
            dataframe[f'sell_macd_{val}'] = ta.MACD(dataframe, fastperiod=val, 
                                            slowperiod=self.sell_macd_slow.value, 
                                            signalperiod=self.sell_macd_signal.value)['macd']
            dataframe[f'sell_macdsignal_{val}'] = ta.MACD(dataframe, fastperiod=val, 
                                                  slowperiod=self.sell_macd_slow.value, 
                                                  signalperiod=self.sell_macd_signal.value)['macdsignal']
            dataframe[f'sell_macdhist_{val}'] = ta.MACD(dataframe, fastperiod=val, 
                                               slowperiod=self.sell_macd_slow.value, 
                                               signalperiod=self.sell_macd_signal.value)['macdhist']
        
        # 均线系统
        for val in self.sell_ema_short.range:
            dataframe[f'sell_ema_short_{val}'] = ta.EMA(dataframe, timeperiod=val)
            
        for val in self.sell_ema_medium.range:
            dataframe[f'sell_ema_medium_{val}'] = ta.EMA(dataframe, timeperiod=val)
            
        for val in self.sell_ema_long.range:
            dataframe[f'sell_ema_long_{val}'] = ta.EMA(dataframe, timeperiod=val)

        # 计算VWAP (空头)
        dataframe['sell_vwap'] = qtpylib.rolling_vwap(dataframe, window=self.sell_vwap_period.value)

        # --- 通用指标计算 ---

        # 计算ATR
        dataframe[f'atr_{self.atr_period.value}'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        
        # 计算ADX
        for val in self.adx_period.range:
            dataframe[f'adx_{val}'] = ta.ADX(dataframe, timeperiod=val)
            dataframe[f'plus_di_{val}'] = ta.PLUS_DI(dataframe, timeperiod=val)
            dataframe[f'minus_di_{val}'] = ta.MINUS_DI(dataframe, timeperiod=val)
        
        # 布林带计算
        for val in self.bb_period.range:
            bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=val, stds=self.bb_std.value)
            dataframe[f'bb_lowerband_{val}'] = bollinger['lower']
            dataframe[f'bb_middleband_{val}'] = bollinger['mid']
            dataframe[f'bb_upperband_{val}'] = bollinger['upper']
            dataframe[f'bb_width_{val}'] = ((bollinger['upper'] - bollinger['lower']) / bollinger['mid'])
        
        # 成交量分析
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_mean']
        
        # 动量计算
        dataframe['momentum'] = ta.MOM(dataframe, timeperiod=14)
        
        # 波动率计算
        dataframe['volatility'] = dataframe[f'atr_{self.atr_period.value}'] / dataframe['close'] * 100
        
        # RSI指标 (新增)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # 斐波那契回调/扩展位计算
        dataframe['fib_high'] = dataframe['high'].rolling(window=self.fib_period.value).max()
        dataframe['fib_low'] = dataframe['low'].rolling(window=self.fib_period.value).min()
        dataframe['fib_range'] = dataframe['fib_high'] - dataframe['fib_low']
        
        # 斐波那契水平位
        dataframe['fib_23_6'] = dataframe['fib_low'] + dataframe['fib_range'] * 0.236
        dataframe['fib_38_2'] = dataframe['fib_low'] + dataframe['fib_range'] * 0.382
        dataframe['fib_50_0'] = dataframe['fib_low'] + dataframe['fib_range'] * 0.5
        dataframe['fib_61_8'] = dataframe['fib_low'] + dataframe['fib_range'] * 0.618
        dataframe['fib_76_4'] = dataframe['fib_low'] + dataframe['fib_range'] * 0.764
        
        # 识别近期波段极值点
        dataframe['pivot_high'] = (dataframe['high'].shift(2) < dataframe['high'].shift(1)) & \
                                (dataframe['high'].shift(1) > dataframe['high']) & \
                                (dataframe['high'].shift(3) < dataframe['high'].shift(2)) & \
                                (dataframe['high'].shift(4) < dataframe['high'].shift(2))
        
        dataframe['pivot_low'] = (dataframe['low'].shift(2) > dataframe['low'].shift(1)) & \
                               (dataframe['low'].shift(1) < dataframe['low']) & \
                               (dataframe['low'].shift(3) > dataframe['low'].shift(2)) & \
                               (dataframe['low'].shift(4) > dataframe['low'].shift(2))
        
        # --- V4 入场逻辑：在主趋势中捕捉回调结束点 (多空分离) ---

        # 1. 定义主趋势
        is_uptrend = (dataframe[f'buy_ema_medium_{self.buy_ema_medium.value}'] > dataframe[f'buy_ema_long_{self.buy_ema_long.value}'])
        is_downtrend = (dataframe[f'sell_ema_medium_{self.sell_ema_medium.value}'] < dataframe[f'sell_ema_long_{self.sell_ema_long.value}'])

        # 2. 识别回调/反弹结束的信号 (MACD穿越0轴)
        macd_cross_up = qtpylib.crossed_above(dataframe[f'buy_macdhist_{self.buy_macd_fast.value}'], 0)
        macd_cross_down = qtpylib.crossed_below(dataframe[f'sell_macdhist_{self.sell_macd_fast.value}'], 0)

        # 3. 应用过滤器 (通用)
        filters_ok = (
            (dataframe[f'adx_{self.adx_period.value}'] > self.adx_threshold.value) &
            (dataframe['volume_ratio'] > self.volume_threshold.value) &
            (dataframe[f'bb_width_{self.bb_period.value}'] > self.bb_width_threshold.value)
        )
        
        # 4. 增加额外的多空过滤条件 (V4新增)
        long_filters = (
            (dataframe['rsi'] > 40) &  # RSI不在超卖区
            (dataframe['close'] > dataframe[f'bb_middleband_{self.bb_period.value}'])  # 价格在BB中线上方
        )
        
        short_filters = (
            (dataframe['rsi'] < 60) &  # RSI不在超买区
            (dataframe['close'] < dataframe[f'bb_middleband_{self.bb_period.value}'])  # 价格在BB中线下方
        )

        # 生成买入信号: 主升浪中的回调结束
        dataframe['buy_signal'] = is_uptrend & macd_cross_up & filters_ok & long_filters
        
        # 生成卖出信号: 主跌浪中的反弹结束
        dataframe['sell_signal'] = is_downtrend & macd_cross_down & filters_ok & short_filters
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        根据计算出的指标生成买入信号
        """
        dataframe.loc[:, 'enter_tag'] = ''

        # 多头入场条件
        dataframe.loc[dataframe['buy_signal'], 'enter_long'] = 1
        dataframe.loc[dataframe['buy_signal'], 'enter_tag'] = 'long_signal'

        # 空头入场条件
        dataframe.loc[dataframe['sell_signal'], 'enter_short'] = 1
        dataframe.loc[dataframe['sell_signal'], 'enter_tag'] = 'short_signal'

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        定义离场信号
        当反向动量出现时 (MACD反向穿越0轴) 离场
        """
        dataframe.loc[:, ['exit_long', 'exit_short']] = 0
        
        # 多头离场条件: MACD动量转为负 (使用多头参数)
        dataframe.loc[
            qtpylib.crossed_below(dataframe[f'buy_macdhist_{self.buy_macd_fast.value}'], 0),
            ['exit_long', 'exit_tag']] = (1, 'long_exit_macd_cross')
        
        # 空头离场条件: MACD动量转为正 (使用空头参数)
        dataframe.loc[
            qtpylib.crossed_above(dataframe[f'sell_macdhist_{self.sell_macd_fast.value}'], 0),
            ['exit_short', 'exit_tag']] = (1, 'short_exit_macd_cross')
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        V4版：优化的动态止损函数
        包括初始ATR止损和自定义追踪止损
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            # 如果数据不可用，回退到静态止损
            return -self.hopt_stoploss.value

        last_candle = dataframe.iloc[-1]
        atr_value = last_candle.get(f'atr_{self.atr_period.value}')

        if not atr_value or atr_value == 0:
            return -self.hopt_stoploss.value

        # --- 1. 计算初始ATR止损价 ---
        initial_stop_pct = (atr_value * self.atr_multiplier.value) / trade.open_rate
        
        # --- 2. 计算追踪止损价 ---
        trailing_stop_price = None
        trailing_offset = self.hopt_trailing_stop_positive_offset.value
        trailing_distance = self.hopt_trailing_stop_positive.value

        if trade.is_short:
            # 对于空头，最高利润对应最低价格
            trade_candles = dataframe.loc[dataframe['date'] >= trade.open_date_utc]
            if not trade_candles.empty:
                min_rate_since_open = trade_candles['low'].min()
                highest_profit = trade.calc_profit_ratio(min_rate_since_open)
                
                if highest_profit > trailing_offset:
                    locked_in_profit_ratio = highest_profit - trailing_distance
                    trailing_stop_price = trade.open_rate * (1 - locked_in_profit_ratio)
        else: # 多头
            highest_profit = trade.calc_profit_ratio(trade.max_rate)
            if highest_profit > trailing_offset:
                locked_in_profit_ratio = highest_profit - trailing_distance
                trailing_stop_price = trade.open_rate * (1 + locked_in_profit_ratio)

        # --- 3. 决定最终止损价 ---
        if trade.is_short:
            initial_stop_price = trade.open_rate * (1 + initial_stop_pct)
            # 对于空头，我们取更低的止损价（更接近当前价格，风险更小）
            if trailing_stop_price:
                final_stop_price = min(initial_stop_price, trailing_stop_price)
            else:
                final_stop_price = initial_stop_price
        else: # 多头
            initial_stop_price = trade.open_rate * (1 - initial_stop_pct)
            # 对于多头，我们取更高的止损价（更接近当前价格，风险更小）
            if trailing_stop_price:
                final_stop_price = max(initial_stop_price, trailing_stop_price)
            else:
                final_stop_price = initial_stop_price
        
        # --- 4. 将止损价转换为框架要求的相对百分比 ---
        # 返回值必须为负数，代表与当前价格的距离百分比。
        # 我们计算价格距离，并确保返回的是一个负值。
        distance = abs((final_stop_price / current_rate) - 1.0)
        return -distance

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                            time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                            side: str, **kwargs) -> bool:
        """
        入场确认回调 - 这里可以加入额外的逻辑
        """
        # 获取当前数据框
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return False
            
        # 获取最新K线
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 根据交易方向进行额外检查
        if side == "long":
            # 多头交易额外检查 - 确保不在明显阻力位附近
            if last_candle['close'] > last_candle['fib_61_8']:
                # 价格已经突破61.8%斐波那契位，可能有较强动能
                return True
            else:
                # 检查价格是否在布林带上轨附近(可能遇阻)
                if last_candle['close'] > last_candle[f'bb_upperband_{self.bb_period.value}'] * 0.98:
                    return False
        
        elif side == "short":
            # 空头交易额外检查 - 确保不在明显支撑位附近
            if last_candle['close'] < last_candle['fib_38_2']:
                # 价格已经跌破38.2%斐波那契位，可能有较强下跌动能
                return True
            else:
                # 检查价格是否在布林带下轨附近(可能遇支撑)
                if last_candle['close'] < last_candle[f'bb_lowerband_{self.bb_period.value}'] * 1.02:
                    return False
                    
        return True

    def adjust_trade_position(self, trade: 'Trade', current_time: datetime,
                              current_rate: float, current_profit: float, min_stake: float,
                              max_stake: float, **kwargs) -> Optional[float]:
        """
        仓位调整 - 实现简单的加仓逻辑
        """
        # 如果当前利润为负且亏损超过1%，不考虑加仓
        if current_profit < -0.01:
            return None
            
        # 获取交易对的数据
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        if dataframe.empty:
            return None
            
        # 获取最新K线
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 检查是否有强烈的趋势信号
        strong_trend = last_candle[f'adx_{self.adx_period.value}'] > self.adx_threshold.value * 1.5
        
        # 检查方向性信号
        if trade.is_short:
            # 空头交易 - 检查是否有更强的下跌信号
            if strong_trend and last_candle[f'minus_di_{self.adx_period.value}'] > last_candle[f'plus_di_{self.adx_period.value}'] * 1.2:
                # 计算加仓金额 - 使用原始仓位的一部分
                return trade.stake_amount * 0.5
        else:
            # 多头交易 - 检查是否有更强的上涨信号
            if strong_trend and last_candle[f'plus_di_{self.adx_period.value}'] > last_candle[f'minus_di_{self.adx_period.value}'] * 1.2:
                # 计算加仓金额 - 使用原始仓位的一部分
                return trade.stake_amount * 0.5
                
        return None 