#!/bin/bash
#
# 该脚本提供了一个通用的 Freqtrade 优化工作流，采用两阶段方法：
# 1. 运行优化: 为交易列表中的每个交易对执行 `freqtrade hyperopt`，
#    并将标准的、独立的优化结果文件保存在 `user_data/hyperopt_results/` 中。
# 2. 处理结果: 调用一个专门的Python脚本来解析上述结果文件，
#    找到每个交易对的最佳参数，并生成最终的策略参数配置文件。
#
# 此脚本由 main.sh 调用，并从其接收环境变量。

# --- Trap for graceful exit ---
trap "echo; echo '捕获到中断信号，正在强制退出...'; exit 1" INT TERM

# --- 脚本开始 ---
echo "启动通用优化工作流 (标准版)..."
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"

# --- 步骤 0: 校验环境变量并设置路径 ---
if [ -z "$PROJECT_ROOT" ] || [ -z "$OPT_STRATEGY_NAME" ]; then
    echo "错误: 必要的环境变量 (e.g., PROJECT_ROOT, OPT_STRATEGY_NAME) 未设置。" >&2
    echo "请通过主脚本 main.sh 运行此工作流。" >&2
    exit 1
fi

# 定义路径
CONFIG_FILE="${PROJECT_ROOT}/user_data/${CONFIG_FILE_NAME}"
HYPEROPT_RESULTS_DIR="${PROJECT_ROOT}/user_data/hyperopt_results"

# --- 从环境变量读取优化参数, 若为空则使用默认值 ---
TIMEFRAME=${TIMEFRAME:-"15m"}
HYPEROPT_TIMERANGE=${HYPEROPT_TIMERANGE:-"20240101-"}
# 关键修改：优先使用 main.sh 传递的覆盖值，否则使用原始值
HYPEROPT_SPACES=${HYPEROPT_SPACES_OVERRIDE:-$HYPEROPT_SPACES}
EPOCHS=${EPOCHS:-200}
JOBS=${JOBS:--1}

# 关键修复：从传入的 HYPEROPT_SPACES 变量中分离出不同的参数空间
# 这样我们就可以在第一步只运行 buy/sell，在第二步只运行 stoploss 和 trailing
SIGNAL_SPACES=$(echo "$HYPEROPT_SPACES" | sed -e 's/\bstoploss\b//g' -e 's/\btrailing\b//g' | xargs)
echo "检测到信号参数空间 (第一步): ${SIGNAL_SPACES:-'(无)'}"

# 构造风控参数空间
RISK_SPACES=""
if [[ "$HYPEROPT_SPACES" == *"stoploss"* ]]; then
    RISK_SPACES="stoploss"
fi
if [[ "$HYPEROPT_SPACES" == *"trailing"* ]]; then
    RISK_SPACES="$RISK_SPACES trailing"
fi
# 清理前后可能产生的多余空格
RISK_SPACES=$(echo "$RISK_SPACES" | xargs)

if [ -n "$RISK_SPACES" ]; then
    echo "检测到风控参数空间 (第二步): $RISK_SPACES"
fi

echo "将使用以下配置进行优化："
echo "  - 优化策略: ${OPT_STRATEGY_NAME}"
echo "  - 配置文件: ${CONFIG_FILE}"
echo "  - 时间周期: ${TIMEFRAME}"
echo "  - 时间范围: ${HYPEROPT_TIMERANGE}"
echo "  - 参数空间: ${HYPEROPT_SPACES}"
echo "  - 优化轮数: ${EPOCHS}"

# --- 函数定义 ---
set_permissions() {
    echo "正在更新项目文件权限以兼容Docker容器..."
    if [ -d "${PROJECT_ROOT}/user_data" ]; then
        sudo chmod -R a+rX "${PROJECT_ROOT}/user_data"
        echo "权限更新完成。"
    else
        echo "警告: '${PROJECT_ROOT}/user_data' 目录不存在，跳过权限设置。"
    fi
}

# --- 步骤 1: 环境准备 ---
set_permissions

# 从主配置文件中提取 pair_whitelist 列表
PAIRS=$(jq -r '.exchange.pair_whitelist[]' "${CONFIG_FILE}")
# 关键修复：检查 jq 命令是否成功执行以及 PAIRS 变量是否为空
if [ $? -ne 0 ] || [ -z "$PAIRS" ]; then
    echo "错误：无法从 ${CONFIG_FILE} 中读取或解析 'exchange.pair_whitelist'。" >&2
    echo "请确保该文件存在、格式正确，并包含一个非空的交易对列表。" >&2
    exit 1
fi

# --- 步骤 2: 循环运行优化 ---
TOTAL_PAIRS=$(echo "$PAIRS" | wc -w)
CURRENT_PAIR_NUM=0

for PAIR in $PAIRS; do
    CURRENT_PAIR_NUM=$((CURRENT_PAIR_NUM + 1))
    echo "-------------------------------------------------------------------"
    echo "[${CURRENT_PAIR_NUM}/${TOTAL_PAIRS}] 开始优化交易对: $PAIR"
    echo "-------------------------------------------------------------------"
    
    # 定义路径和文件名
    CONFIG_FILE_DOCKER=$(echo "$CONFIG_FILE" | sed "s|^${PROJECT_ROOT}/||")
    PAIR_FILENAME=$(echo "$PAIR" | sed -e 's/\//_/g' -e 's/:/--/g')
    PARAMS_FILE_HOST="${PROJECT_ROOT}/user_data/hyperopt_results/strategy_${OPT_STRATEGY_NAME}_${PAIR_FILENAME}.json"
    STRATEGY_DEFAULT_PARAMS_HOST="${PROJECT_ROOT}/user_data/${STRATEGY_PATH}/${OPT_STRATEGY_NAME}.json"

    # --- 步骤 1a: 运行信号参数优化 (e.g., Buy/Sell) ---
    if [ -n "$SIGNAL_SPACES" ]; then
        echo "正在为 $PAIR 执行信号参数优化 (空间: $SIGNAL_SPACES)..."
        cd "$PROJECT_ROOT" && docker compose run --rm -T freqtrade hyperopt \
            --strategy "$OPT_STRATEGY_NAME" \
            --strategy-path "user_data/${STRATEGY_PATH}" \
            --config "$CONFIG_FILE_DOCKER" \
            --timeframe "$TIMEFRAME" \
            --timerange "$HYPEROPT_TIMERANGE" \
            --hyperopt-loss ShortTradeDurHyperOptLoss \
            --spaces $SIGNAL_SPACES \
            --ignore-missing-spaces \
            -e "$EPOCHS" \
            -j "$JOBS" \
            --random-state 123 \
            --min-trades 1 \
            -p "$PAIR"
        
        if [ $? -ne 0 ]; then
            echo "警告: 为 $PAIR 的信号参数优化任务失败，跳过此交易对。"
            continue
        fi
        echo "信号参数优化成功。策略参数文件已更新。"
    else
        echo "未指定信号优化空间 (buy/sell)，跳过第一步。"
    fi

    # --- 步骤 1b: 运行风控参数优化 (Stoploss/Trailing) ---
    if [ -n "$RISK_SPACES" ]; then
        echo "正在为 $PAIR 执行风控参数优化 (空间: $RISK_SPACES)..."
        cd "$PROJECT_ROOT" && docker compose run --rm -T freqtrade hyperopt \
            --strategy "$OPT_STRATEGY_NAME" \
            --strategy-path "user_data/${STRATEGY_PATH}" \
            --config "$CONFIG_FILE_DOCKER" \
            --timeframe "$TIMEFRAME" \
            --timerange "$HYPEROPT_TIMERANGE" \
            --hyperopt-loss MaxDrawDownHyperOptLoss \
            --spaces $RISK_SPACES \
            --ignore-missing-spaces \
            -e "$EPOCHS" \
            -j "$JOBS" \
            --random-state 123 \
            --min-trades 1 \
            -p "$PAIR"

        if [ $? -eq 0 ]; then
            echo "风控参数优化成功。策略参数文件已包含合并后参数。"
        else
            echo "警告: 为 $PAIR 的风控参数优化任务失败。"
        fi
    fi

    # --- 步骤 1c: 保存最终结果 ---
    echo "正在保存 $PAIR 的最终参数..."
    if [ -f "$STRATEGY_DEFAULT_PARAMS_HOST" ]; then
        mkdir -p "$(dirname "$PARAMS_FILE_HOST")"
        cp "$STRATEGY_DEFAULT_PARAMS_HOST" "$PARAMS_FILE_HOST"
        echo "已将 $PAIR 的最终参数保存至 $PARAMS_FILE_HOST"
    else
        echo "警告: 未找到由 Freqtrade 生成的最终参数文件，无法为 $PAIR 保存结果。"
    fi
done

echo "==================================================================="
echo "所有交易对的优化任务均已执行完毕。"
echo "优化结果已保存在 'user_data/hyperopt_results/' 目录中。"
echo "请继续执行步骤2来处理这些结果并生成参数文件。"
echo "==================================================================="

# 再次设置权限，以确保新创建的文件权限正确
set_permissions

echo ""
echo "优化脚本执行完毕。"
echo "" 