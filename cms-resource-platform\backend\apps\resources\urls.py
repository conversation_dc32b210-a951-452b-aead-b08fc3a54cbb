"""
资源管理URL配置
"""
from django.urls import path, include
from . import views

app_name = 'resources'

urlpatterns = [
    # 分类和标签
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('tags/', views.TagListView.as_view(), name='tag_list'),
    
    # 资源管理
    path('', views.ResourceListView.as_view(), name='resource_list'),
    path('create/', views.ResourceCreateView.as_view(), name='resource_create'),
    path('my/', views.MyResourcesView.as_view(), name='my_resources'),
    path('featured/', views.FeaturedResourcesView.as_view(), name='featured_resources'),
    path('stats/', views.resource_stats, name='resource_stats'),
    
    # 资源详情和操作
    path('<int:pk>/', views.ResourceDetailView.as_view(), name='resource_detail'),
    path('<int:pk>/update/', views.ResourceUpdateView.as_view(), name='resource_update'),
    path('<int:pk>/delete/', views.ResourceDeleteView.as_view(), name='resource_delete'),
    
    # 资源交互
    path('<int:pk>/download/', views.ResourceDownloadView.as_view(), name='resource_download'),
    path('<int:pk>/favorite/', views.ResourceFavoriteView.as_view(), name='resource_favorite'),
    path('<int:pk>/rating/', views.ResourceRatingView.as_view(), name='resource_rating'),
    path('<int:pk>/comments/', views.ResourceCommentListView.as_view(), name='resource_comments'),
]
