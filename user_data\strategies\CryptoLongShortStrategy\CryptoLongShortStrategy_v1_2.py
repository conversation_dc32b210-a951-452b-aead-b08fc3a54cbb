# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib


class CryptoLongShortStrategy_v1_2(IStrategy):
    """
    Crypto Long-Short Strategy v1.2 - 信号过滤优化版本
    
    第二次迭代改进重点:
    - 大幅提高信号强度阈值，显著减少低质量交易
    - 增加多重确认机制，确保信号可靠性
    - 引入冷却期机制，避免频繁交易
    - 优化市场环境过滤，只在最佳条件下交易
    
    目标:
    - 降低日均交易频率至20次以下
    - 提高信号质量和胜率
    - 减少噪音交易
    """

    # Strategy interface version
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy
    timeframe = '1h'
    
    # Can our strategy go long and short?
    can_short: bool = True
    
    # Leverage settings
    leverage_optimize = False
    leverage_num = 2.0  # 进一步降低杠杆
    
    # ROI table - 更保守的利润目标
    minimal_roi = {
        "0": 0.15,   # 15% profit target (提高)
        "60": 0.10,  # 10% after 60 minutes
        "120": 0.06, # 6% after 120 minutes
        "240": 0.03  # 3% after 240 minutes
    }

    # Stoploss - 进一步收紧
    stoploss = -0.10  # 10% base stop loss

    # Trailing stoploss - 更积极的追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02   # Start trailing at 2% profit
    trailing_stop_positive_offset = 0.03  # Trail by 3%
    trailing_only_offset_is_reached = True
    
    # === v1.2 优化参数 - 大幅提高阈值 ===
    
    # === 信号强度参数 - 显著提高 ===
    min_signal_strength = DecimalParameter(0.75, 0.90, default=0.80, space="buy", optimize=True, load=True)  # 大幅提高
    priority_signal_threshold = DecimalParameter(0.90, 0.98, default=0.95, space="buy", optimize=True, load=True)  # 接近完美信号
    
    # === RSI参数 - 更极端的阈值 ===
    rsi_overbought_threshold = DecimalParameter(80.0, 90.0, default=85.0, space="buy", optimize=True, load=True)
    rsi_oversold_threshold = DecimalParameter(10.0, 20.0, default=15.0, space="buy", optimize=True, load=True)
    
    # === 成交量确认参数 - 更严格 ===
    volume_surge_multiplier = DecimalParameter(5.0, 10.0, default=8.0, space="buy", optimize=True, load=True)
    min_volume_ratio = DecimalParameter(3.0, 5.0, default=4.0, space="buy", optimize=True, load=True)
    
    # === 价格变化阈值 - 更严格 ===
    pump_threshold_1h = DecimalParameter(0.12, 0.25, default=0.20, space="buy", optimize=True, load=True)
    dump_threshold_1h = DecimalParameter(0.12, 0.25, default=0.20, space="buy", optimize=True, load=True)
    
    # === 新增冷却期参数 ===
    trade_cooldown_hours = IntParameter(2, 6, default=4, space="buy", optimize=True, load=True)  # 交易冷却期
    pair_cooldown_hours = IntParameter(6, 12, default=8, space="buy", optimize=True, load=True)  # 单个交易对冷却期
    
    # === 多重确认参数 ===
    signal_confirmation_bars = IntParameter(3, 6, default=4, space="buy", optimize=True, load=True)  # 信号确认K线数
    trend_consistency_threshold = DecimalParameter(0.8, 0.95, default=0.90, space="buy", optimize=True, load=True)  # 趋势一致性
    
    # === 市场环境过滤 - 更严格 ===
    market_volatility_max = DecimalParameter(0.02, 0.06, default=0.04, space="buy", optimize=True, load=True)
    market_strength_min = DecimalParameter(0.7, 0.9, default=0.8, space="buy", optimize=True, load=True)

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200  # 增加启动蜡烛数

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        v1.2 指标计算 - 增加更多过滤指标
        """
        # 基础技术指标
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=14)
        dataframe['rsi_smooth'] = ta.EMA(dataframe['rsi'], timeperiod=5)  # 更平滑的RSI
        
        # 多重EMA系统
        dataframe['ema_12'] = ta.EMA(dataframe['close'], timeperiod=12)
        dataframe['ema_26'] = ta.EMA(dataframe['close'], timeperiod=26)
        dataframe['ema_50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['ema_100'] = ta.EMA(dataframe['close'], timeperiod=100)
        dataframe['ema_200'] = ta.EMA(dataframe['close'], timeperiod=200)
        
        # MACD
        macd, macdsignal, macdhist = ta.MACD(dataframe['close'])
        dataframe['macd'] = macd
        dataframe['macd_signal'] = macdsignal
        dataframe['macd_hist'] = macdhist
        
        # ATR and volatility
        dataframe['atr'] = ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=14)
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']
        dataframe['volatility_ma'] = ta.SMA(dataframe['volatility'], timeperiod=20)
        
        # 成交量指标 - 增强
        dataframe['volume_sma_10'] = ta.SMA(dataframe['volume'], timeperiod=10)
        dataframe['volume_sma_30'] = ta.SMA(dataframe['volume'], timeperiod=30)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma_30']
        dataframe['volume_trend'] = dataframe['volume_sma_10'] / dataframe['volume_sma_30']
        
        # 价格变化指标
        dataframe['price_change_1h'] = dataframe['close'].pct_change(1)
        dataframe['price_change_4h'] = dataframe['close'].pct_change(4)
        dataframe['price_change_12h'] = dataframe['close'].pct_change(12)
        dataframe['price_change_24h'] = dataframe['close'].pct_change(24)
        
        # 趋势强度和一致性
        dataframe = self.calculate_trend_metrics(dataframe)
        
        # Z-score计算 - 更长周期
        dataframe = self.calculate_zscore_indicators(dataframe)
        
        # 多空信号指标 - 更严格
        dataframe = self.calculate_enhanced_long_short_indicators(dataframe)
        
        # 信号质量评分 - 重新设计
        dataframe = self.calculate_v12_signal_quality_score(dataframe)
        
        # 市场环境评估 - 更全面
        dataframe = self.calculate_comprehensive_market_environment(dataframe)
        
        # 冷却期检查
        dataframe = self.calculate_cooldown_filters(dataframe, metadata)
        
        return dataframe
    
    def calculate_trend_metrics(self, dataframe: DataFrame) -> DataFrame:
        """计算趋势强度和一致性指标"""
        
        # EMA排列强度 - 5重EMA确认
        ema_bullish_alignment = (
            (dataframe['ema_12'] > dataframe['ema_26']) &
            (dataframe['ema_26'] > dataframe['ema_50']) &
            (dataframe['ema_50'] > dataframe['ema_100']) &
            (dataframe['ema_100'] > dataframe['ema_200'])
        ).astype(int)
        
        ema_bearish_alignment = (
            (dataframe['ema_12'] < dataframe['ema_26']) &
            (dataframe['ema_26'] < dataframe['ema_50']) &
            (dataframe['ema_50'] < dataframe['ema_100']) &
            (dataframe['ema_100'] < dataframe['ema_200'])
        ).astype(int)
        
        # 趋势一致性 - 过去N个K线的一致性
        consistency_period = 6
        dataframe['bullish_consistency'] = ema_bullish_alignment.rolling(consistency_period).mean()
        dataframe['bearish_consistency'] = ema_bearish_alignment.rolling(consistency_period).mean()
        
        # 价格相对EMA位置强度
        dataframe['price_ema_strength'] = abs(dataframe['close'] - dataframe['ema_50']) / dataframe['ema_50']
        
        # MACD趋势确认
        dataframe['macd_bullish'] = (
            (dataframe['macd'] > dataframe['macd_signal']) &
            (dataframe['macd'] > 0) &
            (dataframe['macd_hist'] > 0)
        ).astype(int)
        
        dataframe['macd_bearish'] = (
            (dataframe['macd'] < dataframe['macd_signal']) &
            (dataframe['macd'] < 0) &
            (dataframe['macd_hist'] < 0)
        ).astype(int)
        
        return dataframe
    
    def calculate_zscore_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算Z-score异常检测指标 - 更长周期更稳定"""
        
        lookback_period = 72  # 72小时回看期，更稳定
        
        # 价格变化Z-score
        for period in ['1h', '4h', '12h']:
            col_name = f'price_change_{period}'
            zscore_col = f'price_{period}_zscore'
            
            rolling_mean = dataframe[col_name].rolling(lookback_period).mean()
            rolling_std = dataframe[col_name].rolling(lookback_period).std()
            
            dataframe[zscore_col] = (dataframe[col_name] - rolling_mean) / rolling_std
            dataframe[zscore_col] = dataframe[zscore_col].fillna(0)
        
        # 成交量Z-score
        dataframe['volume_zscore'] = (
            (dataframe['volume_ratio'] - dataframe['volume_ratio'].rolling(lookback_period).mean()) / 
            dataframe['volume_ratio'].rolling(lookback_period).std()
        ).fillna(0)
        
        # 综合异常分数
        dataframe['anomaly_score_long'] = (
            np.where(dataframe['price_1h_zscore'] < -3, 30,
                    np.where(dataframe['price_1h_zscore'] < -2.5, 20,
                            np.where(dataframe['price_1h_zscore'] < -2, 10, 0))) +
            np.where(dataframe['volume_zscore'] > 3, 20,
                    np.where(dataframe['volume_zscore'] > 2, 10, 0))
        )
        
        dataframe['anomaly_score_short'] = (
            np.where(dataframe['price_1h_zscore'] > 3, 30,
                    np.where(dataframe['price_1h_zscore'] > 2.5, 20,
                            np.where(dataframe['price_1h_zscore'] > 2, 10, 0))) +
            np.where(dataframe['volume_zscore'] > 3, 20,
                    np.where(dataframe['volume_zscore'] > 2, 10, 0))
        )
        
        return dataframe
    
    def calculate_enhanced_long_short_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算增强的多空信号指标 - 更严格的条件"""
        
        # === 做多信号指标 - 极严格 ===
        dataframe['extreme_oversold'] = (
            (dataframe['rsi_smooth'] < self.rsi_oversold_threshold.value) &
            (dataframe['rsi_smooth'] > dataframe['rsi_smooth'].shift(1)) &
            (dataframe['rsi_smooth'] > dataframe['rsi_smooth'].shift(2)) &
            (dataframe['rsi_smooth'] > dataframe['rsi_smooth'].shift(3))  # 连续3期上升
        )
        
        dataframe['strong_support_bounce'] = (
            (dataframe['close'] > dataframe['low'].rolling(30).min() * 1.02) &
            (dataframe['close'] < dataframe['low'].rolling(30).min() * 1.06) &
            (dataframe['volume_ratio'] > self.min_volume_ratio.value) &
            (dataframe['volume_trend'] > 1.2)
        )
        
        dataframe['perfect_bullish_alignment'] = (
            (dataframe['bullish_consistency'] >= self.trend_consistency_threshold.value) &
            (dataframe['macd_bullish'] == 1) &
            (dataframe['price_ema_strength'] > 0.02)
        )
        
        dataframe['volume_explosion_up'] = (
            (dataframe['volume_ratio'] > self.volume_surge_multiplier.value) &
            (dataframe['price_change_1h'] > 0.03) &
            (dataframe['price_change_4h'] > 0.08)
        )
        
        # === 做空信号指标 - 极严格 ===
        dataframe['extreme_overbought'] = (
            (dataframe['rsi_smooth'] > self.rsi_overbought_threshold.value) &
            (dataframe['rsi_smooth'] < dataframe['rsi_smooth'].shift(1)) &
            (dataframe['rsi_smooth'] < dataframe['rsi_smooth'].shift(2)) &
            (dataframe['rsi_smooth'] < dataframe['rsi_smooth'].shift(3))  # 连续3期下降
        )
        
        dataframe['strong_resistance_rejection'] = (
            (dataframe['close'] < dataframe['high'].rolling(30).max() * 0.98) &
            (dataframe['close'] > dataframe['high'].rolling(30).max() * 0.94) &
            (dataframe['volume_ratio'] > self.min_volume_ratio.value) &
            (dataframe['volume_trend'] > 1.2)
        )
        
        dataframe['perfect_bearish_alignment'] = (
            (dataframe['bearish_consistency'] >= self.trend_consistency_threshold.value) &
            (dataframe['macd_bearish'] == 1) &
            (dataframe['price_ema_strength'] > 0.02)
        )
        
        dataframe['volume_explosion_down'] = (
            (dataframe['volume_ratio'] > self.volume_surge_multiplier.value) &
            (dataframe['price_change_1h'] < -0.03) &
            (dataframe['price_change_4h'] < -0.08)
        )
        
        return dataframe
    
    def calculate_v12_signal_quality_score(self, dataframe: DataFrame) -> DataFrame:
        """v1.2 信号质量评分 - 重新设计，更严格"""
        
        # === 做多信号质量评分 (0-100分) ===
        long_score = np.zeros(len(dataframe))
        
        # 基础技术条件 (0-20分) - 降低权重
        long_score += np.where(dataframe['extreme_oversold'], 15, 0)
        long_score += np.where(dataframe['strong_support_bounce'], 5, 0)
        
        # 趋势确认 (0-30分) - 提高权重
        long_score += np.where(dataframe['perfect_bullish_alignment'], 30, 0)
        
        # 成交量确认 (0-25分)
        long_score += np.where(dataframe['volume_explosion_up'], 25, 0)
        
        # 异常检测 (0-25分)
        long_score += np.clip(dataframe['anomaly_score_long'], 0, 25)
        
        dataframe['long_signal_quality'] = np.clip(long_score, 0, 100)
        
        # === 做空信号质量评分 (0-100分) ===
        short_score = np.zeros(len(dataframe))
        
        # 基础技术条件 (0-20分) - 降低权重
        short_score += np.where(dataframe['extreme_overbought'], 15, 0)
        short_score += np.where(dataframe['strong_resistance_rejection'], 5, 0)
        
        # 趋势确认 (0-30分) - 提高权重
        short_score += np.where(dataframe['perfect_bearish_alignment'], 30, 0)
        
        # 成交量确认 (0-25分)
        short_score += np.where(dataframe['volume_explosion_down'], 25, 0)
        
        # 异常检测 (0-25分)
        short_score += np.clip(dataframe['anomaly_score_short'], 0, 25)
        
        dataframe['short_signal_quality'] = np.clip(short_score, 0, 100)
        
        return dataframe
    
    def calculate_comprehensive_market_environment(self, dataframe: DataFrame) -> DataFrame:
        """计算全面的市场环境指标"""
        
        # 市场趋势强度
        dataframe['strong_bull_market'] = (
            (dataframe['close'] > dataframe['ema_200']) &
            (dataframe['bullish_consistency'] > 0.8) &
            (dataframe['price_change_24h'] > 0.10) &
            (dataframe['macd_bullish'] == 1)
        )
        
        dataframe['strong_bear_market'] = (
            (dataframe['close'] < dataframe['ema_200']) &
            (dataframe['bearish_consistency'] > 0.8) &
            (dataframe['price_change_24h'] < -0.10) &
            (dataframe['macd_bearish'] == 1)
        )
        
        # 市场波动率过滤 - 更严格
        dataframe['acceptable_volatility'] = (
            dataframe['volatility'] < self.market_volatility_max.value
        )
        
        # 流动性评估 - 更严格
        dataframe['excellent_liquidity'] = (
            (dataframe['volume_ratio'] > 2.0) &
            (dataframe['volume_trend'] > 1.1) &
            (dataframe['volume_sma_10'] > dataframe['volume_sma_30'])
        )
        
        # 市场强度综合评分
        market_strength = np.zeros(len(dataframe))
        market_strength += np.where(dataframe['strong_bull_market'] | dataframe['strong_bear_market'], 40, 0)
        market_strength += np.where(dataframe['acceptable_volatility'], 30, 0)
        market_strength += np.where(dataframe['excellent_liquidity'], 30, 0)
        
        dataframe['market_strength'] = market_strength / 100  # 标准化到0-1
        
        return dataframe
    
    def calculate_cooldown_filters(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算冷却期过滤器"""
        
        # 这里简化处理，实际应该检查交易历史
        # 在实际实现中，需要访问交易历史来计算冷却期
        dataframe['trade_cooldown_ok'] = True  # 简化处理
        dataframe['pair_cooldown_ok'] = True   # 简化处理
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        v1.2 入场信号生成 - 极严格的信号筛选
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''
        
        # === 基础市场环境过滤 - 更严格 ===
        market_filter = (
            (dataframe['market_strength'] >= self.market_strength_min.value) &
            (dataframe['excellent_liquidity']) &
            (dataframe['acceptable_volatility']) &
            (dataframe['trade_cooldown_ok']) &
            (dataframe['pair_cooldown_ok'])
        )
        
        # === 做多入场条件 - 极严格 ===
        # 只有最高质量的信号才能通过
        ultra_priority_long = (
            (dataframe['long_signal_quality'] >= self.priority_signal_threshold.value * 100) &
            (dataframe['extreme_oversold']) &
            (dataframe['perfect_bullish_alignment']) &
            (dataframe['volume_explosion_up']) &
            (dataframe['anomaly_score_long'] >= 40) &  # 极高异常分数
            (~dataframe['strong_bear_market']) &
            market_filter
        )
        
        # 高质量标准信号 - 仍然很严格
        high_quality_long = (
            (dataframe['long_signal_quality'] >= self.min_signal_strength.value * 100) &
            (dataframe['extreme_oversold'] | dataframe['strong_support_bounce']) &
            (dataframe['perfect_bullish_alignment']) &
            (dataframe['volume_explosion_up'] | (dataframe['volume_ratio'] > 5)) &
            (dataframe['anomaly_score_long'] >= 25) &
            (~dataframe['strong_bear_market']) &
            market_filter
        )
        
        # === 做空入场条件 - 极严格 ===
        # 只有最高质量的信号才能通过
        ultra_priority_short = (
            (dataframe['short_signal_quality'] >= self.priority_signal_threshold.value * 100) &
            (dataframe['extreme_overbought']) &
            (dataframe['perfect_bearish_alignment']) &
            (dataframe['volume_explosion_down']) &
            (dataframe['anomaly_score_short'] >= 40) &  # 极高异常分数
            (~dataframe['strong_bull_market']) &
            market_filter
        )
        
        # 高质量标准信号 - 仍然很严格
        high_quality_short = (
            (dataframe['short_signal_quality'] >= self.min_signal_strength.value * 100) &
            (dataframe['extreme_overbought'] | dataframe['strong_resistance_rejection']) &
            (dataframe['perfect_bearish_alignment']) &
            (dataframe['volume_explosion_down'] | (dataframe['volume_ratio'] > 5)) &
            (dataframe['anomaly_score_short'] >= 25) &
            (~dataframe['strong_bull_market']) &
            market_filter
        )
        
        # === 多重确认机制 ===
        # 确保信号在连续几个K线内保持高质量
        confirmation_bars = self.signal_confirmation_bars.value
        
        for i in range(1, confirmation_bars):
            ultra_priority_long &= (dataframe['long_signal_quality'].shift(i) >= 70)
            ultra_priority_short &= (dataframe['short_signal_quality'].shift(i) >= 70)
            high_quality_long &= (dataframe['long_signal_quality'].shift(i) >= 60)
            high_quality_short &= (dataframe['short_signal_quality'].shift(i) >= 60)
        
        # === 设置信号 ===
        dataframe.loc[ultra_priority_long, ['enter_long', 'enter_tag']] = (1, 'ultra_long_v1.2')
        dataframe.loc[ultra_priority_short, ['enter_short', 'enter_tag']] = (1, 'ultra_short_v1.2')
        
        dataframe.loc[high_quality_long & ~ultra_priority_long, ['enter_long', 'enter_tag']] = (1, 'high_long_v1.2')
        dataframe.loc[high_quality_short & ~ultra_priority_short, ['enter_short', 'enter_tag']] = (1, 'high_short_v1.2')
        
        # === 信号冲突处理 - 更保守 ===
        conflict_mask = (dataframe['enter_long'] == 1) & (dataframe['enter_short'] == 1)
        
        # 如果有冲突，优先选择质量更高的，如果差距不大则都取消
        quality_diff = abs(dataframe['long_signal_quality'] - dataframe['short_signal_quality'])
        
        # 质量差距小于15分则都取消（更保守）
        dataframe.loc[conflict_mask & (quality_diff < 15), ['enter_long', 'enter_short']] = 0
        dataframe.loc[conflict_mask & (quality_diff >= 15) & (dataframe['long_signal_quality'] > dataframe['short_signal_quality']), 'enter_short'] = 0
        dataframe.loc[conflict_mask & (quality_diff >= 15) & (dataframe['short_signal_quality'] > dataframe['long_signal_quality']), 'enter_long'] = 0
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        v1.2 出场信号生成 - 更快的利润锁定
        """
        # 初始化出场信号
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = ''
        
        # === 做多出场条件 - 更快利润锁定 ===
        long_quick_profit = (
            (dataframe['rsi'] > 70) &  # 降低RSI要求
            (dataframe['price_change_1h'] < -0.01) &  # 小幅回调就出场
            (dataframe['volume_ratio'] > 2.0) &
            (dataframe['long_signal_quality'] < 50)  # 信号质量下降
        )
        
        long_trend_reversal = (
            (dataframe['perfect_bearish_alignment']) &
            (dataframe['extreme_overbought']) &
            (dataframe['volume_explosion_down'])
        )
        
        # === 做空出场条件 - 更快利润锁定 ===
        short_quick_profit = (
            (dataframe['rsi'] < 30) &  # 提高RSI要求
            (dataframe['price_change_1h'] > 0.01) &  # 小幅反弹就出场
            (dataframe['volume_ratio'] > 2.0) &
            (dataframe['short_signal_quality'] < 50)  # 信号质量下降
        )
        
        short_trend_reversal = (
            (dataframe['perfect_bullish_alignment']) &
            (dataframe['extreme_oversold']) &
            (dataframe['volume_explosion_up'])
        )
        
        # 设置出场信号
        dataframe.loc[long_quick_profit, ['exit_long', 'exit_tag']] = (1, 'long_quick_profit_v1.2')
        dataframe.loc[long_trend_reversal, ['exit_long', 'exit_tag']] = (1, 'long_reversal_v1.2')
        
        dataframe.loc[short_quick_profit, ['exit_short', 'exit_tag']] = (1, 'short_quick_profit_v1.2')
        dataframe.loc[short_trend_reversal, ['exit_short', 'exit_tag']] = (1, 'short_reversal_v1.2')
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        v1.2 动态止损 - 更积极的利润保护
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return self.stoploss
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 基础止损
        base_stoploss = self.stoploss
        
        # 根据利润调整止损 - 更积极
        if current_profit > 0.10:  # 10%以上利润
            return -0.015  # 收紧至1.5%
        elif current_profit > 0.06:  # 6%以上利润
            return -0.03   # 收紧至3%
        elif current_profit > 0.03:  # 3%以上利润
            return -0.05   # 收紧至5%
        
        # 根据信号质量调整
        if trade.is_short:
            signal_quality = current_candle.get('short_signal_quality', 50)
        else:
            signal_quality = current_candle.get('long_signal_quality', 50)
        
        if signal_quality < 40:  # 信号质量严重下降
            return base_stoploss * 0.6  # 大幅收紧止损
        elif signal_quality < 60:  # 信号质量下降
            return base_stoploss * 0.8  # 收紧止损
        
        return base_stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str], 
                side: str, **kwargs) -> float:
        """
        v1.2 动态杠杆管理 - 更保守
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return 1.0
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 基础杠杆 - 进一步降低
        base_leverage = 2.0
        
        # 根据信号类型调整杠杆
        if entry_tag:
            if 'ultra' in entry_tag:
                leverage_multiplier = 1.0  # 最高质量信号
            elif 'high' in entry_tag:
                leverage_multiplier = 0.8  # 高质量信号
            else:
                leverage_multiplier = 0.5
        else:
            leverage_multiplier = 0.5
        
        # 根据市场强度调整
        market_strength = current_candle.get('market_strength', 0.5)
        leverage_multiplier *= (0.5 + market_strength * 0.5)  # 0.5-1.0倍调整
        
        final_leverage = min(max_leverage, base_leverage * leverage_multiplier)
        return max(1.0, final_leverage)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                           side: str, **kwargs) -> bool:
        """
        v1.2 交易确认 - 最终的严格检查
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return False
        
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 市场强度检查
        if current_candle.get('market_strength', 0) < self.market_strength_min.value:
            return False
        
        # 流动性检查
        if not current_candle.get('excellent_liquidity', False):
            return False
        
        # 波动率检查
        if not current_candle.get('acceptable_volatility', False):
            return False
        
        # 信号质量最终检查
        if side == 'long':
            signal_quality = current_candle.get('long_signal_quality', 0)
            if signal_quality < self.min_signal_strength.value * 100:
                return False
            # 确保不是在强熊市中做多
            if current_candle.get('strong_bear_market', False):
                return False
        else:  # short
            signal_quality = current_candle.get('short_signal_quality', 0)
            if signal_quality < self.min_signal_strength.value * 100:
                return False
            # 确保不是在强牛市中做空
            if current_candle.get('strong_bull_market', False):
                return False
        
        return True

    def informative_pairs(self):
        """
        定义需要的额外数据对
        """
        return []