# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import logging
import pandas_ta as pta
from freqtrade.persistence import Trade
from datetime import datetime
import json
from pathlib import Path

logger = logging.getLogger(__name__)

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy - V5 (Optimized Production Version)
#
#   作者: Gemini & User
#   版本: V5.2-Optimized
#
#   策略理念:
#   - HAPBS V5的生产版本，用于实盘或回测优化后的参数。
#   - 从外部JSON文件为每个交易对加载独立的参数。
#   - 继承了V5的所有核心风控逻辑 (ATR动态止损, 高水位追踪)。
# --------------------------------

class HAPBS_V5_Optimized(IStrategy):

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 高级仓位管理配置 ---
    position_adjustment_enable = True
    max_entry_position_adjustment = 1

    # --- V5 风控参数 (占位符) ---
    stoploss = -0.99 
    use_custom_stoploss = True
    use_custom_exit = True

    # --- 参数从JSON加载 ---
    def __init__(self, config: dict):
        super().__init__(config)
        self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                self.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it to function. Please create it.")
            self.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}. Strategy may not function correctly.")
            self.custom_info = {}

    # --- 初始止损 (V5.1核心) ---
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        if pair not in self.custom_info: return 0.99
        pair_settings = self.custom_info[pair]

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        # 使用交易开仓那一刻的ATR值来计算初始止损
        trade_entry_candle = dataframe.loc[dataframe['date'] == trade.open_date_utc]
        if not trade_entry_candle.empty:
            atr_val = trade_entry_candle.iloc[0]['atr']
            if atr_val > 0:
                multiplier = pair_settings.get('atr_stoploss_multiplier', 2.5)
                return (atr_val / trade.open_rate) * multiplier
        
        return 0.99

    # --- 动态追踪止损 (V5.1核心) ---
    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        if pair not in self.custom_info:
            return None
        pair_settings = self.custom_info[pair]
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1]

        # 激活追踪止损
        trailing_profit_pct = pair_settings.get('atr_trailing_profit_pct', 0.03)
        if current_profit > trailing_profit_pct:
            atr_val = last_candle['atr']
            offset_multiplier = pair_settings.get('atr_trailing_offset_multiplier', 1.5)
            if trade.enter_tag == 'long_entry':
                # 计算新的止损价格
                new_stop_price = current_rate - (atr_val * offset_multiplier)
                # 高水位逻辑: 止损只上移
                trade.stop_loss = max(trade.stop_loss, new_stop_price)
            elif trade.enter_tag == 'short_entry':
                new_stop_price = current_rate + (atr_val * offset_multiplier)
                # 高水位逻辑: 止损只下移
                trade.stop_loss = min(trade.stop_loss, new_stop_price)
        
        return None

    # --- 指标计算 ---
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        if pair not in self.custom_info:
            # 如果没有参数，也需要创建列以避免崩溃
            dataframe['enter_long'] = 0
            dataframe['enter_short'] = 0
            dataframe['enter_tag'] = None
            return dataframe
        
        pair_settings = self.custom_info[pair]
        
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        dataframe['ha_strong_bull'] = (dataframe['ha_close'] > dataframe['ha_open']) & (dataframe['ha_open'] == dataframe['ha_low'])
        dataframe['ha_strong_bear'] = (dataframe['ha_close'] < dataframe['ha_open']) & (dataframe['ha_open'] == dataframe['ha_high'])
        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_long'])
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_long'])
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_short'])
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_short'])
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=14)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=14)
        dataframe['chop'] = pta.chop(dataframe['high'], dataframe['low'], dataframe['close'], length=14)
        
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_sma_long'] = ta.SMA(dataframe['obv'], timeperiod=pair_settings['obv_period_long'])
        dataframe['obv_sma_short'] = ta.SMA(dataframe['obv'], timeperiod=pair_settings['obv_period_short'])

        return dataframe

    # --- 入场逻辑 ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        if pair not in self.custom_info:
            dataframe[['enter_long', 'enter_short', 'enter_tag']] = (0, 0, None)
            return dataframe
        
        pair_settings = self.custom_info[pair]

        # Ensure columns exist, in case they were not created in populate_indicators
        if 'enter_long' not in dataframe.columns:
            dataframe['enter_long'] = 0
        if 'enter_tag' not in dataframe.columns:
            dataframe['enter_tag'] = None
        if 'enter_short' not in dataframe.columns:
            dataframe['enter_short'] = 0
            
        long_prerequisites = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (qtpylib.crossed_above(dataframe['obv'], dataframe['obv_sma_long'])) &
            (dataframe['chop'] < pair_settings['chop_threshold_long']) &
            (dataframe['plus_di'] > dataframe['minus_di'])
        )
        cond_long_entry_signal = (
            qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long']) |
            dataframe['ha_strong_bull']
        )
        dataframe.loc[long_prerequisites & cond_long_entry_signal, ['enter_long', 'enter_tag']] = (1, 'long_entry')

        short_prerequisites = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (qtpylib.crossed_below(dataframe['obv'], dataframe['obv_sma_short'])) &
            (dataframe['chop'] < pair_settings['chop_threshold_short']) &
            (dataframe['minus_di'] > dataframe['plus_di'])
        )
        cond_short_entry_signal = (
            qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short']) |
            dataframe['ha_strong_bear']
        )
        dataframe.loc[short_prerequisites & cond_short_entry_signal, ['enter_short', 'enter_tag']] = (1, 'short_entry')
        return dataframe

    # --- 加仓逻辑 ---
    def adjust_trade_position(self, trade: Trade, current_time: datetime, current_rate: float,
                              current_profit: float, **kwargs) -> float:
        
        pair = trade.pair
        if pair not in self.custom_info:
            return None
        pair_settings = self.custom_info[pair]
        
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # 使用 trade.nr_of_successful_entries 替代蜡烛计数器作为简化的时间锁
        if trade.enter_tag == 'long_entry':
            if trade.nr_of_successful_entries > pair_settings['pyramiding_lock_candles_long']:
                if current_profit > pair_settings['pyramiding_profit_pct_long']:
                    if last_candle['ha_strong_bull']:
                        return trade.stake_amount
        
        elif trade.enter_tag == 'short_entry':
            if trade.nr_of_successful_entries > pair_settings['pyramiding_lock_candles_short']:
                if current_profit > pair_settings['pyramiding_profit_pct_short']:
                    if last_candle['ha_strong_bear']:
                        return trade.stake_amount

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Exit logic is handled by custom_exit and custom_stoploss
        return dataframe 