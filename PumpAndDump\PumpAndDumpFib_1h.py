# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy, stoploss_from_open
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from datetime import datetime, timedelta
from freqtrade.persistence import Trade
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy,informative

# --- Strategy specific imports ---
import numpy as np
import pandas as pd
import logging
import pytz

log = logging.getLogger(__name__)


class PumpAndDumpFib1h(IStrategy):
    """
    ## Pump and Dump Fibonacci Strategy (1h Version) V1.2

    **作者:** Gemini 2.5 Pro & User
    **版本:** 1.2
    **核心理念:**
    - **入场逻辑:** 完全沿用 PumpAndDumpLongShort V1.6 的入场条件。
    - **出场优化:** 采用斐波那契扩展位作为动态止盈目标。
    - **ATR自适应杠杆:** 杠杆大小根据市场波动率（ATR）动态调整，高波动低杠杆，低波动高杠杆。
    - **图表增强:** 在图表上绘制斐波那契波段高/低点及止盈目标，方便复盘分析。

    **时间框架:** 1小时 (1h)
    """
    INTERFACE_VERSION = 3

    # --- 策略核心参数 ---
    timeframe = '1h'
    can_short = True
    process_only_new_candles = True

    # --- 启动和回测所需的数据量 ---
    startup_candle_count: int = 100 # 需要更多数据来计算斐波那契

    # --- 订单类型配置 ---
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': True
    }
    
    # --- 止损和止盈设置 ---
    stoploss = -0.99 # 虚拟值，实际止损由 custom_stoploss 控制
    use_custom_stoploss = True
    use_custom_exit = True
    trailing_stop = False

    # Optional plot configuration
    plot_config = {
        'main_plot': {
            'fib_swing_high': {
                'color': 'rgba(7, 255, 7, 0.4)',
                'plotly': {'symbol': 'cross', 'size': 10, 'name': 'Swing High'}
            },
            'fib_swing_low': {
                'color': 'rgba(255, 7, 7, 0.4)',
                'plotly': {'symbol': 'cross', 'size': 10, 'name': 'Swing Low'}
            },
            'fib_exit_target': {
                'color': 'rgba(7, 7, 255, 0.5)',
                'plotly': {'symbol': 'diamond', 'size': 10, 'name': 'TP Target'}
            },
        },
        'subplots': {
            "Signals": {
                'climax_candle': {'color': '#FFDD32', 'type': 'bar'},
                'panic_candle': {'color': '#FF00FF', 'type': 'bar'},
            },
            "ATR": {'atr': {'color': 'blue'}},
            "RSI": {'rsi': {'color': 'orange'}},
        }
    }

    # =========================================================================================================
    # --- HYPEROPT PARAMETERS ---
    # =========================================================================================================

    # --- 斐波那契出场参数 ---
    fib_lookback_period = IntParameter(12, 48, default=24, space="buy", optimize=True)
    fib_exit_level = DecimalParameter(0.5, 1.7, default=1.0, decimals=3, space="sell", optimize=True)

    # --- 做空 (Short) 参数 ---
    short_consecutive_green_candles = IntParameter(2, 5, default=3, space="buy", optimize=True)
    short_pump_rsi_threshold = IntParameter(60, 80, default=65, space="buy", optimize=True)
    short_upper_wick_body_ratio = DecimalParameter(1.2, 2.0, default=1.3, decimals=1, space="buy", optimize=True)
    short_climax_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    short_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    short_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    short_min_climax_conditions = IntParameter(3, 5, default=4, space="buy", optimize=True)
    
    # --- 做多 (Long) 参数 (与做空相反) ---
    long_consecutive_red_candles = IntParameter(2, 5, default=3, space="buy", optimize=True)
    long_dump_rsi_threshold = IntParameter(20, 40, default=35, space="buy", optimize=True)
    long_lower_wick_body_ratio = DecimalParameter(1.2, 2.0, default=1.3, decimals=1, space="buy", optimize=True)
    long_panic_sma_period = IntParameter(10, 40, default=20, space="buy", optimize=True)
    long_volume_spike_multiplier = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True)
    long_range_spike_multiplier = DecimalParameter(1.5, 2.5, default=1.5, decimals=1, space="buy", optimize=True)
    long_min_panic_conditions = IntParameter(3, 5, default=4, space="buy", optimize=True)

    # --- 通用风险管理和冷却参数 ---
    stoploss_lookback = IntParameter(5, 20, default=10, space="sell", optimize=False)
    cooldown_period = IntParameter(6, 48, default=12, space="buy", optimize=False)
    
    # --- ATR 自适应杠杆参数 ---
    atr_period = IntParameter(10, 20, default=14, space="buy", optimize=False)
    leverage_target_atr_pct = DecimalParameter(0.02, 0.15, default=0.07, decimals=3, space="buy", optimize=False)
    leverage_max = DecimalParameter(2.0, 10.0, default=5.0, decimals=1, space="buy", optimize=False)

    # 存储每个交易对的斐波那契水平
    trade_fib_levels: dict = {}

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- ATR ---
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)

        # --- Plotting Columns ---
        dataframe['fib_swing_high'] = np.nan
        dataframe['fib_swing_low'] = np.nan
        dataframe['fib_exit_target'] = np.nan
        
        # --- 原有指标计算 ---
        dataframe['is_green_candle'] = (dataframe['close'] > dataframe['open']).astype(int)
        dataframe['is_red_candle'] = (dataframe['close'] < dataframe['open']).astype(int)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        # --- 做空信号 (Short Signals) ---
        dataframe['volume_is_rising'] = dataframe['volume'] > dataframe['volume'].shift(1)
        dataframe['green_with_rising_vol'] = (dataframe['is_green_candle'] == 1) & (dataframe['volume_is_rising'] == 1)
        dataframe['consecutive_green_rising_vol'] = dataframe['green_with_rising_vol'].rolling(
            window=self.short_consecutive_green_candles.value).sum()
        
        body = abs(dataframe['close'] - dataframe['open'])
        upper_wick = dataframe['high'] - np.maximum(dataframe['open'], dataframe['close'])
        dataframe['shooting_star'] = ((upper_wick > body * self.short_upper_wick_body_ratio.value) & (body > 0.000001)).astype('int')
        
        dataframe['range_1h'] = dataframe['high'] - dataframe['low']
        short_range_sma = ta.SMA(dataframe['range_1h'], timeperiod=self.short_climax_sma_period.value)
        short_volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.short_climax_sma_period.value)
        
        dataframe['short_range_spike'] = (dataframe['range_1h'] > short_range_sma * self.short_range_spike_multiplier.value).astype('int')
        dataframe['short_volume_spike'] = (dataframe['volume'] > short_volume_sma * self.short_volume_spike_multiplier.value).astype('int')
        
        short_trend_cond = (dataframe['consecutive_green_rising_vol'] >= self.short_consecutive_green_candles.value).astype('int')
        short_rsi_cond = (dataframe['rsi'] >= self.short_pump_rsi_threshold.value).astype('int')
        
        dataframe['climax_conditions_met'] = (short_trend_cond + short_rsi_cond + dataframe['shooting_star'] + dataframe['short_volume_spike'] + dataframe['short_range_spike'])
        dataframe['climax_candle'] = (dataframe['climax_conditions_met'] >= self.short_min_climax_conditions.value).astype('int')

        is_shrinking_volume = dataframe['volume'] < dataframe['volume'].shift(1)
        is_lower_high = dataframe['high'] <= dataframe['high'].shift(1)
        dataframe['pause_candle_short'] = ((dataframe['is_green_candle'] == 1) & (is_shrinking_volume == 1) & (is_lower_high == 1)).astype('int')

        # --- 做多信号 (Long Signals) ---
        dataframe['red_with_rising_vol'] = (dataframe['is_red_candle'] == 1) & (dataframe['volume_is_rising'] == 1)
        dataframe['consecutive_red_rising_vol'] = dataframe['red_with_rising_vol'].rolling(
            window=self.long_consecutive_red_candles.value).sum()

        lower_wick = np.minimum(dataframe['open'], dataframe['close']) - dataframe['low']
        dataframe['hammer'] = ((lower_wick > body * self.long_lower_wick_body_ratio.value) & (body > 0.000001)).astype('int')

        long_range_sma = ta.SMA(dataframe['range_1h'], timeperiod=self.long_panic_sma_period.value)
        long_volume_sma = ta.SMA(dataframe['volume'], timeperiod=self.long_panic_sma_period.value)

        dataframe['long_range_spike'] = (dataframe['range_1h'] > long_range_sma * self.long_range_spike_multiplier.value).astype('int')
        dataframe['long_volume_spike'] = (dataframe['volume'] > long_volume_sma * self.long_volume_spike_multiplier.value).astype('int')
        
        long_trend_cond = (dataframe['consecutive_red_rising_vol'] >= self.long_consecutive_red_candles.value).astype('int')
        long_rsi_cond = (dataframe['rsi'] <= self.long_dump_rsi_threshold.value).astype('int')

        dataframe['panic_conditions_met'] = (long_trend_cond + long_rsi_cond + dataframe['hammer'] + dataframe['long_volume_spike'] + dataframe['long_range_spike'])
        dataframe['panic_candle'] = (dataframe['panic_conditions_met'] >= self.long_min_panic_conditions.value).astype('int')
        
        is_higher_low = dataframe['low'] >= dataframe['low'].shift(1)
        dataframe['pause_candle_long'] = ((dataframe['is_red_candle'] == 1) & (is_shrinking_volume == 1) & (is_higher_low == 1)).astype('int')

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        cooldown_seconds = self.cooldown_period.value * 3600

        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''

        # --- 冷却期逻辑 ---
        if Trade.get_trades_proxy(pair=pair, is_open=True):
            return dataframe
        
        closed_trades = Trade.get_trades_proxy(pair=pair, is_open=False)
        if closed_trades:
            last_trade = sorted(closed_trades, key=lambda t: t.close_date_utc, reverse=True)[0]
            if (dataframe['date'].iloc[-1].to_pydatetime().replace(tzinfo=pytz.UTC) - last_trade.close_date_utc) < timedelta(seconds=cooldown_seconds):
                return dataframe

        # --- 使用原版入场逻辑 ---
        short_trigger_1 = (dataframe['climax_candle'].shift(1) == 1) & (dataframe['is_red_candle'] == 1)
        short_trigger_2 = (dataframe['climax_candle'].shift(2) == 1) & (dataframe['pause_candle_short'].shift(1) == 1) & (dataframe['is_red_candle'] == 1)
        short_mask = short_trigger_1 | short_trigger_2
        dataframe.loc[short_mask, 'enter_short'] = 1
        dataframe.loc[short_mask, 'enter_tag'] = 'short_fib_exit_v1.1'

        long_trigger_1 = (dataframe['panic_candle'].shift(1) == 1) & (dataframe['is_green_candle'] == 1)
        long_trigger_2 = (dataframe['panic_candle'].shift(2) == 1) & (dataframe['pause_candle_long'].shift(1) == 1) & (dataframe['is_green_candle'] == 1)
        long_mask = long_trigger_1 | long_trigger_2
        dataframe.loc[long_mask, 'enter_long'] = 1
        dataframe.loc[long_mask, 'enter_tag'] = 'long_fib_exit_v1.1'

        # --- 为使用斐波那契出场的交易计算和存储波段高/低点 ---
        entry_indices = dataframe[(dataframe['enter_long'] == 1) | (dataframe['enter_short'] == 1)].index

        for idx in entry_indices:
            if idx == 0: continue

            signal_candle_idx = -1
            is_short = dataframe.loc[idx, 'enter_short'] == 1
            
            # 定位触发信号的 climax/panic K线
            if is_short:
                if dataframe.loc[idx - 1, 'climax_candle'] == 1:
                    signal_candle_idx = idx - 1
                elif idx > 1 and dataframe.loc[idx - 2, 'climax_candle'] == 1:
                    signal_candle_idx = idx - 2
            else:  # is_long
                if dataframe.loc[idx - 1, 'panic_candle'] == 1:
                    signal_candle_idx = idx - 1
                elif idx > 1 and dataframe.loc[idx - 2, 'panic_candle'] == 1:
                    signal_candle_idx = idx - 2

            if signal_candle_idx != -1 and signal_candle_idx >= self.fib_lookback_period.value:
                signal_candle = dataframe.loc[signal_candle_idx]
                lookback_df = dataframe.loc[signal_candle_idx - self.fib_lookback_period.value : signal_candle_idx - 1]
                
                swing_high = 0
                swing_low = 0
                
                if is_short:
                    swing_low = lookback_df['low'].min()
                    swing_high = signal_candle['high']
                    # For plotting
                    dataframe.loc[idx, 'fib_exit_target'] = swing_high - (swing_high - swing_low) * self.fib_exit_level.value
                else:  # is_long
                    swing_high = lookback_df['high'].max()
                    swing_low = signal_candle['low']
                    # For plotting
                    dataframe.loc[idx, 'fib_exit_target'] = swing_low + (swing_high - swing_low) * self.fib_exit_level.value

                # Store for plotting
                dataframe.loc[idx, 'fib_swing_high'] = swing_high
                dataframe.loc[idx, 'fib_swing_low'] = swing_low

                # Store for custom_exit logic
                self.trade_fib_levels[f"{pair}_{dataframe.loc[idx, 'date']}"] = {
                    'swing_high': swing_high,
                    'swing_low': swing_low
                }
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        return dataframe

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        
        trade_key = f"{pair}_{trade.open_date.replace(tzinfo=None)}"
        fib_data = self.trade_fib_levels.get(trade_key)

        if not fib_data:
            return None # 没有斐波那契数据，不执行此退出逻辑
        
        swing_high = fib_data['swing_high']
        swing_low = fib_data['swing_low']
        price_range = swing_high - swing_low

        if price_range == 0: return None

        if trade.is_short:
            # 做空止盈目标是向下扩展
            exit_target = swing_high - price_range * self.fib_exit_level.value
            if current_rate < exit_target:
                return 'fib_take_profit'
        else: # 做多
            # 做多止盈目标是向上扩展
            exit_target = swing_low + price_range * self.fib_exit_level.value
            if current_rate > exit_target:
                return 'fib_take_profit'
        
        return None

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if dataframe is None or dataframe.empty:
            return self.stoploss
        
        if trade is None or trade.open_date is None:
            return self.stoploss

        try:
            trade_open_date_utc = trade.open_date.replace(tzinfo=pytz.UTC)
            signal_candle_df = dataframe[dataframe['date'] < trade_open_date_utc]
        except Exception:
            return self.stoploss
            
        if signal_candle_df.empty:
            return self.stoploss

        signal_candle_index = signal_candle_df.index[-1]
        lookback_period = self.stoploss_lookback.value
        start_index = max(0, signal_candle_index - lookback_period + 1)
        lookback_df = dataframe.iloc[start_index : signal_candle_index + 1]
        
        if lookback_df.empty:
            return self.stoploss
        
        if trade.is_short:
            return lookback_df['high'].max()
        else:
            return lookback_df['low'].min()

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str, side: str,
                 **kwargs) -> float:

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe is None or dataframe.empty or 'atr' not in dataframe.columns:
            return 1.0

        current_atr = dataframe['atr'].iloc[-1]
        if current_rate == 0 or current_atr == 0:
            return 1.0
        
        atr_pct = current_atr / current_rate
        
        # ATR 越高，杠杆越低
        leverage = self.leverage_target_atr_pct.value / atr_pct
        
        # 确保杠杆在合理范围内
        leverage = max(1.0, leverage)
        leverage = min(leverage, self.leverage_max.value, max_leverage)
        
        return leverage

class PumpAndDumpFib1hHyperopt(PumpAndDumpFib1h):
    pass 