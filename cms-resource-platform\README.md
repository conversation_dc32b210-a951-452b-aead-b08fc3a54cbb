# CMS资源管理平台

一个基于Next.js + Django的图文资源内容管理系统，支持用户权限、积分系统、资源下载等功能。

## 🎯 项目特点

- 📚 **内容管理**: 图文资源展示、分类管理、标签系统
- 👥 **用户系统**: 注册登录、权限管理、积分系统
- 💰 **商业功能**: 会员系统、积分充值、卡密兑换
- 🔒 **安全保护**: 防爬虫、资源加密、下载保护
- 🚀 **高性能**: 缓存优化、CDN加速、响应式设计

## 🏗️ 技术架构

### 前端
- **框架**: Next.js 14 + TypeScript
- **样式**: Tailwind CSS + Ant Design
- **状态管理**: Zustand
- **图片优化**: Next.js Image组件
- **SEO优化**: SSR/SSG支持

### 后端
- **框架**: Django 4.2 + Django REST Framework
- **数据库**: PostgreSQL + Redis缓存
- **认证**: JWT + Django权限系统
- **文件存储**: 本地存储 + CDN
- **任务队列**: Celery + Redis

### 部署
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **缓存**: Redis + 浏览器缓存
- **安全**: HTTPS + 防火墙

## 📁 项目结构

```
cms-resource-platform/
├── frontend/                 # Next.js前端应用
│   ├── src/
│   │   ├── app/             # App Router页面
│   │   ├── components/      # 可复用组件
│   │   ├── lib/            # 工具函数和配置
│   │   ├── hooks/          # 自定义Hooks
│   │   └── types/          # TypeScript类型定义
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                  # Django后端API
│   ├── apps/               # Django应用模块
│   │   ├── users/          # 用户管理
│   │   ├── resources/      # 资源管理
│   │   ├── payments/       # 支付积分
│   │   └── core/           # 核心功能
│   ├── config/             # Django配置
│   ├── static/             # 静态文件
│   └── requirements.txt
├── docker-compose.yml        # 容器编排
├── nginx.conf               # Nginx配置
└── README.md
```

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- Git

### 一键启动

1. **克隆项目**
```bash
git clone <repository-url>
cd cms-resource-platform
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他参数
```

3. **一键启动**
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

### 手动启动

1. **启动数据库服务**
```bash
docker-compose up -d postgres redis
```

2. **运行数据库迁移**
```bash
docker-compose run --rm backend python manage.py migrate
```

3. **创建超级用户**
```bash
docker-compose run --rm backend python manage.py createsuperuser
```

4. **启动所有服务**
```bash
docker-compose up -d
```

### 访问地址
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- 管理后台: http://localhost:8000/admin
- API文档: http://localhost:8000/api/docs/

## 📋 功能模块

### 用户系统
- [x] 用户注册/登录
- [x] 个人资料管理
- [x] 用户组权限
- [x] 积分系统
- [x] 收藏和历史

### 资源管理
- [x] 资源上传投稿
- [x] 分类和标签
- [x] 搜索和筛选
- [x] 图文展示
- [x] 安全下载

### 商业功能
- [x] 会员系统
- [x] 积分充值
- [x] 卡密兑换
- [x] 下载权限控制

### 安全特性
- [x] 防爬虫保护
- [x] 资源加密
- [x] 访问限制
- [x] 数据备份

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/cms_db
REDIS_URL=redis://localhost:6379

# 安全配置
SECRET_KEY=your-secret-key
JWT_SECRET=your-jwt-secret

# 文件存储
MEDIA_ROOT=/path/to/media
STATIC_ROOT=/path/to/static

# 第三方服务
CDN_URL=https://your-cdn.com
EMAIL_HOST=smtp.gmail.com
```

## 📈 性能指标

- **并发支持**: 1000+ 同时在线用户
- **响应时间**: < 200ms (缓存命中)
- **文件上传**: 支持100MB大文件
- **搜索性能**: 全文搜索 < 50ms

## 🛡️ 安全措施

- JWT认证 + 权限控制
- 文件类型验证
- 防SQL注入
- XSS防护
- CSRF保护
- 频率限制
- IP白名单

## 📞 技术支持

如有问题，请联系开发团队或查看文档。

## 📄 许可证

MIT License
