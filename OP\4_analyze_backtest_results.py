import os
import re
import json
import pandas as pd
from datetime import datetime

# --- 配置 ---
# 该脚本通过环境变量从 main.sh 获取配置
# PROJECT_ROOT, STRATEGY_PATH, BT_STRATEGY_NAME

# --- 排名逻辑常量 ---
MIN_WIN_RATE = 50.0 # 最低胜率要求
MINIMUM_TRADES = 1  # 最少交易次数要求
MAX_DRAWDOWN_PCT = 20.0 # 最大回撤要求

# 权重配置 (总和应为 1.0)
WEIGHTS = {
    'profit': 0.45,      # 总利润 (比重最高)
    'drawdown': 0.25,    # 最大回撤 (比重次高)
    'win_rate': 0.25,    # 胜率 (比重次高)
    'duration': 0.05     # 平均持仓时间 (比重较低)
}

def get_config_from_env():
    """从环境变量中获取并验证配置"""
    project_root = os.getenv("PROJECT_ROOT")
    strategy_path = os.getenv("STRATEGY_PATH")
    strategy_name = os.getenv("BT_STRATEGY_NAME")

    if not all([project_root, strategy_path, strategy_name]):
        print("错误: 必要的环境变量 (PROJECT_ROOT, STRATEGY_PATH, BT_STRATEGY_NAME) 未设置。")
        exit(1)
        
    strategy_dir = os.path.join(project_root, "user_data", strategy_path)
    # 输入文件是原始日志
    raw_log_file = os.path.join(strategy_dir, f"{strategy_name}_backtest_raw_log.txt")
    # 输出文件是Markdown报告
    report_file = os.path.join(strategy_dir, f"{strategy_name}_ranked_report.md")

    if not os.path.exists(raw_log_file):
        print(f"错误: 未找到原始日志文件 '{raw_log_file}'。")
        exit(1)

    return raw_log_file, report_file, strategy_name

def parse_duration_to_minutes(duration_str: str) -> int:
    """将 freqtrade 的持仓时间字符串 (例如 '12:42', '58', '2 days, 8:10:00') 解析为总分钟数。"""
    total_minutes = 0
    duration_str = duration_str.strip()

    # 首先处理天数
    if 'day' in duration_str:
        parts = duration_str.split(',')
        days_part = parts[0]
        time_part = parts[1] if len(parts) > 1 else '0:0'
        
        day_match = re.search(r'(\d+)', days_part)
        if day_match:
            total_minutes += int(day_match.group(1)) * 24 * 60
    else:
        time_part = duration_str

    # 然后处理时间部分 (HH:MM:SS, HH:MM, or MM)
    time_parts = time_part.strip().split(':')
    try:
        if len(time_parts) == 1 and time_parts[0].isdigit(): # 只有分钟
            total_minutes += int(time_parts[0])
        elif len(time_parts) == 2: # HH:MM
            total_minutes += int(time_parts[0]) * 60 + int(time_parts[1])
        elif len(time_parts) == 3: # HH:MM:SS
            total_minutes += int(time_parts[0]) * 60 + int(time_parts[1])
    except ValueError:
        # 如果转换失败，返回一个默认值或记录一个错误
        print(f"警告: 无法解析持仓时间 '{duration_str}'，将记为0。")
        return 0
        
    return total_minutes

def parse_summary_file(file_path: str, strategy_name: str) -> list:
    """解析原始回测日志文件并提取每个交易对的关键指标。"""
    print(f"正在解析日志文件: {file_path}")
    results = []
    current_pair = None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 按交易对分割日志块
    pair_blocks = re.split(r'==================== START BACKTEST FOR (.+?) ====================', content)
    
    for i in range(1, len(pair_blocks), 2):
        pair = pair_blocks[i].strip()
        block = pair_blocks[i+1]
        
        # 使用更严格的正则表达式来确保匹配到完整的策略行
        # 它要求必须包含策略名，并且后面至少有6个'│'分隔的列
        data_line_match = re.search(rf'│\s*{re.escape(strategy_name)}\s*│(?:.*│){{6,}}', block)

        if not data_line_match:
            # 对于没有交易的配对，这是正常现象，可以选择静默处理或低级别日志
            # print(f"信息: 未在 {pair} 的日志块中找到完整的策略摘要行，已跳过。")
            continue

        line_content = data_line_match.group(0)
        parts = [p.strip() for p in line_content.split('│')[1:-1]]

        # 添加一个健壮性检查，确保我们有足够的列来解析
        if len(parts) < 8:
            print(f"警告: 解析 {pair} 的数据时发现列数不足({len(parts)}<8)，已跳过。 行: '{line_content}'")
            continue

        try:
            trades = int(parts[1])
            tot_profit_pct = float(parts[4])
            avg_duration_str = parts[5]
            avg_duration = parse_duration_to_minutes(avg_duration_str)
            
            win_rate_str = parts[6].split()[-1]
            win_rate = float(win_rate_str)
            
            drawdown_str = re.search(r'([\d.]+)\s*%$', parts[7])
            drawdown_pct = float(drawdown_str.group(1)) if drawdown_str else 0.0
            
            results.append({
                'pair': pair,
                'trades': trades,
                'profit_pct': tot_profit_pct,
                'win_rate': win_rate,
                'drawdown_pct': drawdown_pct,
                'duration_min': avg_duration
            })
        except (ValueError, IndexError) as e:
            print(f"警告: 解析 {pair} 的数据时出错: {e} - 行: '{line_content}'")
            
    return results

def calculate_composite_score(df: pd.DataFrame) -> pd.DataFrame:
    """计算综合得分。"""
    print("正在计算综合得分...")
    
    # 1. 最小化指标处理 (数值越小越好)
    # 对 'drawdown_pct' 和 'duration_min' 取倒数，这样它们就变成了数值越大越好
    # 为避免除以零，给0值加上一个很小的数
    df['drawdown_inv'] = 1 / (df['drawdown_pct'] + 1e-6)
    df['duration_inv'] = 1 / (df['duration_min'] + 1e-6)
    
    # 2. 归一化 (Min-Max Scaling)
    # 将所有指标缩放到 0-1 区间
    metrics_to_normalize = {
        'profit_pct': 'profit_norm',
        'win_rate': 'win_rate_norm',
        'drawdown_inv': 'drawdown_norm',
        'duration_inv': 'duration_norm'
    }
    
    for metric, new_name in metrics_to_normalize.items():
        min_val = df[metric].min()
        max_val = df[metric].max()
        if (max_val - min_val) == 0:
            df[new_name] = 0.5 # 如果所有值都一样, 则给一个中性分数
        else:
            df[new_name] = (df[metric] - min_val) / (max_val - min_val)
            
    # 3. 加权计算总分
    df['score'] = (
        df['profit_norm'] * WEIGHTS['profit'] +
        df['drawdown_norm'] * WEIGHTS['drawdown'] +
        df['win_rate_norm'] * WEIGHTS['win_rate'] +
        df['duration_norm'] * WEIGHTS['duration']
    ) * 100 # 将分数乘以100，更直观
    
    return df

def generate_report(df: pd.DataFrame, output_path: str, strategy_name: str):
    """生成排版精美的Markdown报告。"""
    print(f"正在生成Markdown报告: {output_path}")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"# {strategy_name} 回测分析报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"基于以下规则进行排名:\n")
        f.write(f"- 最低交易次数: **{MINIMUM_TRADES}**\n")
        f.write(f"- 最低胜率要求: **{MIN_WIN_RATE:.1f}%**\n")
        f.write(f"- 最大回撤要求: **<{MAX_DRAWDOWN_PCT:.1f}%**\n")
        f.write(f"- 总利润要求: **>= 0%**\n")
        f.write(f"- 综合得分权重: `Profit: {WEIGHTS['profit']*100}%`, `Drawdown: {WEIGHTS['drawdown']*100}%`, `Win Rate: {WEIGHTS['win_rate']*100}%`, `Duration: {WEIGHTS['duration']*100}%`\n\n")

        # --- 主排行榜 ---
        f.write("## 🏆 综合表现排行榜\n")
        f.write("根据综合得分排序，得分越高越好。\n\n")
        
        # 准备表格数据
        df_sorted = df.sort_values(by='score', ascending=False)
        
        header = ["Rank", "Pair", "Score", "Profit %", "Drawdown %", "Win Rate %", "Avg Dur (min)", "Trades"]
        
        # 动态计算列宽
        all_rows_as_strings = []
        for i, row in df_sorted.iterrows():
            row_data = [
                str(len(all_rows_as_strings) + 1),
                f"`{row['pair']}`",
                f"**{row['score']:.2f}**",
                f"{row['profit_pct']:.2f}",
                f"{row['drawdown_pct']:.2f}",
                f"{row['win_rate']:.2f}",
                f"{row['duration_min']:.0f}",
                str(row['trades'])
            ]
            all_rows_as_strings.append(row_data)

        col_widths = [len(h) for h in header]
        for row in all_rows_as_strings:
            for i, cell in enumerate(row):
                if len(cell.replace('*','')) > col_widths[i]: # 去除markdown标记计算宽度
                    col_widths[i] = len(cell.replace('*',''))
        
        # 打印表头和分隔线
        header_line = "| " + " | ".join([h.center(col_widths[i]) for i, h in enumerate(header)]) + " |"
        f.write(header_line + "\n")
        sep_line = "|:" + ":|:".join(['-' * col_widths[i] for i in range(len(header))]) + ":|"
        f.write(sep_line + "\n")

        # 打印数据行
        for row in all_rows_as_strings:
            padded_cells = [
                row[0].center(col_widths[0]),
                row[1].ljust(col_widths[1]),
                row[2].rjust(col_widths[2]),
                row[3].rjust(col_widths[3]),
                row[4].rjust(col_widths[4]),
                row[5].rjust(col_widths[5]),
                row[6].rjust(col_widths[6]),
                row[7].rjust(col_widths[7]),
            ]
            f.write("| " + " | ".join(padded_cells) + " |\n")

    print(f"报告已成功生成: {output_path}")

def main():
    """主函数，负责协调整个分析流程。"""
    raw_log_file, report_file, strategy_name = get_config_from_env()
    
    # 1. 解析原始日志文件
    raw_data = parse_summary_file(raw_log_file, strategy_name)
    if not raw_data:
        print("警告: 未能从日志文件中解析出任何数据。")
        # 确保在这种情况下也生成一个空的报告文件，避免下游脚本失败
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# {strategy_name} 回测分析报告\n\n")
            f.write("未能从日志文件中解析出任何有效的策略回测结果。\n")
        return

    df = pd.DataFrame(raw_data)
    
    # 2. 数据筛选
    initial_count = len(df)
    df_filtered = df[
        (df['trades'] >= MINIMUM_TRADES) & 
        (df['win_rate'] >= MIN_WIN_RATE) &
        (df['drawdown_pct'] < MAX_DRAWDOWN_PCT) &
        (df['profit_pct'] >= 0)
    ].copy() # 使用 .copy() 避免 SettingWithCopyWarning
    filtered_count = len(df_filtered)
    print(f"共解析出 {initial_count} 个交易对。经过筛选 (交易次数 >= {MINIMUM_TRADES}, 胜率 >= {MIN_WIN_RATE}%, 最大回撤 < {MAX_DRAWDOWN_PCT}%, 总利润 >= 0) 后，剩余 {filtered_count} 个交易对进入排名。")

    if df_filtered.empty:
        print("警告: 没有交易对满足最低要求，无法生成排名报告。")
        # 仍然生成一个空报告文件
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# {strategy_name} 回测分析报告\n\n")
            f.write("没有交易对满足最低要求，无法生成排名报告。\n")
        return
        
    # 3. 计算综合得分
    df_scored = calculate_composite_score(df_filtered)
    
    # 4. 生成最终报告
    generate_report(df_scored, report_file, strategy_name)

if __name__ == "__main__":
    print("--- 启动回测结果分析与排名脚本 ---")
    main()
    print("--- 脚本执行完毕 ---") 