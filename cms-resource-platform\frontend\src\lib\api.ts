import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import Cookies from 'js-cookie'

// API基础URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理认证错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除认证状态
      Cookies.remove('token')
      Cookies.remove('refresh_token')
      
      // 如果不是登录页面，重定向到登录页
      if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)

// 认证API
export const authAPI = {
  login: (data: { email: string; password: string }) =>
    api.post('/auth/login/', data),
  
  register: (data: any) =>
    api.post('/auth/register/', data),
  
  getMe: () =>
    api.get('/auth/profile/'),
  
  updateProfile: (data: any) =>
    api.put('/auth/profile/', data),
  
  changePassword: (data: { old_password: string; new_password: string; new_password_confirm: string }) =>
    api.post('/auth/password/change/', data),
  
  resetPassword: (email: string) =>
    api.post('/auth/password/reset/', { email }),
  
  logout: () =>
    api.post('/auth/logout/'),
  
  refreshToken: (refreshToken: string) =>
    api.post('/auth/token/refresh/', { refresh: refreshToken }),
}

// 资源API
export const resourceAPI = {
  getResources: (params?: any) =>
    api.get('/resources/', { params }),
  
  getResource: (id: number) =>
    api.get(`/resources/${id}/`),
  
  createResource: (data: any) =>
    api.post('/resources/create/', data),
  
  updateResource: (id: number, data: any) =>
    api.put(`/resources/${id}/update/`, data),
  
  deleteResource: (id: number) =>
    api.delete(`/resources/${id}/delete/`),
  
  downloadResource: (id: number) =>
    api.post(`/resources/${id}/download/`),
  
  favoriteResource: (id: number) =>
    api.post(`/resources/${id}/favorite/`),
  
  unfavoriteResource: (id: number) =>
    api.delete(`/resources/${id}/favorite/`),
  
  rateResource: (id: number, data: { score: number; comment?: string }) =>
    api.post(`/resources/${id}/rating/`, data),
  
  getComments: (id: number) =>
    api.get(`/resources/${id}/comments/`),
  
  addComment: (id: number, data: { content: string; parent?: number }) =>
    api.post(`/resources/${id}/comments/`, data),
  
  getMyResources: () =>
    api.get('/resources/my/'),
  
  getFeaturedResources: () =>
    api.get('/resources/featured/'),
}

// 分类和标签API
export const categoryAPI = {
  getCategories: () =>
    api.get('/resources/categories/'),
  
  getTags: (search?: string) =>
    api.get('/resources/tags/', { params: { search } }),
}

// 用户API
export const userAPI = {
  getStats: () =>
    api.get('/auth/stats/'),
  
  getPointsHistory: () =>
    api.get('/auth/points/history/'),
  
  signIn: () =>
    api.post('/auth/signin/'),
  
  getSignInRecords: () =>
    api.get('/auth/signin/records/'),
  
  getFavorites: () =>
    api.get('/auth/favorites/'),
  
  getHistory: () =>
    api.get('/auth/history/'),
}

// 支付API
export const paymentAPI = {
  getPointsPackages: () =>
    api.get('/payments/points/packages/'),
  
  getVIPPackages: () =>
    api.get('/payments/vip/packages/'),
  
  createOrder: (data: { package_id: number; package_type: string; payment_method: string }) =>
    api.post('/payments/orders/create/', data),
  
  getOrder: (orderNo: string) =>
    api.get(`/payments/orders/${orderNo}/`),
  
  redeemCard: (code: string) =>
    api.post('/payments/cards/redeem/', { code }),
}

// 上传API
export const uploadAPI = {
  uploadFile: (file: File, onProgress?: (progress: number) => void) => {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.post('/upload/file/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      },
    })
  },
  
  uploadImage: (file: File, onProgress?: (progress: number) => void) => {
    const formData = new FormData()
    formData.append('image', file)
    
    return api.post('/upload/image/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      },
    })
  },
}

export default api
