"""
支付和积分系统模型
"""
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel
from apps.users.models import User
import uuid
import secrets


class PointsPackage(BaseModel):
    """积分套餐"""
    name = models.CharField('套餐名称', max_length=100)
    description = models.TextField('描述', blank=True)
    points = models.PositiveIntegerField('积分数量')
    price = models.DecimalField('价格', max_digits=10, decimal_places=2)
    bonus_points = models.PositiveIntegerField('赠送积分', default=0)
    discount_rate = models.DecimalField(
        '折扣率',
        max_digits=5,
        decimal_places=4,
        default=1.0,
        validators=[MinValueValidator(0), MaxValueValidator(1)]
    )
    is_active = models.BooleanField('是否启用', default=True)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    class Meta:
        verbose_name = '积分套餐'
        verbose_name_plural = '积分套餐'
        ordering = ['sort_order', 'price']
    
    def __str__(self):
        return f"{self.name} - {self.points}积分"
    
    @property
    def total_points(self):
        """总积分数"""
        return self.points + self.bonus_points
    
    @property
    def final_price(self):
        """最终价格"""
        return self.price * self.discount_rate


class VIPPackage(BaseModel):
    """VIP套餐"""
    name = models.CharField('套餐名称', max_length=100)
    description = models.TextField('描述', blank=True)
    duration_days = models.PositiveIntegerField('有效天数')
    price = models.DecimalField('价格', max_digits=10, decimal_places=2)
    original_price = models.DecimalField('原价', max_digits=10, decimal_places=2, null=True, blank=True)
    features = models.JSONField('功能特权', default=list)
    bonus_points = models.PositiveIntegerField('赠送积分', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    sort_order = models.PositiveIntegerField('排序', default=0)
    
    class Meta:
        verbose_name = 'VIP套餐'
        verbose_name_plural = 'VIP套餐'
        ordering = ['sort_order', 'duration_days']
    
    def __str__(self):
        return f"{self.name} - {self.duration_days}天"
    
    @property
    def discount_rate(self):
        """折扣率"""
        if self.original_price and self.original_price > 0:
            return self.price / self.original_price
        return 1.0


class PaymentOrder(BaseModel):
    """支付订单"""
    order_no = models.CharField('订单号', max_length=32, unique=True)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='payment_orders',
        verbose_name='用户'
    )
    
    # 订单类型
    order_type = models.CharField(
        '订单类型',
        max_length=20,
        choices=[
            ('points', '积分充值'),
            ('vip', 'VIP购买'),
            ('resource', '资源购买'),
        ]
    )
    
    # 商品信息
    product_id = models.CharField('商品ID', max_length=50, blank=True)
    product_name = models.CharField('商品名称', max_length=200)
    product_description = models.TextField('商品描述', blank=True)
    
    # 金额信息
    amount = models.DecimalField('订单金额', max_digits=10, decimal_places=2)
    discount_amount = models.DecimalField('优惠金额', max_digits=10, decimal_places=2, default=0)
    final_amount = models.DecimalField('实付金额', max_digits=10, decimal_places=2)
    
    # 支付信息
    payment_method = models.CharField(
        '支付方式',
        max_length=20,
        choices=[
            ('alipay', '支付宝'),
            ('wechat', '微信支付'),
            ('balance', '余额支付'),
            ('card', '卡密兑换'),
        ]
    )
    payment_channel = models.CharField('支付渠道', max_length=50, blank=True)
    transaction_id = models.CharField('交易号', max_length=100, blank=True)
    
    # 订单状态
    status = models.CharField(
        '订单状态',
        max_length=20,
        choices=[
            ('pending', '待支付'),
            ('paid', '已支付'),
            ('delivered', '已发货'),
            ('completed', '已完成'),
            ('cancelled', '已取消'),
            ('refunded', '已退款'),
        ],
        default='pending'
    )
    
    # 时间信息
    paid_at = models.DateTimeField('支付时间', null=True, blank=True)
    delivered_at = models.DateTimeField('发货时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    expires_at = models.DateTimeField('过期时间', null=True, blank=True)
    
    # 备注信息
    note = models.TextField('备注', blank=True)
    admin_note = models.TextField('管理员备注', blank=True)
    
    class Meta:
        verbose_name = '支付订单'
        verbose_name_plural = '支付订单'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['order_no']),
            models.Index(fields=['transaction_id']),
        ]
    
    def __str__(self):
        return f"订单{self.order_no} - {self.user.display_name}"
    
    def save(self, *args, **kwargs):
        if not self.order_no:
            self.order_no = self.generate_order_no()
        super().save(*args, **kwargs)
    
    @staticmethod
    def generate_order_no():
        """生成订单号"""
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_str = secrets.token_hex(4).upper()
        return f"ORD{timestamp}{random_str}"


class CardCode(BaseModel):
    """卡密"""
    code = models.CharField('卡密', max_length=32, unique=True)
    card_type = models.CharField(
        '卡密类型',
        max_length=20,
        choices=[
            ('points', '积分卡'),
            ('vip', 'VIP卡'),
            ('discount', '折扣卡'),
        ]
    )
    
    # 卡密价值
    points_value = models.PositiveIntegerField('积分价值', default=0)
    vip_days = models.PositiveIntegerField('VIP天数', default=0)
    discount_rate = models.DecimalField(
        '折扣率',
        max_digits=5,
        decimal_places=4,
        default=1.0,
        validators=[MinValueValidator(0), MaxValueValidator(1)]
    )
    
    # 使用信息
    is_used = models.BooleanField('是否已使用', default=False)
    used_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='used_cards',
        verbose_name='使用者'
    )
    used_at = models.DateTimeField('使用时间', null=True, blank=True)
    
    # 有效期
    expires_at = models.DateTimeField('过期时间', null=True, blank=True)
    
    # 批次信息
    batch_no = models.CharField('批次号', max_length=32, blank=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_cards',
        verbose_name='创建者'
    )
    
    class Meta:
        verbose_name = '卡密'
        verbose_name_plural = '卡密'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_used', '-created_at']),
            models.Index(fields=['batch_no']),
        ]
    
    def __str__(self):
        return f"卡密{self.code} - {self.get_card_type_display()}"
    
    def save(self, *args, **kwargs):
        if not self.code:
            self.code = self.generate_code()
        super().save(*args, **kwargs)
    
    @staticmethod
    def generate_code():
        """生成卡密"""
        return secrets.token_hex(16).upper()
    
    def is_valid(self):
        """检查卡密是否有效"""
        if self.is_used:
            return False
        if self.expires_at and self.expires_at < timezone.now():
            return False
        return True
    
    def use(self, user):
        """使用卡密"""
        if not self.is_valid():
            raise ValueError('卡密无效或已过期')
        
        self.is_used = True
        self.used_by = user
        self.used_at = timezone.now()
        self.save()
        
        # 根据卡密类型给用户相应奖励
        if self.card_type == 'points' and self.points_value > 0:
            user.add_points(self.points_value, f'使用积分卡密 {self.code}')
        elif self.card_type == 'vip' and self.vip_days > 0:
            # 延长VIP时间
            if user.vip_expire_date and user.vip_expire_date > timezone.now():
                user.vip_expire_date += timezone.timedelta(days=self.vip_days)
            else:
                user.vip_expire_date = timezone.now() + timezone.timedelta(days=self.vip_days)
            user.is_vip = True
            user.save()


class Withdrawal(BaseModel):
    """提现申请"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='withdrawals',
        verbose_name='用户'
    )
    amount = models.DecimalField('提现金额', max_digits=10, decimal_places=2)
    fee = models.DecimalField('手续费', max_digits=10, decimal_places=2, default=0)
    actual_amount = models.DecimalField('实际到账', max_digits=10, decimal_places=2)
    
    # 提现方式
    withdraw_type = models.CharField(
        '提现方式',
        max_length=20,
        choices=[
            ('alipay', '支付宝'),
            ('wechat', '微信'),
            ('bank', '银行卡'),
        ]
    )
    account_info = models.JSONField('账户信息', default=dict)
    
    # 状态
    status = models.CharField(
        '状态',
        max_length=20,
        choices=[
            ('pending', '待审核'),
            ('approved', '已审核'),
            ('processing', '处理中'),
            ('completed', '已完成'),
            ('rejected', '已拒绝'),
        ],
        default='pending'
    )
    
    # 处理信息
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_withdrawals',
        verbose_name='处理人'
    )
    processed_at = models.DateTimeField('处理时间', null=True, blank=True)
    reject_reason = models.TextField('拒绝原因', blank=True)
    transaction_id = models.CharField('交易号', max_length=100, blank=True)
    
    class Meta:
        verbose_name = '提现申请'
        verbose_name_plural = '提现申请'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 提现 {self.amount}元"
