# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy
from pandas import DataFrame
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# --------------------------------
#   Heikin Ashi Pivot Breakout Strategy (HAPBS) - Pair Optimized
#
#   作者: Gemini & User
#   版本: PairOptimized
#
#   策略理念:
#   - 本策略是 HAPBS_Final 的一个变体，允许为每个交易对配置独立的优化参数。
#   - 它从一个 JSON 配置文件 (HAPBS_PairOptimized_Settings.json) 中读取每个交易对的参数设置。
#   - 核心逻辑与 Final 版本保持一致，但在指标计算和入场条件判断时，会使用对应交易对的专属参数。
# --------------------------------

class HAPBS_PairOptimizedV2(IStrategy):

    def __init__(self, config: dict):
        super().__init__(config)
        self.load_pair_settings()

    def load_pair_settings(self) -> None:
        class_name = self.__class__.__name__
        settings_filename = Path(__file__).parent / f"{class_name}_Settings.json"
        
        try:
            with open(settings_filename, "r") as f:
                self.custom_info = json.load(f)
                logger.info(f"Successfully loaded pair settings from {settings_filename}.")
        except FileNotFoundError:
            logger.warning(f"Settings file not found at {settings_filename}. This strategy requires it to function.")
            self.custom_info = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from settings file: {e}. Strategy may not function correctly.")
            self.custom_info = {}

    # --- Freqtrade 核心配置 ---
    timeframe = '15m'
    can_short = True
    process_only_new_candles = True
    startup_candle_count: int = 200

    # --- 禁用全局风控参数，因为我们将使用自定义回调函数 ---
    stoploss = -0.99 
    trailing_stop = True
    minimal_roi = {}
    
    # --- 激活自定义风控回调 ---
    use_custom_stoploss = True
    use_custom_exit = True

    # --- 图表配置 ---
    plot_config = {
        'main_plot': {
            'ema_short_long': {'color': 'blue', 'linestyle': '-'},
            'ema_long_long': {'color': 'cyan', 'linestyle': '-'},
            'ema_short_short': {'color': 'red', 'linestyle': '--'},
            'ema_long_short': {'color': 'magenta', 'linestyle': '--'},
            'ema_200': {'color': 'black', 'linestyle': ':'},
        },
        'subplots': {
            "ADX": {
                'adx': {'color': 'green'},
            },
        },
    }

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        为每个交易对应用从JSON加载的自定义初始止损。
        """
        if pair in self.custom_info and 'risk_management' in self.custom_info[pair]:
            return self.custom_info[pair]['risk_management'].get('stoploss', -0.99)
        # 如果没有找到特定设置，返回一个非常大的值以避免意外触发
        return -0.99

    def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float,
                    current_profit: float, **kwargs):
        """
        为每个交易对应用从JSON加载的自定义ROI和追踪止损逻辑。
        """
        if pair not in self.custom_info or 'risk_management' not in self.custom_info[pair]:
            return None # 没有特定设置，不执行任何操作

        rm_settings = self.custom_info[pair]['risk_management']

        # --- 自定义ROI逻辑 ---
        if 'minimal_roi' in rm_settings:
            trade_duration = (current_time - trade.open_date_utc).total_seconds() / 60
            for key_minutes, roi_value in rm_settings['minimal_roi'].items():
                if trade_duration > int(key_minutes) and current_profit > roi_value:
                    return f"roi_json_{key_minutes}m_{roi_value*100}%"
        
        # --- 自定义追踪止损逻辑 ---
        if rm_settings.get('trailing_stop', False):
            # 激活追踪止损
            if current_profit > rm_settings.get('trailing_stop_positive', 0):
                # 只有当达到偏移量时才进行追踪
                if rm_settings.get('trailing_only_offset_is_reached', False) and \
                   current_profit < (rm_settings.get('trailing_stop_positive', 0) + rm_settings.get('trailing_stop_positive_offset', 0)):
                    return None

                # 计算新的止损价格
                offset = rm_settings.get('trailing_stop_positive_offset', 0)
                if trade.is_short:
                    new_stop_price = current_rate * (1 + offset)
                    # 高水位逻辑: 止损只下移
                    trade.stop_loss = min(trade.stop_loss, new_stop_price)
                else:
                    new_stop_price = current_rate * (1 - offset)
                    # 高水位逻辑: 止损只上移
                    trade.stop_loss = max(trade.stop_loss, new_stop_price)
        
        return None # 本函数只用于修改止损或产生离场信号，常规情况返回None

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        if pair not in self.custom_info:
            logger.warning(f"No settings found for pair {pair} in JSON file. Skipping indicator population.")
            return dataframe

        pair_settings = self.custom_info[pair]

        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe['ha_open'] = heikinashi['open']
        dataframe['ha_close'] = heikinashi['close']
        dataframe['ha_high'] = heikinashi['high']
        dataframe['ha_low'] = heikinashi['low']
        
        dataframe['ha_strong_bear'] = (
            (dataframe['ha_close'] < dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_high'])
        )
        
        dataframe['ha_strong_bull'] = (
            (dataframe['ha_close'] > dataframe['ha_open']) &
            (dataframe['ha_open'] == dataframe['ha_low'])
        )

        dataframe['ema_short_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_long'])
        dataframe['ema_long_long'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_long'])
        dataframe['ema_short_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_short_period_short'])
        dataframe['ema_long_short'] = ta.EMA(dataframe, timeperiod=pair_settings['ema_long_period_short'])
        
        # 使用JSON中为该交易对指定的长期过滤周期，如果未指定，则默认为200
        long_filter_period = pair_settings.get('ema_long_filter_period', 200)
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=long_filter_period)
        
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['volume_mean'] = dataframe['volume'].rolling(window=20).mean()
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        pair = metadata['pair']
        if pair not in self.custom_info:
            return dataframe

        pair_settings = self.custom_info[pair]

        long_conditions = (
            (dataframe['ema_short_long'] > dataframe['ema_long_long']) &
            (dataframe['ema_long_long'] > dataframe['ema_200']) &
            (qtpylib.crossed_above(dataframe['ha_close'], dataframe['ema_short_long'])) &
            (dataframe['ha_strong_bull']) &
            (dataframe['adx'] > pair_settings['adx_threshold_long']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_long'])
        )
        dataframe.loc[long_conditions, ['enter_long', 'enter_tag']] = (1, 'long_final')

        short_conditions = (
            (dataframe['ema_short_short'] < dataframe['ema_long_short']) &
            (dataframe['ema_long_short'] < dataframe['ema_200']) &
            (qtpylib.crossed_below(dataframe['ha_close'], dataframe['ema_short_short'])) &
            (dataframe['ha_strong_bear']) &
            (dataframe['adx'] > pair_settings['adx_threshold_short']) &
            (dataframe['volume'] > dataframe['volume_mean'] * pair_settings['volume_factor_short'])
        )
        dataframe.loc[short_conditions, ['enter_short', 'enter_tag']] = (1, 'short_final')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return dataframe 